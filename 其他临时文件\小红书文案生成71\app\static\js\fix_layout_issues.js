/**
 * 修复布局问题的脚本
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('开始修复布局问题...');
    
    // 1. 移除所有顶部标题布局和右侧顶部条
    function removeTopLayouts() {
        // 移除右侧顶部的标题条
        const topHeaders = document.querySelectorAll('.top-header, .page-header, .content-header');
        topHeaders.forEach(header => {
            console.log('移除顶部标题条:', header);
            header.remove();
        });

        // 移除主内容区域的第一个子元素（如果不是标签页或内容区域）
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            const firstChild = mainContent.firstElementChild;
            if (firstChild &&
                !firstChild.classList.contains('tab-navigation') &&
                !firstChild.classList.contains('content-area')) {
                console.log('移除主内容区域顶部元素:', firstChild);
                firstChild.remove();
            }
        }

        const selectors = [
            '.d-flex.justify-content-between.align-items-center',
            '.d-flex.justify-content-between',
            '.d-flex.align-items-center'
        ];

        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                // 检查是否包含标题
                const hasTitle = el.querySelector('h1, h2, h3, .h3, .page-title');
                const hasButtons = el.querySelector('.btn, button, a[class*="btn"]');
                const isInMainContent = el.closest('.main-content');

                if (hasTitle && isInMainContent) {
                    if (hasButtons) {
                        // 如果有按钮，只移除标题，保留按钮容器
                        console.log('移除标题，保留按钮容器:', el);
                        const titles = el.querySelectorAll('h1, h2, h3, .h1, .h2, .h3, .page-title');
                        titles.forEach(title => title.remove());

                        // 调整容器样式
                        el.style.marginBottom = '1rem';
                        el.style.justifyContent = 'flex-end';
                    } else {
                        // 如果没有按钮，移除整个容器
                        console.log('移除整个标题布局:', el);
                        el.remove();
                    }
                }
            });
        });
    }
    
    // 2. 移除所有页面标题
    function removeTitles() {
        const titles = document.querySelectorAll('h1, .h3, .page-title');
        titles.forEach(title => {
            console.log('移除标题:', title.textContent);
            title.remove();
        });
    }
    
    // 3. 修复控制面板标签点击 - 已禁用，由标签管理器接管
    function fixDashboardTab() {
        console.log('控制面板处理已移至标签管理器，跳过fix_layout_issues的处理');
        return;

        // 以下代码已禁用，避免与tabs.js冲突
        /*
        const dashboardTab = document.querySelector('.tab-item[data-url="/dashboard"]');
        if (dashboardTab) {
            console.log('修复控制面板标签点击');
            dashboardTab.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('控制面板标签被点击');

                // 激活标签
                document.querySelectorAll('.tab-item').forEach(tab => {
                    tab.classList.remove('active');
                });
                this.classList.add('active');

                // 加载控制面板内容
                loadDashboardContent();
            });
        }
        */
    }

    // 4. 加载控制面板内容 - 已禁用，由标签管理器接管
    function loadDashboardContent() {
        console.log('控制面板内容加载已移至标签管理器');
        return;

        // 以下代码已禁用
        /*
        const contentArea = document.querySelector('.content-area');
        if (contentArea) {
            console.log('加载控制面板内容');
            contentArea.innerHTML = `
                <div class="container-fluid">
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card shadow">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">欢迎使用小红书文案生成系统</h5>
                                </div>
                                <div class="card-body">
                                    <p>您好，欢迎使用系统！</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        */
    }
    
    // 执行修复
    setTimeout(() => {
        removeTopLayouts();
        removeTitles();
        fixDashboardTab();
    }, 100);
    
    // 监听内容变化
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                setTimeout(() => {
                    removeTopLayouts();
                    removeTitles();
                }, 50);
            }
        });
    });
    
    const contentArea = document.querySelector('.content-area');
    if (contentArea) {
        observer.observe(contentArea, { childList: true, subtree: true });
    }
});

// 全局函数供调试使用
window.debugLayout = function() {
    console.log('=== 布局调试信息 ===');
    console.log('标签页数量:', document.querySelectorAll('.tab-item').length);
    console.log('内容区域:', document.querySelector('.content-area'));
    console.log('顶部布局:', document.querySelectorAll('.d-flex.justify-content-between').length);
    console.log('标题数量:', document.querySelectorAll('h1, .h3').length);
};
