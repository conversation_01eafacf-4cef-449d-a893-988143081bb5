<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}小红书文案生成系统{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% if current_user.is_authenticated %}
    <link rel="stylesheet" href="{{ url_for('static', filename='css/sidebar.css') }}">
    {% else %}
    <!-- 未登录页面专用样式 -->
    <style>
        /* 重置所有样式 */
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0 !important;
            padding: 0 !important;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            overflow-x: hidden !important;
        }

        /* 强制重置Bootstrap的容器样式 */
        .container, .container-fluid, .row, .col, [class*="col-"] {
            margin: 0 !important;
            padding: 0 !important;
            width: auto !important;
            max-width: none !important;
        }

        .auth-container {
            min-height: 100vh !important;
            width: 100vw !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            padding: 20px !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            z-index: 9999 !important;
        }

        .auth-container .card {
            max-width: 400px !important;
            width: 100% !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
            border: none !important;
            border-radius: 15px !important;
            overflow: hidden !important;
            margin: 0 !important;
        }

        .auth-container .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            text-align: center !important;
            padding: 30px 20px !important;
            border: none !important;
        }

        .auth-container .card-body {
            padding: 30px !important;
            background: white !important;
        }

        .auth-container .form-control {
            border-radius: 8px !important;
            border: 2px solid #e1e5e9 !important;
            padding: 12px 15px !important;
            font-size: 16px !important;
            transition: all 0.3s ease !important;
            width: 100% !important;
        }

        .auth-container .form-control:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
        }

        .auth-container .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            border: none !important;
            border-radius: 8px !important;
            padding: 12px 30px !important;
            font-weight: 600 !important;
            width: 100% !important;
            transition: all 0.3s ease !important;
        }

        .auth-container .btn-primary:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4) !important;
        }

        /* 隐藏可能的其他元素 */
        .flash-messages {
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            z-index: 10000 !important;
            max-width: 400px !important;
        }
    </style>
    {% endif %}
    {% block styles %}{% endblock %}
</head>
<body>
    {% if current_user.is_authenticated %}
    <!-- 左侧菜单布局 -->
    <div class="app-container">
        <!-- 左侧菜单 -->
        <aside class="sidebar" id="sidebar">
            <!-- 品牌标识 -->
            <div class="sidebar-header">
                <div class="brand">
                    <i class="fas fa-edit"></i>
                    <span class="brand-text">文案生成系统</span>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            
            <!-- 用户信息 -->
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-details">
                    <div class="user-name">{{ current_user.username }}</div>
                    <div class="user-role">{{ current_user.real_name or '用户' }}</div>
                </div>
                <div class="user-menu dropdown">
                    <button class="btn btn-link dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('navigation.my_permissions') }}" data-ajax-link>
                            <i class="fas fa-key me-2"></i>我的权限
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}" data-ajax-link>
                            <i class="fas fa-lock me-2"></i>修改密码
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                            <i class="fas fa-sign-out-alt me-2"></i>退出登录
                        </a></li>
                    </ul>
                </div>
            </div>
            
            <!-- 菜单导航 -->
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    {% set menu_items = get_user_menu_items() %}
                    {% for item in menu_items %}
                        {% if not item.parent_id %}
                            <li class="nav-item">
                                <a href="{{ item.url }}" class="nav-link" 
                                   {% if not item.url.startswith('http') and not item.url.startswith('/admin/') %}data-ajax-link{% endif %}>
                                    <i class="{{ item.icon or 'fas fa-circle' }}"></i>
                                    <span class="nav-text">{{ item.name }}</span>
                                    {% if item.name == '通知中心' %}
                                        <span class="badge bg-danger notification-badge" id="sidebarNotificationBadge" style="display: none;">0</span>
                                    {% endif %}
                                </a>
                            </li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 标签页导航 - 紧贴顶部 -->
            <div class="tab-navigation" id="tabNavigation">
                <div class="tab-list" id="tabList">
                    <div class="tab-item active" data-url="/dashboard" data-title="控制面板">
                        <span class="tab-title">控制面板</span>
                        <button class="tab-close" onclick="closeTab('/dashboard')" style="display: none;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="tab-controls">
                    <button class="btn btn-sm btn-outline-secondary" onclick="closeAllTabs()" title="关闭所有标签">
                        <i class="fas fa-times-circle"></i>
                    </button>
                </div>
            </div>

            <!-- 页面内容 -->
            <div class="content-wrapper" id="main-content">
                <div class="content-area">
                    {% block content_auth %}{% endblock %}
                </div>
            </div>
        </main>
    </div>
    {% else %}
    <!-- 未登录用户的内容 -->
    <div class="auth-container" id="auth-container">
        <div class="auth-card-wrapper">
            {% block content %}{% endblock %}
        </div>
    </div>
    {% endif %}

    <!-- Flash 消息 -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>

    {% if current_user.is_authenticated %}
    <!-- 侧边栏JS -->
    <script src="{{ url_for('static', filename='js/sidebar.js') }}"></script>
    <!-- 标签页JS -->
    <script src="{{ url_for('static', filename='js/tabs.js') }}"></script>
    <!-- 布局修复JS -->
    <script src="{{ url_for('static', filename='js/fix_layout_issues.js') }}"></script>
    <!-- AJAX导航JS -->
    <script src="{{ url_for('static', filename='js/ajax-nav.js') }}"></script>

    <!-- 立即执行的菜单显示脚本 -->
    <script>
        // 立即执行，确保菜单显示
        $(document).ready(function() {
            console.log('初始化侧边栏');
        });
    </script>
    {% endif %}
    
    {% block scripts %}{% endblock %}
</body>
</html>
