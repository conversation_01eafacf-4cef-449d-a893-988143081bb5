"""
权限导航视图
"""
from flask import Blueprint, render_template
from flask_login import login_required, current_user

navigation = Blueprint('navigation', __name__, url_prefix='/nav')

def get_user_menu_items():
    """获取用户菜单项（供模板使用）"""
    if not current_user.is_authenticated:
        return []

    # 定义所有菜单项及其权限要求
    all_menu_items = [
        {
            'name': '控制面板',
            'url': '/dashboard',
            'icon': 'fas fa-tachometer-alt',
            'permission': 'dashboard_access',
            'parent_id': None,
            'order': 1
        },
        # 用户管理菜单
        {
            'name': '用户管理',
            'url': '/user-management/users',
            'icon': 'fas fa-users',
            'permission': 'user_manage',
            'parent_id': None,
            'order': 2
        },
        # 客户管理菜单
        {
            'name': '客户管理',
            'url': '/clients/',
            'icon': 'fas fa-building',
            'permission': 'client_manage',
            'parent_id': None,
            'order': 3
        },
        # 模板管理菜单
        {
            'name': '模板管理',
            'url': '/templates/',
            'icon': 'fas fa-layer-group',
            'permission': 'template_manage',
            'parent_id': None,
            'order': 4
        },
        # 初审文案菜单
        {
            'name': '初审文案',
            'url': '/contents/',
            'icon': 'fas fa-clipboard-check',
            'permission': 'content_manage',
            'parent_id': None,
            'order': 5
        },
        # 生成文案菜单
        {
            'name': '生成文案',
            'url': '/contents/generate',
            'icon': 'fas fa-plus-circle',
            'permission': 'content_generate',
            'parent_id': None,
            'order': 6
        },
        # 话题管理菜单
        {
            'name': '话题管理',
            'url': '/topics/',
            'icon': 'fas fa-hashtag',
            'permission': 'topic_manage',
            'parent_id': None,
            'order': 7
        },
        # 任务管理菜单
        {
            'name': '任务管理',
            'url': '/tasks/',
            'icon': 'fas fa-tasks',
            'permission': 'task_manage',
            'parent_id': None,
            'order': 8
        },
        # 发布管理菜单
        {
            'name': '发布管理',
            'url': '/publish/',
            'icon': 'fas fa-paper-plane',
            'permission': 'publish_manage',
            'parent_id': None,
            'order': 9
        },
        # 文案补充菜单
        {
            'name': '文案补充',
            'url': '/supplement/',
            'icon': 'fas fa-sync',
            'permission': 'supplement_manage',
            'parent_id': None,
            'order': 10
        },
        # 文案展示菜单
        {
            'name': '文案展示',
            'url': '/display/',
            'icon': 'fas fa-calendar',
            'permission': 'display_manage',
            'parent_id': None,
            'order': 11
        },
        # 通知中心菜单
        {
            'name': '通知中心',
            'url': '/notifications/',
            'icon': 'fas fa-bell',
            'permission': 'notification_manage',
            'parent_id': None,
            'order': 12
        },
        # 数据统计菜单
        {
            'name': '数据统计',
            'url': '/stats/',
            'icon': 'fas fa-chart-bar',
            'permission': 'stats_view',
            'parent_id': None,
            'order': 13
        },
        # 导入导出菜单
        {
            'name': '导入导出',
            'url': '/export/',
            'icon': 'fas fa-file-export',
            'permission': 'export_manage',
            'parent_id': None,
            'order': 14
        },
        # 系统设置菜单
        {
            'name': '系统设置',
            'url': '/system/',
            'icon': 'fas fa-cog',
            'permission': 'system_settings',
            'parent_id': None,
            'order': 15
        }
    ]

    # 过滤出用户有权限的菜单项
    user_menu_items = []
    for item in all_menu_items:
        # 如果没有权限要求，或者用户有对应权限
        if not item['permission'] or current_user.has_permission(item['permission']):
            user_menu_items.append(type('MenuItem', (), item)())

    return sorted(user_menu_items, key=lambda x: x.order)

@navigation.route('/permissions')
@login_required
def permissions():
    """显示用户有权限访问的所有功能"""
    
    # 定义所有功能模块及其权限要求
    all_functions = [
        {
            'name': '首页',
            'url': '/',
            'icon': 'fas fa-home',
            'permission': None,
            'description': '系统首页和概览'
        },
        # 用户管理功能
        {
            'name': '用户管理',
            'url': '/user-management/users',
            'icon': 'fas fa-users',
            'permission': 'user.view',
            'description': '管理系统用户'
        },
        # 客户管理功能
        {
            'name': '客户管理',
            'url': '/clients/',
            'icon': 'fas fa-building',
            'permission': 'client.view',
            'description': '管理客户信息'
        },
        # 模板管理功能
        {
            'name': '模板管理',
            'url': '/templates/',
            'icon': 'fas fa-layer-group',
            'permission': 'template.view',
            'description': '管理文案模板'
        },
        # 文案管理功能
        {
            'name': '文案管理',
            'url': '/contents/',
            'icon': 'fas fa-file-alt',
            'permission': 'content.view',
            'description': '查看、编辑和管理文案'
        },
        # 初审管理功能
        {
            'name': '初审管理',
            'url': '/first-review/',
            'icon': 'fas fa-check-circle',
            'permission': 'first_review.view',
            'description': '初审文案内容'
        },
        # 图片管理功能
        {
            'name': '图片管理',
            'url': '/image-management/',
            'icon': 'fas fa-images',
            'permission': 'image.view',
            'description': '管理文案图片'
        },
        # 终审管理功能
        {
            'name': '终审管理',
            'url': '/final-review/',
            'icon': 'fas fa-clipboard-check',
            'permission': 'final_review.view',
            'description': '终审文案内容'
        },
        # 发布管理功能
        {
            'name': '发布管理',
            'url': '/publish/',
            'icon': 'fas fa-paper-plane',
            'permission': 'publish.view',
            'description': '管理文案发布'
        },
        # 通知中心功能
        {
            'name': '通知中心',
            'url': '/notifications/',
            'icon': 'fas fa-bell',
            'permission': None,
            'description': '查看系统通知'
        },
        # 系统设置功能
        {
            'name': '系统设置',
            'url': '/system/',
            'icon': 'fas fa-cog',
            'permission': 'system.view',
            'description': '系统配置设置'
        }
    ]
    
    # 过滤出用户有权限访问的功能
    user_functions = []
    for func in all_functions:
        # 如果没有权限要求，或者用户有对应权限
        if not func['permission'] or current_user.has_permission(func['permission']):
            user_functions.append(func)
    
    return render_template('navigation/permissions.html', 
                         functions=user_functions,
                         title='功能导航')

@navigation.route('/my-permissions')
@login_required
def my_permissions():
    """显示用户的权限详情"""

    # 获取用户的所有权限
    user_permissions = []

    # 直接分配的权限
    for perm in current_user.permissions:
        user_permissions.append({
            'name': perm.name,
            'description': perm.description,
            'source': '直接分配'
        })

    # 通过角色获得的权限
    for role in current_user.roles:
        for perm in role.permissions:
            # 避免重复
            if not any(p['name'] == perm.name for p in user_permissions):
                user_permissions.append({
                    'name': perm.name,
                    'description': perm.description,
                    'source': f'角色: {role.name}'
                })

    return render_template('navigation/my_permissions.html',
                         permissions=user_permissions,
                         title='我的权限')
