{% extends "base_simple.html" %}

{% block title %}{{ title }} - 小红书文案生成系统{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
{% endblock %}

{% block content_auth %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h2>{{ title }}</h2>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        <div class="mb-3">
                            {{ form.name.label(class="form-label") }}
                            {{ form.name(class="form-control") }}
                            {% if form.name.errors %}
                                {% for error in form.name.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.parent_id.label(class="form-label") }}
                            {{ form.parent_id(class="form-select") }}
                            {% if form.parent_id.errors %}
                                {% for error in form.parent_id.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.sort_order.label(class="form-label") }}
                            {{ form.sort_order(class="form-control") }}
                            {% if form.sort_order.errors %}
                                {% for error in form.sort_order.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                            <div class="form-text">数字越小排序越靠前</div>
                        </div>
                        <div class="d-grid gap-2">
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 