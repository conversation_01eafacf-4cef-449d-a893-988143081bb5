{% extends "base.html" %}

{% block title %}模板管理 - 小红书文案生成系统{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
<style>
    .template-actions {
        white-space: nowrap;
    }
    .template-status {
        width: 80px;
    }
    /* 添加标记样式 */
    .template-mark {
        background-color: #e9f5ff;
        border: 1px solid #b3d7ff;
        border-radius: 3px;
        padding: 2px 4px;
        font-weight: bold;
        color: #0066cc;
        display: inline-block;
        margin: 0 2px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    /* 筛选区域样式 */
    .filter-section {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        border: 1px solid #e9ecef;
    }
    /* 状态切换按钮样式 */
    .status-toggle {
        cursor: pointer;
    }
    .status-toggle:hover {
        opacity: 0.8;
    }
</style>
{% endblock %}

{% block content_auth %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>模板管理</h2>
        </div>
        <div class="col-md-4 text-end">
            <button type="button" class="btn btn-primary" onclick="showAddTemplateModal()">
                <i class="bi bi-plus-lg"></i> 添加模板
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="showCategoriesModal()">
                <i class="bi bi-folder"></i> 分类管理
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="showMarksModal()">
                <i class="bi bi-tag"></i> 标记管理
            </button>
        </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section">
        <form id="filterForm" method="GET" action="{{ url_for('template.index') }}">
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="category" class="form-label">分类筛选</label>
                        <select class="form-select" id="category" name="category_id">
                            <option value="">全部分类</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if request.args.get('category_id')|int == category.id %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="status" class="form-label">状态筛选</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">全部状态</option>
                            <option value="1" {% if request.args.get('status') == '1' %}selected{% endif %}>启用</option>
                            <option value="0" {% if request.args.get('status') == '0' %}selected{% endif %}>禁用</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <div class="mb-3 w-100">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search"></i> 筛选
                        </button>
                        <a href="{{ url_for('template.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i> 重置
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>标题</th>
                            <th>分类</th>
                            <th>创建者</th>
                            <th>更新时间</th>
                            <th class="template-status">状态</th>
                            <th class="template-actions">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for template in templates %}
                        <tr>
                            <td>{{ template.id }}</td>
                            <td class="template-title">{% set formatted_title = template.title|replace('{', '<span class="template-mark">《')|replace('}', '》</span>') %}{{ formatted_title|safe }}</td>
                            <td>{{ template.category.name }}</td>
                            <td>{{ template.creator.real_name or template.creator.username }}</td>
                            <td>{{ template.updated_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td class="template-status">
                                <span class="badge status-toggle {% if template.status %}bg-success{% else %}bg-secondary{% endif %}" 
                                      data-id="{{ template.id }}" 
                                      data-status="{{ template.status|int }}">
                                    {% if template.status %}启用{% else %}禁用{% endif %}
                                </span>
                            </td>
                            <td class="template-actions">
                                <a href="{{ url_for('template.preview_template', id=template.id) }}" class="btn btn-sm btn-info" title="预览">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ url_for('template.edit_template', id=template.id) }}" class="btn btn-sm btn-primary" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <button class="btn btn-sm btn-danger delete-template" data-id="{{ template.id }}" title="删除">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="7" class="text-center">暂无模板数据</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个模板吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 立即执行函数，确保在DOM加载完成后立即处理标记
    (function() {
        // 处理标题中的标记，将{标记名}转换为《标记名》并添加样式
        function processTemplateTitles() {
            document.querySelectorAll('.template-title').forEach(function(titleElement) {
                let title = titleElement.innerHTML;
                // 匹配 {标记名} 格式的内容并转换为《标记名》
                title = title.replace(/\{([^}]+)\}/g, '<span class="template-mark">《$1》</span>');
                titleElement.innerHTML = title;
            });
        }
        
        // 立即执行一次处理
        processTemplateTitles();
        
        // 在DOM完全加载后再次执行，确保处理所有元素
        document.addEventListener('DOMContentLoaded', function() {
            // 再次处理标题，确保不会遗漏
            processTemplateTitles();
            
            // 删除模板
            let deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            let templateId = null;
            
            // 点击删除按钮
            document.querySelectorAll('.delete-template').forEach(function(button) {
                button.addEventListener('click', function() {
                    templateId = this.getAttribute('data-id');
                    deleteModal.show();
                });
            });
            
            // 确认删除
            document.getElementById('confirmDelete').addEventListener('click', function() {
                if (templateId) {
                    fetch(`/templates/delete/${templateId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': '{{ csrf_token() }}'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        deleteModal.hide();
                        if (data.success) {
                            // 删除成功，刷新页面
                            location.reload();
                        } else {
                            // 删除失败，显示错误信息
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('删除失败，请重试');
                    });
                }
            });
            
            // 状态切换
            document.querySelectorAll('.status-toggle').forEach(function(badge) {
                badge.addEventListener('click', function() {
                    const templateId = this.getAttribute('data-id');
                    const currentStatus = parseInt(this.getAttribute('data-status'));
                    const newStatus = currentStatus ? 0 : 1;
                    
                    // 显示加载状态
                    const originalText = this.textContent;
                    this.textContent = '更新中...';
                    
                    // 发送状态更新请求
                    fetch(`/templates/toggle_status/${templateId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({ status: newStatus })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 更新成功，更新UI
                            this.setAttribute('data-status', newStatus);
                            if (newStatus) {
                                this.textContent = '启用';
                                this.classList.remove('bg-secondary');
                                this.classList.add('bg-success');
                            } else {
                                this.textContent = '禁用';
                                this.classList.remove('bg-success');
                                this.classList.add('bg-secondary');
                            }
                        } else {
                            // 更新失败，恢复原状态
                            this.textContent = originalText;
                            alert(data.message || '状态更新失败');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        this.textContent = originalText;
                        alert('状态更新失败，请重试');
                    });
                });
            });
            
            // 筛选表单自动提交
            document.getElementById('category').addEventListener('change', function() {
                document.getElementById('filterForm').submit();
            });
            
            document.getElementById('status').addEventListener('change', function() {
                document.getElementById('filterForm').submit();
            });
        });
    })();
</script>

<!-- 模态框相关的JavaScript函数 - 立即定义为全局函数 -->
<script>
    // 确保函数在全局作用域中定义
    window.showAddTemplateModal = function() {
        console.log('显示添加模板模态框');
        const modal = new bootstrap.Modal(document.getElementById('addTemplateModal'));
        modal.show();

        // 加载添加模板表单
        fetch('/templates/add', {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        })
        .then(response => {
            console.log('添加模板响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.text();
        })
        .then(html => {
            console.log('添加模板HTML长度:', html.length);
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // 尝试多种方式提取表单内容
            let content = doc.querySelector('form');
            if (!content) {
                content = doc.querySelector('.card-body');
            }
            if (!content) {
                content = doc.querySelector('.container');
            }

            if (content) {
                document.getElementById('addTemplateContent').innerHTML = content.outerHTML;
                console.log('添加模板表单加载成功');
                // 重新初始化表单中的组件
                initializeTemplateForm();
            } else {
                console.error('未找到表单内容');
                document.getElementById('addTemplateContent').innerHTML = '<div class="alert alert-danger">未找到表单内容</div>';
            }
        })
        .catch(error => {
            console.error('加载添加模板失败:', error);
            document.getElementById('addTemplateContent').innerHTML = `<div class="alert alert-danger">加载失败: ${error.message}</div>`;
        });
    };

    // 显示分类管理模态框
    window.showCategoriesModal = function() {
        console.log('显示分类管理模态框');
        const modal = new bootstrap.Modal(document.getElementById('categoriesModal'));
        modal.show();

        // 设置AJAX分页处理器
        window.currentModalAjaxHandler = function(page, perPage) {
            loadCategoriesContent(page, perPage);
        };

        // 初始加载分类管理内容
        loadCategoriesContent(1, 20);
    };

    // 加载分类管理内容的函数
    function loadCategoriesContent(page = 1, perPage = 20) {
        console.log(`加载分类管理内容: page=${page}, perPage=${perPage}`);

        // 显示加载状态
        document.getElementById('categoriesContent').innerHTML = `
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>
        `;

        // 构建URL参数
        const params = new URLSearchParams({
            page: page,
            per_page: perPage
        });

        // 加载分类管理内容
        fetch(`/templates/categories?${params.toString()}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        })
        .then(response => {
            console.log('分类管理响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.text();
        })
        .then(html => {
            console.log('分类管理HTML长度:', html.length);
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // 尝试多种方式提取内容
            let content = doc.querySelector('.container');
            if (!content) {
                content = doc.querySelector('.card-body');
            }
            if (!content) {
                content = doc.querySelector('body');
            }

            if (content) {
                document.getElementById('categoriesContent').innerHTML = content.innerHTML;
                console.log('分类管理内容加载成功');
            } else {
                console.error('未找到分类管理内容');
                document.getElementById('categoriesContent').innerHTML = '<div class="alert alert-danger">未找到内容</div>';
            }
        })
        .catch(error => {
            console.error('加载分类管理失败:', error);
            document.getElementById('categoriesContent').innerHTML = `<div class="alert alert-danger">加载失败: ${error.message}</div>`;
        });
    }

    // 显示标记管理模态框
    window.showMarksModal = function() {
        console.log('显示标记管理模态框');
        const modal = new bootstrap.Modal(document.getElementById('marksModal'));
        modal.show();

        // 设置AJAX分页处理器
        window.currentModalAjaxHandler = function(page, perPage) {
            loadMarksContent(page, perPage);
        };

        // 初始加载标记管理内容
        loadMarksContent(1, 20);
    };

    // 加载标记管理内容的函数
    function loadMarksContent(page = 1, perPage = 20) {
        console.log(`加载标记管理内容: page=${page}, perPage=${perPage}`);

        // 显示加载状态
        document.getElementById('marksContent').innerHTML = `
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>
        `;

        // 构建URL参数
        const params = new URLSearchParams({
            page: page,
            per_page: perPage
        });

        // 加载标记管理内容
        fetch(`/templates/marks?${params.toString()}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        })
        .then(response => {
            console.log('标记管理响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.text();
        })
        .then(html => {
            console.log('标记管理HTML长度:', html.length);
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // 尝试多种方式提取内容
            let content = doc.querySelector('.container');
            if (!content) {
                content = doc.querySelector('.card-body');
            }
            if (!content) {
                content = doc.querySelector('body');
            }

            if (content) {
                document.getElementById('marksContent').innerHTML = content.innerHTML;
                console.log('标记管理内容加载成功');
            } else {
                console.error('未找到标记管理内容');
                document.getElementById('marksContent').innerHTML = '<div class="alert alert-danger">未找到内容</div>';
            }
        })
        .catch(error => {
            console.error('加载标记管理失败:', error);
            document.getElementById('marksContent').innerHTML = `<div class="alert alert-danger">加载失败: ${error.message}</div>`;
        });
    }

    // 初始化模板表单
    window.initializeTemplateForm = function() {
        // 这里可以添加SimpleMDE等组件的初始化代码
        console.log('模板表单已加载');
    };

    // 分类管理相关的全局函数
    window.showAddCategoryModal = function() {
        console.log('显示添加分类模态框');
        const modal = new bootstrap.Modal(document.getElementById('addCategoryModal'));
        modal.show();

        // 加载添加分类表单
        fetch('/templates/category/add', {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.text();
        })
        .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            let content = doc.querySelector('form');
            if (!content) {
                content = doc.querySelector('.card-body');
            }
            if (!content) {
                content = doc.querySelector('.container');
            }

            if (content) {
                document.getElementById('addCategoryContent').innerHTML = content.outerHTML;
                console.log('添加分类表单加载成功');
            } else {
                document.getElementById('addCategoryContent').innerHTML = '<div class="alert alert-danger">未找到表单内容</div>';
            }
        })
        .catch(error => {
            console.error('加载添加分类失败:', error);
            document.getElementById('addCategoryContent').innerHTML = `<div class="alert alert-danger">加载失败: ${error.message}</div>`;
        });
    };

    window.showEditCategoryModal = function(categoryId) {
        console.log('显示编辑分类模态框:', categoryId);
        const modal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
        modal.show();

        // 加载编辑分类表单
        fetch(`/templates/category/edit/${categoryId}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.text();
        })
        .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            let content = doc.querySelector('form');
            if (!content) {
                content = doc.querySelector('.card-body');
            }
            if (!content) {
                content = doc.querySelector('.container');
            }

            if (content) {
                document.getElementById('editCategoryContent').innerHTML = content.outerHTML;
                console.log('编辑分类表单加载成功');
            } else {
                document.getElementById('editCategoryContent').innerHTML = '<div class="alert alert-danger">未找到表单内容</div>';
            }
        })
        .catch(error => {
            console.error('加载编辑分类失败:', error);
            document.getElementById('editCategoryContent').innerHTML = `<div class="alert alert-danger">加载失败: ${error.message}</div>`;
        });
    };

    // 标记管理相关的全局函数
    window.showAddMarkModal = function() {
        console.log('显示添加标记模态框');
        const modal = new bootstrap.Modal(document.getElementById('addMarkModal'));
        modal.show();

        // 加载添加标记表单
        fetch('/templates/mark/add', {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.text();
        })
        .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            let content = doc.querySelector('form');
            if (!content) {
                content = doc.querySelector('.card-body');
            }
            if (!content) {
                content = doc.querySelector('.container');
            }

            if (content) {
                document.getElementById('addMarkContent').innerHTML = content.outerHTML;
                console.log('添加标记表单加载成功');
            } else {
                document.getElementById('addMarkContent').innerHTML = '<div class="alert alert-danger">未找到表单内容</div>';
            }
        })
        .catch(error => {
            console.error('加载添加标记失败:', error);
            document.getElementById('addMarkContent').innerHTML = `<div class="alert alert-danger">加载失败: ${error.message}</div>`;
        });
    };

    window.showEditMarkModal = function(markId) {
        console.log('显示编辑标记模态框:', markId);
        const modal = new bootstrap.Modal(document.getElementById('editMarkModal'));
        modal.show();

        // 加载编辑标记表单
        fetch(`/templates/mark/edit/${markId}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.text();
        })
        .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            let content = doc.querySelector('form');
            if (!content) {
                content = doc.querySelector('.card-body');
            }
            if (!content) {
                content = doc.querySelector('.container');
            }

            if (content) {
                document.getElementById('editMarkContent').innerHTML = content.outerHTML;
                console.log('编辑标记表单加载成功');
            } else {
                document.getElementById('editMarkContent').innerHTML = '<div class="alert alert-danger">未找到表单内容</div>';
            }
        })
        .catch(error => {
            console.error('加载编辑标记失败:', error);
            document.getElementById('editMarkContent').innerHTML = `<div class="alert alert-danger">加载失败: ${error.message}</div>`;
        });
    };

    // 清理模态框AJAX处理器
    document.getElementById('categoriesModal').addEventListener('hidden.bs.modal', function () {
        window.currentModalAjaxHandler = null;
        console.log('分类管理模态框已关闭，清理AJAX处理器');
    });

    document.getElementById('marksModal').addEventListener('hidden.bs.modal', function () {
        window.currentModalAjaxHandler = null;
        console.log('标记管理模态框已关闭，清理AJAX处理器');
    });
</script>

<!-- 添加模板模态框 -->
<div class="modal fade" id="addTemplateModal" tabindex="-1" aria-labelledby="addTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addTemplateModalLabel">添加模板</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="addTemplateContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分类管理模态框 -->
<div class="modal fade" id="categoriesModal" tabindex="-1" aria-labelledby="categoriesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoriesModalLabel">分类管理</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="categoriesContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 标记管理模态框 -->
<div class="modal fade" id="marksModal" tabindex="-1" aria-labelledby="marksModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="marksModalLabel">标记管理</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="marksContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分类管理的子模态框 -->
<!-- 添加分类子模态框 -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCategoryModalLabel">添加分类</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="addCategoryContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑分类子模态框 -->
<div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCategoryModalLabel">编辑分类</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="editCategoryContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 标记管理的子模态框 -->
<!-- 添加标记子模态框 -->
<div class="modal fade" id="addMarkModal" tabindex="-1" aria-labelledby="addMarkModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addMarkModalLabel">添加标记</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="addMarkContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑标记子模态框 -->
<div class="modal fade" id="editMarkModal" tabindex="-1" aria-labelledby="editMarkModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editMarkModalLabel">编辑标记</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="editMarkContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}