"""
系统设置模型
"""
from datetime import datetime
from . import db


class SystemSetting(db.Model):
    """系统设置模型"""
    __tablename__ = 'system_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), nullable=False, unique=True)
    value = db.Column(db.Text)
    description = db.Column(db.String(200))
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # 关联
    updater = db.relationship('User', backref=db.backref('updated_settings', lazy='dynamic'))
    
    def __repr__(self):
        return f'<SystemSetting {self.key}>' 