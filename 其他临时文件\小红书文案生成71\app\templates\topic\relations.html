{% extends "base.html" %}

{% block title %}话题关联管理 - 小红书文案生成系统{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
<style>
    .relation-actions {
        white-space: nowrap;
    }
    .relation-weight {
        width: 100px;
    }
</style>
{% endblock %}

{% block content_auth %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>话题关联管理</h2>
            <h5 class="text-muted">{{ topic.name }}</h5>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('topic.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回话题列表
            </a>
        </div>
    </div>

    <!-- 添加关联话题表单 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">添加关联话题</h5>
        </div>
        <div class="card-body">
            {% if form.related_topic_id.choices|length > 0 %}
            <form method="post" action="{{ url_for('topic.add_relation') }}">
                {{ form.csrf_token }}
                {{ form.topic_id }}
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            {{ form.related_topic_id.label(class="form-label") }}
                            {{ form.related_topic_id(class="form-select") }}
                            {% if form.related_topic_id.errors %}
                                {% for error in form.related_topic_id.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            {{ form.weight.label(class="form-label") }}
                            {{ form.weight(class="form-control") }}
                            {% if form.weight.errors %}
                                {% for error in form.weight.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                            <div class="form-text">数字越大权重越高（1-10）</div>
                        </div>
                    </div>
                </div>
                <div class="d-grid gap-2">
                    {{ form.submit(class="btn btn-primary") }}
                </div>
            </form>
            {% else %}
            <div class="alert alert-info mb-0">
                没有可添加的关联话题，请先创建更多话题。
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 关联话题列表 -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">已关联话题</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>关联话题</th>
                            <th class="relation-weight">权重</th>
                            <th class="relation-actions">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for relation in relations %}
                        <tr>
                            <td>{{ relation.id }}</td>
                            <td>{{ relation.related_topic.name }}</td>
                            <td class="relation-weight">{{ relation.weight }}</td>
                            <td class="relation-actions">
                                <button class="btn btn-sm btn-danger delete-relation" data-id="{{ relation.id }}" title="删除关联">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="4" class="text-center">暂无关联话题</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个话题关联吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 删除话题关联
        let deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        let relationId = null;
        
        // 点击删除按钮
        document.querySelectorAll('.delete-relation').forEach(function(button) {
            button.addEventListener('click', function() {
                relationId = this.getAttribute('data-id');
                deleteModal.show();
            });
        });
        
        // 确认删除
        document.getElementById('confirmDelete').addEventListener('click', function() {
            if (relationId) {
                fetch(`/topic/relation/delete/${relationId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token() }}'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    deleteModal.hide();
                    if (data.success) {
                        // 删除成功，刷新页面
                        location.reload();
                    } else {
                        // 删除失败，显示错误信息
                        alert(data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除失败，请重试');
                });
            }
        });
    });
</script>
{% endblock %} 