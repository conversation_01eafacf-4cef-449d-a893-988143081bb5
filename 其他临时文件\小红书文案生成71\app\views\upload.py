# -*- coding: utf-8 -*-
"""
上传相关视图
"""

import os
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from app.utils.uploads import upload_manager

# 创建上传蓝图
upload_bp = Blueprint('upload', __name__, url_prefix='/upload')


@upload_bp.route('/image', methods=['POST'])
@login_required
def upload_image():
    """上传图片（CKEditor使用）"""
    f = request.files.get('upload')
    if not f:
        return jsonify(uploaded=0, error={'message': '没有文件被上传'})
    
    # 检查文件类型
    allowed_extensions = ['jpg', 'jpeg', 'png', 'gif']
    if '.' not in f.filename or f.filename.rsplit('.', 1)[1].lower() not in allowed_extensions:
        return jsonify(uploaded=0, error={'message': '不支持的文件类型'})
    
    # 保存文件
    try:
        # 使用上传管理器保存文件
        relative_path = upload_manager.save_file(f, 'ckeditor')
        url = request.host_url.rstrip('/') + '/static/' + relative_path
        
        # 返回CKEditor需要的格式
        return jsonify(
            uploaded=1,
            fileName=f.filename,
            url=url
        )
    except Exception as e:
        current_app.logger.error(f"上传图片失败: {str(e)}")
        return jsonify(uploaded=0, error={'message': '上传失败，请重试'})


@upload_bp.route('/content-image', methods=['POST'])
@login_required
def upload_content_image():
    """上传文案图片"""
    if not current_user.has_permission('image_upload'):
        return jsonify(success=False, message='您没有权限上传图片')
    
    f = request.files.get('image')
    if not f:
        return jsonify(success=False, message='没有文件被上传')
    
    # 检查文件类型
    allowed_extensions = ['jpg', 'jpeg', 'png', 'gif']
    if '.' not in f.filename or f.filename.rsplit('.', 1)[1].lower() not in allowed_extensions:
        return jsonify(success=False, message='不支持的文件类型')
    
    # 保存文件
    try:
        # 使用上传管理器保存文件
        relative_path = upload_manager.save_file(f, 'content')
        url = request.host_url.rstrip('/') + '/static/' + relative_path
        
        return jsonify(
            success=True,
            fileName=f.filename,
            url=url
        )
    except Exception as e:
        current_app.logger.error(f"上传文案图片失败: {str(e)}")
        return jsonify(success=False, message='上传失败，请重试') 