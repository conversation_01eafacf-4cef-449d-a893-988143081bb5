# Flask 核心框架
Flask==3.1.1
Werkzeug==3.0.1

# 数据库相关
Flask-SQLAlchemy==3.1.1
SQLAlchemy==2.0.23
PyMySQL==1.1.0
Flask-Migrate==4.0.5

# 用户认证和权限
Flask-Login==0.6.3

# 表单处理和安全
Flask-WTF==1.2.1
WTForms==3.1.1
Flask-CORS==4.0.0

# 图片处理
Pillow==10.1.0

# 定时任务
APScheduler==3.10.4

# 数据处理和导出
pandas==2.1.4
openpyxl==3.1.2

# 日期时间处理
python-dateutil==2.8.2

# 标记安全
MarkupSafe==2.1.3

# 模板引擎
Jinja2==3.1.2

# 配置管理
python-dotenv==1.0.0

# 时区处理
pytz==2023.3

# 加密相关
cryptography==41.0.8

# HTTP 请求
requests==2.31.0

# 字符编码检测
chardet==5.2.0

# 数学计算
numpy==1.25.2

# 文本处理
bleach==6.1.0

# URL 处理
urllib3==2.1.0

# 时间处理
arrow==1.3.0

# 邮件发送（可选）
Flask-Mail==0.9.1

# 缓存（可选）
Flask-Caching==2.1.0
