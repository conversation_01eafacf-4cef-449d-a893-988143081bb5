2025-07-30 20:13:07,289 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-30 20:13:07,290 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
INFO:sqlalchemy.engine.Engine:SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
2025-07-30 20:13:07,291 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-30 20:13:07,291 INFO sqlalchemy.engine.Engine [cached since 199.3s ago] {'pk_1': 3}
INFO:sqlalchemy.engine.Engine:[cached since 199.3s ago] {'pk_1': 3}
2025-07-30 20:13:07,291 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
INFO:sqlalchemy.engine.Engine:SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
DEBUG - Loading user editor with ID 3
2025-07-30 20:13:07,292 INFO sqlalchemy.engine.Engine [cached since 199.3s ago] {'pk_1': 3}
INFO:sqlalchemy.engine.Engine:[cached since 199.3s ago] {'pk_1': 3}
2025-07-30 20:13:07,293 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
INFO:sqlalchemy.engine.Engine:SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 20:13:07,293 INFO sqlalchemy.engine.Engine [cached since 199.3s ago] {'param_1': 3}
DEBUG - Loading user editor with ID 3
INFO:sqlalchemy.engine.Engine:[cached since 199.3s ago] {'param_1': 3}
2025-07-30 20:13:07,294 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
INFO:sqlalchemy.engine.Engine:SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
DEBUG - User roles: ['图文编辑']
2025-07-30 20:13:07,294 INFO sqlalchemy.engine.Engine [cached since 199.3s ago] {'param_1': 3}
INFO:sqlalchemy.engine.Engine:[cached since 199.3s ago] {'param_1': 3}
2025-07-30 20:13:07,295 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id
INFO:sqlalchemy.engine.Engine:SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id
DEBUG - User roles: ['图文编辑']
2025-07-30 20:13:07,296 INFO sqlalchemy.engine.Engine [cached since 199.3s ago] {'param_1': 7}
INFO:sqlalchemy.engine.Engine:[cached since 199.3s ago] {'param_1': 7}
2025-07-30 20:13:07,296 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id
INFO:sqlalchemy.engine.Engine:SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id
DEBUG - Role 图文编辑 permissions: ['content.view', 'image.view', 'image.manage']
2025-07-30 20:13:07,297 INFO sqlalchemy.engine.Engine [cached since 199.3s ago] {'param_1': 7}
INFO:sqlalchemy.engine.Engine:[cached since 199.3s ago] {'param_1': 7}
2025-07-30 20:13:07,297 INFO sqlalchemy.engine.Engine SELECT contents.id AS contents_id, contents.client_id AS contents_client_id, contents.task_id AS contents_task_id, contents.batch_id AS contents_batch_id, contents.template_id AS contents_template_id, contents.title AS contents_title, contents.content AS contents_content, contents.topics AS contents_topics, contents.location AS 
contents_location, contents.image_urls AS contents_image_urls, contents.display_date AS contents_display_date, contents.display_time AS contents_display_time, contents.workflow_status AS contents_workflow_status, contents.publish_status AS contents_publish_status, contents.client_review_status AS contents_client_review_status, contents.internal_review_status AS contents_internal_review_status, contents.publish_priority AS contents_publish_priority, contents.publish_time AS contents_publish_time, contents.publish_error AS contents_publish_error, contents.publish_retry_count AS contents_publish_retry_count, contents.status_update_time AS contents_status_update_time, contents.created_at AS contents_created_at, contents.updated_at AS contents_updated_at, contents.created_by AS contents_created_by, contents.reviewer_id AS contents_reviewer_id, contents.review_time AS contents_review_time, contents.image_editor_id AS contents_image_editor_id, contents.content_editor_id AS contents_content_editor_id, contents.is_deleted AS contents_is_deleted, contents.deleted_at AS contents_deleted_at, contents.deleted_by AS contents_deleted_by, contents.ext_json AS contents_ext_json, contents.content_completed AS contents_content_completed, contents.image_completed AS contents_image_completed
FROM contents
WHERE contents.id = %(pk_1)s
DEBUG - Role 图文编辑 permissions: ['content.view', 'image.view', 'image.manage']
INFO:sqlalchemy.engine.Engine:SELECT contents.id AS contents_id, contents.client_id AS contents_client_id, contents.task_id AS contents_task_id, contents.batch_id AS contents_batch_id, contents.template_id AS contents_template_id, contents.title AS contents_title, contents.content AS contents_content, contents.topics AS contents_topics, contents.location AS contents_location, contents.image_urls AS contents_image_urls, contents.display_date AS contents_display_date, contents.display_time AS contents_display_time, contents.workflow_status AS contents_workflow_status, contents.publish_status AS contents_publish_status, contents.client_review_status AS contents_client_review_status, contents.internal_review_status AS contents_internal_review_status, contents.publish_priority AS contents_publish_priority, contents.publish_time AS contents_publish_time, contents.publish_error AS contents_publish_error, contents.publish_retry_count AS contents_publish_retry_count, contents.status_update_time AS contents_status_update_time, contents.created_at AS contents_created_at, contents.updated_at AS contents_updated_at, contents.created_by AS contents_created_by, contents.reviewer_id AS contents_reviewer_id, contents.review_time AS contents_review_time, contents.image_editor_id AS contents_image_editor_id, contents.content_editor_id AS contents_content_editor_id, contents.is_deleted AS contents_is_deleted, contents.deleted_at AS contents_deleted_at, contents.deleted_by AS contents_deleted_by, contents.ext_json AS contents_ext_json, contents.content_completed AS contents_content_completed, contents.image_completed AS contents_image_completed
FROM contents
WHERE contents.id = %(pk_1)s
2025-07-30 20:13:07,298 INFO sqlalchemy.engine.Engine SELECT content_images.id AS content_images_id, content_images.content_id AS content_images_content_id, content_images.image_path AS content_images_image_path, content_images.thumbnail_path AS content_images_thumbnail_path, content_images.original_name AS content_images_original_name, content_images.file_size AS content_images_file_size, content_images.image_order AS content_images_image_order, content_images.upload_time AS content_images_upload_time, content_images.is_deleted AS content_images_is_deleted, content_images.deleted_at AS content_images_deleted_at
FROM content_images
WHERE content_images.content_id = %(content_id_1)s AND content_images.is_deleted = false ORDER BY content_images.image_order, content_images.upload_time
INFO:sqlalchemy.engine.Engine:SELECT content_images.id AS content_images_id, content_images.content_id AS content_images_content_id, content_images.image_path AS content_images_image_path, content_images.thumbnail_path AS content_images_thumbnail_path, content_images.original_name AS content_images_original_name, content_images.file_size AS content_images_file_size, content_images.image_order AS content_images_image_order, content_images.upload_time AS content_images_upload_time, content_images.is_deleted AS content_images_is_deleted, content_images.deleted_at AS content_images_deleted_at
FROM content_images
WHERE content_images.content_id = %(content_id_1)s AND content_images.is_deleted = false ORDER BY content_images.image_order, content_images.upload_time
2025-07-30 20:13:07,299 INFO sqlalchemy.engine.Engine [cached since 157.2s ago] {'pk_1': 104}
INFO:sqlalchemy.engine.Engine:[cached since 157.2s ago] {'pk_1': 104}
2025-07-30 20:13:07,300 INFO sqlalchemy.engine.Engine [cached since 190.7s ago] {'content_id_1': 104}
INFO:sqlalchemy.engine.Engine:[cached since 190.7s ago] {'content_id_1': 104}
2025-07-30 20:13:07,301 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-30 20:13:07,302 INFO sqlalchemy.engine.Engine ROLLBACK
127.0.0.1 - - [30/Jul/2025 20:13:07] "GET /simple/api/images/104 HTTP/1.1" 200 -
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:werkzeug:127.0.0.1 - - [30/Jul/2025 20:13:07] "GET /simple/api/images/104 HTTP/1.1" 200 -
127.0.0.1 - - [30/Jul/2025 20:13:07] "GET /simple/api/contents/104 HTTP/1.1" 403 -
INFO:werkzeug:127.0.0.1 - - [30/Jul/2025 20:13:07] "GET /simple/api/contents/104 HTTP/1.1" 403 -