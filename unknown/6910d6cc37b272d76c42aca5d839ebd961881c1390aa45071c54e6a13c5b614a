/**
 * 全局函数管理系统
 * 解决AJAX页面切换后函数丢失的问题
 */

// 全局函数存储
window.GlobalFunctionManager = {
    functions: {},
    initializers: {},
    
    // 注册函数
    register: function(name, func) {
        this.functions[name] = func;
        window[name] = func;
        console.log('注册全局函数:', name);
    },
    
    // 注册页面初始化函数
    registerInitializer: function(pageName, initFunc) {
        this.initializers[pageName] = initFunc;
        console.log('注册页面初始化函数:', pageName);
    },
    
    // 重新绑定所有函数
    rebindAll: function() {
        console.log('重新绑定所有全局函数...');
        for (const [name, func] of Object.entries(this.functions)) {
            window[name] = func;
        }
    },
    
    // 执行页面初始化
    initializePage: function(url) {
        console.log('执行页面初始化，URL:', url);
        
        // 确定页面类型
        let pageType = 'common';
        if (url.includes('/final-review')) {
            pageType = 'final-review';
        } else if (url.includes('/image-upload')) {
            pageType = 'image-upload';
        } else if (url.includes('/first-review')) {
            pageType = 'first-review';
        } else if (url.includes('/contents')) {
            pageType = 'contents';
        } else if (url.includes('/clients')) {
            pageType = 'clients';
        }
        
        // 执行通用初始化
        if (this.initializers['common']) {
            try {
                this.initializers['common']();
            } catch (e) {
                console.error('通用初始化失败:', e);
            }
        }
        
        // 执行页面特定初始化
        if (this.initializers[pageType]) {
            try {
                this.initializers[pageType]();
            } catch (e) {
                console.error('页面初始化失败:', pageType, e);
            }
        }
    },
    
    // 重新绑定onclick事件
    rebindOnclickEvents: function() {
        console.log('重新绑定onclick事件...');
        
        // 处理所有带有onclick属性的元素
        document.querySelectorAll('[onclick]').forEach(element => {
            const onclickCode = element.getAttribute('onclick');
            
            // 移除原有的onclick属性
            element.removeAttribute('onclick');
            
            // 添加新的事件监听器
            element.addEventListener('click', function(e) {
                try {
                    // 创建函数并执行
                    const func = new Function('event', onclickCode);
                    func.call(this, e);
                } catch (error) {
                    console.error('执行onclick代码失败:', error, onclickCode);
                }
            });
        });
    },
    
    // 完整的页面重新初始化
    reinitializePage: function(url = window.location.href) {
        console.log('=== 开始页面重新初始化 ===');
        
        // 1. 重新绑定全局函数
        this.rebindAll();
        
        // 2. 重新绑定onclick事件
        this.rebindOnclickEvents();
        
        // 3. 执行页面初始化
        this.initializePage(url);
        
        // 4. 触发自定义事件
        if (typeof $ !== 'undefined') {
            $(document).trigger('pageReinitialized', [url]);
        }
        
        console.log('=== 页面重新初始化完成 ===');
    }
};

// 简化的全局函数
window.registerGlobalFunction = function(name, func) {
    window.GlobalFunctionManager.register(name, func);
};

window.registerPageInitializer = function(pageName, initFunc) {
    window.GlobalFunctionManager.registerInitializer(pageName, initFunc);
};

window.reinitializePage = function(url) {
    window.GlobalFunctionManager.reinitializePage(url);
};

// 监听DOM变化，自动重新绑定事件
if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver(function(mutations) {
        let shouldRebind = false;
        
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // 检查是否有新添加的带onclick属性的元素
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // 元素节点
                        if (node.hasAttribute && node.hasAttribute('onclick')) {
                            shouldRebind = true;
                        }
                        // 检查子元素
                        if (node.querySelectorAll) {
                            const onclickElements = node.querySelectorAll('[onclick]');
                            if (onclickElements.length > 0) {
                                shouldRebind = true;
                            }
                        }
                    }
                });
            }
        });
        
        if (shouldRebind) {
            console.log('检测到新的onclick元素，重新绑定事件...');
            setTimeout(() => {
                window.GlobalFunctionManager.rebindOnclickEvents();
            }, 100);
        }
    });
    
    // 开始观察
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

console.log('全局函数管理系统已加载');
