{% extends "base.html" %}

{% block title %}日志设置{% endblock %}

{% block content_auth %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">日志设置</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('system.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回系统设置
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5>日志级别设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.log_level.label(class="form-label") }}
                                    {{ form.log_level(class="form-select") }}
                                    {% if form.log_level.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.log_level.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">设置系统日志记录的级别</small>
                                </div>
                                
                                <div class="alert alert-info">
                                    <h5><i class="fas fa-info-circle"></i> 日志级别说明</h5>
                                    <ul class="mb-0">
                                        <li><strong>调试(DEBUG)</strong>：记录详细的调试信息，包括变量值、函数调用等</li>
                                        <li><strong>信息(INFO)</strong>：记录一般操作信息，如用户登录、操作执行等</li>
                                        <li><strong>警告(WARNING)</strong>：记录可能导致问题的情况，但不影响系统正常运行</li>
                                        <li><strong>错误(ERROR)</strong>：记录导致功能无法正常运行的错误</li>
                                        <li><strong>严重错误(CRITICAL)</strong>：记录可能导致系统崩溃的严重错误</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5>日志保留设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.log_retention_days.label(class="form-label") }}
                                    {{ form.log_retention_days(class="form-control") }}
                                    {% if form.log_retention_days.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.log_retention_days.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">设置系统日志保留的天数，超过此天数的日志将被自动清理</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                            <a href="{{ url_for('system.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 日志文件说明 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h3 class="card-title">日志文件说明</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>日志文件</th>
                                    <th>说明</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>app.log</code></td>
                                    <td>主应用日志，记录系统运行的主要信息</td>
                                </tr>
                                <tr>
                                    <td><code>error.log</code></td>
                                    <td>错误日志，记录系统运行过程中的错误信息</td>
                                </tr>
                                <tr>
                                    <td><code>access.log</code></td>
                                    <td>访问日志，记录系统的访问请求信息</td>
                                </tr>
                                <tr>
                                    <td><code>db.log</code></td>
                                    <td>数据库日志，记录数据库操作信息</td>
                                </tr>
                                <tr>
                                    <td><code>scheduler.log</code></td>
                                    <td>定时任务日志，记录定时任务执行信息</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle"></i> 注意：日志文件存储在 <code>app/logs/</code> 目录下，请确保该目录有足够的磁盘空间和适当的权限。
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 