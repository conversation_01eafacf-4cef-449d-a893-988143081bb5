#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图片处理工具类
"""

import os
import time
import hashlib
from PIL import Image
from werkzeug.utils import secure_filename
from flask import current_app

class ImageUploadHandler:
    """图片上传处理器"""

    # 默认允许的图片格式
    DEFAULT_ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp'}

    # 默认最大文件大小 (10MB)
    DEFAULT_MAX_FILE_SIZE = 10 * 1024 * 1024

    def __init__(self, upload_folder=None, max_file_size=None, allowed_extensions=None):
        """
        初始化图片上传处理器

        Args:
            upload_folder: 上传文件夹路径
            max_file_size: 最大文件大小（字节）
            allowed_extensions: 允许的文件扩展名集合
        """
        self.upload_folder = upload_folder or current_app.config.get('UPLOAD_FOLDER', 'uploads')

        # 从系统设置读取配置
        self._load_settings()

        # 允许通过参数覆盖系统设置
        if max_file_size is not None:
            self.max_file_size = max_file_size
        if allowed_extensions is not None:
            self.allowed_extensions = allowed_extensions

        # 确保上传目录存在
        self._ensure_upload_dir()

    def _load_settings(self):
        """从系统设置加载配置"""
        try:
            from app.models.system_setting import SystemSetting

            # 读取最大文件大小
            max_size_setting = SystemSetting.get_value('IMAGE_UPLOAD_MAX_SIZE', str(self.DEFAULT_MAX_FILE_SIZE))
            self.max_file_size = int(max_size_setting)

            # 读取允许的文件类型
            allowed_types_setting = SystemSetting.get_value('IMAGE_UPLOAD_ALLOWED_TYPES', 'jpg,jpeg,png,gif,webp')
            self.allowed_extensions = set(ext.strip().lower() for ext in allowed_types_setting.split(','))

            print(f"从系统设置加载图片配置:")
            print(f"  最大文件大小: {self.max_file_size} 字节 ({self.max_file_size / (1024*1024):.1f}MB)")
            print(f"  允许的格式: {self.allowed_extensions}")

        except Exception as e:
            print(f"加载系统设置失败，使用默认值: {e}")
            self.max_file_size = self.DEFAULT_MAX_FILE_SIZE
            self.allowed_extensions = self.DEFAULT_ALLOWED_EXTENSIONS
    
    def _ensure_upload_dir(self):
        """确保上传目录存在"""
        if not os.path.exists(self.upload_folder):
            os.makedirs(self.upload_folder, exist_ok=True)

        # 创建子目录 - 按年月组织
        from datetime import datetime
        current_date = datetime.now()
        year_month = current_date.strftime('%Y%m')  # 使用YYYYMM格式，避免路径分隔符问题

        subdirs = [
            os.path.join('images', year_month),
            os.path.join('thumbnails', year_month)
        ]
        for subdir in subdirs:
            subdir_path = os.path.join(self.upload_folder, subdir)
            if not os.path.exists(subdir_path):
                os.makedirs(subdir_path, exist_ok=True)
    
    def _allowed_file(self, filename):
        """检查文件扩展名是否允许"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in self.allowed_extensions
    
    def _generate_filename(self, original_filename, content_id):
        """生成唯一的文件名"""
        # 获取文件扩展名
        ext = original_filename.rsplit('.', 1)[1].lower() if '.' in original_filename else 'jpg'
        
        # 生成时间戳
        timestamp = str(int(time.time()))
        
        # 生成哈希值（基于内容ID和时间戳）
        hash_input = f"{content_id}_{timestamp}_{original_filename}"
        file_hash = hashlib.md5(hash_input.encode()).hexdigest()[:8]
        
        # 组合文件名
        filename = f"content_{content_id}_{timestamp}_{file_hash}.{ext}"
        
        return filename
    
    def validate_file(self, file):
        """
        验证上传的文件
        
        Args:
            file: 上传的文件对象
            
        Returns:
            tuple: (is_valid, error_message)
        """
        if not file:
            return False, "没有选择文件"
        
        if file.filename == '':
            return False, "文件名为空"
        
        if not self._allowed_file(file.filename):
            return False, f"不支持的文件格式，支持的格式：{', '.join(sorted(self.allowed_extensions))}"
        
        # 检查文件大小（需要先读取文件内容）
        file.seek(0, 2)  # 移动到文件末尾
        file_size = file.tell()
        file.seek(0)  # 重置文件指针
        
        if file_size > self.max_file_size:
            max_size_mb = self.max_file_size / (1024 * 1024)
            return False, f"文件大小超过限制（最大 {max_size_mb:.1f}MB）"
        
        return True, None
    
    def save_image(self, file, content_id):
        """
        保存图片文件
        
        Args:
            file: 上传的文件对象
            content_id: 文案ID
            
        Returns:
            dict: 包含文件信息的字典
        """
        # 验证文件
        is_valid, error_msg = self.validate_file(file)
        if not is_valid:
            raise ValueError(error_msg)
        
        # 生成文件名
        original_filename = secure_filename(file.filename)
        filename = self._generate_filename(original_filename, content_id)
        
        # 保存路径 - 按年月组织
        from datetime import datetime
        current_date = datetime.now()
        year_month = current_date.strftime('%Y%m')  # 使用YYYYMM格式

        image_path = os.path.join(self.upload_folder, 'images', year_month, filename)
        
        try:
            # 保存原图
            file.save(image_path)
            
            # 获取文件大小
            file_size = os.path.getsize(image_path)
            
            # 生成缩略图
            thumbnail_path = self._generate_thumbnail(image_path, filename)
            
            return {
                'filename': filename,
                'original_name': original_filename,
                'image_path': os.path.join('images', year_month, filename).replace('\\', '/'),
                'thumbnail_path': thumbnail_path,
                'file_size': file_size,
                'upload_time': time.time()
            }
            
        except Exception as e:
            # 如果保存失败，清理已创建的文件
            if os.path.exists(image_path):
                os.remove(image_path)
            raise Exception(f"保存图片失败：{str(e)}")
    
    def _generate_thumbnail(self, image_path, filename):
        """
        生成缩略图
        
        Args:
            image_path: 原图路径
            filename: 文件名
            
        Returns:
            str: 缩略图相对路径
        """
        try:
            # 缩略图文件名 - 按年月组织
            from datetime import datetime
            current_date = datetime.now()
            year_month = current_date.strftime('%Y%m')  # 使用YYYYMM格式

            name, ext = os.path.splitext(filename)
            thumbnail_filename = f"{name}_thumb{ext}"
            thumbnail_path = os.path.join(self.upload_folder, 'thumbnails', year_month, thumbnail_filename)
            
            # 打开原图
            with Image.open(image_path) as img:
                # 转换为RGB模式（处理RGBA等格式）
                if img.mode in ('RGBA', 'LA', 'P'):
                    # 创建白色背景
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 生成缩略图（最大300x300）
                img.thumbnail((300, 300), Image.Resampling.LANCZOS)
                
                # 保存缩略图
                img.save(thumbnail_path, 'JPEG', quality=85, optimize=True)
            
            return os.path.join('thumbnails', year_month, thumbnail_filename).replace('\\', '/')
            
        except Exception as e:
            current_app.logger.warning(f"生成缩略图失败：{str(e)}")
            return None
    
    def delete_image(self, image_path, thumbnail_path=None):
        """
        删除图片文件
        
        Args:
            image_path: 图片路径
            thumbnail_path: 缩略图路径
        """
        try:
            # 删除原图
            full_image_path = os.path.join(self.upload_folder, image_path)
            if os.path.exists(full_image_path):
                os.remove(full_image_path)
            
            # 删除缩略图
            if thumbnail_path:
                full_thumbnail_path = os.path.join(self.upload_folder, thumbnail_path)
                if os.path.exists(full_thumbnail_path):
                    os.remove(full_thumbnail_path)
                    
        except Exception as e:
            current_app.logger.error(f"删除图片失败：{str(e)}")
    
    def get_image_info(self, image_path):
        """
        获取图片信息
        
        Args:
            image_path: 图片路径
            
        Returns:
            dict: 图片信息
        """
        try:
            full_path = os.path.join(self.upload_folder, image_path)
            if not os.path.exists(full_path):
                return None
            
            with Image.open(full_path) as img:
                return {
                    'width': img.width,
                    'height': img.height,
                    'format': img.format,
                    'mode': img.mode,
                    'size': os.path.getsize(full_path)
                }
                
        except Exception as e:
            current_app.logger.error(f"获取图片信息失败：{str(e)}")
            return None
