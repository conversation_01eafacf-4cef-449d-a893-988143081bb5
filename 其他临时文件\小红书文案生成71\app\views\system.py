"""
系统设置视图
"""
from datetime import datetime, time
from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from sqlalchemy import desc

from app import db
from app.models import SystemSetting
from app.models.user import User, Role
from app.utils.decorators import permission_required, ajax_aware
from app.forms.system import BasicSettingForm, ReviewSettingForm, PublishSettingForm, LogSettingForm, ArchiveSettingForm, ApiSettingForm
from app.forms.notification import NotificationSettingForm
from app.utils.api_auth import generate_api_key

# 创建蓝图
system_bp = Blueprint('system', __name__)

@system_bp.route('/')
@login_required
@permission_required('admin_access')
@ajax_aware
def index():
    """系统设置首页"""
    return render_template('system/index.html')

@system_bp.route('/basic', methods=['GET', 'POST'])
@login_required
@permission_required('admin_access')
def basic():
    """基础设置"""
    form = BasicSettingForm()
    
    # 从数据库获取当前设置
    if request.method == 'GET':
        # 默认每日展示数量
        daily_content_count = SystemSetting.query.filter_by(key='daily_content_count').first()
        if daily_content_count:
            form.daily_content_count.data = int(daily_content_count.value)
        else:
            form.daily_content_count.data = 5
        
        # 默认展示开始时间
        display_start_time = SystemSetting.query.filter_by(key='display_start_time').first()
        if display_start_time and display_start_time.value:
            try:
                form.display_start_time.data = datetime.strptime(display_start_time.value, '%H:%M').time()
            except ValueError:
                form.display_start_time.data = time(9, 0)  # 默认9:00
        else:
            form.display_start_time.data = time(9, 0)  # 默认9:00
        
        # 默认最小间隔时间
        interval_min = SystemSetting.query.filter_by(key='interval_min').first()
        if interval_min:
            form.interval_min.data = int(interval_min.value)
        else:
            form.interval_min.data = 30
        
        # 默认最大间隔时间
        interval_max = SystemSetting.query.filter_by(key='interval_max').first()
        if interval_max:
            form.interval_max.data = int(interval_max.value)
        else:
            form.interval_max.data = 120
        
        # 链接有效期
        link_expiration_days = SystemSetting.query.filter_by(key='link_expiration_days').first()
        if link_expiration_days:
            form.link_expiration_days.data = int(link_expiration_days.value)
        else:
            form.link_expiration_days.data = 0  # 0表示永久有效
    
    if form.validate_on_submit():
        # 保存设置
        update_system_setting('daily_content_count', str(form.daily_content_count.data))
        
        if form.display_start_time.data:
            update_system_setting('display_start_time', form.display_start_time.data.strftime('%H:%M'))
        else:
            update_system_setting('display_start_time', '09:00')
        
        update_system_setting('interval_min', str(form.interval_min.data))
        update_system_setting('interval_max', str(form.interval_max.data))
        update_system_setting('link_expiration_days', str(form.link_expiration_days.data))
        
        db.session.commit()
        flash('基础设置已保存', 'success')
        return redirect(url_for('system.basic'))
    
    return render_template('system/basic.html', form=form)

@system_bp.route('/review', methods=['GET', 'POST'])
@login_required
@permission_required('admin_access')
def review():
    """审核流程设置"""
    form = ReviewSettingForm()
    
    # 从数据库获取当前设置
    if request.method == 'GET':
        # 自动通过初审
        auto_review_first = SystemSetting.query.filter_by(key='auto_review_first').first()
        if auto_review_first:
            form.auto_review_first.data = auto_review_first.value == '1'
        
        # 自动通过最终审核
        auto_review_final = SystemSetting.query.filter_by(key='auto_review_final').first()
        if auto_review_final:
            form.auto_review_final.data = auto_review_final.value == '1'
        
        # 自动通过延迟时间
        auto_review_delay = SystemSetting.query.filter_by(key='auto_review_delay').first()
        if auto_review_delay:
            form.auto_review_delay.data = int(auto_review_delay.value)
        else:
            form.auto_review_delay.data = 30
        
        # 拒绝理由必填
        rejection_reason_required = SystemSetting.query.filter_by(key='rejection_reason_required').first()
        if rejection_reason_required:
            form.rejection_reason_required.data = rejection_reason_required.value == '1'
        else:
            form.rejection_reason_required.data = True
        
        # 自动确认时间
        auto_confirm_minutes = SystemSetting.query.filter_by(key='auto_confirm_minutes').first()
        if auto_confirm_minutes:
            form.auto_confirm_minutes.data = int(auto_confirm_minutes.value)
        else:
            form.auto_confirm_minutes.data = 60
    
    if form.validate_on_submit():
        # 保存设置
        update_system_setting('auto_review_first', '1' if form.auto_review_first.data else '0')
        update_system_setting('auto_review_final', '1' if form.auto_review_final.data else '0')
        update_system_setting('auto_review_delay', str(form.auto_review_delay.data))
        update_system_setting('rejection_reason_required', '1' if form.rejection_reason_required.data else '0')
        update_system_setting('auto_confirm_minutes', str(form.auto_confirm_minutes.data))
        
        db.session.commit()
        flash('审核流程设置已保存', 'success')
        return redirect(url_for('system.review'))
    
    return render_template('system/review.html', form=form)

@system_bp.route('/publish', methods=['GET', 'POST'])
@login_required
@permission_required('admin_access')
def publish():
    """发布设置"""
    form = PublishSettingForm()
    
    # 从数据库获取当前设置
    if request.method == 'GET':
        # 发布超时时间
        publish_timeout = SystemSetting.query.filter_by(key='publish_timeout').first()
        if publish_timeout:
            form.publish_timeout.data = int(publish_timeout.value)
        else:
            form.publish_timeout.data = 24
        
        # 超时处理策略
        timeout_action = SystemSetting.query.filter_by(key='timeout_action').first()
        if timeout_action:
            form.timeout_action.data = timeout_action.value
        
        # 超时通知角色
        timeout_notify_roles = SystemSetting.query.filter_by(key='timeout_notify_roles').first()
        if timeout_notify_roles:
            form.timeout_notify_roles.data = timeout_notify_roles.value
    
    if form.validate_on_submit():
        # 保存设置
        update_system_setting('publish_timeout', str(form.publish_timeout.data))
        update_system_setting('timeout_action', form.timeout_action.data)
        update_system_setting('timeout_notify_roles', form.timeout_notify_roles.data)
        
        db.session.commit()
        flash('发布设置已保存', 'success')
        return redirect(url_for('system.publish'))
    
    return render_template('system/publish.html', form=form)

@system_bp.route('/log', methods=['GET', 'POST'])
@login_required
@permission_required('admin_access')
def log():
    """日志设置"""
    form = LogSettingForm()
    
    # 从数据库获取当前设置
    if request.method == 'GET':
        # 日志级别
        log_level = SystemSetting.query.filter_by(key='log_level').first()
        if log_level:
            form.log_level.data = log_level.value
        
        # 日志保留天数
        log_retention_days = SystemSetting.query.filter_by(key='log_retention_days').first()
        if log_retention_days:
            form.log_retention_days.data = int(log_retention_days.value)
        else:
            form.log_retention_days.data = 30
    
    if form.validate_on_submit():
        # 保存设置
        update_system_setting('log_level', form.log_level.data)
        update_system_setting('log_retention_days', str(form.log_retention_days.data))
        
        db.session.commit()
        flash('日志设置已保存', 'success')
        return redirect(url_for('system.log'))
    
    return render_template('system/log.html', form=form)

@system_bp.route('/archive', methods=['GET', 'POST'])
@login_required
@permission_required('admin_access')
def archive():
    """归档设置"""
    form = ArchiveSettingForm()
    
    # 从数据库获取当前设置
    if request.method == 'GET':
        # 文案归档天数
        archive_content_days = SystemSetting.query.filter_by(key='archive_content_days').first()
        if archive_content_days:
            form.archive_content_days.data = int(archive_content_days.value)
        else:
            form.archive_content_days.data = 365
        
        # 日志归档天数
        archive_log_days = SystemSetting.query.filter_by(key='archive_log_days').first()
        if archive_log_days:
            form.archive_log_days.data = int(archive_log_days.value)
        else:
            form.archive_log_days.data = 30
        
        # 通知归档天数
        archive_notification_days = SystemSetting.query.filter_by(key='archive_notification_days').first()
        if archive_notification_days:
            form.archive_notification_days.data = int(archive_notification_days.value)
        else:
            form.archive_notification_days.data = 30
    
    if form.validate_on_submit():
        # 保存设置
        update_system_setting('archive_content_days', str(form.archive_content_days.data))
        update_system_setting('archive_log_days', str(form.archive_log_days.data))
        update_system_setting('archive_notification_days', str(form.archive_notification_days.data))
        
        db.session.commit()
        flash('归档设置已保存', 'success')
        return redirect(url_for('system.archive'))
    
    return render_template('system/archive.html', form=form)

@system_bp.route('/notification', methods=['GET', 'POST'])
@login_required
@permission_required('admin_access')
def notification():
    """通知系统设置"""
    form = NotificationSettingForm()
    
    # 从系统设置中加载当前配置
    if request.method == 'GET':
        # 获取各项设置
        enable_all = SystemSetting.query.filter_by(key='notification_enable_all').first()
        if enable_all:
            form.enable_all.data = (enable_all.value == 'true')
        
        enable_review_status = SystemSetting.query.filter_by(key='notification_enable_review_status').first()
        if enable_review_status:
            form.enable_review_status.data = (enable_review_status.value == 'true')
        
        enable_client_operation = SystemSetting.query.filter_by(key='notification_enable_client_operation').first()
        if enable_client_operation:
            form.enable_client_operation.data = (enable_client_operation.value == 'true')
        
        enable_publish_notice = SystemSetting.query.filter_by(key='notification_enable_publish_notice').first()
        if enable_publish_notice:
            form.enable_publish_notice.data = (enable_publish_notice.value == 'true')
        
        enable_system_change = SystemSetting.query.filter_by(key='notification_enable_system_change').first()
        if enable_system_change:
            form.enable_system_change.data = (enable_system_change.value == 'true')
        
        recipient_roles = SystemSetting.query.filter_by(key='notification_recipient_roles').first()
        if recipient_roles:
            form.recipient_roles.data = recipient_roles.value
        
        display_count = SystemSetting.query.filter_by(key='notification_display_count').first()
        if display_count:
            form.display_count.data = display_count.value
        
        auto_mark_read = SystemSetting.query.filter_by(key='notification_auto_mark_read').first()
        if auto_mark_read:
            form.auto_mark_read.data = (auto_mark_read.value == 'true')
    
    if form.validate_on_submit():
        # 保存设置
        update_system_setting('notification_enable_all', str(form.enable_all.data).lower())
        update_system_setting('notification_enable_review_status', str(form.enable_review_status.data).lower())
        update_system_setting('notification_enable_client_operation', str(form.enable_client_operation.data).lower())
        update_system_setting('notification_enable_publish_notice', str(form.enable_publish_notice.data).lower())
        update_system_setting('notification_enable_system_change', str(form.enable_system_change.data).lower())
        update_system_setting('notification_recipient_roles', form.recipient_roles.data)
        update_system_setting('notification_display_count', form.display_count.data)
        update_system_setting('notification_auto_mark_read', str(form.auto_mark_read.data).lower())
        
        db.session.commit()
        flash('通知设置已保存', 'success')
        return redirect(url_for('system.notification'))
    
    return render_template(
        'notification/settings.html',
        form=form
    )

@system_bp.route('/api_settings', methods=['GET', 'POST'])
@login_required
@permission_required('admin_access')
def api_settings():
    """API设置"""
    form = ApiSettingForm()
    
    # 从数据库获取当前API密钥
    api_key_setting = SystemSetting.query.filter_by(key='API_KEY').first()
    current_api_key = api_key_setting.value if api_key_setting else ''
    
    if form.validate_on_submit():
        # 如果选择生成新密钥
        if form.generate_new.data == '1':
            new_api_key = generate_api_key()
            update_system_setting('API_KEY', new_api_key)
            db.session.commit()
            flash('API密钥已成功重新生成', 'success')
        else:
            # 使用用户输入的密钥
            update_system_setting('API_KEY', form.api_key.data)
            db.session.commit()
            flash('API密钥已保存', 'success')
        
        return redirect(url_for('system.api_settings'))
    
    # GET请求，显示当前API密钥
    if request.method == 'GET' and current_api_key:
        form.api_key.data = current_api_key
    
    return render_template('system/api_settings.html', form=form, current_api_key=current_api_key)

@system_bp.route('/api/settings')
@login_required
@permission_required('admin_access')
def api_settings_list():
    """API：获取所有系统设置"""
    settings = SystemSetting.query.all()
    result = {}
    
    for setting in settings:
        result[setting.key] = {
            'value': setting.value,
            'description': setting.description,
            'updated_at': setting.updated_at.strftime('%Y-%m-%d %H:%M:%S') if setting.updated_at else None
        }
    
    return jsonify({
        'success': True,
        'settings': result
    })

# 辅助函数
def update_system_setting(key, value):
    """更新系统设置"""
    setting = SystemSetting.query.filter_by(key=key).first()
    if setting:
        setting.value = value
        setting.updated_at = datetime.now()
        setting.updated_by = current_user.id
    else:
        setting = SystemSetting(
            key=key,
            value=value,
            description=f'系统设置：{key}',
            updated_at=datetime.now(),
            updated_by=current_user.id
        )
        db.session.add(setting)
    
    return setting 