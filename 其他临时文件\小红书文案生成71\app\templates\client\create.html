{% extends "base.html" %}

{% block title %}添加客户{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-md-10 col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="mb-0">添加新客户</h3>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('client.client_create') }}" data-ajax-form>
                        {{ form.csrf_token }}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.name.label(class="form-label") }}
                                    {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                    {% if form.name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.name.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.contact.label(class="form-label") }}
                                    {{ form.contact(class="form-control" + (" is-invalid" if form.contact.errors else "")) }}
                                    {% if form.contact.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.contact.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.phone.label(class="form-label") }}
                                    {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                                    {% if form.phone.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.phone.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.email.label(class="form-label") }}
                                    {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                    {% if form.email.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.email.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.daily_content_count.label(class="form-label") }}
                                    {{ form.daily_content_count(class="form-control" + (" is-invalid" if form.daily_content_count.errors else "")) }}
                                    {% if form.daily_content_count.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.daily_content_count.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">每日向客户展示的文案数量</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.display_start_time.label(class="form-label") }}
                                    {{ form.display_start_time(class="form-control" + (" is-invalid" if form.display_start_time.errors else "")) }}
                                    {% if form.display_start_time.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.display_start_time.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">每日开始展示文案的时间，留空表示全天</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.interval_min.label(class="form-label") }}
                                    {{ form.interval_min(class="form-control" + (" is-invalid" if form.interval_min.errors else "")) }}
                                    {% if form.interval_min.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.interval_min.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">两条文案展示之间的最小间隔时间（分钟）</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.interval_max.label(class="form-label") }}
                                    {{ form.interval_max(class="form-control" + (" is-invalid" if form.interval_max.errors else "")) }}
                                    {% if form.interval_max.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.interval_max.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">两条文案展示之间的最大间隔时间（分钟）</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-group">
                                    {{ form.address.label(class="form-label") }}
                                    {{ form.address(class="form-control" + (" is-invalid" if form.address.errors else "")) }}
                                    {% if form.address.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.address.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-group">
                                    {{ form.remark.label(class="form-label") }}
                                    {{ form.remark(class="form-control" + (" is-invalid" if form.remark.errors else ""), rows=3) }}
                                    {% if form.remark.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.remark.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    {{ form.need_review(class="form-check-input" + (" is-invalid" if form.need_review.errors else "")) }}
                                    {{ form.need_review.label(class="form-check-label") }}
                                    {% if form.need_review.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.need_review.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <div class="form-text text-muted">启用后，文案需要客户审核才能发布</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    {{ form.status(class="form-check-input" + (" is-invalid" if form.status.errors else "")) }}
                                    {{ form.status.label(class="form-check-label") }}
                                    {% if form.status.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.status.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <div class="form-text text-muted">禁用后，客户将无法访问系统</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ url_for('client.client_list') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> 返回列表
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 创建客户
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 