{% extends "base.html" %}

{% block title %}归档设置{% endblock %}

{% block content_auth %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">归档设置</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('system.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回系统设置
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5>文案归档设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.archive_content_days.label(class="form-label") }}
                                    {{ form.archive_content_days(class="form-control") }}
                                    {% if form.archive_content_days.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.archive_content_days.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">设置文案数据的归档天数，超过此天数的文案将被归档</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5>日志归档设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.archive_log_days.label(class="form-label") }}
                                    {{ form.archive_log_days(class="form-control") }}
                                    {% if form.archive_log_days.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.archive_log_days.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">设置系统日志的归档天数，超过此天数的日志将被归档</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5>通知归档设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.archive_notification_days.label(class="form-label") }}
                                    {{ form.archive_notification_days(class="form-control") }}
                                    {% if form.archive_notification_days.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.archive_notification_days.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">设置系统通知的归档天数，超过此天数的通知将被归档</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-secondary">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                            <a href="{{ url_for('system.index') }}" class="btn btn-light">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 归档说明 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h3 class="card-title">归档说明</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> 什么是数据归档？</h5>
                        <p>数据归档是将不常用的历史数据从主数据库移动到归档存储中的过程。归档的数据仍然可以通过专用接口查询，但不会占用主数据库的存储空间和影响系统性能。</p>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="card-title mb-0">文案归档</h5>
                                </div>
                                <div class="card-body">
                                    <p>已发布且超过指定天数的文案将被归档。归档的文案可以通过归档查询功能查看，但不会出现在常规文案列表中。</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="card-title mb-0">日志归档</h5>
                                </div>
                                <div class="card-body">
                                    <p>超过指定天数的系统日志将被归档。归档的日志将被压缩并存储在归档目录中，可以通过日志管理工具查看。</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="card-title mb-0">通知归档</h5>
                                </div>
                                <div class="card-body">
                                    <p>已读且超过指定天数的通知将被归档。归档的通知可以通过通知历史查询功能查看，但不会出现在常规通知列表中。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 