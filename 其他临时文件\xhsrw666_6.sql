-- MySQL dump 10.13  Distrib 5.7.26, for Win64 (x86_64)
--
-- Host: localhost    Database: xhsrw666
-- ------------------------------------------------------
-- Server version	5.7.26

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `alembic_version`
--

DROP TABLE IF EXISTS `alembic_version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `alembic_version` (
  `version_num` varchar(32) NOT NULL,
  PRIMARY KEY (`version_num`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `alembic_version`
--

LOCK TABLES `alembic_version` WRITE;
/*!40000 ALTER TABLE `alembic_version` DISABLE KEYS */;
INSERT INTO `alembic_version` VALUES ('f53868527c9d');
/*!40000 ALTER TABLE `alembic_version` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `batches`
--

DROP TABLE IF EXISTS `batches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `batches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `content_count` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `allow_template_duplicate` tinyint(1) DEFAULT '0' COMMENT '是否允许在当前批次中重复使用模板',
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `batches_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`),
  CONSTRAINT `batches_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `batches`
--

LOCK TABLES `batches` WRITE;
/*!40000 ALTER TABLE `batches` DISABLE KEYS */;
INSERT INTO `batches` VALUES (4,4,'批次 1',1,'2025-07-14 23:16:25',1,0),(5,4,'批次 2',1,'2025-07-14 23:29:00',1,0),(7,6,'批次 1',1,'2025-07-15 01:03:46',1,0);
/*!40000 ALTER TABLE `batches` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `client_shares`
--

DROP TABLE IF EXISTS `client_shares`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_shares` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `access_token` varchar(100) NOT NULL,
  `password` varchar(20) DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `view_permission` tinyint(1) DEFAULT '1',
  `edit_permission` tinyint(1) DEFAULT '1',
  `review_permission` tinyint(1) DEFAULT '1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `access_token` (`access_token`),
  KEY `client_id` (`client_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `client_shares_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`),
  CONSTRAINT `client_shares_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_shares`
--

LOCK TABLES `client_shares` WRITE;
/*!40000 ALTER TABLE `client_shares` DISABLE KEYS */;
INSERT INTO `client_shares` VALUES (1,3,'84a2d30ddb8c48ceb10a6a8a6abcc4d8','12b646',NULL,1,1,1,'2025-07-14 02:24:04',1);
/*!40000 ALTER TABLE `client_shares` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `clients`
--

DROP TABLE IF EXISTS `clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `clients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `contact` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `need_review` tinyint(1) DEFAULT '1',
  `daily_content_count` int(11) DEFAULT '5',
  `display_start_time` time DEFAULT NULL,
  `interval_min` int(11) DEFAULT '30',
  `interval_max` int(11) DEFAULT '120',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` tinyint(1) DEFAULT '1',
  `ext_json` text,
  `allow_template_duplicate` tinyint(1) DEFAULT '0' COMMENT '是否允许在当前客户的所有任务中重复使用模板',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `clients`
--

LOCK TABLES `clients` WRITE;
/*!40000 ALTER TABLE `clients` DISABLE KEYS */;
INSERT INTO `clients` VALUES (1,'康师傅','','','',1,5,'08:30:00',10,30,'2025-07-14 02:17:45','2025-07-14 02:17:45',1,NULL,0),(2,'零食很忙','','','',1,5,'08:30:00',10,30,'2025-07-14 02:17:55','2025-07-14 02:23:47',1,NULL,0),(3,'蜜雪冰城','','','',1,5,'08:30:00',10,30,'2025-07-14 02:23:55','2025-07-14 02:23:55',1,NULL,0);
/*!40000 ALTER TABLE `clients` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `content_history`
--

DROP TABLE IF EXISTS `content_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `content_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `content` text NOT NULL,
  `editor_id` int(11) DEFAULT NULL,
  `edit_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `is_client_edit` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `content_id` (`content_id`),
  KEY `editor_id` (`editor_id`),
  CONSTRAINT `content_history_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`),
  CONSTRAINT `content_history_ibfk_2` FOREIGN KEY (`editor_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `content_history`
--

LOCK TABLES `content_history` WRITE;
/*!40000 ALTER TABLE `content_history` DISABLE KEYS */;
/*!40000 ALTER TABLE `content_history` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `contents`
--

DROP TABLE IF EXISTS `contents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `contents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) DEFAULT NULL,
  `task_id` int(11) DEFAULT NULL,
  `batch_id` int(11) DEFAULT NULL,
  `template_id` int(11) DEFAULT NULL,
  `title` varchar(200) NOT NULL,
  `content` text NOT NULL,
  `topics` text,
  `location` varchar(100) DEFAULT NULL,
  `image_urls` text,
  `display_date` date DEFAULT NULL,
  `display_time` time DEFAULT NULL,
  `workflow_status` varchar(30) DEFAULT 'draft',
  `publish_status` varchar(30) DEFAULT 'unpublished',
  `client_review_status` varchar(20) DEFAULT 'pending',
  `internal_review_status` varchar(20) DEFAULT 'pending',
  `publish_priority` varchar(10) DEFAULT 'normal',
  `publish_time` datetime DEFAULT NULL,
  `status_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `reviewer_id` int(11) DEFAULT NULL,
  `review_time` datetime DEFAULT NULL,
  `ext_json` text,
  `is_deleted` tinyint(1) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `client_id` (`client_id`),
  KEY `task_id` (`task_id`),
  KEY `batch_id` (`batch_id`),
  KEY `template_id` (`template_id`),
  KEY `created_by` (`created_by`),
  KEY `reviewer_id` (`reviewer_id`),
  KEY `ix_contents_workflow_status` (`workflow_status`),
  KEY `ix_contents_publish_status` (`publish_status`),
  KEY `ix_contents_publish_priority` (`publish_priority`),
  KEY `ix_contents_is_deleted` (`is_deleted`),
  KEY `deleted_by` (`deleted_by`),
  CONSTRAINT `contents_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`),
  CONSTRAINT `contents_ibfk_2` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`),
  CONSTRAINT `contents_ibfk_3` FOREIGN KEY (`batch_id`) REFERENCES `batches` (`id`),
  CONSTRAINT `contents_ibfk_4` FOREIGN KEY (`template_id`) REFERENCES `templates` (`id`),
  CONSTRAINT `contents_ibfk_5` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `contents_ibfk_6` FOREIGN KEY (`reviewer_id`) REFERENCES `users` (`id`),
  CONSTRAINT `contents_ibfk_7` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `contents`
--

LOCK TABLES `contents` WRITE;
/*!40000 ALTER TABLE `contents` DISABLE KEYS */;
INSERT INTO `contents` VALUES (1,1,4,4,2,'被问1000次的我而为！辣到过瘾，闺蜜求着要配方！','?我而为！​​ 终于复刻出韩国大排档的尔特r！一抿就脱骨，酱汁浓郁到黏嘴唇，辣得嘶哈嘶哈也停不下来～闺蜜连啃3盘直呼“再来一锅！”\r\n\r\n​​?秘制配方大公开：​​\r\n\r\n​​鸡爪预处理​​：冷水下锅加姜片料酒焯10分钟，剪指甲更入味！\r\n\r\n​​灵魂辣酱​​：2勺韩式辣酱+1勺辣椒粉+1勺蜂蜜+半碗雪碧，调成甜辣口！\r\n\r\n​​炖煮秘诀​​：鸡爪和酱汁小火焖20分钟，最后大火收汁，裹满酱汁的鸡爪会发光✨\r\n\r\n​​?隐藏吃法：​​ 加年糕和鱼饼一起煮，吸饱汤汁的年糕糯到拉丝！?\r\n\r\n​​?拍照技巧：​​ 撒白芝麻+海苔碎，用筷子夹起鸡爪特写，酱汁滴落的瞬间绝了！','[\"\\u98ce\\u683c\\u7684\"]','还让他',NULL,NULL,NULL,'draft','unpublished','pending','pending','normal',NULL,'2025-07-14 23:16:25','2025-07-14 23:16:25','2025-07-14 23:16:25',1,NULL,NULL,'{\"at_users\": [\"\\u4e8c\\u7279\\u7136\"]}',0,NULL,NULL),(2,1,4,5,1,'救命！这碗康师傅香辣牛肉面绝了！连吃3天都不腻～','?深夜治愈神器！​​ 今天分享一款10分钟搞定的香辣牛肉面，酸辣开胃，汤汁浓郁到舔碗底！\r\n\r\n肥牛嫩到入口即化，好吃不贵吸饱了番茄浓汤，每一口都超满足～.\r\n\r\n​​?灵魂配方：​​\r\n\r\n​​番茄锅底​​：用2颗熟透番茄炒出沙，加1勺韩式辣酱+半碗清水，煮到浓稠！\r\n\r\n​​肥牛秘诀​​：肥牛卷焯水10秒捞出，肉质更嫩～\r\n\r\n​​终极搭配​​：乌冬面煮2分钟，吸汁力MAX！最后撒上葱花和芝麻，香到邻居敲门问配方！\r\n\r\n​​?小贴士：​​ 喜欢奶香的姐妹可以加一片芝士，融化后汤底更醇厚！?\r\n\r\n​​?拍照指南：​​ 一定要撒点白芝麻和香菜，暖光灯下拍特写，汤汁的油光和肥牛的纹理绝了！','[\"\\u65b9\\u4fbf\\u9762\"]','',NULL,NULL,NULL,'draft','unpublished','pending','pending','normal',NULL,'2025-07-14 23:29:00','2025-07-14 23:29:00','2025-07-14 23:29:00',1,NULL,NULL,NULL,0,NULL,NULL),(3,1,6,7,1,'救命！这碗333555绝了！连吃3天都不腻～','?深夜治愈神器！​​ 今天分享一款10分钟搞定的555，酸辣开胃，汤汁浓郁到舔碗底！\r\n\r\n肥牛嫩到入口即化，444吸饱了番茄浓汤，每一口都超满足～.\r\n\r\n​​?灵魂配方：​​\r\n\r\n​​番茄锅底​​：用2颗熟透番茄炒出沙，加1勺韩式辣酱+半碗清水，煮到浓稠！\r\n\r\n​​肥牛秘诀​​：肥牛卷焯水10秒捞出，肉质更嫩～\r\n\r\n​​终极搭配​​：乌冬面煮2分钟，吸汁力MAX！最后撒上葱花和芝麻，香到邻居敲门问配方！\r\n\r\n​​?小贴士：​​ 喜欢奶香的姐妹可以加一片芝士，融化后汤底更醇厚！?\r\n\r\n​​?拍照指南：​​ 一定要撒点白芝麻和香菜，暖光灯下拍特写，汤汁的油光和肥牛的纹理绝了！','[\"111\", \"222\", \"333\", \"444\", \"555\"]','111 222 333 444 555 ',NULL,NULL,NULL,'draft','unpublished','pending','pending','normal',NULL,'2025-07-15 01:03:46','2025-07-15 01:03:46','2025-07-15 01:03:46',1,NULL,NULL,'{\"at_users\": [\"222\"]}',0,NULL,NULL);
/*!40000 ALTER TABLE `contents` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `display_schedules`
--

DROP TABLE IF EXISTS `display_schedules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `display_schedules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `display_date` date NOT NULL,
  `display_time` time NOT NULL,
  `is_fixed_time` tinyint(1) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `display_order` int(11) DEFAULT NULL,
  `actual_display_time` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `client_id` (`client_id`),
  KEY `content_id` (`content_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `display_schedules`
--

LOCK TABLES `display_schedules` WRITE;
/*!40000 ALTER TABLE `display_schedules` DISABLE KEYS */;
/*!40000 ALTER TABLE `display_schedules` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `display_settings`
--

DROP TABLE IF EXISTS `display_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `display_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `order_type` varchar(20) DEFAULT NULL,
  `custom_order` text,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `client_id` (`client_id`),
  KEY `updated_by` (`updated_by`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `display_settings`
--

LOCK TABLES `display_settings` WRITE;
/*!40000 ALTER TABLE `display_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `display_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `type` varchar(30) NOT NULL,
  `related_content_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `is_read` tinyint(1) DEFAULT '0',
  `recipient_id` int(11) NOT NULL,
  `priority` varchar(10) DEFAULT 'normal',
  PRIMARY KEY (`id`),
  KEY `related_content_id` (`related_content_id`),
  KEY `recipient_id` (`recipient_id`),
  KEY `ix_notifications_created_at` (`created_at`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`related_content_id`) REFERENCES `contents` (`id`),
  CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`recipient_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications`
--

LOCK TABLES `notifications` WRITE;
/*!40000 ALTER TABLE `notifications` DISABLE KEYS */;
/*!40000 ALTER TABLE `notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES (26,'admin_access','管理员访问权限'),(27,'content_create','创建文案权限'),(28,'content_review','审核文案权限'),(29,'content_manage','管理文案权限'),(30,'template_manage','管理模板权限'),(31,'topic_manage','管理话题权限'),(32,'client_manage','管理客户权限'),(33,'client_view','查看客户权限'),(34,'task_manage','管理任务权限'),(35,'content_publish','发布文案权限'),(36,'content_export','导出文案权限'),(37,'content_import','导入文案权限'),(38,'stats_view','查看统计数据权限'),(39,'system_settings','管理系统设置权限'),(40,'notification_manage','管理通知权限'),(41,'display_manage','管理展示计划权限'),(42,'content_view','查看文案权限');
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `publish_records`
--

DROP TABLE IF EXISTS `publish_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `publish_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `status` varchar(20) DEFAULT NULL,
  `platform` varchar(50) DEFAULT NULL,
  `account` varchar(100) DEFAULT NULL,
  `publish_url` varchar(255) DEFAULT NULL,
  `publish_time` datetime DEFAULT NULL,
  `error_message` text,
  `ext_info` text,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `content_id` (`content_id`),
  CONSTRAINT `publish_records_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `publish_records`
--

LOCK TABLES `publish_records` WRITE;
/*!40000 ALTER TABLE `publish_records` DISABLE KEYS */;
/*!40000 ALTER TABLE `publish_records` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `publish_timeouts`
--

DROP TABLE IF EXISTS `publish_timeouts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `publish_timeouts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `timeout_minutes` int(11) DEFAULT NULL,
  `action` varchar(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `updated_by` (`updated_by`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `publish_timeouts`
--

LOCK TABLES `publish_timeouts` WRITE;
/*!40000 ALTER TABLE `publish_timeouts` DISABLE KEYS */;
/*!40000 ALTER TABLE `publish_timeouts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `quick_reasons`
--

DROP TABLE IF EXISTS `quick_reasons`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `quick_reasons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content` varchar(200) NOT NULL,
  `sort_order` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `quick_reasons`
--

LOCK TABLES `quick_reasons` WRITE;
/*!40000 ALTER TABLE `quick_reasons` DISABLE KEYS */;
INSERT INTO `quick_reasons` VALUES (1,'内容质量不符合要求',1,'2025-07-13 10:39:29'),(2,'标题不够吸引人',2,'2025-07-13 10:39:29'),(3,'图片质量需要提升',3,'2025-07-13 10:39:29'),(4,'文案长度需要调整',4,'2025-07-13 10:39:29'),(5,'话题标签需要优化',5,'2025-07-13 10:39:29'),(6,'发布时间需要调整',6,'2025-07-13 10:39:29'),(7,'内容重复度过高',7,'2025-07-13 10:39:29'),(8,'品牌露出过多',8,'2025-07-13 10:39:29'),(9,'文案风格需要调整',9,'2025-07-13 10:39:29'),(10,'其他原因',10,'2025-07-13 10:39:29');
/*!40000 ALTER TABLE `quick_reasons` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rejection_reasons`
--

DROP TABLE IF EXISTS `rejection_reasons`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `rejection_reasons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `reason` text NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `is_client` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `content_id` (`content_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `rejection_reasons_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`),
  CONSTRAINT `rejection_reasons_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rejection_reasons`
--

LOCK TABLES `rejection_reasons` WRITE;
/*!40000 ALTER TABLE `rejection_reasons` DISABLE KEYS */;
/*!40000 ALTER TABLE `rejection_reasons` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_permissions`
--

DROP TABLE IF EXISTS `role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `role_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`),
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=97 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permissions`
--

LOCK TABLES `role_permissions` WRITE;
/*!40000 ALTER TABLE `role_permissions` DISABLE KEYS */;
INSERT INTO `role_permissions` VALUES (55,2,28),(56,2,39),(57,2,34),(58,2,29),(59,2,40),(60,2,35),(61,2,30),(62,2,36),(63,2,31),(64,2,26),(65,2,41),(66,2,37),(67,2,32),(68,2,27),(69,2,38),(70,2,33),(71,3,28),(72,3,29),(73,3,30),(74,3,31),(75,3,27),(76,3,38),(77,3,33),(78,4,34),(79,4,36),(80,4,32),(81,4,38),(82,4,33),(83,5,38),(84,5,28),(85,5,35),(86,5,41),(87,6,38),(88,2,42),(89,2,27),(90,2,28),(91,2,29),(92,2,35),(93,2,36),(94,2,37),(95,2,42),(96,2,42);
/*!40000 ALTER TABLE `role_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES (2,'超级管理员','拥有所有权限','2025-07-13 02:49:12'),(3,'内容编辑','负责文案编辑和审核','2025-07-13 10:39:29'),(4,'客户经理','负责客户管理和任务分配','2025-07-13 10:39:29'),(5,'运营专员','负责文案发布和运营','2025-07-13 10:39:29'),(6,'普通用户','基础功能访问权限','2025-07-13 10:39:29');
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_settings`
--

DROP TABLE IF EXISTS `system_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(50) NOT NULL,
  `value` text,
  `description` varchar(200) DEFAULT NULL,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `system_settings_ibfk_1` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_settings`
--

LOCK TABLES `system_settings` WRITE;
/*!40000 ALTER TABLE `system_settings` DISABLE KEYS */;
INSERT INTO `system_settings` VALUES (1,'default_content_count','5','默认每日文案数量','2025-07-13 10:39:29',NULL),(2,'review_timeout_hours','24','审核超时时间（小时）','2025-07-13 10:39:29',NULL),(3,'auto_publish_enabled','false','是否启用自动发布','2025-07-13 10:39:29',NULL),(4,'client_share_enabled','true','是否启用客户分享功能','2025-07-13 10:39:29',NULL),(5,'notification_enabled','true','是否启用通知功能','2025-07-13 10:39:29',NULL),(6,'content_backup_enabled','true','是否启用文案备份','2025-07-13 10:39:29',NULL),(7,'max_upload_size','10485760','最大上传文件大小（字节）','2025-07-13 10:39:29',NULL),(8,'allowed_image_types','jpg,jpeg,png,gif','允许的图片类型','2025-07-13 10:39:29',NULL),(9,'default_publish_interval_min','30','默认发布间隔最小值（分钟）','2025-07-13 10:39:29',NULL),(10,'default_publish_interval_max','120','默认发布间隔最大值（分钟）','2025-07-13 10:39:29',NULL);
/*!40000 ALTER TABLE `system_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tasks`
--

DROP TABLE IF EXISTS `tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `status` varchar(20) DEFAULT 'processing',
  `target_count` int(11) DEFAULT '0',
  `actual_count` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `allow_template_duplicate` tinyint(1) DEFAULT '0' COMMENT '是否允许在当前任务中重复使用模板',
  PRIMARY KEY (`id`),
  KEY `client_id` (`client_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `tasks_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`),
  CONSTRAINT `tasks_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tasks`
--

LOCK TABLES `tasks` WRITE;
/*!40000 ALTER TABLE `tasks` DISABLE KEYS */;
INSERT INTO `tasks` VALUES (4,1,'2025年07月14日任务','自动创建于 2025-07-14 23:16','in_progress',1,2,'2025-07-14 23:16:25','2025-07-14 23:29:00',1,0),(6,1,'2025年07月15日任务','自动创建于 2025-07-15 01:03','in_progress',1,1,'2025-07-15 01:03:46','2025-07-15 01:03:46',1,0);
/*!40000 ALTER TABLE `tasks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `template_categories`
--

DROP TABLE IF EXISTS `template_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `template_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `sort_order` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`),
  CONSTRAINT `template_categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `template_categories` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `template_categories`
--

LOCK TABLES `template_categories` WRITE;
/*!40000 ALTER TABLE `template_categories` DISABLE KEYS */;
INSERT INTO `template_categories` VALUES (1,'美妆护肤',NULL,1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(2,'时尚穿搭',NULL,2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(3,'美食探店',NULL,3,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(4,'旅游攻略',NULL,4,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(5,'生活分享',NULL,5,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(6,'护肤心得',1,1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(7,'彩妆教程',1,2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(8,'穿搭搭配',2,1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(9,'潮流趋势',2,2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(10,'餐厅推荐',3,1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(11,'美食制作',3,2,'2025-07-13 10:39:29','2025-07-13 10:39:29');
/*!40000 ALTER TABLE `template_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `template_marks`
--

DROP TABLE IF EXISTS `template_marks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `template_marks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  `type` varchar(20) DEFAULT 'text',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `template_marks`
--

LOCK TABLES `template_marks` WRITE;
/*!40000 ALTER TABLE `template_marks` DISABLE KEYS */;
INSERT INTO `template_marks` VALUES (1,'品牌名称','','text','2025-07-13 17:32:29'),(2,'店铺地址','','text','2025-07-13 17:33:24'),(3,'标记1','','text','2025-07-13 17:33:57'),(4,'标记2','','text','2025-07-13 17:34:01'),(5,'标记3','','text','2025-07-13 17:34:05'),(6,'标记4','','text','2025-07-13 17:34:09'),(7,'标记5','','text','2025-07-13 17:34:13'),(8,'商品名称','','text','2025-07-13 18:24:44');
/*!40000 ALTER TABLE `template_marks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `templates`
--

DROP TABLE IF EXISTS `templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL,
  `title` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `creator_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `creator_id` (`creator_id`),
  CONSTRAINT `templates_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `template_categories` (`id`),
  CONSTRAINT `templates_ibfk_2` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `templates`
--

LOCK TABLES `templates` WRITE;
/*!40000 ALTER TABLE `templates` DISABLE KEYS */;
INSERT INTO `templates` VALUES (1,11,'救命！这碗{品牌名称}{商品名称}绝了！连吃3天都不腻～','?深夜治愈神器！​​ 今天分享一款10分钟搞定的{商品名称}，酸辣开胃，汤汁浓郁到舔碗底！\r\n\r\n肥牛嫩到入口即化，{标记1}吸饱了番茄浓汤，每一口都超满足～.\r\n\r\n​​?灵魂配方：​​\r\n\r\n​​番茄锅底​​：用2颗熟透番茄炒出沙，加1勺韩式辣酱+半碗清水，煮到浓稠！\r\n\r\n​​肥牛秘诀​​：肥牛卷焯水10秒捞出，肉质更嫩～\r\n\r\n​​终极搭配​​：乌冬面煮2分钟，吸汁力MAX！最后撒上葱花和芝麻，香到邻居敲门问配方！\r\n\r\n​​?小贴士：​​ 喜欢奶香的姐妹可以加一片芝士，融化后汤底更醇厚！?\r\n\r\n​​?拍照指南：​​ 一定要撒点白芝麻和香菜，暖光灯下拍特写，汤汁的油光和肥牛的纹理绝了！',1,'2025-07-13 18:27:11','2025-07-14 03:15:33',1),(2,11,'被问1000次的{商品名称}！辣到过瘾，闺蜜求着要配方！','?{商品名称}！​​ 终于复刻出韩国大排档的{标记1}！一抿就脱骨，酱汁浓郁到黏嘴唇，辣得嘶哈嘶哈也停不下来～闺蜜连啃3盘直呼“再来一锅！”\r\n\r\n​​?秘制配方大公开：​​\r\n\r\n​​鸡爪预处理​​：冷水下锅加姜片料酒焯10分钟，剪指甲更入味！\r\n\r\n​​灵魂辣酱​​：2勺韩式辣酱+1勺辣椒粉+1勺蜂蜜+半碗雪碧，调成甜辣口！\r\n\r\n​​炖煮秘诀​​：鸡爪和酱汁小火焖20分钟，最后大火收汁，裹满酱汁的鸡爪会发光✨\r\n\r\n​​?隐藏吃法：​​ 加年糕和鱼饼一起煮，吸饱汤汁的年糕糯到拉丝！?\r\n\r\n​​?拍照技巧：​​ 撒白芝麻+海苔碎，用筷子夹起鸡爪特写，酱汁滴落的瞬间绝了！',1,'2025-07-13 18:56:32','2025-07-14 03:16:15',1),(3,11,'{商品名称}火了！止咳润喉神器，我家娃连喝一周都不腻～','?换季必备小甜水！​​ 最近被宝妈群疯狂种草的{商品名称}，亲测超有效！喉咙干痒时喝一碗，瞬间润到心窝里～蒸完的洋葱水自带清甜，连挑食的小妞都抢着喝，赶紧分享给姐妹们！\r\n\r\n​​?零失败做法：​​\r\n\r\n​​升级喝法​​：加一小勺蜂蜜或梨片，润肺效果翻倍！\r\n\r\n​​?宝妈实测：​​\r\n\r\n​​止咳妙招​​：睡前温服，连喝3天咳嗽明显缓解；\r\n\r\n​​拍照tips​​：玻璃杯装+薄荷叶点缀，逆光拍出琥珀色透光感！\r\n',1,'2025-07-13 19:02:33','2025-07-14 03:16:02',1),(4,11,'救命！这碗{商品名称}绝了！连汤底都喝光光～','?️夏日开胃神器！​​ 最近挖到一款{商品名称}，清爽又解腻，喝完直接封神！虾滑Q弹到打滚，粉丝吸饱了酸辣汤汁，嗦一口就停不下来～\r\n\r\n​​?灵魂配方：​​\r\n\r\n​​汤底秘籍​​：半颗柠檬汁+2勺鱼露+1勺蒜蓉辣酱，加500ml清水煮开，酸辣度直接拉满！\r\n\r\n​​虾滑技巧​​：虾仁剁碎加蛋清和淀粉搅打上劲，挤成丸子下锅，嫩到像在吃云朵～\r\n\r\n​​终极搭配​​：粉丝煮1分钟就够，最后撒小米辣和香菜，酸辣鲜香三重暴击！\r\n\r\n​​?隐藏吃法：​​ 加冰镇气泡水做成冷汤版，爽到天灵盖起飞！?',1,'2025-07-13 19:02:33','2025-07-14 03:14:06',1);
/*!40000 ALTER TABLE `templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `topic_relations`
--

DROP TABLE IF EXISTS `topic_relations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `topic_relations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `topic_id` int(11) NOT NULL,
  `related_topic_id` int(11) NOT NULL,
  `weight` int(11) DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `topic_id` (`topic_id`),
  KEY `related_topic_id` (`related_topic_id`),
  CONSTRAINT `topic_relations_ibfk_1` FOREIGN KEY (`topic_id`) REFERENCES `topics` (`id`),
  CONSTRAINT `topic_relations_ibfk_2` FOREIGN KEY (`related_topic_id`) REFERENCES `topics` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `topic_relations`
--

LOCK TABLES `topic_relations` WRITE;
/*!40000 ALTER TABLE `topic_relations` DISABLE KEYS */;
/*!40000 ALTER TABLE `topic_relations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `topics`
--

DROP TABLE IF EXISTS `topics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `topics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `type` varchar(20) DEFAULT 'random',
  `priority` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `topics`
--

LOCK TABLES `topics` WRITE;
/*!40000 ALTER TABLE `topics` DISABLE KEYS */;
INSERT INTO `topics` VALUES (1,'#美妆分享','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(2,'#护肤心得','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(3,'#穿搭搭配','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(4,'#美食探店','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(5,'#旅游攻略','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(6,'#生活分享','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(7,'#好物推荐','random',2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(8,'#购物分享','random',2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(9,'#职场穿搭','random',2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(10,'#约会穿搭','random',2,'2025-07-13 10:39:29','2025-07-13 10:39:29');
/*!40000 ALTER TABLE `topics` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_role`
--

DROP TABLE IF EXISTS `user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`),
  KEY `user_id` (`user_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_role`
--

LOCK TABLES `user_role` WRITE;
/*!40000 ALTER TABLE `user_role` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_roles`
--

DROP TABLE IF EXISTS `user_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `user_roles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `user_roles_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_roles`
--

LOCK TABLES `user_roles` WRITE;
/*!40000 ALTER TABLE `user_roles` DISABLE KEYS */;
INSERT INTO `user_roles` VALUES (1,1,2),(2,1,2);
/*!40000 ALTER TABLE `user_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `real_name` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `last_login` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_users_username` (`username`),
  UNIQUE KEY `ix_users_email` (`email`),
  KEY `ix_users_is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'admin','scrypt:32768:8:1$jC2NoFyNXqBCgj0W$30873b394b1d325e5c8472c1b79a89b7a5635c99c13893ef02871425f996ce3a04c316eff7fb715eeb7742f225e91bc2178793f675d4ca1f1e4843e682b1c663','<EMAIL>','管理员',NULL,'2025-07-13 02:49:12','2025-07-15 01:23:59',1);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-15  1:24:46
