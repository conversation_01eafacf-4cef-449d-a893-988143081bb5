{% extends "base.html" %}

{% block title %}文案展示系统{% endblock %}

{% block page_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">文案展示计划</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('display.rules') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-cog"></i> 展示规则设置
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 筛选表单 -->
                    <form method="get" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-3">
                                {{ form.client_id.label(class="form-label") }}
                                {{ form.client_id(class="form-select") }}
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">开始日期</label>
                                <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">结束日期</label>
                                <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                            </div>
                            <div class="col-md-2">
                                {{ form.status.label(class="form-label") }}
                                {{ form.status(class="form-select") }}
                            </div>
                            <div class="col-md-2">
                                {{ form.search.label(class="form-label") }}
                                {{ form.search(class="form-control", placeholder="搜索文案标题或内容") }}
                            </div>
                            <div class="col-md-1 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> 筛选
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <!-- 展示计划列表 -->
                    {% if client_id > 0 %}
                    <div class="mb-3">
                        <a href="{{ url_for('display.schedule', client_id=client_id) }}" class="btn btn-success">
                            <i class="fas fa-calendar-plus"></i> 安排展示计划
                        </a>
                    </div>
                    {% endif %}
                    
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>客户</th>
                                    <th>文案ID</th>
                                    <th>文案标题</th>
                                    <th>展示日期</th>
                                    <th>展示时间</th>
                                    <th>固定时间</th>
                                    <th>状态</th>
                                    <th>展示顺序</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for schedule in schedules %}
                                <tr>
                                    <td>{{ schedule.id }}</td>
                                    <td>{{ schedule.client.name }}</td>
                                    <td>{{ schedule.content_id }}</td>
                                    <td>
                                        <a href="{{ url_for('content.content_view', content_id=schedule.content_id) }}" target="_blank">
                                            {{ schedule.content.title|truncate(30) }}
                                        </a>
                                    </td>
                                    <td>{{ schedule.display_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ schedule.display_time.strftime('%H:%M') }}</td>
                                    <td>
                                        {% if schedule.is_fixed_time %}
                                        <span class="badge bg-success">是</span>
                                        {% else %}
                                        <span class="badge bg-secondary">否</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if schedule.status == 'scheduled' %}
                                        <span class="badge bg-primary">已安排</span>
                                        {% elif schedule.status == 'displayed' %}
                                        <span class="badge bg-success">已展示</span>
                                        {% else %}
                                        <span class="badge bg-secondary">已取消</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ schedule.display_order }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('display.edit_schedule', schedule_id=schedule.id) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i> 编辑
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDelete({{ schedule.id }})">
                                                <i class="fas fa-trash"></i> 删除
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="10" class="text-center">没有找到展示计划</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% for page in pagination.iter_pages() %}
                                {% if page %}
                                    {% if page != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('display.index', page=page, client_id=client_id, date_from=date_from, date_to=date_to, status=status, search=search) }}">
                                            {{ page }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个展示计划吗？此操作不可撤销。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" action="">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    function confirmDelete(scheduleId) {
        document.getElementById('deleteForm').action = "{{ url_for('display.delete_schedule', schedule_id=0) }}".replace('0', scheduleId);
        var modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }
</script>
{% endblock %} 