{% extends "base.html" %}

{% block title %}创建任务{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-md-10 col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="mb-0">创建任务</h3>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.name.label(class="form-label") }}
                                    {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                    {% if form.name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.name.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.client_id.label(class="form-label") }}
                                    {{ form.client_id(class="form-select" + (" is-invalid" if form.client_id.errors else "")) }}
                                    {% if form.client_id.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.client_id.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-group">
                                    {{ form.description.label(class="form-label") }}
                                    {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows=4) }}
                                    {% if form.description.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.description.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.target_count.label(class="form-label") }}
                                    {{ form.target_count(class="form-control" + (" is-invalid" if form.target_count.errors else ""), type="number", min="1") }}
                                    {% if form.target_count.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.target_count.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.status.label(class="form-label") }}
                                    {{ form.status(class="form-select" + (" is-invalid" if form.status.errors else "")) }}
                                    {% if form.status.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.status.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ url_for('task.task_list') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> 返回列表
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存任务
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 