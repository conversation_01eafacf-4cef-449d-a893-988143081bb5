{% extends "base.html" %}

{% block title %}补充文案{% endblock %}

{% block page_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">补充文案</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('supplement.task_detail', task_id=task.id) }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回任务详情
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <h5><i class="fas fa-info-circle"></i> 补充信息：</h5>
                        <p>任务：{{ task.name }}</p>
                        <p>客户：{{ task.client.name if task.client else '无' }}</p>
                        <p>补充来源：
                            {% if supplement_source == 'unassigned' %}
                            从未分配文案池补充
                            {% else %}
                            生成新文案
                            {% endif %}
                        </p>
                    </div>
                    
                    <form method="post" action="{{ url_for('supplement.supplement_task', task_id=task.id) }}">
                        {{ form.csrf_token }}
                        <div class="mb-3">
                            <label for="batch_id" class="form-label">{{ form.batch_id.label }}</label>
                            {{ form.batch_id(class="form-select") }}
                            {% if form.batch_id.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.batch_id.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            <label for="count" class="form-label">{{ form.count.label }}</label>
                            {{ form.count(class="form-control", type="number", min=1, max=100) }}
                            {% if form.count.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.count.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">补充数量不能超过已删除的文案数量</small>
                        </div>
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sync"></i> 补充文案
                            </button>
                            <a href="{{ url_for('supplement.task_detail', task_id=task.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 