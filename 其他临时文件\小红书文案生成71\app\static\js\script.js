// 小红书文案生成系统自定义脚本

// 页面加载完成后初始化事件委托
document.addEventListener('DOMContentLoaded', function() {
    initContentPageEvents();
});

// 初始化文案页面事件
function initContentPageEvents() {
    // 删除按钮事件委托
    document.addEventListener('click', function(e) {
        if (e.target.closest('.delete-btn')) {
            e.preventDefault();
            const btn = e.target.closest('.delete-btn');
            const contentId = btn.getAttribute('data-content-id');
            if (contentId) {
                confirmDelete(contentId);
            }
        }
    });

    // 批量操作按钮事件委托
    document.addEventListener('click', function(e) {
        if (e.target.closest('.batch-action-btn')) {
            e.preventDefault();
            confirmBatchAction();
        }
    });
}

// 确认删除函数
function confirmDelete(contentId) {
    console.log('confirmDelete called with contentId:', contentId);

    showCustomConfirm(
        '确认删除',
        '确定要删除这个文案吗？此操作不可恢复。',
        function() {
            // 确认删除
            const formData = new FormData();
            // 获取CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                             document.querySelector('input[name="csrf_token"]')?.value;
            if (csrfToken) {
                formData.append('csrf_token', csrfToken);
            }

            fetch(`/contents/${contentId}/delete`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showCustomToast(data.message, 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showCustomToast('删除失败：' + (data.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                console.error('删除请求失败:', error);
                showCustomToast('删除失败，请重试', 'error');
            });
        },
        function() {
            console.log('删除操作已取消');
        }
    );
}

// 确认批量操作函数
function confirmBatchAction() {
    console.log('confirmBatchAction called');
    const checkboxes = document.querySelectorAll('.content-checkbox:checked');
    console.log('Selected checkboxes:', checkboxes.length);

    if (checkboxes.length === 0) {
        showCustomToast('请至少选择一个文案', 'warning');
        return;
    }

    const contentIds = Array.from(checkboxes).map(cb => cb.value).join(',');
    console.log('Selected content IDs:', contentIds);

    const selectedContentIds = document.getElementById('selectedContentIds');
    if (selectedContentIds) {
        selectedContentIds.value = contentIds;
        console.log('Set selectedContentIds value to:', contentIds);
    } else {
        console.error('selectedContentIds element not found');
    }

    const actionSelect = document.getElementById('action');
    if (!actionSelect) return;

    const action = actionSelect.value;
    let actionText = '';

    // 设置适当的状态值
    const statusInput = document.querySelector('input[name="status"]');
    if (statusInput) {
        if (action === 'review') {
            statusInput.value = 'approved';
        } else if (action === 'publish') {
            statusInput.value = 'published';
        } else if (action === 'delete') {
            statusInput.value = '';
        }
    }

    switch (action) {
        case 'review':
            actionText = '批量审核通过';
            break;
        case 'publish':
            actionText = '批量发布';
            break;
        case 'delete':
            actionText = '批量删除';
            break;
        default:
            actionText = '执行操作';
            break;
    }

    showCustomConfirm(
        '确认批量操作',
        `确定要${actionText}选中的 ${checkboxes.length} 个文案吗？此操作可能无法撤销。`,
        function() {
            console.log('Batch action confirmed');
            const batchActionForm = document.getElementById('batchActionForm');
            if (batchActionForm) {
                console.log('Submitting batch action form');
                batchActionForm.submit();
            } else {
                console.error('Batch action form not found');
            }
        },
        function() {
            console.log('批量操作已取消');
        }
    );
}

// 确保函数在全局作用域中可用
window.showCustomConfirm = function(title, message, onConfirm, onCancel) {
    // 创建对话框HTML
    const modalHtml = `
        <div class="custom-modal-overlay" id="customConfirmModal">
            <div class="custom-modal">
                <div class="custom-modal-header">
                    <h5 class="custom-modal-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        ${title}
                    </h5>
                </div>
                <div class="custom-modal-body">
                    ${message}
                </div>
                <div class="custom-modal-footer">
                    <button type="button" class="btn btn-secondary" id="customConfirmCancel">取消</button>
                    <button type="button" class="btn btn-danger" id="customConfirmOk">确认</button>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = document.getElementById('customConfirmModal');
    const okBtn = document.getElementById('customConfirmOk');
    const cancelBtn = document.getElementById('customConfirmCancel');

    // 显示对话框
    setTimeout(() => modal.classList.add('show'), 10);

    // 绑定事件
    okBtn.addEventListener('click', function() {
        hideModal();
        if (onConfirm) onConfirm();
    });

    cancelBtn.addEventListener('click', function() {
        hideModal();
        if (onCancel) onCancel();
    });

    // 点击遮罩关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            hideModal();
            if (onCancel) onCancel();
        }
    });

    // ESC键关闭
    document.addEventListener('keydown', function escHandler(e) {
        if (e.key === 'Escape') {
            hideModal();
            if (onCancel) onCancel();
            document.removeEventListener('keydown', escHandler);
        }
    });

    function hideModal() {
        modal.classList.remove('show');
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 300);
    }
};

// 自定义提示框
window.showCustomToast = function(message, type = 'success', duration = 3000) {
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-times-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };

    const toastHtml = `
        <div class="custom-toast ${type}" id="customToast">
            <i class="${icons[type] || icons.success}"></i>
            <div class="custom-toast-message">${message}</div>
        </div>
    `;

    // 移除已存在的提示框
    const existingToast = document.getElementById('customToast');
    if (existingToast) {
        existingToast.parentNode.removeChild(existingToast);
    }

    // 添加新提示框
    document.body.insertAdjacentHTML('beforeend', toastHtml);

    const toast = document.getElementById('customToast');

    // 显示提示框
    setTimeout(() => toast.classList.add('show'), 10);

    // 自动隐藏
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, duration);
};

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 自动关闭提示框
    var alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            var closeBtn = new bootstrap.Alert(alert);
            closeBtn.close();
        }, 5000);
    });
    
    // 激活下拉菜单
    var dropdowns = document.querySelectorAll('.dropdown-toggle');
    dropdowns.forEach(function(dropdown) {
        new bootstrap.Dropdown(dropdown);
    });
    
    // 确保导航菜单可见
    var navbarCollapse = document.querySelector('.navbar-collapse');
    if (navbarCollapse && !navbarCollapse.classList.contains('show')) {
        navbarCollapse.classList.add('show');
    }
    
    // 高亮当前活动菜单
    var currentUrl = window.location.href;
    var navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    navLinks.forEach(function(link) {
        var href = link.getAttribute('href');
        if (href && currentUrl.includes(href) && href !== '/') {
            link.classList.add('active');
        }
    });
}); 