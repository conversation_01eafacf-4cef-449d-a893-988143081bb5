# 图片上传页面筛选功能实现说明

## 需求描述
用户希望在图片上传页面的筛选条件中实现以下功能：
1. 只显示在图片上传流程中有任务的客户
2. 实现客户和任务的联动筛选
3. 当选择客户时，任务下拉列表只显示该客户的任务
4. 页面初始状态下，任务下拉列表应该是禁用的，显示"请先选择客户"
5. 筛选功能使用AJAX方式，不刷新整个页面

## 实现方案

### 1. 后端修改

#### 1.1 修改图片上传页面路由 (`app/views/main_simple.py`)

**原有逻辑：**
- 显示所有活跃客户
- 显示所有活跃任务
- 没有筛选条件应用
- 任务列表始终显示所有任务

**新逻辑：**
- 只显示在图片上传流程中有文案的客户（通过JOIN查询）
- 页面初始状态下不显示任何任务（任务列表为空）
- 只有选择客户后才显示该客户的任务
- 应用客户、任务、状态筛选条件
- 使用AJAX实现无刷新筛选

**关键代码修改：**
```python
# 获取有图片上传任务的客户列表（只显示在图片上传流程中有文案的客户）
clients_with_tasks = db.session.query(Client).join(Content).filter(
    and_(
        Content.workflow_status.in_(['first_reviewed', 'image_uploaded']),
        Content.is_deleted == False,
        Client.status == True
    )
).distinct().order_by(Client.name).all()

# 获取任务列表（根据客户筛选）
if client_id:
    # 如果选择了客户，只显示该客户的任务
    tasks = Task.query.filter(
        and_(
            Task.client_id == client_id,
            Task.status.in_(['processing', 'completed', 'in_progress'])
        )
    ).order_by(Task.name).all()
else:
    # 如果没有选择客户，不显示任何任务
    tasks = []
```

#### 1.2 修改获取任务API (`app/views/main_simple.py`)

**功能：** 为客户任务联动提供API支持

**修改内容：**
```python
@main_simple_bp.route('/contents/get-tasks/<int:client_id>')
@login_required
def get_tasks(client_id):
    """获取客户的任务列表"""
    from sqlalchemy import and_
    
    # 获取该客户在图片上传流程中的任务列表
    tasks = db.session.query(Task).join(Content).filter(
        and_(
            Task.client_id == client_id,
            Content.workflow_status.in_(['first_reviewed', 'image_uploaded']),
            Content.is_deleted == False,
            Task.status.in_(['processing', 'completed', 'in_progress'])
        )
    ).distinct().order_by(Task.name).all()
    
    task_list = [{'id': task.id, 'name': task.name} for task in tasks]

    return jsonify({
        'success': True,
        'tasks': task_list
    })
```

### 2. 前端修改

#### 2.1 修改任务下拉列表模板 (`app/templates/image/upload.html`)

**功能：** 根据是否选择客户来控制任务下拉列表的状态

**关键代码：**
```html
<select class="form-select form-select-sm" id="task_filter" name="task_id" {% if not request.args.get('client_id') %}disabled{% endif %}>
    {% if request.args.get('client_id') %}
        <option value="">全部任务 (共{{ tasks|length }}个)</option>
        {% for task in tasks %}
        <option value="{{ task.id }}" {% if request.args.get('task_id') == task.id|string %}selected{% endif %}>
            {{ task.name }}
        </option>
        {% endfor %}
    {% else %}
        <option value="">请先选择客户</option>
    {% endif %}
</select>
```

#### 2.2 添加客户任务联动JavaScript (`app/templates/image/upload.html`)

**功能：** 当用户选择客户时，自动更新任务下拉列表，包含加载状态和错误处理

**关键代码：**
```javascript
// 客户任务联动功能
function initClientTaskLinkage() {
    const clientSelect = document.getElementById('client_filter');
    const taskSelect = document.getElementById('task_filter');

    clientSelect.addEventListener('change', function() {
        const clientId = this.value;

        if (clientId && clientId !== '') {
            // 显示加载状态
            taskSelect.innerHTML = '<option value="">加载中...</option>';
            taskSelect.disabled = true;

            // 获取该客户的任务列表
            fetch(`/simple/contents/get-tasks/${clientId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 清空现有选项
                    taskSelect.innerHTML = '<option value="">全部任务</option>';

                    // 添加该客户的任务选项
                    data.tasks.forEach(task => {
                        const option = document.createElement('option');
                        option.value = task.id;
                        option.textContent = task.name;
                        taskSelect.appendChild(option);
                    });

                    // 更新任务数量显示和状态
                    const taskCount = data.tasks.length;
                    if (taskCount > 0) {
                        taskSelect.querySelector('option[value=""]').textContent = `全部任务 (共${taskCount}个)`;
                        taskSelect.disabled = false;
                    } else {
                        taskSelect.innerHTML = '<option value="">该客户暂无任务</option>';
                        taskSelect.disabled = true;
                    }
                }
            })
            .catch(error => {
                taskSelect.innerHTML = '<option value="">加载失败</option>';
                taskSelect.disabled = true;
                showToast('获取任务列表失败', 'error');
            });
        } else {
            // 如果没有选择客户，重置任务列表
            taskSelect.innerHTML = '<option value="">请先选择客户</option>';
            taskSelect.disabled = true;
        }
    });
}
```

#### 2.2 添加筛选表单AJAX提交

**功能：** 筛选时不刷新整个页面，只更新内容区域

**关键代码：**
```javascript
// 筛选表单提交时自动刷新页面内容
document.querySelector('form[method="GET"]').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const params = new URLSearchParams(formData);
    
    // 构建新的URL
    const newUrl = '/simple/image-upload?' + params.toString();
    
    // 发送AJAX请求更新页面内容
    fetch(newUrl, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        // 更新页面内容
        document.getElementById('page-container').innerHTML = html;
        
        // 更新浏览器URL（不刷新页面）
        history.pushState(null, '', newUrl);
    })
    .catch(error => {
        console.error('筛选失败:', error);
        showToast('筛选失败，请重试', 'error');
    });
});
```

## 测试结果

通过测试脚本验证，实现的功能正常工作：

1. **有图片上传任务的客户筛选**: 只显示康师傅 (ID: 1) - 总计: 1个
2. **没有选择客户时的任务列表**: 总计: 0个（正确，符合新需求）
3. **选择客户1时的任务列表**: 显示3个任务（任务4、6、7）
4. **API端点测试**: 只返回在图片上传流程中的任务7 - 总计: 1个
5. **筛选逻辑测试**: 客户1 + 任务7 返回7个文案

## 功能特点

1. **智能筛选**: 只显示真正有图片上传任务的客户
2. **渐进式交互**: 页面初始状态下任务列表为禁用状态，必须先选择客户
3. **联动效果**: 选择客户后，任务列表自动更新为该客户的任务，包含加载状态
4. **用户体验**: 使用AJAX实现无刷新筛选，提供即时反馈
5. **数据准确**: 通过JOIN查询确保数据的一致性和准确性
6. **错误处理**: 完善的错误处理和用户提示机制

## 使用方法

1. 访问图片上传页面：`http://127.0.0.1:5000/simple/image-upload`
2. 在筛选条件中选择客户，任务下拉列表会自动更新
3. 选择具体任务进行进一步筛选
4. 点击"搜索"按钮应用筛选条件
5. 点击"重置"按钮清除所有筛选条件
