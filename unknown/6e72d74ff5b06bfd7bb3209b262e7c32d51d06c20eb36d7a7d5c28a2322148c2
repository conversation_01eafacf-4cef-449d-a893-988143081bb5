"""
文案相关模型
"""
from datetime import datetime
import json
from . import db


class Content(db.Model):
    """文案模型"""
    __tablename__ = 'contents'
    
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.<PERSON><PERSON>, db.<PERSON>ey('clients.id'), nullable=True)  # 允许为空，用于未分配的文案
    task_id = db.Column(db.Integer, db.ForeignKey('tasks.id'), nullable=True)  # 允许为空，用于未分配的文案
    batch_id = db.Column(db.Integer, db.ForeignKey('batches.id'), nullable=True)  # 允许为空，用于未分配的文案
    template_id = db.Column(db.Integer, db.ForeignKey('templates.id'))
    
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=False)
    topics = db.Column(db.Text)  # 话题列表（JSON格式）
    location = db.Column(db.String(100))  # 定位信息
    image_urls = db.Column(db.Text)  # 图片URL列表（JSON格式）
    
    display_date = db.Column(db.Date)  # 展示日期
    display_time = db.Column(db.Time)  # 展示时间
    
    # 工作流状态：draft草稿, pending_review待初审, first_reviewed初审通过, 
    # pending_image待上传图片, image_uploaded图片已上传, pending_final_review待最终审核, 
    # pending_client_review待客户审核, client_rejected客户已拒绝, client_approved客户已通过,
    # pending_publish待发布, published已发布
    workflow_status = db.Column(db.String(30), default='draft', index=True)
    
    # 发布状态：unpublished未发布, publishing发布中, published发布成功,
    # failed发布失败, partial_published部分发布, publish_timeout发布超时
    publish_status = db.Column(db.String(30), default='unpublished', index=True)
    
    # 客户审核状态：pending未审核, approved已通过, rejected已拒绝
    client_review_status = db.Column(db.String(20), default='pending')
    
    # 内部审核状态：pending未审核, first_approved初审通过, final_approved最终审核通过
    # rejected驳回, final_rej_text终审驳回文案问题, final_rej_img终审驳回图片问题,
    # final_rej_both终审驳回两者都有问题, final_rej_text_ok终审驳回文案问题但图片已修复,
    # final_rej_img_ok终审驳回图片问题但文案已修复, client_rej_text客户驳回文案问题,
    # client_rej_img客户驳回图片问题, client_rej_both客户驳回两者都有问题
    internal_review_status = db.Column(db.String(20), default='pending')
    
    # 发布优先级：high高, normal中, low低
    publish_priority = db.Column(db.String(10), default='normal', index=True)
    
    publish_time = db.Column(db.DateTime)  # API获取时间
    publish_error = db.Column(db.Text)  # 发布提示信息（成功或失败的详细信息）
    publish_retry_count = db.Column(db.Integer, default=0)  # 发布重试次数
    status_update_time = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    reviewer_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    review_time = db.Column(db.DateTime)
    
    # 删除相关字段
    is_deleted = db.Column(db.Boolean, default=False, index=True)  # 是否已删除
    deleted_at = db.Column(db.DateTime)  # 删除时间
    deleted_by = db.Column(db.Integer, db.ForeignKey('users.id'))  # 删除人ID
    
    ext_json = db.Column(db.Text)  # 扩展字段（JSON格式）

    # 完成状态标记字段（用于"两者都有问题"的驳回流程）
    content_completed = db.Column(db.Boolean, default=True, nullable=False)  # 文案是否已重新编辑完成
    image_completed = db.Column(db.Boolean, default=True, nullable=False)  # 图片是否已重新上传完成
    
    # 关联
    creator = db.relationship('User', foreign_keys=[created_by], backref=db.backref('created_contents', lazy='dynamic'))
    reviewer = db.relationship('User', foreign_keys=[reviewer_id], backref=db.backref('reviewed_contents', lazy='dynamic'))
    deleter = db.relationship('User', foreign_keys=[deleted_by], backref=db.backref('deleted_contents', lazy='dynamic'))
    template = db.relationship('Template', backref=db.backref('contents', lazy='dynamic'))
    
    # 修改关系定义，解决冲突
    history_records = db.relationship('ContentHistory', backref=db.backref('related_content'), lazy='dynamic')
    rejection_reasons = db.relationship('RejectionReason', backref='content', lazy='dynamic')
    publish_records = db.relationship('PublishRecord', backref='content', lazy='dynamic')
    
    @property
    def topics_list(self):
        """获取话题列表"""
        if self.topics:
            try:
                return json.loads(self.topics)
            except:
                return []
        return []
    
    @topics_list.setter
    def topics_list(self, value):
        """设置话题列表"""
        if value:
            self.topics = json.dumps(value)
        else:
            self.topics = None
    
    @property
    def image_urls_list(self):
        """获取图片URL列表"""
        if self.image_urls:
            try:
                return json.loads(self.image_urls)
            except:
                return []
        return []
    
    @image_urls_list.setter
    def image_urls_list(self, value):
        """设置图片URL列表"""
        if value:
            self.image_urls = json.dumps(value)
        else:
            self.image_urls = None

    @property
    def location_list(self):
        """获取定位列表"""
        if self.location:
            try:
                # 尝试解析JSON格式
                return json.loads(self.location)
            except:
                # 如果不是JSON格式，尝试按分隔符分割（兼容旧数据）
                if '\n' in self.location:
                    return [loc.strip() for loc in self.location.split('\n') if loc.strip()]
                elif ',' in self.location:
                    return [loc.strip() for loc in self.location.split(',') if loc.strip()]
                elif ' ' in self.location:
                    return [loc.strip() for loc in self.location.split(' ') if loc.strip()]
                else:
                    return [self.location.strip()] if self.location.strip() else []
        return []

    @location_list.setter
    def location_list(self, value):
        """设置定位列表"""
        if value:
            self.location = json.dumps(value)
        else:
            self.location = None

    @property
    def at_users_list(self):
        """获取@用户列表"""
        ext_data = self.ext_data
        return ext_data.get('at_users', []) if ext_data else []

    @at_users_list.setter
    def at_users_list(self, value):
        """设置@用户列表"""
        ext_data = self.ext_data or {}
        ext_data['at_users'] = value if value else []
        self.ext_data = ext_data

    @property
    def ext_data(self):
        """获取扩展数据"""
        if self.ext_json:
            try:
                return json.loads(self.ext_json)
            except:
                return {}
        return {}

    @ext_data.setter
    def ext_data(self, value):
        """设置扩展数据"""
        if value:
            self.ext_json = json.dumps(value)
        else:
            self.ext_json = None
    
    def __repr__(self):
        return f'<Content {self.id}>'


class ContentHistory(db.Model):
    """文案历史模型"""
    __tablename__ = 'content_history'
    
    id = db.Column(db.Integer, primary_key=True)
    content_id = db.Column(db.Integer, db.ForeignKey('contents.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=False)
    editor_id = db.Column(db.Integer, db.ForeignKey('users.id'))  # NULL表示客户编辑
    edit_time = db.Column(db.DateTime, default=datetime.now)
    is_client_edit = db.Column(db.Boolean, default=False)
    
    # 关联
    editor = db.relationship('User', backref=db.backref('edited_content_history', lazy='dynamic'))
    
    def __repr__(self):
        return f'<ContentHistory {self.id}>'


class RejectionReason(db.Model):
    """拒绝理由模型"""
    __tablename__ = 'rejection_reasons'

    id = db.Column(db.Integer, primary_key=True)
    content_id = db.Column(db.Integer, db.ForeignKey('contents.id'), nullable=False)
    reason = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))  # NULL表示客户
    is_client = db.Column(db.Boolean, default=False)  # 是否客户创建

    # 驳回类型：content=文案问题, image=图片问题, both=两者都有问题
    rejection_type = db.Column(db.String(20), default='content', nullable=False)

    # 关联
    creator = db.relationship('User', backref=db.backref('rejection_reasons', lazy='dynamic'))

    def __repr__(self):
        return f'<RejectionReason {self.id}>'

    def get_type_display(self):
        """获取驳回类型的显示文本"""
        type_map = {
            'content': '文案问题',
            'image': '图片问题',
            'both': '文案+图片问题'
        }
        return type_map.get(self.rejection_type, '未知类型')


class QuickReason(db.Model):
    """快捷理由模型"""
    __tablename__ = 'quick_reasons'
    
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.String(200), nullable=False)
    sort_order = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    def __repr__(self):
        return f'<QuickReason {self.id}>' 