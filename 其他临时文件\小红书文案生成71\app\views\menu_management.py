"""
菜单管理视图
"""
from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from app.models import db
from app.models.menu import MenuItem
from app.models.user import Permission
from app.utils.decorators import permission_required

menu_management = Blueprint('menu_management', __name__, url_prefix='/menu')

@menu_management.route('/')
@login_required
@permission_required('admin_access')
def index():
    """菜单管理首页"""
    menu_items = MenuItem.query.order_by(MenuItem.order).all()
    permissions = Permission.query.all()
    return render_template('menu_management/index.html', 
                         menu_items=menu_items,
                         permissions=permissions)

@menu_management.route('/create', methods=['GET', 'POST'])
@login_required
@permission_required('admin_access')
def create():
    """创建菜单项"""
    if request.method == 'POST':
        name = request.form.get('name')
        url = request.form.get('url')
        icon = request.form.get('icon')
        permission = request.form.get('permission')
        parent_id = request.form.get('parent_id')
        order = request.form.get('order', 0, type=int)
        
        # 验证必填字段
        if not name or not url:
            flash('菜单名称和URL不能为空', 'danger')
            return redirect(url_for('menu_management.create'))
        
        # 创建菜单项
        menu_item = MenuItem(
            name=name,
            url=url,
            icon=icon,
            permission=permission if permission else None,
            parent_id=parent_id if parent_id else None,
            order=order
        )
        
        try:
            db.session.add(menu_item)
            db.session.commit()
            flash('菜单项创建成功', 'success')
            return redirect(url_for('menu_management.index'))
        except Exception as e:
            db.session.rollback()
            flash(f'创建失败：{str(e)}', 'danger')
    
    # 获取可作为父菜单的项目
    parent_items = MenuItem.query.filter_by(parent_id=None).all()
    permissions = Permission.query.all()
    
    return render_template('menu_management/create.html',
                         parent_items=parent_items,
                         permissions=permissions)

@menu_management.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@permission_required('admin_access')
def edit(id):
    """编辑菜单项"""
    menu_item = MenuItem.query.get_or_404(id)
    
    if request.method == 'POST':
        menu_item.name = request.form.get('name')
        menu_item.url = request.form.get('url')
        menu_item.icon = request.form.get('icon')
        menu_item.permission = request.form.get('permission') if request.form.get('permission') else None
        menu_item.parent_id = request.form.get('parent_id') if request.form.get('parent_id') else None
        menu_item.order = request.form.get('order', 0, type=int)
        menu_item.is_active = bool(request.form.get('is_active'))
        
        try:
            db.session.commit()
            flash('菜单项更新成功', 'success')
            return redirect(url_for('menu_management.index'))
        except Exception as e:
            db.session.rollback()
            flash(f'更新失败：{str(e)}', 'danger')
    
    # 获取可作为父菜单的项目（排除自己和子菜单）
    parent_items = MenuItem.query.filter(MenuItem.id != id).filter_by(parent_id=None).all()
    permissions = Permission.query.all()
    
    return render_template('menu_management/edit.html',
                         menu_item=menu_item,
                         parent_items=parent_items,
                         permissions=permissions)

@menu_management.route('/delete/<int:id>', methods=['POST'])
@login_required
@permission_required('admin_access')
def delete(id):
    """删除菜单项"""
    menu_item = MenuItem.query.get_or_404(id)
    
    try:
        db.session.delete(menu_item)
        db.session.commit()
        flash('菜单项删除成功', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'删除失败：{str(e)}', 'danger')
    
    return redirect(url_for('menu_management.index'))

@menu_management.route('/init_default_menu', methods=['POST'])
@login_required
@permission_required('admin_access')
def init_default_menu():
    """初始化默认菜单"""
    default_menu_items = [
        {'name': '首页', 'url': '/', 'icon': 'fas fa-home', 'permission': None, 'order': 1},
        {'name': '用户管理', 'url': '/user-management/users', 'icon': 'fas fa-users', 'permission': 'user.view', 'order': 2},
        {'name': '客户管理', 'url': '/clients/', 'icon': 'fas fa-building', 'permission': 'client.view', 'order': 3},
        {'name': '模板管理', 'url': '/templates/', 'icon': 'fas fa-layer-group', 'permission': 'template.view', 'order': 4},
        {'name': '文案管理', 'url': '/contents/', 'icon': 'fas fa-file-alt', 'permission': 'content.view', 'order': 5},
        {'name': '初审管理', 'url': '/first-review/', 'icon': 'fas fa-check-circle', 'permission': 'first_review.view', 'order': 6},
        {'name': '图片管理', 'url': '/image-management/', 'icon': 'fas fa-images', 'permission': 'image.view', 'order': 7},
        {'name': '终审管理', 'url': '/final-review/', 'icon': 'fas fa-clipboard-check', 'permission': 'final_review.view', 'order': 8},
        {'name': '发布管理', 'url': '/publish/', 'icon': 'fas fa-paper-plane', 'permission': 'publish.view', 'order': 9},
        {'name': '通知中心', 'url': '/notifications/', 'icon': 'fas fa-bell', 'permission': None, 'order': 10},
        {'name': '系统设置', 'url': '/system/', 'icon': 'fas fa-cog', 'permission': 'system.view', 'order': 11},
    ]
    
    try:
        # 清空现有菜单
        MenuItem.query.delete()
        
        # 添加默认菜单
        for item_data in default_menu_items:
            menu_item = MenuItem(**item_data)
            db.session.add(menu_item)
        
        db.session.commit()
        flash('默认菜单初始化成功', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'初始化失败：{str(e)}', 'danger')
    
    return redirect(url_for('menu_management.index'))
