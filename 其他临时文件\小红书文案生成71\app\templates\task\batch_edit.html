{% extends "base.html" %}

{% block title %}编辑批次{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-md-8 col-lg-6 mx-auto">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="mb-0">编辑批次</h3>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        
                        <div class="mb-3">
                            <div class="form-group">
                                {{ form.task_id.label(class="form-label") }}
                                {{ form.task_id(class="form-select" + (" is-invalid" if form.task_id.errors else "")) }}
                                {% if form.task_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.task_id.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-group">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                {% if form.name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-group">
                                {{ form.content_count.label(class="form-label") }}
                                {{ form.content_count(class="form-control" + (" is-invalid" if form.content_count.errors else ""), type="number", min="0") }}
                                {% if form.content_count.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.content_count.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ url_for('task.task_view', task_id=batch.task_id) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> 返回任务
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存修改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 