{% extends "base.html" %}

{% block title %}系统设置{% endblock %}

{% block content_auth %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">系统设置</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="card-title mb-0"><i class="fas fa-cogs me-2"></i> 基础设置</h5>
                                </div>
                                <div class="card-body">
                                    <p>配置系统的基本参数，包括默认展示数量、展示时间、间隔时间等。</p>
                                    <a href="{{ url_for('system.basic') }}" class="btn btn-primary">
                                        <i class="fas fa-wrench"></i> 管理基础设置
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h5 class="card-title mb-0"><i class="fas fa-tasks me-2"></i> 审核流程设置</h5>
                                </div>
                                <div class="card-body">
                                    <p>配置文案审核流程，包括自动通过设置、拒绝理由设置、审核时间限制等。</p>
                                    <a href="{{ url_for('system.review') }}" class="btn btn-success">
                                        <i class="fas fa-check-double"></i> 管理审核设置
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-info text-white">
                                    <h5 class="card-title mb-0"><i class="fas fa-paper-plane me-2"></i> 发布设置</h5>
                                </div>
                                <div class="card-body">
                                    <p>配置文案发布相关参数，包括发布超时时间、超时处理策略等。</p>
                                    <a href="{{ url_for('system.publish') }}" class="btn btn-info">
                                        <i class="fas fa-upload"></i> 管理发布设置
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="card-title mb-0"><i class="fas fa-file-alt me-2"></i> 日志设置</h5>
                                </div>
                                <div class="card-body">
                                    <p>配置系统日志参数，包括日志级别、日志保留时间等。</p>
                                    <a href="{{ url_for('system.log') }}" class="btn btn-warning">
                                        <i class="fas fa-list"></i> 管理日志设置
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="card-title mb-0"><i class="fas fa-archive me-2"></i> 归档设置</h5>
                                </div>
                                <div class="card-body">
                                    <p>配置数据归档策略，包括文案归档时间、日志归档时间、通知归档时间等。</p>
                                    <a href="{{ url_for('system.archive') }}" class="btn btn-secondary">
                                        <i class="fas fa-box"></i> 管理归档设置
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="card-title mb-0"><i class="fas fa-bell me-2"></i> 通知设置</h5>
                                </div>
                                <div class="card-body">
                                    <p>配置系统通知参数，包括通知类型、通知接收者、通知方式等。</p>
                                    <a href="{{ url_for('system.notification') }}" class="btn btn-danger">
                                        <i class="fas fa-envelope"></i> 管理通知设置
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-dark text-white">
                                    <h5 class="card-title mb-0"><i class="fas fa-key me-2"></i> API设置</h5>
                                </div>
                                <div class="card-body">
                                    <p>配置API接口参数，包括API密钥管理、接口权限设置等。</p>
                                    <a href="{{ url_for('system.api_settings') }}" class="btn btn-dark">
                                        <i class="fas fa-code"></i> 管理API设置
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 