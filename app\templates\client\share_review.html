{% extends "client/share_layout.html" %}

{% block title %}审核文案 - {{ client.name }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="card mb-4">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h3 class="mb-0">{{ client.name }} - 文案审核</h3>
                <span class="badge bg-success">审核模式</span>
            </div>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> 您可以在此页面查看和审核文案内容。
            </div>
            
            <!-- 文案列表 -->
            <div class="content-list mt-4">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label for="statusFilter" class="form-label">状态筛选</label>
                            <select id="statusFilter" class="form-select">
                                <option value="all">全部状态</option>
                                <option value="pending" selected>待审核</option>
                                <option value="approved">已通过</option>
                                <option value="rejected">已拒绝</option>
                                <option value="published">已发布</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label for="dateFilter" class="form-label">日期筛选</label>
                            <input type="date" id="dateFilter" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label for="searchFilter" class="form-label">搜索</label>
                            <input type="text" id="searchFilter" class="form-control" placeholder="搜索文案内容">
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive mt-3">
                    <table class="table table-hover table-striped">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>标题</th>
                                <th>内容预览</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="contentTableBody">
                            <!-- 这里将通过AJAX加载文案数据 -->
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2">正在加载文案数据...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页控件 -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <span class="text-muted">共 <span id="totalCount">0</span> 条记录</span>
                    </div>
                    <nav aria-label="Page navigation">
                        <ul class="pagination" id="pagination">
                            <!-- 分页将通过JS动态生成 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 文案详情模态框 -->
<div class="modal fade" id="contentDetailModal" tabindex="-1" aria-labelledby="contentDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contentDetailModalLabel">文案详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="contentDetailBody">
                <!-- 文案详情将通过JS动态加载 -->
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载文案详情...</p>
                </div>
            </div>
            <div class="modal-footer">
                <div class="d-flex justify-content-between w-100">
                    <button type="button" class="btn btn-danger" onclick="rejectContent()">
                        <i class="fas fa-times-circle me-1"></i> 拒绝
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-success" onclick="approveContent()">
                        <i class="fas fa-check-circle me-1"></i> 通过
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 拒绝原因模态框 -->
<div class="modal fade" id="rejectReasonModal" tabindex="-1" aria-labelledby="rejectReasonModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="rejectReasonModalLabel">拒绝原因</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="rejectReason" class="form-label">请输入拒绝原因</label>
                    <textarea class="form-control" id="rejectReason" rows="3" placeholder="请输入拒绝原因..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="submitReject()">确认拒绝</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 当前选中的文案ID
    let currentContentId = null;

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 加载文案数据
        loadContents(1);
        
        // 绑定筛选事件
        document.getElementById('statusFilter').addEventListener('change', function() {
            loadContents(1);
        });
        
        document.getElementById('dateFilter').addEventListener('change', function() {
            loadContents(1);
        });
        
        document.getElementById('searchFilter').addEventListener('input', function() {
            if (this.value.length === 0 || this.value.length >= 2) {
                // 当输入为空或至少2个字符时触发搜索
                loadContents(1);
            }
        });
    });
    
    // 加载文案数据
    function loadContents(page) {
        const statusFilter = document.getElementById('statusFilter').value;
        const dateFilter = document.getElementById('dateFilter').value;
        const searchFilter = document.getElementById('searchFilter').value;
        
        // 显示加载中状态
        document.getElementById('contentTableBody').innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载文案数据...</p>
                </td>
            </tr>
        `;
        
        // 构建查询参数
        const params = new URLSearchParams({
            client_id: '{{ client.id }}',
            page: page,
            status: statusFilter,
            date: dateFilter,
            search: searchFilter,
            share_token: '{{ share.access_token }}'
        });
        
        // 发送AJAX请求获取数据
        fetch('/api/client/contents?' + params.toString())
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应不正常');
                }
                return response.json();
            })
            .then(data => {
                renderContents(data);
            })
            .catch(error => {
                console.error('获取数据失败:', error);
                document.getElementById('contentTableBody').innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center py-4 text-danger">
                            <i class="fas fa-exclamation-circle fa-2x mb-3"></i>
                            <p>加载数据失败，请稍后再试</p>
                        </td>
                    </tr>
                `;
            });
    }
    
    // 渲染文案数据
    function renderContents(data) {
        const tbody = document.getElementById('contentTableBody');
        const pagination = document.getElementById('pagination');
        const totalCount = document.getElementById('totalCount');
        
        // 更新总记录数
        totalCount.textContent = data.total;
        
        // 清空表格
        tbody.innerHTML = '';
        
        // 添加数据行
        if (data.contents.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4">暂无符合条件的文案数据</td>
                </tr>
            `;
        } else {
            data.contents.forEach(function(content) {
                // 根据状态设置不同的标签样式
                let statusBadge = '';
                switch (content.status) {
                    case 'pending':
                        statusBadge = '<span class="badge bg-warning">待审核</span>';
                        break;
                    case 'approved':
                        statusBadge = '<span class="badge bg-success">已通过</span>';
                        break;
                    case 'rejected':
                        statusBadge = '<span class="badge bg-danger">已拒绝</span>';
                        break;
                    case 'published':
                        statusBadge = '<span class="badge bg-primary">已发布</span>';
                        break;
                    default:
                        statusBadge = '<span class="badge bg-secondary">未知</span>';
                }
                
                // 截取内容预览
                const contentPreview = content.content.length > 50 ? 
                    content.content.substring(0, 50) + '...' : content.content;
                
                // 添加表格行
                tbody.innerHTML += `
                    <tr>
                        <td>${content.id}</td>
                        <td>${content.title}</td>
                        <td>${contentPreview}</td>
                        <td>${statusBadge}</td>
                        <td>${content.created_at}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-info" onclick="showContentDetail(${content.id})">
                                    <i class="fas fa-eye"></i> 查看
                                </button>
                                ${content.status === 'pending' ? `
                                <button class="btn btn-outline-success" onclick="approveContentDirect(${content.id})">
                                    <i class="fas fa-check"></i> 通过
                                </button>
                                <button class="btn btn-outline-danger" onclick="showRejectModal(${content.id})">
                                    <i class="fas fa-times"></i> 拒绝
                                </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
            });
        }
        
        // 更新分页
        renderPagination(data.page, data.pages);
    }
    
    // 渲染分页控件
    function renderPagination(currentPage, totalPages) {
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = '';
        
        if (totalPages <= 1) {
            return;
        }
        
        // 上一页按钮
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
        prevLi.innerHTML = `
            <a class="page-link" href="javascript:void(0)" onclick="loadContents(${currentPage - 1})" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
            </a>
        `;
        pagination.appendChild(prevLi);
        
        // 页码按钮
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);
        
        if (endPage - startPage < 4) {
            startPage = Math.max(1, endPage - 4);
        }
        
        for (let i = startPage; i <= endPage; i++) {
            const pageLi = document.createElement('li');
            pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
            pageLi.innerHTML = `
                <a class="page-link" href="javascript:void(0)" onclick="loadContents(${i})">${i}</a>
            `;
            pagination.appendChild(pageLi);
        }
        
        // 下一页按钮
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
        nextLi.innerHTML = `
            <a class="page-link" href="javascript:void(0)" onclick="loadContents(${currentPage + 1})" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
            </a>
        `;
        pagination.appendChild(nextLi);
    }
    
    // 显示文案详情
    function showContentDetail(contentId) {
        currentContentId = contentId;
        const modal = new bootstrap.Modal(document.getElementById('contentDetailModal'));
        modal.show();
        
        // 显示加载中状态
        document.getElementById('contentDetailBody').innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载文案详情...</p>
            </div>
        `;
        
        // 发送AJAX请求获取文案详情
        fetch(`/api/client/content/${contentId}?share_token={{ share.access_token }}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应不正常');
                }
                return response.json();
            })
            .then(data => {
                // 渲染文案详情
                renderContentDetail(data);
            })
            .catch(error => {
                console.error('获取文案详情失败:', error);
                document.getElementById('contentDetailBody').innerHTML = `
                    <div class="text-center py-5 text-danger">
                        <i class="fas fa-exclamation-circle fa-3x mb-3"></i>
                        <p>加载文案详情失败，请稍后再试</p>
                    </div>
                `;
            });
    }
    
    // 渲染文案详情
    function renderContentDetail(content) {
        // 根据状态设置不同的标签样式
        let statusBadge = '';
        switch (content.status) {
            case 'pending':
                statusBadge = '<span class="badge bg-warning">待审核</span>';
                break;
            case 'approved':
                statusBadge = '<span class="badge bg-success">已通过</span>';
                break;
            case 'rejected':
                statusBadge = '<span class="badge bg-danger">已拒绝</span>';
                break;
            case 'published':
                statusBadge = '<span class="badge bg-primary">已发布</span>';
                break;
            default:
                statusBadge = '<span class="badge bg-secondary">未知</span>';
        }
        
        // 更新模态框标题
        document.getElementById('contentDetailModalLabel').textContent = `文案详情 - ${content.title}`;
        
        // 更新模态框内容
        document.getElementById('contentDetailBody').innerHTML = `
            <div class="mb-4">
                <div class="d-flex justify-content-between mb-3">
                    <h5 class="card-title">${content.title}</h5>
                    ${statusBadge}
                </div>
                <div class="text-muted mb-3">
                    <small>创建时间: ${content.created_at}</small>
                </div>
                <div class="card">
                    <div class="card-body">
                        <div class="content-text">
                            ${content.content.replace(/\n/g, '<br>')}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <h6 class="mb-3">附加信息</h6>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">话题标签</h6>
                            </div>
                            <div class="card-body">
                                ${content.topics ? content.topics.map(topic => 
                                    `<span class="badge bg-info me-1 mb-1">#${topic}</span>`
                                ).join('') : '<span class="text-muted">无话题标签</span>'}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">位置信息</h6>
                            </div>
                            <div class="card-body">
                                ${content.location ? content.location : '<span class="text-muted">无位置信息</span>'}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 根据状态控制审核按钮的显示
        const modalFooter = document.querySelector('#contentDetailModal .modal-footer');
        if (content.status === 'pending') {
            modalFooter.style.display = 'block';
        } else {
            modalFooter.style.display = 'none';
        }
    }
    
    // 显示拒绝原因模态框
    function showRejectModal(contentId) {
        currentContentId = contentId;
        document.getElementById('rejectReason').value = '';
        const modal = new bootstrap.Modal(document.getElementById('rejectReasonModal'));
        modal.show();
    }
    
    // 直接通过文案
    function approveContentDirect(contentId) {
        currentContentId = contentId;
        approveContent();
    }
    
    // 通过文案
    function approveContent() {
        if (!currentContentId) return;
        
        // 发送审核通过请求
        fetch(`/api/client/content/${currentContentId}/approve`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                share_token: '{{ share.access_token }}'
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应不正常');
            }
            return response.json();
        })
        .then(data => {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('contentDetailModal'));
            if (modal) modal.hide();
            
            // 显示成功提示
            showToast('文案审核通过成功', 'success');
            
            // 重新加载文案列表
            loadContents(1);
        })
        .catch(error => {
            console.error('审核操作失败:', error);
            showToast('审核操作失败，请稍后再试', 'danger');
        });
    }
    
    // 提交拒绝
    function submitReject() {
        if (!currentContentId) return;
        
        const reason = document.getElementById('rejectReason').value;
        if (!reason.trim()) {
            alert('请输入拒绝原因');
            return;
        }
        
        // 发送拒绝请求
        fetch(`/api/client/content/${currentContentId}/reject`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                reason: reason,
                share_token: '{{ share.access_token }}'
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应不正常');
            }
            return response.json();
        })
        .then(data => {
            // 关闭模态框
            const rejectModal = bootstrap.Modal.getInstance(document.getElementById('rejectReasonModal'));
            if (rejectModal) rejectModal.hide();
            
            const detailModal = bootstrap.Modal.getInstance(document.getElementById('contentDetailModal'));
            if (detailModal) detailModal.hide();
            
            // 显示成功提示
            showToast('文案已拒绝', 'success');
            
            // 重新加载文案列表
            loadContents(1);
        })
        .catch(error => {
            console.error('拒绝操作失败:', error);
            showToast('拒绝操作失败，请稍后再试', 'danger');
        });
    }
    
    // 显示提示信息
    function showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = 'position-fixed top-0 end-0 p-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check' : 'exclamation'}-circle me-2"></i> ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;
        document.body.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast.querySelector('.toast'), {
            autohide: true,
            delay: 3000
        });
        bsToast.show();
        
        setTimeout(function() {
            document.body.removeChild(toast);
        }, 3500);
    }
</script>
{% endblock %} 