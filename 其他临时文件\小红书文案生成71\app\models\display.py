"""
文案展示系统模型
"""
from datetime import datetime
import json
from . import db


class DisplaySchedule(db.Model):
    """展示计划模型"""
    __tablename__ = 'display_schedules'
    
    id = db.Column(db.Integer, primary_key=True)
    content_id = db.Column(db.Integer, db.<PERSON>ey('contents.id'), nullable=False)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
    display_date = db.Column(db.Date, nullable=False)  # 展示日期
    display_time = db.Column(db.Time, nullable=False)  # 展示时间
    is_fixed_time = db.Column(db.<PERSON>, default=False)  # 是否固定时间展示
    
    # 状态：scheduled已安排, displayed已展示, canceled已取消
    status = db.Column(db.String(20), default='scheduled')
    
    display_order = db.Column(db.Integer, default=0)  # 展示顺序
    actual_display_time = db.Column(db.DateTime)  # 实际展示时间
    
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # 关联
    content = db.relationship('Content', backref=db.backref('display_schedules', lazy='dynamic'))
    client = db.relationship('Client', backref=db.backref('display_schedules', lazy='dynamic'))
    creator = db.relationship('User', backref=db.backref('created_schedules', lazy='dynamic'))
    
    def __repr__(self):
        return f'<DisplaySchedule {self.id} for Content {self.content_id}>'


class DisplaySetting(db.Model):
    """展示设置模型"""
    __tablename__ = 'display_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False, unique=True)
    
    # 排序方式：priority按优先级, time按时间, random随机排序, custom自定义顺序
    order_type = db.Column(db.String(20), default='priority')
    
    # 自定义顺序列表（JSON格式）
    custom_order = db.Column(db.Text)
    
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # 关联
    client = db.relationship('Client', backref=db.backref('display_setting', uselist=False))
    updater = db.relationship('User', backref=db.backref('updated_display_settings', lazy='dynamic'))
    
    @property
    def custom_order_list(self):
        """获取自定义顺序列表"""
        if self.custom_order:
            try:
                return json.loads(self.custom_order)
            except:
                return []
        return []
    
    @custom_order_list.setter
    def custom_order_list(self, value):
        """设置自定义顺序列表"""
        if value:
            self.custom_order = json.dumps(value)
        else:
            self.custom_order = None
    
    def __repr__(self):
        return f'<DisplaySetting {self.id} for Client {self.client_id}>' 