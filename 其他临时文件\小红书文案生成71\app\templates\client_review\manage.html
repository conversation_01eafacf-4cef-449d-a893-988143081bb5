<!-- 客户审核管理页面 -->
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="bi bi-person-check"></i> 客户审核管理</h2>
            <p class="text-muted">管理客户审核链接和监控审核进度</p>
        </div>
        <div class="col-md-4 text-end">
            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i> 刷新页面
            </button>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-primary">{{ client_data|length }}</h4>
                    <small class="text-muted">总客户数</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-success">{{ client_data|selectattr('has_active_link')|list|length }}</h4>
                    <small class="text-muted">有效链接</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-warning">{{ client_data|map(attribute='pending_count')|sum }}</h4>
                    <small class="text-muted">待客户审核</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-info">{{ client_data|map(attribute='reviewed_count')|sum }}</h4>
                    <small class="text-muted">已审核完成</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 客户列表 -->
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-0">客户审核状态</h6>
        </div>
        <div class="card-body">
            {% if client_data %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>客户信息</th>
                                <th width="120">审核设置</th>
                                <th width="150">文案统计</th>
                                <th width="200">分享链接</th>
                                <th width="200">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in client_data %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div>
                                            <h6 class="mb-1">{{ item.client.name }}</h6>
                                            <small class="text-muted">
                                                <i class="bi bi-calendar"></i> 创建于 {{ item.client.created_at.strftime('%Y-%m-%d') if item.client.created_at else '未知' }}
                                            </small>
                                            {% if item.client.description %}
                                            <br><small class="text-muted">{{ item.client.description }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if item.client.auto_approved %}
                                        <span class="badge bg-info">自动审核</span>
                                    {% else %}
                                        <span class="badge bg-warning">需要审核</span>
                                    {% endif %}
                                    <br>
                                    <small class="text-muted">
                                        每日: {{ item.client.daily_content_count or 0 }} 篇
                                    </small>
                                </td>
                                <td>
                                    <div class="small">
                                        <div class="text-warning">
                                            <i class="bi bi-clock"></i> 待审核: {{ item.pending_count }}
                                        </div>
                                        <div class="text-success">
                                            <i class="bi bi-check"></i> 已审核: {{ item.reviewed_count }}
                                        </div>
                                        <div class="text-muted">
                                            <i class="bi bi-file-text"></i> 总计: {{ item.total_count }}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if item.active_links %}
                                        {% for link in item.active_links %}
                                        <div class="mb-2">
                                            {% if link.is_expired %}
                                                <span class="badge bg-danger">已过期</span>
                                            {% else %}
                                                <span class="badge bg-success">有效</span>
                                                {% if link.days_remaining is not none %}
                                                    <small class="text-muted">{{ link.days_remaining }}天</small>
                                                {% endif %}
                                            {% endif %}
                                            <br>
                                            <small class="text-muted font-monospace">{{ link.share_key[:16] }}...</small>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                        <span class="text-muted">无活跃链接</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group-vertical btn-group-sm" role="group">
                                        {% if item.active_links %}
                                            <button type="button" class="btn btn-outline-primary btn-sm" 
                                                    onclick="viewShareLink('{{ item.active_links[0].share_key }}')">
                                                <i class="bi bi-eye"></i> 查看链接
                                            </button>
                                            <button type="button" class="btn btn-outline-info btn-sm" 
                                                    onclick="copyShareLink('{{ item.active_links[0].share_url }}')">
                                                <i class="bi bi-copy"></i> 复制链接
                                            </button>
                                            <button type="button" class="btn btn-outline-warning btn-sm" 
                                                    onclick="refreshShareLink({{ item.client.id }})">
                                                <i class="bi bi-arrow-clockwise"></i> 刷新链接
                                            </button>
                                        {% else %}
                                            <button type="button" class="btn btn-outline-success btn-sm" 
                                                    onclick="createShareLink({{ item.client.id }})">
                                                <i class="bi bi-plus"></i> 创建链接
                                            </button>
                                        {% endif %}
                                        <button type="button" class="btn btn-outline-secondary btn-sm" 
                                                onclick="manageClient({{ item.client.id }})">
                                            <i class="bi bi-gear"></i> 管理
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-people fs-1 text-muted"></i>
                    <h5 class="text-muted mt-3">暂无客户</h5>
                    <p class="text-muted">请先添加客户信息</p>
                    <button type="button" class="btn btn-outline-primary" onclick="showPage('clients')">
                        <i class="bi bi-arrow-left"></i> 去客户管理
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 分享链接详情模态框 -->
<div class="modal fade" id="shareLinkModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">分享链接详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="shareLinkModalBody">
                <!-- 内容将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>
</div>

<!-- 创建分享链接模态框 -->
<div class="modal fade" id="createLinkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建分享链接</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createLinkForm">
                    <div class="mb-3">
                        <label for="linkExpiresDays" class="form-label">有效期（天）</label>
                        <input type="number" class="form-control" id="linkExpiresDays" value="30" min="1" max="365">
                        <div class="form-text">链接的有效期，默认30天</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitCreateLink()">创建链接</button>
            </div>
        </div>
    </div>
</div>

<!-- 客户管理模态框 -->
<div class="modal fade" id="clientManageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">客户管理</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="clientManageForm">
                    <div class="mb-3">
                        <label class="form-label">审核设置</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="clientAutoApproved">
                            <label class="form-check-label" for="clientAutoApproved">
                                自动审核通过（跳过客户审核环节）
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="clientDailyCount" class="form-label">每日文案数量</label>
                        <input type="number" class="form-control" id="clientDailyCount" min="0" max="100">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitClientManage()">保存设置</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentClientId = null;

// 查看分享链接详情
function viewShareLink(shareKey) {
    fetch(`/simple/api/share-links/${shareKey}/stats`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stats = data.stats;
                const modalBody = document.getElementById('shareLinkModalBody');
                modalBody.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>链接信息</h6>
                            <p><strong>分享密钥:</strong><br><code>${shareKey}</code></p>
                            <p><strong>状态:</strong> ${stats.is_expired ? '<span class="badge bg-danger">已过期</span>' : '<span class="badge bg-success">有效</span>'}</p>
                            ${stats.days_remaining !== null ? `<p><strong>剩余天数:</strong> ${stats.days_remaining} 天</p>` : ''}
                        </div>
                        <div class="col-md-6">
                            <h6>使用统计</h6>
                            <p><strong>待审核文案:</strong> ${stats.pending_count} 篇</p>
                            <p><strong>已审核文案:</strong> ${stats.reviewed_count} 篇</p>
                            <p><strong>总文案数:</strong> ${stats.total_count} 篇</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <h6>分享链接</h6>
                        <div class="input-group">
                            <input type="text" class="form-control" value="${window.location.origin}/client-review/${shareKey}" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('${window.location.origin}/client-review/${shareKey}')">
                                <i class="bi bi-copy"></i> 复制
                            </button>
                        </div>
                    </div>
                `;
                
                const modal = new bootstrap.Modal(document.getElementById('shareLinkModal'));
                modal.show();
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('获取链接详情失败: ' + error.message, 'error');
        });
}

// 复制分享链接
function copyShareLink(shareUrl) {
    copyToClipboard(shareUrl);
}

// 复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showToast('链接已复制到剪贴板', 'success');
    }, function(err) {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast('链接已复制到剪贴板', 'success');
    });
}

// 创建分享链接
function createShareLink(clientId) {
    currentClientId = clientId;
    const modal = new bootstrap.Modal(document.getElementById('createLinkModal'));
    modal.show();
}

// 提交创建链接
function submitCreateLink() {
    const expiresDays = document.getElementById('linkExpiresDays').value;
    
    const formData = new FormData();
    if (expiresDays) {
        formData.append('expires_days', expiresDays);
    }
    
    fetch(`/simple/api/clients/${currentClientId}/share-links`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('createLinkModal')).hide();
            location.reload();
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('创建链接失败: ' + error.message, 'error');
    });
}

// 刷新分享链接
function refreshShareLink(clientId) {
    if (!confirm('确定要刷新分享链接吗？旧链接将失效。')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('expires_days', '30'); // 默认30天
    
    fetch(`/simple/api/clients/${clientId}/share-links/refresh`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            location.reload();
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('刷新链接失败: ' + error.message, 'error');
    });
}

// 管理客户
function manageClient(clientId) {
    currentClientId = clientId;
    
    // 获取客户信息并填充表单
    // 这里可以添加获取客户详细信息的API调用
    
    const modal = new bootstrap.Modal(document.getElementById('clientManageModal'));
    modal.show();
}

// 提交客户管理
function submitClientManage() {
    const autoApproved = document.getElementById('clientAutoApproved').checked;
    const dailyCount = document.getElementById('clientDailyCount').value;
    
    // 这里可以添加更新客户信息的API调用
    showToast('客户设置更新功能待实现', 'info');
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 这里可以使用Bootstrap的Toast组件或其他提示组件
    alert(message);
}

console.log('客户审核管理页面已加载');
</script>
