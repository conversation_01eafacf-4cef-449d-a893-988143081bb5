{% extends "base.html" %}

{% block title %}控制面板 - 小红书文案生成系统{% endblock %}

{% block content_auth %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">欢迎使用小红书文案生成系统</h5>
            </div>
            <div class="card-body">
                <p>您好，{{ current_user.real_name or current_user.username }}！</p>
            </div>
        </div>
    </div>
</div>

<div class="row row-cols-1 row-cols-md-2 row-cols-xl-4 g-4 mb-4">
    {% if current_user.has_permission('admin_access') %}
    <div class="col">
        <div class="card h-100 shadow">
            <div class="card-body">
                <h5 class="card-title"><i class="bi bi-gear-fill me-2"></i>管理后台</h5>
                <p class="card-text">访问系统管理后台，管理用户、角色、权限等。</p>
                <a href="/admin/" class="btn btn-primary">进入</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if current_user.has_permission('admin_access') %}
    <div class="col">
        <div class="card h-100 shadow">
            <div class="card-body">
                <h5 class="card-title"><i class="bi bi-bar-chart-fill me-2"></i>数据统计</h5>
                <p class="card-text">查看系统数据统计，包括文案、任务、用户等统计信息。</p>
                <a href="{{ url_for('stats.index') }}" class="btn btn-primary" data-ajax-link>进入</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if current_user.has_permission('admin_access') %}
    <div class="col">
        <div class="card h-100 shadow">
            <div class="card-body">
                <h5 class="card-title"><i class="bi bi-file-earmark-arrow-up-fill me-2"></i>导入导出</h5>
                <p class="card-text">导入导出系统数据，支持模板、话题、客户和文案数据的导入导出。</p>
                <a href="{{ url_for('export.index') }}" class="btn btn-primary" data-ajax-link>进入</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if current_user.has_permission('template_manage') %}
    <div class="col">
        <div class="card h-100 shadow">
            <div class="card-body">
                <h5 class="card-title"><i class="bi bi-file-earmark-text me-2"></i>模板管理</h5>
                <p class="card-text">管理文案模板和标记，创建新模板。</p>
                <a href="{{ url_for('template.index') }}" class="btn btn-primary">进入</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if current_user.has_permission('topic_manage') %}
    <div class="col">
        <div class="card h-100 shadow">
            <div class="card-body">
                <h5 class="card-title"><i class="bi bi-hash me-2"></i>话题管理</h5>
                <p class="card-text">管理话题和话题关联，设置话题优先级。</p>
                <a href="{{ url_for('topic.index') }}" class="btn btn-primary">进入</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if current_user.has_permission('client_manage') %}
    <div class="col">
        <div class="card h-100 shadow">
            <div class="card-body">
                <h5 class="card-title"><i class="bi bi-people me-2"></i>客户管理</h5>
                <p class="card-text">管理客户信息和权限。</p>
                <a href="{{ url_for('client.client_list') }}" class="btn btn-primary">进入</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if current_user.has_permission('task_manage') %}
    <div class="col">
        <div class="card h-100 shadow">
            <div class="card-body">
                <h5 class="card-title"><i class="bi bi-list-task me-2"></i>任务管理</h5>
                <p class="card-text">创建和管理文案生成任务。</p>
                <a href="{{ url_for('task.task_list') }}" class="btn btn-primary">进入</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if current_user.has_permission('content_review') %}
    <div class="col">
        <div class="card h-100 shadow">
            <div class="card-body">
                <h5 class="card-title"><i class="bi bi-check-circle me-2"></i>文案审核</h5>
                <p class="card-text">审核待处理的文案。</p>
                <a href="{{ url_for('content.content_list') }}" class="btn btn-primary">进入</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if current_user.has_permission('content_publish') %}
    <div class="col">
        <div class="card h-100 shadow">
            <div class="card-body">
                <h5 class="card-title"><i class="bi bi-send me-2"></i>文案发布</h5>
                <p class="card-text">管理文案发布优先级。</p>
                <a href="{{ url_for('content.content_list') }}" class="btn btn-primary">进入</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if current_user.has_permission('admin_access') %}
    <div class="col">
        <div class="card h-100 shadow">
            <div class="card-body">
                <h5 class="card-title"><i class="bi bi-chat-dots me-2"></i>理由管理</h5>
                <p class="card-text">管理拒绝理由和快捷理由。</p>
                <a href="{{ url_for('reason.reason_list') }}" class="btn btn-primary" data-ajax-link>进入</a>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">待处理任务</h5>
            </div>
            <div class="card-body">
                <p>这里将显示您需要处理的任务。</p>
                <!-- 待处理任务列表将在后续开发中添加 -->
                <p class="text-muted">暂无待处理任务</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">系统公告</h5>
            </div>
            <div class="card-body">
                <p>欢迎使用小红书文案生成系统！</p>
                <p>当前系统版本：1.0.0</p>
                <!-- 系统公告将在后续开发中添加 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
{% endblock %} 