{% extends "base.html" %}

{% block title %}创建快捷理由{% endblock %}

{% block content_auth %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">创建快捷理由</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('reason.reason_create') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        
                        <div class="mb-3">
                            <label for="content" class="form-label">理由内容</label>
                            <textarea class="form-control" id="content" name="content" rows="3" required></textarea>
                            <div class="form-text">输入拒绝理由内容，将作为快捷选项显示在审核界面</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="sort_order" class="form-label">排序顺序</label>
                            <input type="number" class="form-control" id="sort_order" name="sort_order" value="0" min="0">
                            <div class="form-text">数字越小排序越靠前</div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('reason.reason_list') }}" class="btn btn-secondary">返回列表</a>
                            <button type="submit" class="btn btn-primary">创建</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 