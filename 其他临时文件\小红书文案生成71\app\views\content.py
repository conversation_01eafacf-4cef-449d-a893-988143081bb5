"""
文案内容管理视图
"""
import json
import random
import re
from datetime import datetime
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc, and_, or_

from app.models import db
from app.models.content import Content, ContentHistory, RejectionReason, QuickReason
from app.models.client import Client
from app.models.task import Task, Batch
from app.models.template import Template, TemplateCategory
from app.models.topic import Topic
from app.forms.content import ContentForm, ContentReviewForm, ContentFilterForm, ContentBatchActionForm, GenerateContentForm
from app.utils.decorators import permission_required, ajax_aware
from app.utils.uploads import upload_manager

# 创建蓝图
content_bp = Blueprint('content', __name__)


@content_bp.route('/')
@login_required
@permission_required('content_view')
@ajax_aware
def content_list():
    """初审文案列表"""
    # 添加调试输出
    print(f"DEBUG - User accessing content_list: {current_user.username}")
    print(f"DEBUG - User has content_view permission: {current_user.has_permission('content_view')}")
    print(f"DEBUG - User roles: {[role.name for role in current_user.roles]}")

    form = ContentFilterForm()

    # 初始化表单选择项
    form.client_id.choices = [(0, '全部客户')] + [(c.id, c.name) for c in Client.query.filter_by(status=True).all()]
    form.task_id.choices = [(0, '全部任务')] + [(t.id, t.name) for t in Task.query.all()]

    # 修改状态选择项，专注于初审相关状态
    form.status.choices = [
        ('', '全部状态'),
        ('draft', '草稿'),
        ('pending_review', '待初审'),
        ('first_reviewed', '初审通过')
    ]
    
    # 获取筛选参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    client_id = request.args.get('client_id', 0, type=int)
    task_id = request.args.get('task_id', 0, type=int)
    status = request.args.get('status', '')
    date_from = request.args.get('date_from') or ''
    date_to = request.args.get('date_to') or ''
    search = request.args.get('search', '')
    sort_by = request.args.get('sort_by', 'updated_at')
    sort_order = request.args.get('sort_order', 'desc')
    
    # 构建查询
    query = Content.query
    
    # 过滤已删除的文案
    query = query.filter(Content.is_deleted == False)
    
    # 应用筛选条件
    if client_id > 0:
        query = query.filter(Content.client_id == client_id)
    
    if task_id > 0:
        query = query.filter(Content.task_id == task_id)
    
    if status:
        query = query.filter(Content.workflow_status == status)
    else:
        # 默认只显示初审相关的状态
        query = query.filter(Content.workflow_status.in_(['draft', 'pending_review']))
    
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Content.created_at >= date_from)
        except:
            pass
    
    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(Content.created_at <= date_to)
        except:
            pass
    
    if search:
        query = query.filter(
            or_(
                Content.title.like(f'%{search}%'),
                Content.content.like(f'%{search}%')
            )
        )
    
    # 应用排序
    if sort_order == 'desc':
        query = query.order_by(desc(getattr(Content, sort_by)))
    else:
        query = query.order_by(getattr(Content, sort_by))
    
    # 分页
    pagination = query.paginate(page=page, per_page=per_page)
    contents = pagination.items
    
    # 批量操作表单
    batch_form = ContentBatchActionForm()
    
    context = {
        'title': '初审文案 - 小红书文案生成系统',
        'contents': contents,
        'pagination': pagination,
        'form': form,
        'batch_form': batch_form,
        'client_id': client_id,
        'task_id': task_id,
        'status': status,
        'date_from': date_from,
        'date_to': date_to,
        'search': search,
        'sort_by': sort_by,
        'sort_order': sort_order
    }
    
    return render_template('content/list.html', **context)





@content_bp.route('/create', methods=['GET', 'POST'])
@login_required
@permission_required('content_create')
def content_create():
    """创建文案"""
    form = ContentForm()
    
    # 初始化表单选择项
    form.client_id.choices = [(c.id, c.name) for c in Client.query.filter_by(status=True).all()]
    form.task_id.choices = [(t.id, t.name) for t in Task.query.all()]
    form.template_id.choices = [(0, '不使用模板')] + [(t.id, t.title) for t in Template.query.filter_by(status=True).all()]
    
    if form.validate_on_submit():
        # 创建新文案
        content = Content(
            client_id=form.client_id.data,
            task_id=form.task_id.data,
            batch_id=1,  # 默认批次，实际应该根据任务获取或创建批次
            template_id=form.template_id.data if form.template_id.data > 0 else None,
            title=form.title.data,
            content=form.content.data,
            location=form.location.data,
            display_date=form.display_date.data,
            display_time=form.display_time.data,
            publish_priority=form.publish_priority.data,
            workflow_status='draft',
            created_by=current_user.id
        )
        
        # 处理话题
        if form.topics.data:
            topics = [t.strip() for t in form.topics.data.split(',')]
            content.topics_list = topics
        
        # 保存到数据库
        db.session.add(content)
        db.session.commit()
        
        # 处理图片上传
        if form.images.data and any(form.images.data):
            image_urls = []
            for image in form.images.data:
                if image.filename:
                    try:
                        relative_path = upload_manager.save_file(image, 'content')
                        url = url_for('static', filename=relative_path, _external=True)
                        image_urls.append(url)
                    except Exception as e:
                        current_app.logger.error(f"上传图片失败: {str(e)}")
            
            if image_urls:
                content.image_urls_list = image_urls
                content.workflow_status = 'image_uploaded'
                db.session.commit()
        
        flash('文案创建成功', 'success')
        return redirect(url_for('content.content_list'))
    
    return render_template('content/create.html', form=form)


@content_bp.route('/get-template-marks/<int:category_id>')
@login_required
@ajax_aware
def get_template_marks(category_id):
    """获取模板分类下的所有标记"""
    try:
        # 记录请求信息
        current_app.logger.info(f"获取模板分类标记: category_id={category_id}")
        
        # 查询该分类下的所有模板
        templates = Template.query.filter_by(category_id=category_id, status=True).all()
        
        if not templates:
            current_app.logger.warning(f"模板分类 {category_id} 下没有可用模板")
            return jsonify({
                'success': False,
                'message': '该分类下没有可用模板'
            })
        
        # 提取所有模板中的标记
        all_marks = set()
        # 定义固定标记，这些不需要在标记替换设置中显示
        fixed_marks = {'话题', '定位', '@用户', 'at_users', 'location', 'topics'}

        for template in templates:
            try:
                # 合并标题和内容中的标记
                content = str(template.title or '') + ' ' + str(template.content or '')
                marks = re.findall(r'\{([^}]+)\}', content)
                # 过滤掉固定标记
                filtered_marks = [mark for mark in marks if mark not in fixed_marks]
                all_marks.update(filtered_marks)
            except Exception as e:
                current_app.logger.error(f"处理模板 {template.id} 时出错: {str(e)}")
                continue

        # 按字母顺序排序
        sorted_marks = sorted(list(all_marks))
        
        current_app.logger.info(f"成功获取模板分类 {category_id} 的标记: {len(sorted_marks)} 个标记, {len(templates)} 个模板")
        
        # 提交事务，避免长时间保持连接
        db.session.commit()
        
        return jsonify({
            'success': True,
            'marks': sorted_marks,
            'template_count': len(templates)
        })
    except Exception as e:
        # 回滚事务
        db.session.rollback()
        error_msg = f"获取模板标记时出错: {str(e)}"
        current_app.logger.error(error_msg)
        return jsonify({
            'success': False,
            'message': '加载标记失败，请重试',
            'error': str(e)
        }), 500


@content_bp.route('/generate', methods=['GET', 'POST'])
@login_required
@permission_required('content_create')
@ajax_aware
def content_generate():
    """批量生成文案"""
    # 添加请求信息日志
    print(f"DEBUG - content_generate: method={request.method}, args={request.args}")
    
    form = GenerateContentForm()
    
    # 初始化客户选择项
    form.client_id.choices = [(c.id, c.name) for c in Client.query.filter_by(status=True).all()]
    
    # 初始化模板分类选择项
    form.template_category_id.choices = [(c.id, c.name) for c in TemplateCategory.query.all()]
    
    # 根据选择的客户加载任务
    if request.method == 'GET':
        # 获取客户ID，如果没有提供，则使用第一个客户
        client_id = request.args.get('client_id')
        if not client_id and form.client_id.choices:
            client_id = form.client_id.choices[0][0]
            print(f"DEBUG - GET请求: 未提供客户ID，使用第一个客户: {client_id}")
            
        if client_id:
            client_id = int(client_id)
            tasks = Task.query.filter_by(client_id=client_id).all()
            form.task_id.choices = [(0, '-- 创建新任务 --')] + [(t.id, t.name) for t in tasks]
            
            # 设置默认任务名称为"年年年年年月月日日任务"格式
            now = datetime.now()
            default_task_name = f'{now.year}年{now.month:02d}月{now.day:02d}日任务'
            form.new_task_name.data = default_task_name
            
            # 检查默认任务名称是否已存在
            existing_task = Task.query.filter_by(
                client_id=client_id,
                name=default_task_name
            ).first()
            
            if existing_task:
                print(f"DEBUG - GET请求: 发现同名任务: id={existing_task.id}, name={existing_task.name}")
                # 如果存在同名任务，获取该任务下的批次数量
                batch_count = Batch.query.filter_by(task_id=existing_task.id).count()
                form.batch_name.data = f'批次 {batch_count + 1}'
                print(f"DEBUG - GET请求: 设置批次名称为: {form.batch_name.data}")
            else:
                # 新任务的第一个批次
                form.batch_name.data = '批次 1'
                print("DEBUG - GET请求: 新任务，设置批次名称为: 批次 1")
        else:
            form.task_id.choices = [(0, '-- 创建新任务 --')]
            # 设置默认任务名称为"年年年年年月月日日任务"格式
            now = datetime.now()
            form.new_task_name.data = f'{now.year}年{now.month:02d}月{now.day:02d}日任务'
            form.batch_name.data = '批次 1'
    
    # 如果是POST请求，在验证前自动设置批次名称
    if request.method == 'POST':
        # 添加调试信息：记录表单提交的数据
        print(f"DEBUG - 接收到表单提交: {request.form}")
        
        # 检查前端表单验证状态
        form_validated = request.form.get('form_validated', '0')
        print(f"DEBUG - 表单验证状态: form_validated={form_validated}")
        
        if form_validated == '0':
            # 如果前端验证失败，直接返回当前页面，不进行后续处理
            print("DEBUG - 前端验证失败，返回表单页面")
            context = {
                'title': '批量生成文案 - 小红书文案生成系统',
                'form': form
            }
            return render_template('content/generate.html', **context)
        
        # 特殊处理：如果task_id为0（创建新任务），确保new_task_name有值
        if 'task_id' in request.form and request.form['task_id'] == '0':
            print("DEBUG - 检测到创建新任务选项")
            
            # 获取或生成任务名称
            new_task_name = request.form.get('new_task_name')
            if not new_task_name:
                print("DEBUG - 创建新任务但未提供任务名称")
                # 设置默认任务名称为"年年年年年月月日日任务"格式
                now = datetime.now()
                new_task_name = f'{now.year}年{now.month:02d}月{now.day:02d}日任务'
                form.new_task_name.data = new_task_name
                print(f"DEBUG - 自动生成任务名称: {new_task_name}")
            else:
                form.new_task_name.data = new_task_name
            
            # 检查是否已存在同名任务
            client_id = request.form.get('client_id')
            if client_id:
                existing_task = Task.query.filter_by(
                    client_id=int(client_id),
                    name=new_task_name
                ).first()
                
                if existing_task:
                    print(f"DEBUG - 发现同名任务: id={existing_task.id}, name={existing_task.name}")
                    # 如果存在同名任务，获取该任务下的批次数量
                    batch_count = Batch.query.filter_by(task_id=existing_task.id).count()
                    form.batch_name.data = f'批次 {batch_count + 1}'
                    print(f"DEBUG - 设置批次名称为: {form.batch_name.data}")
                else:
                    # 新任务的第一个批次
                    form.batch_name.data = '批次 1'
                    print("DEBUG - 新任务，设置批次名称为: 批次 1")
        
        # 确保提交的task_id在choices列表中
        task_id = request.form.get('task_id')
        if task_id and int(task_id) > 0:
            # 获取任务信息，确保它存在
            task = Task.query.get(int(task_id))
            if task:
                # 更新choices列表，确保包含当前选择的任务
                client_id = request.form.get('client_id')
                if client_id:
                    tasks = Task.query.filter_by(client_id=int(client_id)).all()
                    form.task_id.choices = [(0, '-- 创建新任务 --')] + [(t.id, t.name) for t in tasks]
                    # 如果当前task_id不在列表中，添加它
                    if not any(t[0] == int(task_id) for t in form.task_id.choices):
                        form.task_id.choices.append((task.id, task.name))
                
                # 设置批次名称
                batch_count = Batch.query.filter_by(task_id=int(task_id)).count()
                form.batch_name.data = f'批次 {batch_count + 1}'
                print(f"DEBUG - 现有任务，设置批次名称为: {form.batch_name.data}")
        
        # 确保所有选择字段都有有效的选项
        if not form.client_id.choices:
            form.client_id.choices = [(c.id, c.name) for c in Client.query.filter_by(status=True).all()]
            print("DEBUG - 重新初始化客户选择项")
        
        if not form.template_category_id.choices:
            form.template_category_id.choices = [(c.id, c.name) for c in TemplateCategory.query.all()]
            print("DEBUG - 重新初始化模板分类选择项")
        
        # 确保task_id字段有有效的选项
        if not form.task_id.choices:
            client_id = request.form.get('client_id')
            if client_id:
                tasks = Task.query.filter_by(client_id=int(client_id)).all()
                form.task_id.choices = [(0, '-- 创建新任务 --')] + [(t.id, t.name) for t in tasks]
                print("DEBUG - 重新初始化任务选择项")
            else:
                form.task_id.choices = [(0, '-- 创建新任务 --')]
                print("DEBUG - 设置默认任务选择项")
        
        # 确保publish_priority字段有有效的选项
        if not form.publish_priority.choices:
            form.publish_priority.choices = [
                ('high', '高优先级'),
                ('normal', '普通优先级'),
                ('low', '低优先级')
            ]
            print("DEBUG - 重新初始化发布优先级选择项")
    
    if form.validate_on_submit():
        print("DEBUG - 表单验证通过，开始处理数据")
        try:
            # 获取表单数据
            client_id = form.client_id.data
            template_category_id = form.template_category_id.data
            count = form.count.data
            keywords_data = form.keywords.data
            required_topics_data = form.required_topics.data
            random_topics_data = form.random_topics.data
            max_topics_count = form.max_topics_count.data
            random_topics_count = form.random_topics_count.data
            location_data = form.location.data
            at_users_data = form.at_users.data
            random_at_users_count = form.random_at_users_count.data
            publish_priority = form.publish_priority.data
            avoid_duplicates = form.avoid_duplicates.data
            allow_template_duplicate = form.allow_template_duplicate.data

            # 处理定位数据 - 随机选择1个
            selected_location = None
            if location_data:
                # 处理多种分隔符：换行符、空格
                locations = []
                if '\n' in location_data:
                    # 按行分割（标签输入方式）
                    locations = [loc.strip() for loc in location_data.replace('\r\n', '\n').split('\n') if loc.strip()]
                elif ' ' in location_data:
                    # 按空格分割（前端可能传递的格式）
                    locations = [loc.strip() for loc in location_data.split(' ') if loc.strip()]
                else:
                    # 单个定位
                    locations = [location_data.strip()] if location_data.strip() else []

                if locations:
                    # 随机选择1个定位
                    selected_location = random.choice(locations)
                print(f"DEBUG - 原始定位数据: {repr(location_data)}")
                print(f"DEBUG - 分割后定位数据: {locations}")
                print(f"DEBUG - 随机选择的定位: {selected_location}")
            
            print(f"DEBUG - 表单数据: client_id={client_id}, template_category_id={template_category_id}, count={count}")
            print(f"DEBUG - 关键词数据: {keywords_data}")
            
            # 处理任务
            task_id = form.task_id.data
            print(f"DEBUG - 任务ID: {task_id}, 类型: {type(task_id)}")
            
            if task_id == 0:
                # 创建新任务
                task_name = form.new_task_name.data
                print(f"DEBUG - 创建新任务: {task_name}")
                
                # 检查是否已存在同名任务
                existing_task = Task.query.filter_by(
                    client_id=client_id,
                    name=task_name
                ).first()
                
                if existing_task:
                    print(f"DEBUG - 使用已存在的同名任务: id={existing_task.id}, name={existing_task.name}")
                    task_id = existing_task.id
                    task = existing_task  # 设置task变量
                else:
                    # 创建全新任务
                    task = Task(
                        client_id=client_id,
                        name=task_name,
                        description=f'自动创建于 {datetime.now().strftime("%Y-%m-%d %H:%M")}',
                        status='in_progress',
                        target_count=count,
                        created_by=current_user.id
                    )
                    db.session.add(task)
                    db.session.flush()  # 获取新任务ID
                    task_id = task.id
                    print(f"DEBUG - 创建新任务成功: id={task_id}, name={task_name}")
            else:
                # 使用现有任务
                task = Task.query.get_or_404(task_id)
                print(f"DEBUG - 使用现有任务: id={task_id}, name={task.name}")
            
            # 创建新批次
            batch_count = Batch.query.filter_by(task_id=task_id).count()
            batch_name = f'批次 {batch_count + 1}'
            batch = Batch(
                task_id=task_id,
                name=batch_name,
                content_count=0,  # 稍后更新
                created_by=current_user.id
            )
            db.session.add(batch)
            db.session.flush()  # 获取新批次ID
            batch_id = batch.id
            print(f"DEBUG - 创建新批次: id={batch_id}, name={batch_name}")
            
            # 导入模板过滤函数
            from app.utils.content_generator import get_available_templates

            # 获取可用模板（根据重复控制设置过滤）
            available_templates = get_available_templates(
                template_category_id, task_id, batch_id,
                int(allow_template_duplicate)
            )
            print(f"DEBUG - 找到可用模板数量: {len(available_templates)}")

            # 获取该分类下的所有模板总数（用于提示）
            all_templates = Template.query.filter_by(category_id=template_category_id, status=True).all()
            print(f"DEBUG - 分类下模板总数: {len(all_templates)}")

            if not available_templates:
                flash('错误：没有可用的模板。所有模板都已被使用，请选择"允许重复使用模板"或选择其他模板分类', 'error')
                print(f"DEBUG - 没有可用模板")
                db.session.rollback()
                return redirect(url_for('content.generate'))

            # 如果不允许重复且可用模板数量小于请求数量，调整数量
            if int(allow_template_duplicate) == 0 and len(available_templates) < count:
                flash(f'警告：可用模板数量({len(available_templates)})小于请求数量({count})，调整生成数量为{len(available_templates)}', 'warning')
                print(f"DEBUG - 模板数量不足，调整数量: 可用模板数={len(available_templates)}, 原需求数={count}")
                count = len(available_templates)

            # 随机选择模板
            selected_templates = random.sample(available_templates, min(count, len(available_templates)))

            # 如果允许重复且需要更多模板
            if int(allow_template_duplicate) > 0 and len(selected_templates) < count:
                # 重复选择模板直到达到请求数量
                additional_count = count - len(selected_templates)
                additional_templates = []

                for _ in range(additional_count):
                    additional_templates.append(random.choice(available_templates))

                selected_templates.extend(additional_templates)
                print(f"DEBUG - 允许重复，补充模板: 原有{len(selected_templates)-len(additional_templates)}, 补充{len(additional_templates)}")
            
            print(f"DEBUG - 选择的模板数量: {len(selected_templates)}")
            
            # 解析关键词设置
            keywords_dict = {}
            for line in keywords_data.strip().split('\n'):
                if ':' in line:
                    mark, keywords_str = line.split(':', 1)
                    mark = mark.strip()
                    # 支持标签式输入，每个关键词单独一行
                    if ',' in keywords_str:
                        keywords = [k.strip() for k in keywords_str.split(',') if k.strip()]
                    else:
                        keywords = [keywords_str.strip()]
                    if keywords:
                        keywords_dict[mark] = keywords
            
            print(f"DEBUG - 解析的关键词: {keywords_dict}")
            
            # 解析话题
            required_topics = []
            if required_topics_data:
                required_topics = [t.strip() for t in required_topics_data.split('\n') if t.strip()]
            
            random_topic_pool = []
            if random_topics_data:
                random_topic_pool = [t.strip() for t in random_topics_data.split('\n') if t.strip()]
            
            # 解析@用户
            at_users = []
            if at_users_data:
                at_users = [u.strip() for u in at_users_data.split('\n') if u.strip()]
            
            print(f"DEBUG - 话题: 必选={required_topics}, 随机池={random_topic_pool}")
            print(f"DEBUG - @用户: {at_users}")

            # 预处理话题组合（在模板循环外部处理一次）
            final_topics = required_topics.copy()
            print(f"DEBUG - 必选话题: {required_topics}")
            print(f"DEBUG - 随机话题池: {random_topic_pool}")
            print(f"DEBUG - 最大话题数量: {max_topics_count}")
            print(f"DEBUG - 随机话题数量设置: {random_topics_count}")

            if random_topic_pool and max_topics_count > len(final_topics):
                # 计算可以添加的随机话题数量 = 最大话题数量 - 已选必选话题数量
                available_random_count = max_topics_count - len(final_topics)
                # 确保随机话题数量不超过可用话题池大小和可添加数量
                actual_random_count = min(available_random_count, len(random_topic_pool))
                print(f"DEBUG - 可添加随机话题数量: {available_random_count}")
                print(f"DEBUG - 实际随机话题数量: {actual_random_count}")
                if actual_random_count > 0:
                    random_selected = random.sample(random_topic_pool, actual_random_count)
                    final_topics.extend(random_selected)
                    print(f"DEBUG - 选择的随机话题: {random_selected}")

            print(f"DEBUG - 最终话题列表: {final_topics}")

            # 生成文案
            generated_count = 0
            duplicates_count = 0
            current_batch_contents = []  # 记录当前批次已生成的内容，避免批次内重复

            for template in selected_templates:
                if generated_count >= count:
                    break
                    
                # 替换模板中的标记
                title = template.title
                content = template.content
                
                # 查找所有标记 {标记名}
                marks = re.findall(r'\{([^}]+)\}', content)
                title_marks = re.findall(r'\{([^}]+)\}', title)
                
                # 合并所有标记
                all_marks = set(marks + title_marks)
                
                print(f"DEBUG - 处理模板: id={template.id}, 标记={all_marks}")
                
                # 替换每个标记
                replacements = {}
                replacement_info = {}  # 保存替换信息用于显示

                for mark in all_marks:
                    if mark in keywords_dict:
                        # 随机选择一个关键词
                        replacement = random.choice(keywords_dict[mark])
                        replacements[mark] = replacement
                        replacement_info[mark] = replacement

                        # 替换内容中的标记
                        pattern = r'\{' + re.escape(mark) + r'\}'
                        content = re.sub(pattern, replacement, content)
                        title = re.sub(pattern, replacement, title)

                # 如果没有关键词替换，为了避免完全相同的内容，可以在内容末尾添加微小变化
                if not replacements and avoid_duplicates:
                    # 添加一个不可见的标识符来区分相同模板生成的内容
                    content += f"\u200b"  # 零宽度空格，不影响显示但能区分内容
                
                print(f"DEBUG - 替换后标题: {title}")
                
                # 检查是否有重复内容
                if avoid_duplicates:
                    # 检查数据库中是否有重复（只检查未删除的文案）
                    existing_content = Content.query.filter(
                        Content.title == title,
                        Content.content == content,
                        Content.is_deleted == 0  # 只检查未删除的文案
                    ).first()

                    # 检查当前批次中是否有重复
                    batch_duplicate = any(
                        item['title'] == title and item['content'] == content
                        for item in current_batch_contents
                    )

                    if existing_content or batch_duplicate:
                        duplicates_count += 1
                        print(f"DEBUG - 发现重复内容，跳过: {title}")
                        continue
                
                # 话题已在外部处理，直接使用final_topics
                
                # 创建新文案
                new_content = Content(
                    client_id=client_id,
                    task_id=task_id,
                    batch_id=batch_id,
                    template_id=template.id,
                    title=title,
                    content=content,
                    location=selected_location,  # 直接设置随机选择的定位
                    publish_priority=publish_priority,
                    workflow_status='draft',
                    created_by=current_user.id
                )

                print(f"DEBUG - 设置定位: {selected_location}")
                
                # 设置话题
                if final_topics:
                    new_content.topics_list = final_topics
                
                # 设置扩展数据（@用户和标记替换信息）
                ext_data = new_content.ext_data or {}

                # 保存标记替换信息
                if replacement_info:
                    ext_data['mark_replacements'] = replacement_info
                    print(f"DEBUG - 保存标记替换信息: {replacement_info}")

                # 设置@用户
                if at_users:
                    # 根据设置的数量随机选择@用户
                    actual_at_users_count = min(random_at_users_count, len(at_users))
                    if actual_at_users_count > 0:
                        selected_at_users = random.sample(at_users, actual_at_users_count)
                        ext_data['at_users'] = selected_at_users
                        print(f"DEBUG - 设置@用户: 从{len(at_users)}个中选择{actual_at_users_count}个: {selected_at_users}")
                    else:
                        print(f"DEBUG - @用户数量设置为0，不存储@用户")

                # 保存扩展数据
                if ext_data:
                    new_content.ext_data = ext_data
                
                # 记录到当前批次内容中
                current_batch_contents.append({
                    'title': title,
                    'content': content
                })

                # 保存到数据库
                db.session.add(new_content)
                generated_count += 1
                print(f"DEBUG - 添加新文案: {title}")
            
            # 提交事务
            db.session.commit()
            print(f"DEBUG - 提交事务成功")
            
            # 更新批次的文案数量
            batch.content_count = generated_count
            db.session.commit()
            print(f"DEBUG - 更新批次文案数量: {generated_count}")
            
            # 更新任务的实际文案数量
            try:
                # 直接从数据库获取任务对象，确保安全
                task_obj = Task.query.get(task_id)
                if task_obj:
                    task_obj.actual_count = Content.query.filter_by(task_id=task_id).count()
                    db.session.commit()
                    print(f"DEBUG - 更新任务实际文案数量: {task_obj.actual_count}")
                else:
                    print(f"WARNING - 无法找到任务ID: {task_id}")
            except Exception as e:
                print(f"WARNING - 更新任务实际文案数量失败: {str(e)}")
                # 不影响主流程，继续执行
            
            # 显示结果消息
            if duplicates_count > 0:
                success_message = f'成功生成 {generated_count} 篇文案，跳过 {duplicates_count} 篇重复文案'
                flash(success_message, 'success')
            else:
                success_message = f'成功生成 {generated_count} 篇文案'
                flash(success_message, 'success')
            
            try:
                # 强制执行提交并确保事务完成
                db.session.flush()
                db.session.commit()
                
                # 打印日志，确认生成的文案数量
                print(f"INFO - 成功生成 {generated_count} 篇文案，task_id={task_id}, batch_id={batch_id}")
                
                # 重定向到文案列表，不带筛选参数，显示所有文章
                redirect_url = url_for('content.content_list')
                
                # 如果是 AJAX 请求，返回 JSON 响应
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    print(f"DEBUG - 返回AJAX成功响应，重定向到: {redirect_url}")
                    return jsonify({
                        'success': True,
                        'message': success_message,
                        'redirect': redirect_url
                    })
                
                print(f"DEBUG - 返回普通重定向到: {redirect_url}")
                return redirect(redirect_url)  # 默认显示所有文章
            except Exception as e:
                # 捕获可能的提交错误
                db.session.rollback()
                print(f"ERROR - 文案生成提交失败: {str(e)}")
                error_message = f'文案生成可能未完全保存，请检查列表: {str(e)}'
                flash(error_message, 'warning')
                
                # 如果是 AJAX 请求，返回 JSON 响应
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({
                        'success': False,
                        'message': error_message,
                        'redirect': url_for('content.content_list')
                    })
                    
                return redirect(url_for('content.content_list'))
        except Exception as e:
            db.session.rollback()
            import traceback
            print(f"ERROR - 生成文案过程中发生异常: {str(e)}")
            print(f"ERROR - 异常堆栈: {traceback.format_exc()}")
            error_message = f'生成文案失败: {str(e)}'
            flash(error_message, 'danger')
            
            # 如果是 AJAX 请求，返回 JSON 响应
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'success': False,
                    'message': error_message,
                    'redirect': None
                })
                
            context = {
                'title': '批量生成文案 - 小红书文案生成系统',
                'form': form
            }
            return render_template('content/generate.html', **context)
    else:
        # 表单验证失败
        if request.method == 'POST':
            print(f"DEBUG - 表单验证失败: {form.errors}")
    
    context = {
        'title': '批量生成文案 - 小红书文案生成系统',
        'form': form
    }
    
    return render_template('content/generate.html', **context)


@content_bp.route('/<int:content_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('content_edit')
def content_edit(content_id):
    """编辑文案"""
    content = Content.query.get_or_404(content_id)
    
    # 检查是否可以编辑
    if content.workflow_status in ['pending_client_review', 'client_approved', 'published']:
        flash('当前状态的文案不能编辑', 'danger')
        return redirect(url_for('content.content_view', content_id=content_id))
    
    form = ContentForm(obj=content)
    
    # 初始化表单选择项
    form.client_id.choices = [(c.id, c.name) for c in Client.query.filter_by(status=True).all()]
    form.task_id.choices = [(t.id, t.name) for t in Task.query.all()]
    form.template_id.choices = [(0, '不使用模板')] + [(t.id, t.title) for t in Template.query.filter_by(status=True).all()]
    
    # 填充话题
    if content.topics:
        form.topics.data = ', '.join(content.topics_list)
    
    if form.validate_on_submit():
        # 保存编辑前的版本到历史记录
        history = ContentHistory(
            content_id=content.id,
            title=content.title,
            content=content.content,
            editor_id=current_user.id,
            edit_time=datetime.now(),
            is_client_edit=False
        )
        db.session.add(history)
        
        # 更新文案内容
        content.title = form.title.data
        content.content = form.content.data
        content.location = form.location.data
        content.display_date = form.display_date.data
        content.display_time = form.display_time.data
        content.publish_priority = form.publish_priority.data
        
        # 处理话题
        if form.topics.data:
            topics = [t.strip() for t in form.topics.data.split(',')]
            content.topics_list = topics
        else:
            content.topics = None
        
        # 保存到数据库
        db.session.commit()
        
        # 处理图片上传
        if form.images.data and any(form.images.data):
            image_urls = content.image_urls_list or []
            for image in form.images.data:
                if image.filename:
                    try:
                        relative_path = upload_manager.save_file(image, 'content')
                        url = url_for('static', filename=relative_path, _external=True)
                        image_urls.append(url)
                    except Exception as e:
                        current_app.logger.error(f"上传图片失败: {str(e)}")
            
            if image_urls:
                content.image_urls_list = image_urls
                if content.workflow_status == 'draft':
                    content.workflow_status = 'image_uploaded'
                db.session.commit()
        
        flash('文案更新成功', 'success')
        return redirect(url_for('content.content_view', content_id=content_id))
    
    return render_template('content/edit.html', form=form, content=content)


@content_bp.route('/<int:content_id>/view')
@login_required
@permission_required('content_view')
def content_view(content_id):
    """查看文案详情"""
    content = Content.query.get_or_404(content_id)
    
    # 获取历史版本
    history = ContentHistory.query.filter_by(content_id=content_id).order_by(ContentHistory.edit_time.desc()).all()
    
    # 获取拒绝理由
    rejections = RejectionReason.query.filter_by(content_id=content_id).order_by(RejectionReason.created_at.desc()).all()
    
    # 获取快捷理由列表（用于审核表单）
    quick_reasons = QuickReason.query.order_by(QuickReason.sort_order).all()
    
    # 审核表单
    review_form = ContentReviewForm()
    review_form.quick_reason_id.choices = [(0, '选择快捷理由')] + [(r.id, r.content) for r in quick_reasons]
    
    return render_template(
        'content/view.html',
        content=content,
        history=history,
        rejections=rejections,
        review_form=review_form
    )


@content_bp.route('/<int:content_id>/review', methods=['POST'])
@login_required
@permission_required('content_review')
def content_review(content_id):
    """审核文案"""
    content = Content.query.get_or_404(content_id)
    
    # 检查当前状态是否可以审核
    valid_states = ['draft', 'pending_review', 'image_uploaded', 'pending_final_review']
    if content.workflow_status not in valid_states:
        flash('当前文案状态不允许审核', 'danger')
        return redirect(url_for('content.content_view', content_id=content_id))
    
    form = ContentReviewForm()
    
    if form.validate_on_submit():
        status = form.status.data
        
        if status == 'approved':
            # 根据当前状态设置下一个状态
            if content.workflow_status == 'draft' or content.workflow_status == 'pending_review':
                # 初审通过
                content.workflow_status = 'first_reviewed'
                content.internal_review_status = 'first_approved'
                flash('初审已通过', 'success')
            
            elif content.workflow_status == 'image_uploaded' or content.workflow_status == 'pending_final_review':
                # 最终审核通过
                
                # 检查客户是否需要审核
                client = Client.query.get(content.client_id)
                if client and client.need_review:
                    content.workflow_status = 'pending_client_review'
                    flash('最终审核已通过，等待客户审核', 'success')
                else:
                    content.workflow_status = 'pending_publish'
                    content.client_review_status = 'approved'
                    flash('最终审核已通过，文案已进入待发布状态', 'success')
                
                content.internal_review_status = 'final_approved'
            
            # 记录审核人和审核时间
            content.reviewer_id = current_user.id
            content.review_time = datetime.now()
            
        elif status == 'rejected':
            # 拒绝文案
            reason_text = form.reason.data
            quick_reason_id = form.quick_reason_id.data
            
            # 如果选择了快捷理由且没有填写自定义理由
            if quick_reason_id and quick_reason_id > 0 and not reason_text:
                quick_reason = QuickReason.query.get(quick_reason_id)
                if quick_reason:
                    reason_text = quick_reason.content
            
            # 创建拒绝理由记录
            if reason_text:
                rejection = RejectionReason(
                    content_id=content.id,
                    reason=reason_text,
                    created_by=current_user.id,
                    is_client=False
                )
                db.session.add(rejection)
            
            # 设置状态为草稿
            content.workflow_status = 'draft'
            content.internal_review_status = 'pending'
            flash('文案已被拒绝，已退回到草稿状态', 'warning')
        
        # 保存更改
        db.session.commit()
        
        return redirect(url_for('content.content_view', content_id=content_id))
    
    for field, errors in form.errors.items():
        for error in errors:
            flash(f'{getattr(form, field).label.text}: {error}', 'danger')
    
    return redirect(url_for('content.content_view', content_id=content_id))


@content_bp.route('/<int:content_id>/submit', methods=['POST'])
@login_required
@permission_required('content_edit')
def content_submit(content_id):
    """提交文案进入审核流程"""
    content = Content.query.get_or_404(content_id)
    
    # 检查当前状态
    if content.workflow_status != 'draft':
        flash('只有草稿状态的文案可以提交审核', 'danger')
        return redirect(url_for('content.content_view', content_id=content_id))
    
    # 更新状态
    content.workflow_status = 'pending_review'
    db.session.commit()
    
    flash('文案已提交审核', 'success')
    return redirect(url_for('content.content_view', content_id=content_id))


@content_bp.route('/<int:content_id>/delete', methods=['POST'])
@login_required
def content_delete(content_id):
    """删除文案"""
    content = Content.query.get_or_404(content_id)

    # 检查是否可以删除
    if content.workflow_status in ['pending_client_review', 'client_approved', 'pending_publish', 'published']:
        flash('当前状态的文案不能删除', 'danger')
        return redirect(url_for('content.content_view', content_id=content_id))
    
    # 获取自动补充选项
    auto_supplement = request.form.get('auto_supplement', 'false')
    
    # 标记文案为已删除
    content.is_deleted = True
    content.deleted_at = datetime.now()
    content.deleted_by = current_user.id
    
    db.session.commit()
    
    # 如果请求是AJAX，返回JSON响应
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # 如果需要自动补充，调用补充接口
        if auto_supplement == 'true':
            from app.views.supplement import delete_and_supplement
            return delete_and_supplement(content_id)

        return jsonify({
            'success': True,
            'message': '文案已标记为删除',
            'redirect': url_for('content.content_list')
        })

    flash('文案已标记为删除', 'success')
    return redirect(url_for('content.content_list'))


@content_bp.route('/batch-action', methods=['POST'])
@login_required
def content_batch_action():
    """批量操作文案"""
    form = ContentBatchActionForm()

    # 跳过表单验证，直接处理请求
    action = request.form.get('action')
    content_ids = request.form.get('content_ids', '').split(',')
    status = request.form.get('status')
    reason = request.form.get('reason')
    
    if not content_ids or not content_ids[0]:
        flash('请选择要操作的文案', 'danger')
        return redirect(url_for('content.content_list'))
    
    # 获取所有选中的文案
    contents = Content.query.filter(Content.id.in_(content_ids)).all()
    
    if action == 'review' and current_user.has_permission('content_review'):
        # 批量审核
        count = 0
        for content in contents:
            # 检查状态是否允许审核
            valid_states = ['draft', 'pending_review', 'image_uploaded', 'pending_final_review']
            if content.workflow_status in valid_states:
                if status == 'approved':
                    # 根据当前状态设置下一个状态
                    if content.workflow_status == 'draft' or content.workflow_status == 'pending_review':
                        content.workflow_status = 'first_reviewed'
                        content.internal_review_status = 'first_approved'
                    
                    elif content.workflow_status == 'image_uploaded' or content.workflow_status == 'pending_final_review':
                        # 检查客户是否需要审核
                        client = Client.query.get(content.client_id)
                        if client and client.need_review:
                            content.workflow_status = 'pending_client_review'
                        else:
                            content.workflow_status = 'pending_publish'
                            content.client_review_status = 'approved'
                        
                        content.internal_review_status = 'final_approved'
                    
                    # 记录审核人和审核时间
                    content.reviewer_id = current_user.id
                    content.review_time = datetime.now()
                    count += 1
                    
                elif status == 'rejected':
                    # 创建拒绝理由记录
                    if reason:
                        rejection = RejectionReason(
                            content_id=content.id,
                            reason=reason,
                            created_by=current_user.id,
                            is_client=False
                        )
                        db.session.add(rejection)
                    
                    # 设置状态为草稿
                    content.workflow_status = 'draft'
                    content.internal_review_status = 'pending'
                    count += 1
        
        db.session.commit()
        flash(f'已批量{status == "approved" and "通过" or "拒绝"} {count} 篇文案', 'success')
        
    elif action == 'publish' and current_user.has_permission('content_publish'):
        # 批量发布
        count = 0
        for content in contents:
            # 检查状态是否允许发布
            if content.workflow_status == 'client_approved' or content.workflow_status == 'pending_publish':
                if status == 'pending_publish':
                    content.workflow_status = 'pending_publish'
                elif status == 'published':
                    content.workflow_status = 'published'
                    content.publish_status = 'published'
                    content.publish_time = datetime.now()
                
                count += 1
        
        db.session.commit()
        flash(f'已将 {count} 篇文案标记为{status == "pending_publish" and "待发布" or "已发布"}', 'success')
        
    elif action == 'delete':
        # 批量删除 - 移除权限检查
        count = 0
        for content in contents:
            # 检查状态是否允许删除
            if content.workflow_status not in ['pending_publish', 'published']:
                # 标记文案为已删除
                content.is_deleted = True
                content.deleted_at = datetime.now()
                content.deleted_by = current_user.id
                count += 1
        
        db.session.commit()
        flash(f'已成功删除 {count} 篇文案', 'success')
    
    return redirect(url_for('content.content_list'))


@content_bp.route('/test-delete')
@login_required
def test_delete():
    """测试删除功能"""
    return render_template('test_delete.html')


@content_bp.route('/get-template/<int:template_id>')
@login_required
@ajax_aware
def get_template(template_id):
    """获取模板详情"""
    template = Template.query.get_or_404(template_id)
    return jsonify({
        'success': True,
        'title': template.title,
        'content': template.content
    })


@content_bp.route('/get-tasks/<int:client_id>')
@login_required
@ajax_aware
def get_tasks(client_id):
    """获取客户的任务列表"""
    tasks = Task.query.filter_by(client_id=client_id).all()
    return jsonify({
        'success': True,
        'tasks': [{'id': task.id, 'name': task.name} for task in tasks]
    })


@content_bp.route('/get-batches/<int:task_id>')
@login_required
@ajax_aware
def get_batches(task_id):
    """获取任务的批次列表"""
    batches = Batch.query.filter_by(task_id=task_id).all()
    return jsonify({
        'success': True,
        'batches': [{'id': batch.id, 'name': batch.name} for batch in batches]
    }) 


@content_bp.route('/get-template-availability/<int:category_id>/<int:task_id>/<string:duplicate_control>')
def get_template_availability(category_id, task_id, duplicate_control):
    """获取模板可用情况"""
    try:
        # 记录开始时间，用于性能分析
        import time
        start_time = time.time()
        
        # 记录请求参数
        current_app.logger.info(f"获取模板可用情况: category_id={category_id}, task_id={task_id}, duplicate_control={duplicate_control}")
        
        # 优化查询：获取分类下的所有模板总数
        total_templates = Template.query.filter_by(category_id=category_id, status=True).count()
        query_time_1 = time.time() - start_time
        current_app.logger.info(f"查询模板总数耗时: {query_time_1:.4f}秒, 结果: {total_templates}个模板")
        
        # 初始化已使用模板数
        used_templates = 0
        available_templates = total_templates
        
        # 检查是否是新任务，且有同名任务存在
        existing_task_id = None
        is_new_task = task_id == 0  # 记录原始的新任务状态

        if task_id == 0:
            # 获取客户ID和任务名称
            client_id = request.args.get('client_id')
            task_name = request.args.get('task_name')

            if client_id and task_name:
                try:
                    client_id = int(client_id)
                    # 查询是否存在同名任务
                    existing_task = Task.query.filter_by(
                        client_id=client_id,
                        name=task_name
                    ).first()

                    if existing_task:
                        existing_task_id = existing_task.id
                        current_app.logger.info(f"检测到同名任务: id={existing_task_id}, name={task_name}")

                        # 如果是重复控制模式，使用同名任务的ID进行后续查询
                        if duplicate_control in ['no_duplicate_task', 'no_duplicate_all']:
                            task_id = existing_task_id
                            current_app.logger.info(f"使用同名任务ID进行重复检查: {task_id}")

                except (ValueError, TypeError):
                    current_app.logger.warning("客户ID格式无效")

        # 如果是新任务且没有重复控制要求，或者没有找到同名任务，则所有模板都可用
        if is_new_task and (duplicate_control not in ['no_duplicate_all', 'no_duplicate_task'] or
                           (duplicate_control == 'no_duplicate_task' and not existing_task_id)):
            current_app.logger.info("检测到新任务请求，所有模板都可用")
            total_time = time.time() - start_time
            return jsonify({
                'success': True,
                'total_templates': total_templates,
                'used_templates': 0,
                'available_templates': total_templates,
                'query_time': round(total_time, 4),
                'is_new_task': True
            })
        
        # 根据重复控制选项获取已使用的模板
        if duplicate_control == 'no_duplicate_task':
            # 优化查询：直接使用COUNT和子查询而不是获取完整列表
            query_start = time.time()
            # 只统计特定分类的模板
            used_templates = db.session.query(db.func.count(db.distinct(Content.template_id))).join(
                Template, Content.template_id == Template.id
            ).filter(
                Content.task_id == task_id,
                Content.template_id.isnot(None),
                Content.is_deleted != True,
                Template.category_id == category_id
            ).scalar() or 0
            
            query_time_2 = time.time() - query_start
            current_app.logger.info(f"查询任务已用模板数耗时: {query_time_2:.4f}秒, 结果: {used_templates}个已用模板")
            
        elif duplicate_control == 'no_duplicate_all':
            # 获取该任务所属的客户ID
            query_start = time.time()
            client_id = None
            
            if task_id > 0:
                task = Task.query.get(task_id)
                if not task:
                    return jsonify({
                        'success': False,
                        'message': f"任务ID {task_id} 不存在"
                    })
                client_id = task.client_id
            else:
                # 对于新任务，从请求参数中获取客户ID
                client_id = request.args.get('client_id')
                if client_id:
                    try:
                        client_id = int(client_id)
                    except (ValueError, TypeError):
                        return jsonify({
                            'success': False,
                            'message': "客户ID格式无效"
                        })
                else:
                    current_app.logger.warning("新任务请求未提供客户ID，无法检查客户级别的模板使用情况")
                    return jsonify({
                        'success': False,
                        'message': "需要提供客户ID以检查模板可用情况"
                    })
            
            # 优化查询：直接使用COUNT和JOIN
            used_templates = db.session.query(db.func.count(db.distinct(Content.template_id))).join(
                Task, Content.task_id == Task.id
            ).join(
                Template, Content.template_id == Template.id
            ).filter(
                Task.client_id == client_id,
                Content.template_id.isnot(None),
                Content.is_deleted != True,
                Template.category_id == category_id  # 通过模板表关联查询，只统计特定分类的模板
            ).scalar() or 0
            
            query_time_2 = time.time() - query_start
            current_app.logger.info(f"查询客户已用模板数耗时: {query_time_2:.4f}秒, 结果: {used_templates}个已用模板")
        
        # 计算可用模板数量
        available_templates = total_templates - used_templates
        if available_templates < 0:
            available_templates = 0
        
        # 记录总耗时
        total_time = time.time() - start_time
        current_app.logger.info(f"获取模板可用情况总耗时: {total_time:.4f}秒")
        
        return jsonify({
            'success': True,
            'total_templates': total_templates,
            'used_templates': used_templates,
            'available_templates': available_templates,
            'query_time': round(total_time, 4)
        })
    except Exception as e:
        current_app.logger.error(f"获取模板可用情况出错: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f"获取模板可用情况失败: {str(e)}"
        }) 


@content_bp.route('/get-batch-info', methods=['GET'])
@login_required
@ajax_aware
def get_batch_info():
    """根据客户ID和任务名称获取批次信息"""
    client_id = request.args.get('client_id')
    task_name = request.args.get('task_name')
    
    if not client_id or not task_name:
        return jsonify({
            'success': False,
            'message': '缺少必要参数'
        })
    
    try:
        # 查询是否存在同名任务
        existing_task = Task.query.filter_by(
            client_id=int(client_id),
            name=task_name
        ).first()
        
        if existing_task:
            # 如果存在同名任务，获取该任务下的批次数量
            batch_count = Batch.query.filter_by(task_id=existing_task.id).count()
            batch_name = f'批次 {batch_count + 1}'
            
            return jsonify({
                'success': True,
                'task_exists': True,
                'task_id': existing_task.id,
                'task_name': existing_task.name,
                'batch_name': batch_name,
                'batch_count': batch_count
            })
        else:
            # 新任务的第一个批次
            return jsonify({
                'success': True,
                'task_exists': False,
                'batch_name': '批次 1'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'查询批次信息失败: {str(e)}'
        }) 