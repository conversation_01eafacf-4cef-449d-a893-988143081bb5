<!-- 专用于添加模板的模态框内容 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <form method="post" id="add-template-form">
                        {{ form.csrf_token }}
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    {{ form.title.label(class="form-label") }}
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-card-heading"></i>
                                        </span>
                                        {{ form.title(class="form-control", id="add_title_field", placeholder="请输入模板标题，点击下方标记按钮可插入标记") }}
                                    </div>
                                    {% if form.title.errors %}
                                        {% for error in form.title.errors %}
                                        <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        提示：点击下方的标记按钮可以快速插入标记到标题中 | 当前焦点：<span id="add-title-focus-indicator" class="text-success">点击此处输入标题</span>
                                    </small>
                                </div>

                                <!-- 可用标记按钮 - 移到标题下面 -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-tags"></i> 可用标记
                                    </label>
                                    <div class="d-flex flex-wrap gap-2">
                                        {% for mark in marks %}
                                        <button type="button" class="btn btn-outline-primary btn-sm mark-button"
                                                onclick="if(window.insertAddTemplateMark) { window.insertAddTemplateMark('{{ mark.name }}'); } else { addInsertMark('{{ mark.name }}'); }"
                                                title="{{ mark.description }}">
                                            <i class="bi bi-tag"></i> {{ mark.name }}
                                        </button>
                                        {% endfor %}
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="bi bi-lightbulb"></i>
                                        点击标记按钮将在当前焦点位置插入标记
                                    </small>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    {{ form.category_id.label(class="form-label") }}
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-folder"></i>
                                        </span>
                                        {{ form.category_id(class="form-select") }}
                                    </div>
                                    {% if form.category_id.errors %}
                                        {% for error in form.category_id.errors %}
                                        <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>


                        </div>


                        <div class="mb-3">
                            <label class="form-label">
                                <i class="bi bi-file-text"></i> {{ form.content.label.text }}
                            </label>
                            {{ form.content(class="form-control", id="add_content", rows="15", style="min-height: 400px; font-family: 'Courier New', monospace;", placeholder="请输入模板内容，点击上方标记按钮可插入标记...") }}
                            {% if form.content.errors %}
                                {% for error in form.content.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                            <small class="form-text text-muted">
                                <i class="bi bi-info-circle"></i>
                                提示：点击上方的标记按钮可以快速插入标记到内容中 | 当前焦点：<span id="add-content-focus-indicator" class="text-primary">点击此处输入内容</span>
                            </small>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle"></i> 取消
                            </button>

                            <div class="d-flex align-items-center gap-3">
                                <!-- 状态开关 -->
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="add_status_switch" name="status" {{ 'checked' if form.status.data else '' }}>
                                    <label class="form-check-label" for="add_status_switch">
                                        <span id="add_status_text">{{ '启用' if form.status.data else '禁用' }}</span>
                                    </label>
                                </div>

                                <button type="button" class="btn btn-primary" id="add-submit-btn">
                                    <i class="bi bi-check-circle"></i> 添加模板
                                </button>
                            </div>
                        </div>

                        {% if form.status.errors %}
                            {% for error in form.status.errors %}
                            <div class="invalid-feedback d-block mt-2">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .mark-button {
        margin: 5px;
    }
    
    /* 标记的通用样式 */
    .template-mark {
        background-color: #e9f5ff;
        border: 1px solid #b3d7ff;
        border-radius: 3px;
        padding: 2px 4px;
        font-weight: bold;
        color: #0066cc;
        display: inline-block;
        margin: 0 2px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* 输入框焦点状态指示 */
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .current-focus {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    }

    .has-marks {
        background-color: #f8f9fa;
    }
</style>

<script>
// 立即执行的测试
console.log('🚀 添加模板JavaScript文件开始加载 - ' + new Date().toLocaleTimeString());

// 全局变量（用于添加表单）
window.addTemplateCurrentFocus = null; // 默认无焦点

// 添加模板专用的插入标记函数（全局函数）
window.addInsertMark = function(markName) {
    console.log('🏷️ 添加模板插入标记:', markName);

    const markText = '{' + markName + '}';
    const targetField = window.addTemplateCurrentFocus;

    if (!targetField) {
        console.error('❌ 没有焦点字段');
        // 如果没有焦点，默认插入到内容字段
        const addContentField = document.getElementById('add_content');
        if (addContentField) {
            window.addTemplateCurrentFocus = addContentField;
            addContentField.focus();
        } else {
            return;
        }
    }

    console.log('📍 插入到字段:', window.addTemplateCurrentFocus.id);

    // 获取当前光标位置
    const startPos = window.addTemplateCurrentFocus.selectionStart || 0;
    const endPos = window.addTemplateCurrentFocus.selectionEnd || 0;

    // 在光标位置插入标记
    window.addTemplateCurrentFocus.value =
        window.addTemplateCurrentFocus.value.substring(0, startPos) +
        markText +
        window.addTemplateCurrentFocus.value.substring(endPos);

    // 将光标位置设置到插入的标记之后
    const newPos = startPos + markText.length;
    window.addTemplateCurrentFocus.selectionStart = window.addTemplateCurrentFocus.selectionEnd = newPos;

    // 保持焦点在目标输入框
    window.addTemplateCurrentFocus.focus();

    console.log('✅ 标记插入成功');
};

// 初始化函数
function initAddTemplateForm() {
    console.log('🔄 开始初始化添加模板表单 - ' + new Date().toLocaleTimeString());
    console.log('🔍 当前页面URL:', window.location.href);
    console.log('🔍 document.readyState:', document.readyState);

    // 获取表单元素
    const addTitleField = document.getElementById('add_title_field');
    const addContentField = document.getElementById('add_content');
    const addSubmitBtn = document.getElementById('add-submit-btn');

    console.log('🔍 查找表单元素:', {
        addTitleField: addTitleField,
        addContentField: addContentField,
        addSubmitBtn: addSubmitBtn
    });

    // 更详细的元素查找调试
    console.log('🔍 页面中所有input元素:', document.querySelectorAll('input'));
    console.log('🔍 页面中所有textarea元素:', document.querySelectorAll('textarea'));
    console.log('🔍 页面中所有button元素:', document.querySelectorAll('button'));
    console.log('🔍 页面中所有带ID的元素:', Array.from(document.querySelectorAll('[id]')).map(el => el.id));

    if (!addTitleField || !addContentField || !addSubmitBtn) {
        console.error('❌ 找不到添加表单元素');
        return;
    }

    console.log('✅ 找到添加表单元素');

    // 设置默认焦点
    window.addTemplateCurrentFocus = addContentField;
    console.log('🎯 设置默认焦点到内容字段:', addContentField);
    
    // 更新焦点指示器
    function updateAddFocusIndicator() {
        console.log('🔄 更新添加表单焦点指示器');
        
        // 移除所有焦点指示
        addTitleField.classList.remove('current-focus');
        addContentField.classList.remove('current-focus');

        // 更新焦点指示文本
        const titleIndicator = document.getElementById('add-title-focus-indicator');
        const contentIndicator = document.getElementById('add-content-focus-indicator');

        if (window.addTemplateCurrentFocus === addTitleField) {
            console.log('📍 设置标题为当前焦点');
            addTitleField.classList.add('current-focus');
            if (titleIndicator) titleIndicator.textContent = '当前活动 ✓';
            if (contentIndicator) contentIndicator.textContent = '点击此处输入内容';
        } else if (window.addTemplateCurrentFocus === addContentField) {
            console.log('📍 设置内容为当前焦点');
            addContentField.classList.add('current-focus');
            if (titleIndicator) titleIndicator.textContent = '点击此处输入标题';
            if (contentIndicator) contentIndicator.textContent = '当前活动 ✓';
        }
    }
    
    // 设置事件监听器
    console.log('🔧 设置添加表单事件监听器');
    console.log('🔧 标题字段元素:', addTitleField);
    console.log('🔧 内容字段元素:', addContentField);

    // 标题字段事件
    addTitleField.addEventListener('focus', function(e) {
        console.log('🎯 添加-标题字段获得焦点', e);
        window.addTemplateCurrentFocus = this;
        updateAddFocusIndicator();
    });

    addTitleField.addEventListener('click', function(e) {
        console.log('🖱️ 添加-标题字段被点击', e);
        window.addTemplateCurrentFocus = this;
        updateAddFocusIndicator();
    });

    addTitleField.addEventListener('mousedown', function(e) {
        console.log('🖱️ 添加-标题字段 mousedown', e);
        window.addTemplateCurrentFocus = this;
        updateAddFocusIndicator();
    });

    // 内容字段事件
    addContentField.addEventListener('focus', function(e) {
        console.log('🎯 添加-内容字段获得焦点', e);
        window.addTemplateCurrentFocus = this;
        updateAddFocusIndicator();
    });

    addContentField.addEventListener('click', function(e) {
        console.log('🖱️ 添加-内容字段被点击', e);
        window.addTemplateCurrentFocus = this;
        updateAddFocusIndicator();
    });

    addContentField.addEventListener('mousedown', function(e) {
        console.log('🖱️ 添加-内容字段 mousedown', e);
        window.addTemplateCurrentFocus = this;
        updateAddFocusIndicator();
    });

    console.log('✅ 事件监听器设置完成');
    
    // 初始化焦点指示器
    updateAddFocusIndicator();

    // 状态开关事件
    const statusSwitch = document.getElementById('add_status_switch');
    const statusText = document.getElementById('add_status_text');
    if (statusSwitch && statusText) {
        statusSwitch.addEventListener('change', function() {
            statusText.textContent = this.checked ? '启用' : '禁用';
            console.log('📊 状态开关变更:', this.checked ? '启用' : '禁用');
        });
    }

    // 提交按钮事件
    addSubmitBtn.addEventListener('click', function() {
        console.log('🔄 添加表单提交');
        submitAddForm();
    });

    // 状态开关事件
    const addStatusSwitch = document.getElementById('add_status_switch');
    const addStatusText = document.getElementById('add_status_text');

    if (addStatusSwitch && addStatusText) {
        addStatusSwitch.addEventListener('change', function() {
            addStatusText.textContent = this.checked ? '启用' : '禁用';
            console.log('🔄 状态开关切换:', this.checked ? '启用' : '禁用');
        });
        console.log('✅ 状态开关事件监听器设置完成');
    }

    console.log('✅ 添加模板表单初始化完成');
}

// 多种方式确保初始化函数执行
console.log('🔄 准备执行初始化函数...');

// 方式1：立即执行
if (document.readyState === 'complete') {
    console.log('📄 文档已完全加载，立即执行初始化');
    initAddTemplateForm();
} else {
    console.log('📄 文档未完全加载，等待加载完成');

    // 方式2：DOM加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOMContentLoaded 事件触发');
            setTimeout(initAddTemplateForm, 100);
        });
    }

    // 方式3：延迟执行
    setTimeout(function() {
        console.log('⏰ 延迟执行初始化函数');
        initAddTemplateForm();
    }, 200);

    // 方式4：窗口加载完成后执行
    window.addEventListener('load', function() {
        console.log('🪟 window load 事件触发');
        setTimeout(initAddTemplateForm, 100);
    });
}

// 添加表单提交函数
function submitAddForm() {
    console.log('🔄 开始提交添加表单');

    // 防止重复提交
    if (window.isSubmittingAddTemplate) {
        console.log('⚠️ 表单正在提交中，忽略重复请求');
        return;
    }

    const form = document.getElementById('add-template-form');
    const addTitleField = document.getElementById('add_title_field');
    const addContentField = document.getElementById('add_content');

    if (!form || !addTitleField || !addContentField) {
        console.error('❌ 找不到表单元素');
        return;
    }

    // 设置提交状态
    window.isSubmittingAddTemplate = true;

    // 禁用提交按钮
    const submitBtn = document.getElementById('add-submit-btn');
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 提交中...';
    }
    
    const submitUrl = `/simple/templates/add`;
    console.log('🎯 提交URL:', submitUrl);
    
    // 创建FormData
    const formData = new FormData(form);
    
    // 提交表单
    fetch(submitUrl, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('📨 服务器响应:', data);
        
        if (data.success) {
            console.log('✅ 添加保存成功');
            showToast('添加成功！', 'success');

            // 关闭模态框并清理遮罩层
            const modalElement = document.getElementById('addTemplateModal');
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();

                // 确保遮罩层被移除
                setTimeout(() => {
                    const backdrops = document.querySelectorAll('.modal-backdrop');
                    backdrops.forEach(backdrop => backdrop.remove());

                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';

                    console.log('🧹 已清理模态框遮罩层');
                }, 300);
            }

            // 刷新页面
            setTimeout(() => {
                location.reload();
            }, 500);
        } else {
            console.log('❌ 添加保存失败:', data.message);
            showToast('保存失败：' + (data.message || '未知错误'), 'danger');

            // 重置提交状态
            window.isSubmittingAddTemplate = false;
            const submitBtn = document.getElementById('add-submit-btn');
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="bi bi-check-circle"></i> 添加模板';
            }
        }
    })
    .catch(error => {
        console.error('❌ 提交失败:', error);
        showToast('提交失败：' + error.message, 'danger');

        // 重置提交状态
        window.isSubmittingAddTemplate = false;
        const submitBtn = document.getElementById('add-submit-btn');
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="bi bi-check-circle"></i> 添加模板';
        }
    });
}
</script>
