<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        button { margin: 5px; padding: 10px 15px; }
        .result { margin-top: 10px; padding: 10px; background: #f5f5f5; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>任务管理API测试</h1>
    
    <div class="test-section">
        <h3>1. 测试创建任务</h3>
        <button onclick="testCreateTask()">创建任务</button>
        <div id="createResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 测试获取任务详情</h3>
        <input type="number" id="taskId" placeholder="任务ID" value="1">
        <button onclick="testGetTask()">获取任务详情</button>
        <div id="getResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 测试更新任务</h3>
        <input type="number" id="updateTaskId" placeholder="任务ID" value="1">
        <button onclick="testUpdateTask()">更新任务</button>
        <div id="updateResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. 测试删除任务</h3>
        <input type="number" id="deleteTaskId" placeholder="任务ID" value="1">
        <button onclick="testDeleteTask()">删除任务</button>
        <div id="deleteResult" class="result"></div>
    </div>

    <script>
        function testCreateTask() {
            const formData = new FormData();
            formData.append('name', '测试任务' + Date.now());
            formData.append('description', '这是一个测试任务');
            formData.append('client_id', '1');
            formData.append('target_count', '10');
            formData.append('status', 'processing');

            fetch('/simple/api/tasks', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                return response.text();
            })
            .then(text => {
                console.log('Response text:', text);
                document.getElementById('createResult').textContent = text;
                try {
                    const data = JSON.parse(text);
                    document.getElementById('createResult').textContent = JSON.stringify(data, null, 2);
                } catch (e) {
                    document.getElementById('createResult').textContent = 'Response is not JSON:\n' + text;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('createResult').textContent = 'Error: ' + error.message;
            });
        }

        function testGetTask() {
            const taskId = document.getElementById('taskId').value;
            
            fetch(`/simple/api/tasks/${taskId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    document.getElementById('getResult').textContent = JSON.stringify(data, null, 2);
                } catch (e) {
                    document.getElementById('getResult').textContent = 'Response is not JSON:\n' + text;
                }
            })
            .catch(error => {
                document.getElementById('getResult').textContent = 'Error: ' + error.message;
            });
        }

        function testUpdateTask() {
            const taskId = document.getElementById('updateTaskId').value;
            const formData = new FormData();
            formData.append('name', '更新的任务名称' + Date.now());
            formData.append('description', '更新的任务描述');
            formData.append('target_count', '20');
            formData.append('status', 'processing');

            fetch(`/simple/api/tasks/${taskId}`, {
                method: 'PUT',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    document.getElementById('updateResult').textContent = JSON.stringify(data, null, 2);
                } catch (e) {
                    document.getElementById('updateResult').textContent = 'Response is not JSON:\n' + text;
                }
            })
            .catch(error => {
                document.getElementById('updateResult').textContent = 'Error: ' + error.message;
            });
        }

        function testDeleteTask() {
            const taskId = document.getElementById('deleteTaskId').value;
            
            fetch(`/simple/api/tasks/${taskId}`, {
                method: 'DELETE',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    document.getElementById('deleteResult').textContent = JSON.stringify(data, null, 2);
                } catch (e) {
                    document.getElementById('deleteResult').textContent = 'Response is not JSON:\n' + text;
                }
            })
            .catch(error => {
                document.getElementById('deleteResult').textContent = 'Error: ' + error.message;
            });
        }
    </script>
</body>
</html>
