{% extends "base.html" %}

{% block title %}AJAX测试页面{% endblock %}

{% block content_auth %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3>AJAX导航测试</h3>
                </div>
                <div class="card-body">
                    <p>这是一个测试AJAX导航的页面。如果你能看到这个页面，说明AJAX导航正常工作。</p>
                    <p>当前时间: <strong id="current-time">{{ now }}</strong></p>
                    
                    <div class="mt-4">
                        <h4>测试链接</h4>
                        <ul class="list-group">
                            <li class="list-group-item">
                                <a href="{{ url_for('main.dashboard') }}" data-ajax-link>返回控制面板</a>
                            </li>
                            <li class="list-group-item">
                                <a href="{{ url_for('content.content_list') }}" data-ajax-link>文案列表</a>
                            </li>
                            <li class="list-group-item">
                                <a href="{{ url_for('content.content_generate') }}" data-ajax-link>批量生成文案</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 更新当前时间
    function updateTime() {
        const now = new Date();
        document.getElementById('current-time').textContent = now.toLocaleString();
    }
    
    // 每秒更新一次时间
    setInterval(updateTime, 1000);
    
    // 初始化时更新一次
    updateTime();
</script>
{% endblock %} 