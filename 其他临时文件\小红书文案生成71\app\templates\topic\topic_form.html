{% extends "base.html" %}

{% block title %}{{ title }} - 小红书文案生成系统{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
{% endblock %}

{% block content_auth %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>{{ title }}</h2>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('topic.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回话题列表
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        <div class="mb-3">
                            {{ form.name.label(class="form-label") }}
                            {{ form.name(class="form-control") }}
                            {% if form.name.errors %}
                                {% for error in form.name.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.type.label(class="form-label") }}
                            {{ form.type(class="form-select") }}
                            {% if form.type.errors %}
                                {% for error in form.type.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.priority.label(class="form-label") }}
                            {{ form.priority(class="form-control") }}
                            {% if form.priority.errors %}
                                {% for error in form.priority.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                            <div class="form-text">数字越大优先级越高（0-100）</div>
                        </div>
                        <div class="d-grid gap-2">
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 