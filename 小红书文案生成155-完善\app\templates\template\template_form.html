{% extends "base_simple.html" %}

{% block title %}{{ title }} - 小红书文案生成系统{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
<style>
    .mark-button {
        margin: 5px;
    }
    
    /* 标记的通用样式 */
    .template-mark {
        background-color: #e9f5ff;
        border: 1px solid #b3d7ff;
        border-radius: 3px;
        padding: 2px 4px;
        font-weight: bold;
        color: #0066cc;
        display: inline-block;
        margin: 0 2px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* 输入框焦点状态指示 */
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .current-focus {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    }

    /* 标记高亮样式 - 使用背景图片模拟高亮 */
    .has-marks {
        background-image: linear-gradient(to right,
            transparent 0%,
            rgba(255, 243, 205, 0.3) 0%,
            rgba(255, 243, 205, 0.3) 100%,
            transparent 100%);
    }

    /* 标记美化输入框样式 */
    .form-control {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .form-control.current-focus {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    }

    /* 标记文本美化 - 使用CSS来高亮{标记}格式 */
    .form-control {
        background-image: none;
        background-repeat: no-repeat;
        background-attachment: local;
        background-position: 0 0;
    }

    /* 为包含标记的输入框添加特殊样式 */
    .form-control:focus {
        background-color: #fafafa;
    }

    /* 标记预览样式 */
    .mark-preview {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        font-family: inherit;
        line-height: 1.5;
        min-height: 42px;
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
    }

    .mark-preview.content-area {
        min-height: 120px;
        font-family: 'Courier New', monospace;
        white-space: pre-wrap;
        word-wrap: break-word;
    }

    /* 富文本编辑器样式 */
    .rich-text-editor {
        border: 2px solid #ced4da;
        border-radius: 0.375rem;
        padding: 0.75rem;
        font-size: 1rem;
        line-height: 1.5;
        color: #212529;
        background-color: #fff;
        background-clip: padding-box;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        outline: none;
        min-height: 42px;
        font-family: inherit;
        cursor: text;
        position: relative;
        display: block;
        width: 100%;
        flex: 1;
    }

    /* 在input-group中的富文本编辑器 */
    .input-group .rich-text-editor {
        border-left: none;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    .rich-text-editor:hover {
        border-color: #86b7fe;
    }

    .rich-text-editor:focus {
        border-color: #0d6efd;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    .rich-text-editor.current-focus {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25) !important;
    }

    .rich-text-editor[contenteditable="true"]:empty::before {
        content: attr(data-placeholder);
        color: #6c757d;
        font-style: italic;
        pointer-events: none;
    }

    /* 内容区域特殊样式 */
    .rich-text-editor.content-area {
        min-height: 400px !important;
        height: 400px;
        font-family: 'Courier New', monospace;
        white-space: pre-wrap;
        word-wrap: break-word;
        line-height: 1.6;
        overflow-y: auto;
    }

    /* 确保编辑器可见 */
    .rich-text-editor {
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* 内联标记标签样式 - 重新设计 */
    .inline-mark-tag {
        display: inline-block;
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        color: #1565c0;
        font-weight: 600;
        border: 2px solid #2196f3;
        border-radius: 16px;
        padding: 4px 12px;
        margin: 0 3px;
        font-size: 0.9em;
        line-height: 1.4;
        cursor: pointer;
        transition: all 0.3s ease;
        user-select: none;
        white-space: nowrap;
        vertical-align: middle;
        box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);
        position: relative;
    }

    .inline-mark-tag::before {
        content: '🏷️';
        margin-right: 4px;
        font-size: 0.9em;
    }

    .inline-mark-tag:hover {
        background: linear-gradient(135deg, #bbdefb, #90caf9);
        border-color: #1976d2;
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
    }

    /* 不同类型标记的颜色 - 更明显的设计 */
    .inline-mark-tag.mark-type-text {
        background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
        color: #2e7d32;
        border-color: #4caf50;
        box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
    }

    .inline-mark-tag.mark-type-text:hover {
        background: linear-gradient(135deg, #c8e6c9, #a5d6a7);
        border-color: #388e3c;
        box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
    }

    .inline-mark-tag.mark-type-topic {
        background: linear-gradient(135deg, #fff8e1, #ffecb3);
        color: #f57f17;
        border-color: #ffc107;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.2);
    }

    .inline-mark-tag.mark-type-topic:hover {
        background: linear-gradient(135deg, #ffecb3, #ffe082);
        border-color: #ffa000;
        box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
    }

    .inline-mark-tag.mark-type-location {
        background: linear-gradient(135deg, #ffebee, #ffcdd2);
        color: #c62828;
        border-color: #f44336;
        box-shadow: 0 2px 4px rgba(244, 67, 54, 0.2);
    }

    .inline-mark-tag.mark-type-location:hover {
        background: linear-gradient(135deg, #ffcdd2, #ef9a9a);
        border-color: #d32f2f;
        box-shadow: 0 4px 8px rgba(244, 67, 54, 0.3);
    }

    .inline-mark-tag.mark-type-user {
        background: linear-gradient(135deg, #f3e5f5, #e1bee7);
        color: #7b1fa2;
        border-color: #9c27b0;
        box-shadow: 0 2px 4px rgba(156, 39, 176, 0.2);
    }

    .inline-mark-tag.mark-type-user:hover {
        background: linear-gradient(135deg, #e1bee7, #ce93d8);
        border-color: #8e24aa;
        box-shadow: 0 4px 8px rgba(156, 39, 176, 0.3);
    }

    /* 插入动画 - 更明显的效果 */
    @keyframes markInsertAnimation {
        0% {
            transform: scale(0.5) translateY(-10px) rotate(-5deg);
            opacity: 0;
            background: linear-gradient(135deg, #ffeb3b, #ffc107);
        }
        50% {
            transform: scale(1.2) translateY(-5px) rotate(2deg);
            opacity: 0.9;
            background: linear-gradient(135deg, #ffeb3b, #ffc107);
        }
        100% {
            transform: scale(1) translateY(0) rotate(0deg);
            opacity: 1;
        }
    }

    /* 编辑器焦点增强 */
    .rich-text-editor:focus {
        background-color: #fafafa;
    }

    /* 标记选中状态 */
    .inline-mark-tag:active {
        transform: scale(0.95);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    /* 编辑器内容为空时的提示样式增强 */
    .rich-text-editor[contenteditable="true"]:empty::before {
        color: #999;
        font-style: italic;
        opacity: 0.7;
    }
</style>
{% endblock %}

{% block content_auth %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h2>{{ title }}</h2>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <form method="post" id="template-form">
                        {{ form.csrf_token }}
                        <input type="hidden" name="redirect_after_submit" value="{{ url_for('template.index') }}">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    {{ form.title.label(class="form-label") }}
                                    <!-- 标记美化预览 -->
                                    <div id="title-preview" class="mark-preview" style="display: none;"></div>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-card-heading"></i>
                                        </span>
                                        {{ form.title(class="form-control", id="title_field", placeholder="请输入模板标题，点击下方标记按钮可插入标记") }}
                                    </div>
                                    {% if form.title.errors %}
                                        {% for error in form.title.errors %}
                                        <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        标记格式：{标记名} | 当前焦点：<span id="title-focus-indicator" class="text-success">点击此处输入标题</span>
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    {{ form.category_id.label(class="form-label") }}
                                    {{ form.category_id(class="form-select") }}
                                    {% if form.category_id.errors %}
                                        {% for error in form.category_id.errors %}
                                        <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="bi bi-tags"></i> 可用标记
                                <small class="text-muted">(点击插入到内容中)</small>
                            </label>
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex flex-wrap gap-2">
                                        {% for mark in marks %}
                                        <button type="button" class="btn btn-sm btn-outline-primary mark-button"
                                                onclick="insertMark('{{ mark.name }}')"
                                                title="插入标记: {{ mark.name }}">
                                            <i class="bi bi-tag"></i> {{ mark.name }}
                                        </button>
                                        {% else %}
                                        <div class="text-muted">
                                            <i class="bi bi-info-circle"></i> 暂无可用标记，请先在标记管理中添加标记
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="bi bi-file-text"></i> {{ form.content.label.text }}
                            </label>
                            <!-- 标记美化预览 -->
                            <div id="content-preview" class="mark-preview" style="display: none;"></div>
                            {{ form.content(class="form-control", id="content", rows="15", style="min-height: 400px; font-family: 'Courier New', monospace;", placeholder="请输入模板内容，点击上方标记按钮可插入标记...") }}
                            {% if form.content.errors %}
                                {% for error in form.content.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                            <small class="form-text text-muted">
                                <i class="bi bi-info-circle"></i>
                                提示：点击上方的标记按钮可以快速插入标记到内容中 | 当前焦点：<span id="content-focus-indicator" class="text-primary">点击此处输入内容</span>
                            </small>
                        </div>
                        
                        <div class="mb-3 form-check">
                            {{ form.status(class="form-check-input") }}
                            {{ form.status.label(class="form-check-label") }}
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="button" id="submit-btn" class="btn btn-primary">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 全局变量
    var currentFocusElement = null;
    var titleField = null;
    var contentField = null;

    // 全局函数定义

    // 更新焦点指示器
    function updateFocusIndicator() {
        if (!titleField || !contentField) return;

        // 移除所有焦点指示
        titleField.classList.remove('current-focus');
        contentField.classList.remove('current-focus');

        // 更新焦点指示文本
        var titleIndicator = document.getElementById('title-focus-indicator');
        var contentIndicator = document.getElementById('content-focus-indicator');

        if (currentFocusElement === titleField) {
            titleField.classList.add('current-focus');
            if (titleIndicator) titleIndicator.textContent = '当前活动 ✓';
            if (contentIndicator) contentIndicator.textContent = '点击此处输入内容';
        } else if (currentFocusElement === contentField) {
            contentField.classList.add('current-focus');
            if (titleIndicator) titleIndicator.textContent = '点击此处输入标题';
            if (contentIndicator) contentIndicator.textContent = '当前活动 ✓';
        }

        // 更新标记按钮的提示文本
        var focusText = currentFocusElement === titleField ? '标题' : '内容';
        document.querySelectorAll('.mark-button').forEach(function(btn) {
            var markName = btn.textContent.trim().replace('🏷️ ', '');
            btn.title = `插入标记 "${markName}" 到${focusText}区域`;
        });
    }

    // 更新标记高亮显示
    function updateMarkHighlight(element) {
        if (!element) return;
        // 使用CSS来高亮显示标记
        var content = element.value;
        if (content.includes('{') && content.includes('}')) {
            element.classList.add('has-marks');
        } else {
            element.classList.remove('has-marks');
        }
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 获取DOM元素
        titleField = document.getElementById('title_field');
        contentField = document.getElementById('content');

        console.log('DOM加载完成，初始化元素:', {
            titleField: titleField,
            contentField: contentField
        });

        // 如果元素不存在，输出错误信息
        if (!titleField) {
            console.error('找不到标题输入框元素 #title_field');
            return;
        }
        if (!contentField) {
            console.error('找不到内容输入框元素 #content');
            return;
        }

        // 初始化完成，开始设置事件监听器
    
    // 页面加载时，保持{标记名}格式不变
    document.addEventListener('DOMContentLoaded', function() {
        // 标记格式保持为{标记名}，不需要转换
        // 如果有其他格式的标记，转换为{标记名}格式
        if (titleField.value) {
            titleField.value = titleField.value.replace(/《([^》]+)》/g, '{$1}');
        }

        // 处理内容中的标记格式
        if (contentField.value) {
            contentField.value = contentField.value.replace(/《([^》]+)》/g, '{$1}');
        }

        // 设置初始焦点为内容编辑器
        currentFocusElement = contentField;

        // 监听标题输入框的焦点事件
        titleField.addEventListener('focus', function() {
            currentFocusElement = titleField;
            updateFocusIndicator();
            console.log('焦点在标题输入框');
        });

        titleField.addEventListener('click', function() {
            currentFocusElement = titleField;
            updateFocusIndicator();
        });

        titleField.addEventListener('input', function() {
            updateMarkHighlight(titleField);
        });

        // 监听内容输入框的焦点事件
        contentField.addEventListener('focus', function() {
            currentFocusElement = contentField;
            updateFocusIndicator();
            console.log('焦点在内容输入框');
        });

        contentField.addEventListener('click', function() {
            currentFocusElement = contentField;
            updateFocusIndicator();
        });

        contentField.addEventListener('input', function() {
            updateMarkHighlight(contentField);
        });

        // 初始化高亮显示
        updateMarkHighlight(titleField);
        updateMarkHighlight(contentField);
        
        // 监听提交按钮点击事件
        document.getElementById('submit-btn').addEventListener('click', function() {
            submitForm();
        });
    });
    
    // 这部分代码已经在上面的DOMContentLoaded中处理了
    
    // 插入标记到当前焦点的输入框
    function insertMark(markName) {
        console.log('insertMark被调用，标记名:', markName);
        console.log('当前元素状态:', {
            titleField: titleField,
            contentField: contentField,
            currentFocusElement: currentFocusElement
        });

        // 如果元素还没有初始化，尝试重新获取
        if (!titleField) {
            titleField = document.getElementById('title_field');
        }
        if (!contentField) {
            contentField = document.getElementById('content');
        }

        if (!titleField || !contentField) {
            console.error('无法找到输入框元素');
            alert('无法找到输入框元素，请刷新页面重试');
            return;
        }

        var markText = '{' + markName + '}';
        var targetField = (currentFocusElement === titleField) ? titleField : contentField;

        console.log('插入标记:', markName, '到:', targetField === titleField ? '标题' : '内容');

        // 获取当前光标位置
        var startPos = targetField.selectionStart || 0;
        var endPos = targetField.selectionEnd || 0;

        // 在光标位置插入标记
        targetField.value =
            targetField.value.substring(0, startPos) +
            markText +
            targetField.value.substring(endPos);

        // 将光标位置设置到插入的标记之后
        var newPos = startPos + markText.length;
        targetField.selectionStart = targetField.selectionEnd = newPos;

        // 保持焦点在目标输入框
        targetField.focus();

        // 触发高亮更新
        if (typeof updateMarkHighlight === 'function') {
            updateMarkHighlight(targetField);
        }
    }
    
    // 提交表单
    function submitForm() {
        // 标记格式已经是{标记名}，不需要转换
        // 如果有其他格式，统一转换为{标记名}
        titleField.value = titleField.value.replace(/《([^》]+)》/g, '{$1}');
        contentField.value = contentField.value.replace(/《([^》]+)》/g, '{$1}');
        
        // 获取表单元素
        var form = document.getElementById('template-form');
        
        // 创建FormData对象
        var formData = new FormData(form);
        
        // 发送AJAX请求
        fetch(form.action || window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-No-AJAX': 'true'
            }
        })
        .then(function(response) {
            if (response.ok) {
                return response.json();
            }
            throw new Error('网络响应不正常');
        })
        .then(function(data) {
            if (data.success) {
                // 提交成功，跳转到模板列表页面
                window.location.href = '{{ url_for("template.index") }}';
            } else {
                // 提交失败，显示错误信息
                alert('保存失败：' + (data.message || '未知错误'));
            }
        })
        .catch(function(error) {
            console.error('提交表单出错:', error);
            alert('保存失败，请重试');
        });
    }
</script>
{% endblock %} 