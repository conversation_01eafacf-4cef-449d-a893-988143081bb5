<!-- 通用分页组件 -->
{% set filtered_args = {} %}
{% for key, value in request.args.items() %}
    {% if key not in ['page', 'per_page'] %}
        {% set _ = filtered_args.update({key: value}) %}
    {% endif %}
{% endfor %}

<div class="d-flex justify-content-between align-items-center mt-4">
    <!-- 每页显示数量选择 -->
    <div class="d-flex align-items-center">
        <span class="me-2">每页显示：</span>
        <select class="form-select form-select-sm" style="width: auto;" onchange="changePageSize(this.value, event)">
            <option value="10" {% if per_page == 10 %}selected{% endif %}>10条</option>
            <option value="20" {% if per_page == 20 %}selected{% endif %}>20条</option>
            <option value="30" {% if per_page == 30 %}selected{% endif %}>30条</option>
            <option value="50" {% if per_page == 50 %}selected{% endif %}>50条</option>
            <option value="80" {% if per_page == 80 %}selected{% endif %}>80条</option>
            <option value="100" {% if per_page == 100 %}selected{% endif %}>100条</option>
        </select>
        <span class="ms-3 text-muted">
            共 {{ pagination.total }} 条记录，第 {{ pagination.page }} / {{ pagination.pages }} 页
        </span>
    </div>

    <!-- 分页导航 -->
    <nav aria-label="分页导航">
        <ul class="pagination pagination-sm mb-0">
            <!-- 首页 -->
            <li class="page-item {% if not pagination.has_prev %}disabled{% endif %}">
                <a class="page-link" href="{% if pagination.has_prev %}{{ url_for(request.endpoint, page=1, per_page=per_page, **filtered_args) }}{% else %}#{% endif %}"
                   onclick="{% if pagination.has_prev %}event.preventDefault(); changePage(1, event); return false;{% else %}return false;{% endif %}">
                    <i class="bi bi-chevron-double-left"></i>
                </a>
            </li>

            <!-- 上一页 -->
            <li class="page-item {% if not pagination.has_prev %}disabled{% endif %}">
                <a class="page-link" href="{% if pagination.has_prev %}{{ url_for(request.endpoint, page=pagination.prev_num, per_page=per_page, **filtered_args) }}{% else %}#{% endif %}"
                   onclick="{% if pagination.has_prev %}event.preventDefault(); changePage({{ pagination.prev_num }}, event); return false;{% else %}return false;{% endif %}">
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>

            <!-- 页码 -->
            {% for page_num in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                {% if page_num %}
                    {% if page_num != pagination.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for(request.endpoint, page=page_num, per_page=per_page, **filtered_args) }}"
                               onclick="event.preventDefault(); changePage({{ page_num }}, event); return false;">{{ page_num }}</a>
                        </li>
                    {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                    {% endif %}
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                {% endif %}
            {% endfor %}

            <!-- 下一页 -->
            <li class="page-item {% if not pagination.has_next %}disabled{% endif %}">
                <a class="page-link" href="{% if pagination.has_next %}{{ url_for(request.endpoint, page=pagination.next_num, per_page=per_page, **filtered_args) }}{% else %}#{% endif %}"
                   onclick="{% if pagination.has_next %}event.preventDefault(); changePage({{ pagination.next_num }}, event); return false;{% else %}return false;{% endif %}">
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>

            <!-- 末页 -->
            <li class="page-item {% if not pagination.has_next %}disabled{% endif %}">
                <a class="page-link" href="{% if pagination.has_next %}{{ url_for(request.endpoint, page=pagination.pages, per_page=per_page, **filtered_args) }}{% else %}#{% endif %}"
                   onclick="{% if pagination.has_next %}event.preventDefault(); changePage({{ pagination.pages }}, event); return false;{% else %}return false;{% endif %}">
                    <i class="bi bi-chevron-double-right"></i>
                </a>
            </li>
        </ul>
    </nav>
</div>

<!-- 分页JavaScript函数 -->
<script>
// 检查是否在模态框中
function isInModal() {
    // 检查当前元素是否在模态框内
    const currentScript = document.currentScript || document.querySelector('script:last-of-type');
    if (currentScript) {
        const modal = currentScript.closest('.modal');
        if (modal) return true;
    }

    // 检查是否有显示的模态框
    const visibleModal = document.querySelector('.modal.show');
    if (visibleModal) return true;

    // 检查分页组件是否在模态框内容中
    const paginationElements = document.querySelectorAll('.pagination, .form-select');
    for (let element of paginationElements) {
        if (element.closest('.modal')) {
            return true;
        }
    }

    return false;
}

// 改变页面大小
function changePageSize(newSize, evt) {
    console.log('changePageSize called with:', newSize);

    // 检查当前元素是否在模态框内
    const selectElement = evt?.target || event?.target || document.querySelector('.form-select');
    const isInModalElement = selectElement && selectElement.closest('.modal');

    console.log('isInModalElement:', !!isInModalElement);
    console.log('window.currentModalAjaxHandler:', !!window.currentModalAjaxHandler);

    // 如果在模态框中且有AJAX处理器
    if (isInModalElement && window.currentModalAjaxHandler && typeof window.currentModalAjaxHandler === 'function') {
        console.log('使用模态框AJAX处理器');
        window.currentModalAjaxHandler(1, newSize);
        return;
    }

    // 如果在模态框中但没有AJAX处理器
    if (isInModalElement) {
        console.log('在模态框中，但没有AJAX处理器');
        alert('模态框分页处理器未设置');
        return;
    }

    // 如果在简化版本页面中
    if (window.changePageForSimple && typeof window.changePageForSimple === 'function') {
        console.log('使用简化版本页面处理器');
        window.changePageForSimple(1, newSize);
    } else {
        // 在普通页面中，使用页面跳转
        console.log('使用页面跳转');
        const url = new URL(window.location);
        url.searchParams.set('per_page', newSize);
        url.searchParams.set('page', '1'); // 重置到第一页
        window.location.href = url.toString();
    }
}

// 改变页码
function changePage(pageNum, evt) {
    console.log('changePage called with:', pageNum);

    // 检查当前元素是否在模态框内
    const paginationElement = evt?.target || event?.target || document.querySelector('.pagination a, .pagination button');
    const isInModalElement = paginationElement && paginationElement.closest('.modal');

    console.log('isInModalElement:', !!isInModalElement);
    console.log('window.currentModalAjaxHandler:', !!window.currentModalAjaxHandler);

    // 如果在模态框中且有AJAX处理器
    if (isInModalElement && window.currentModalAjaxHandler && typeof window.currentModalAjaxHandler === 'function') {
        console.log('使用模态框AJAX处理器');
        const currentPerPage = document.querySelector('.form-select').value || 20;
        window.currentModalAjaxHandler(pageNum, currentPerPage);
        return;
    }

    // 如果在模态框中但没有AJAX处理器
    if (isInModalElement) {
        console.log('在模态框中，但没有AJAX处理器');
        alert('模态框分页处理器未设置');
        return;
    }

    // 如果在简化版本页面中
    if (window.changePageForSimple && typeof window.changePageForSimple === 'function') {
        console.log('使用简化版本页面处理器');
        const currentPerPage = document.querySelector('.form-select').value || 20;
        window.changePageForSimple(pageNum, currentPerPage);
    } else {
        // 在普通页面中，使用页面跳转
        console.log('使用页面跳转');
        const url = new URL(window.location);
        url.searchParams.set('page', pageNum);
        window.location.href = url.toString();
    }
}


</script>


