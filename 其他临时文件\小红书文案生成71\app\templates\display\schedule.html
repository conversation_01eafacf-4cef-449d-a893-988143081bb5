{% extends "base.html" %}

{% block title %}安排展示计划{% endblock %}

{% block page_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">安排展示计划 - {{ client.name }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('display.rules') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回规则列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> 展示计划信息：</h5>
                        <ul>
                            <li>每日展示数量：{{ client.daily_content_count }}</li>
                            <li>展示开始时间：{{ client.display_start_time.strftime('%H:%M') if client.display_start_time else '09:00' }}</li>
                            <li>可用文案数量：{{ available_count }}</li>
                        </ul>
                    </div>
                    
                    <form method="post">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        
                        <div class="mb-3">
                            <label class="form-label">选择展示日期</label>
                            <input type="date" name="display_date" class="form-control" required>
                            <small class="form-text text-muted">选择要安排展示的日期</small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">安排方式</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="auto_schedule" id="autoScheduleTrue" value="true" checked>
                                <label class="form-check-label" for="autoScheduleTrue">
                                    自动安排（系统将根据设置的排序规则自动选择文案和时间）
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="auto_schedule" id="autoScheduleFalse" value="false">
                                <label class="form-check-label" for="autoScheduleFalse">
                                    手动安排（自己选择文案和时间）
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-calendar-plus"></i> 下一步
                            </button>
                            <a href="{{ url_for('display.rules') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 