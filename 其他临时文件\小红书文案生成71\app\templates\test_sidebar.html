{% extends "base_new.html" %}

{% block title %}测试左侧菜单{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">测试页面</li>
{% endblock %}

{% block content_auth %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-test me-2"></i>左侧菜单测试页面
                    </h5>
                </div>
                <div class="card-body">
                    <h6>功能特点：</h6>
                    <ul>
                        <li>✅ 左侧固定菜单栏</li>
                        <li>✅ 基于用户权限的动态菜单</li>
                        <li>✅ 可折叠菜单（点击左上角按钮）</li>
                        <li>✅ 响应式设计（移动端适配）</li>
                        <li>✅ 面包屑导航</li>
                        <li>✅ 通知徽章</li>
                        <li>✅ 用户信息显示</li>
                    </ul>
                    
                    <h6 class="mt-4">测试功能：</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <button class="btn btn-primary" onclick="testBreadcrumb()">
                                <i class="fas fa-route me-2"></i>测试面包屑
                            </button>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-warning" onclick="testNotification()">
                                <i class="fas fa-bell me-2"></i>测试通知徽章
                            </button>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-info" onclick="testLoading()">
                                <i class="fas fa-spinner me-2"></i>测试加载状态
                            </button>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-info-circle me-2"></i>使用说明：</h6>
                        <p class="mb-0">
                            这是新的左侧菜单布局。菜单项根据用户权限动态显示，
                            支持桌面端和移动端，具有现代化的设计风格。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
function testBreadcrumb() {
    if (window.sidebarUtils) {
        window.sidebarUtils.setBreadcrumb([
            { name: '测试模块', url: '/test' },
            { name: '子页面', url: '/test/sub' },
            { name: '当前页面' }
        ]);
    }
}

function testNotification() {
    if (window.sidebarUtils) {
        const count = Math.floor(Math.random() * 10) + 1;
        window.sidebarUtils.updateNotificationBadge(count);
    }
}

function testLoading() {
    if (window.sidebarUtils) {
        window.sidebarUtils.showLoading();
        setTimeout(() => {
            window.sidebarUtils.hideLoading();
        }, 2000);
    }
}
</script>
{% endblock %}
