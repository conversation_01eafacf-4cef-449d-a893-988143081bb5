-- MySQL dump 10.13  Distrib 5.7.26, for Win64 (x86_64)
--
-- Host: localhost    Database: xhsrw666
-- ------------------------------------------------------
-- Server version	5.7.26

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `alembic_version`
--

DROP TABLE IF EXISTS `alembic_version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `alembic_version` (
  `version_num` varchar(32) NOT NULL,
  PRIMARY KEY (`version_num`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `alembic_version`
--

LOCK TABLES `alembic_version` WRITE;
/*!40000 ALTER TABLE `alembic_version` DISABLE KEYS */;
INSERT INTO `alembic_version` VALUES ('f53868527c9d');
/*!40000 ALTER TABLE `alembic_version` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `batches`
--

DROP TABLE IF EXISTS `batches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `batches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `content_count` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `allow_template_duplicate` tinyint(1) DEFAULT '0' COMMENT '是否允许在当前批次中重复使用模板',
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `batches_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`),
  CONSTRAINT `batches_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `batches`
--

LOCK TABLES `batches` WRITE;
/*!40000 ALTER TABLE `batches` DISABLE KEYS */;
INSERT INTO `batches` VALUES (4,4,'批次 1',1,'2025-07-14 23:16:25',1,0),(5,4,'批次 2',1,'2025-07-14 23:29:00',1,0),(7,6,'批次 1',1,'2025-07-15 01:03:46',1,0),(8,6,'批次 2',4,'2025-07-15 10:39:20',1,0),(9,6,'批次 3',2,'2025-07-15 10:45:17',1,0),(10,6,'批次 4',3,'2025-07-15 10:56:01',1,0),(11,6,'批次 5',3,'2025-07-15 10:57:08',1,0),(12,6,'批次 6',4,'2025-07-15 11:07:47',1,0),(13,6,'批次 7',1,'2025-07-15 12:19:55',1,0),(14,6,'批次 8',1,'2025-07-15 12:27:17',1,0),(15,6,'批次 9',4,'2025-07-15 12:42:30',1,0),(16,6,'批次 10',1,'2025-07-15 12:44:20',1,0),(17,6,'批次 11',4,'2025-07-15 13:08:15',1,0),(18,6,'批次 12',1,'2025-07-15 13:14:52',1,0),(19,6,'批次 13',1,'2025-07-15 13:15:54',1,0),(20,6,'批次 14',1,'2025-07-15 13:23:18',1,0),(21,6,'批次 15',2,'2025-07-15 13:29:22',1,0),(22,6,'批次 15',2,'2025-07-15 13:29:22',1,0),(23,6,'批次 17',1,'2025-07-15 14:31:55',1,0),(24,6,'批次 18',3,'2025-07-15 14:33:23',1,0),(25,6,'批次 18',3,'2025-07-15 14:33:23',1,0),(26,6,'批次 20',5,'2025-07-15 14:50:07',1,0),(27,6,'批次 20',5,'2025-07-15 14:50:08',1,0),(28,6,'批次 22',5,'2025-07-15 14:59:31',1,0),(29,6,'批次 23',5,'2025-07-15 15:13:22',1,0),(30,6,'批次 24',5,'2025-07-15 15:21:47',1,0),(31,7,'批次 1',1,'2025-07-21 02:34:44',1,0),(32,7,'批次 2',1,'2025-07-21 02:52:43',1,0),(33,7,'批次 3',1,'2025-07-21 02:58:42',1,0),(34,7,'批次 4',5,'2025-07-21 03:13:49',1,0),(35,7,'批次 5',1,'2025-07-21 03:18:55',1,0);
/*!40000 ALTER TABLE `batches` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `client_shares`
--

DROP TABLE IF EXISTS `client_shares`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_shares` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `access_token` varchar(100) NOT NULL,
  `password` varchar(20) DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `view_permission` tinyint(1) DEFAULT '1',
  `edit_permission` tinyint(1) DEFAULT '1',
  `review_permission` tinyint(1) DEFAULT '1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `access_token` (`access_token`),
  KEY `client_id` (`client_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `client_shares_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`),
  CONSTRAINT `client_shares_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_shares`
--

LOCK TABLES `client_shares` WRITE;
/*!40000 ALTER TABLE `client_shares` DISABLE KEYS */;
INSERT INTO `client_shares` VALUES (1,3,'84a2d30ddb8c48ceb10a6a8a6abcc4d8','12b646',NULL,1,1,1,'2025-07-14 02:24:04',1);
/*!40000 ALTER TABLE `client_shares` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `clients`
--

DROP TABLE IF EXISTS `clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `clients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `contact` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `need_review` tinyint(1) DEFAULT '1',
  `daily_content_count` int(11) DEFAULT '5',
  `display_start_time` time DEFAULT NULL,
  `interval_min` int(11) DEFAULT '30',
  `interval_max` int(11) DEFAULT '120',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` tinyint(1) DEFAULT '1',
  `ext_json` text,
  `allow_template_duplicate` tinyint(1) DEFAULT '0' COMMENT '是否允许在当前客户的所有任务中重复使用模板',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `clients`
--

LOCK TABLES `clients` WRITE;
/*!40000 ALTER TABLE `clients` DISABLE KEYS */;
INSERT INTO `clients` VALUES (1,'康师傅','','','',1,5,'08:30:00',10,30,'2025-07-14 02:17:45','2025-07-19 17:26:24',1,NULL,0),(3,'蜜雪冰城1','','','',1,5,'08:30:00',10,30,'2025-07-14 02:23:55','2025-07-20 19:02:21',1,NULL,0),(4,'许府牛','','','',1,5,NULL,10,30,'2025-07-15 23:13:33','2025-07-20 23:27:16',1,NULL,0),(6,'娃哈哈','饿','','',1,5,'08:30:00',10,30,'2025-07-15 23:19:13','2025-07-20 23:14:58',1,'{\"remark\": \"\\u997f\"}',0),(7,'东方宾馆','','','',1,5,'08:30:00',10,30,'2025-07-16 03:50:27','2025-07-17 00:11:34',1,NULL,0),(8,'海底捞','','','',1,5,'08:30:00',10,30,'2025-07-16 23:24:50','2025-07-20 19:02:22',1,NULL,0),(10,'地方各方','','','',1,5,'08:30:00',10,30,'2025-07-19 16:46:47','2025-07-19 16:46:47',1,NULL,0);
/*!40000 ALTER TABLE `clients` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `content_history`
--

DROP TABLE IF EXISTS `content_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `content_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `content` text NOT NULL,
  `editor_id` int(11) DEFAULT NULL,
  `edit_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `is_client_edit` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `content_id` (`content_id`),
  KEY `editor_id` (`editor_id`),
  CONSTRAINT `content_history_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`),
  CONSTRAINT `content_history_ibfk_2` FOREIGN KEY (`editor_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `content_history`
--

LOCK TABLES `content_history` WRITE;
/*!40000 ALTER TABLE `content_history` DISABLE KEYS */;
/*!40000 ALTER TABLE `content_history` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `contents`
--

DROP TABLE IF EXISTS `contents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `contents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) DEFAULT NULL,
  `task_id` int(11) DEFAULT NULL,
  `batch_id` int(11) DEFAULT NULL,
  `template_id` int(11) DEFAULT NULL,
  `title` varchar(200) NOT NULL,
  `content` text NOT NULL,
  `topics` text,
  `location` varchar(100) DEFAULT NULL,
  `image_urls` text,
  `display_date` date DEFAULT NULL,
  `display_time` time DEFAULT NULL,
  `workflow_status` varchar(30) DEFAULT 'draft',
  `publish_status` varchar(30) DEFAULT 'unpublished',
  `client_review_status` varchar(20) DEFAULT 'pending',
  `internal_review_status` varchar(20) DEFAULT 'pending',
  `publish_priority` varchar(10) DEFAULT 'normal',
  `publish_time` datetime DEFAULT NULL,
  `status_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `reviewer_id` int(11) DEFAULT NULL,
  `review_time` datetime DEFAULT NULL,
  `ext_json` text,
  `is_deleted` tinyint(1) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `client_id` (`client_id`),
  KEY `task_id` (`task_id`),
  KEY `batch_id` (`batch_id`),
  KEY `template_id` (`template_id`),
  KEY `created_by` (`created_by`),
  KEY `reviewer_id` (`reviewer_id`),
  KEY `ix_contents_workflow_status` (`workflow_status`),
  KEY `ix_contents_publish_status` (`publish_status`),
  KEY `ix_contents_publish_priority` (`publish_priority`),
  KEY `ix_contents_is_deleted` (`is_deleted`),
  KEY `deleted_by` (`deleted_by`),
  CONSTRAINT `contents_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`),
  CONSTRAINT `contents_ibfk_2` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`),
  CONSTRAINT `contents_ibfk_3` FOREIGN KEY (`batch_id`) REFERENCES `batches` (`id`),
  CONSTRAINT `contents_ibfk_4` FOREIGN KEY (`template_id`) REFERENCES `templates` (`id`),
  CONSTRAINT `contents_ibfk_5` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `contents_ibfk_6` FOREIGN KEY (`reviewer_id`) REFERENCES `users` (`id`),
  CONSTRAINT `contents_ibfk_7` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `contents`
--

LOCK TABLES `contents` WRITE;
/*!40000 ALTER TABLE `contents` DISABLE KEYS */;
INSERT INTO `contents` VALUES (1,1,7,31,96,'早餐新选择！{品牌名称}{商品名称}开启元气一天☀️','? {店铺地址}\r\n早起就是为了这口{商品名称}！现点现做，酥脆爆浆，搭配咖啡绝绝子～打工人早餐首选 #早餐推荐 #美食日记',NULL,NULL,NULL,NULL,NULL,'draft','unpublished','pending','pending','normal',NULL,'2025-07-21 02:34:44','2025-07-21 02:34:44','2025-07-21 02:34:44',1,NULL,NULL,NULL,0,NULL,NULL),(2,1,7,32,95,'本地人才知道的隐藏美味！{品牌名称}{商品名称}?','?{店铺地址}\r\n同事强烈安利的{商品名称}，果然没让我失望！独家秘制配方，吃完还想打包十份带走！ #地方美食 #必吃清单',NULL,NULL,NULL,NULL,NULL,'draft','unpublished','pending','pending','normal',NULL,'2025-07-21 02:52:43','2025-07-21 02:52:43','2025-07-21 02:52:43',1,NULL,NULL,NULL,0,NULL,NULL),(3,1,7,33,95,'本地人才知道的隐藏美味！eeee?','?444\r\n同事强烈安利的ee，果然没让我失望！独家秘制配方，吃完还想打包十份带走！ #地方美食 #必吃清单',NULL,NULL,NULL,NULL,NULL,'draft','unpublished','pending','pending','normal',NULL,'2025-07-21 02:58:42','2025-07-21 02:58:42','2025-07-21 02:58:42',1,NULL,NULL,'{\"mark_replacements\": {\"\\u5e97\\u94fa\\u5730\\u5740\": \"444\", \"\\u54c1\\u724c\\u540d\\u79f0\": \"ee\", \"\\u5546\\u54c1\\u540d\\u79f0\": \"ee\"}}',0,NULL,NULL),(4,1,7,34,102,'节日限定?111333错过等一年！','? 555\r\n圣诞季必吃的限定款333！节日氛围拉满，味道更是惊喜～建议提前预约哦 #季节限定 #节日美食',NULL,NULL,NULL,NULL,NULL,'draft','unpublished','pending','pending','normal',NULL,'2025-07-21 03:13:49','2025-07-21 03:13:49','2025-07-21 03:13:49',1,NULL,NULL,'{\"mark_replacements\": {\"\\u54c1\\u724c\\u540d\\u79f0\": \"111\", \"\\u5e97\\u94fa\\u5730\\u5740\": \"555\", \"\\u5546\\u54c1\\u540d\\u79f0\": \"333\"}}',0,NULL,NULL),(5,1,7,34,99,'打工人午餐救星！11122215分钟上菜⚡','? 222\r\n工作日中午的快乐源泉～222量大实惠，上菜速度感人！已经连续吃一周了 #工作餐推荐 #快餐美食',NULL,NULL,NULL,NULL,NULL,'draft','unpublished','pending','pending','normal',NULL,'2025-07-21 03:13:49','2025-07-21 03:13:49','2025-07-21 03:13:49',1,NULL,NULL,'{\"mark_replacements\": {\"\\u54c1\\u724c\\u540d\\u79f0\": \"111\", \"\\u5e97\\u94fa\\u5730\\u5740\": \"222\", \"\\u5546\\u54c1\\u540d\\u79f0\": \"222\"}}',0,NULL,NULL),(6,1,7,34,97,'深夜放毒时间到！444222太罪恶了?','? 555\r\n半夜饿到不行发现444还营业！222热乎乎的超治愈，减肥什么的明天再说啦～ #夜宵美食 #深夜食堂',NULL,NULL,NULL,NULL,NULL,'draft','unpublished','pending','pending','normal',NULL,'2025-07-21 03:13:49','2025-07-21 03:13:49','2025-07-21 03:13:49',1,NULL,NULL,'{\"mark_replacements\": {\"\\u54c1\\u724c\\u540d\\u79f0\": \"444\", \"\\u5e97\\u94fa\\u5730\\u5740\": \"555\", \"\\u5546\\u54c1\\u540d\\u79f0\": \"222\"}}',0,NULL,NULL),(7,1,7,34,100,'家庭聚餐就选这！111222老少皆宜????','? 111\r\n带全家来吃111，222获得一致好评！爸妈都说下次还要来～ #家庭餐厅 #聚餐推荐',NULL,NULL,NULL,NULL,NULL,'draft','unpublished','pending','pending','normal',NULL,'2025-07-21 03:13:49','2025-07-21 03:13:49','2025-07-21 03:13:49',1,NULL,NULL,'{\"mark_replacements\": {\"\\u54c1\\u724c\\u540d\\u79f0\": \"111\", \"\\u5e97\\u94fa\\u5730\\u5740\": \"111\", \"\\u5546\\u54c1\\u540d\\u79f0\": \"222\"}}',0,NULL,NULL),(8,1,7,34,93,'111必点单品！111一口就爱上?','?333\r\n今天终于吃到111的招牌111，口感绝了！外脆里嫩，酱汁是灵魂～吃完立刻安利给闺蜜? #美食打卡 #吃货日常',NULL,NULL,NULL,NULL,NULL,'draft','unpublished','pending','pending','normal',NULL,'2025-07-21 03:13:49','2025-07-21 03:13:49','2025-07-21 03:13:49',1,NULL,NULL,'{\"mark_replacements\": {\"\\u54c1\\u724c\\u540d\\u79f0\": \"111\", \"\\u5e97\\u94fa\\u5730\\u5740\": \"333\", \"\\u5546\\u54c1\\u540d\\u79f0\": \"111\"}}',0,NULL,NULL),(9,1,7,35,93,'555必点单品！333一口就爱上?','?111\r\n今天终于吃到555的招牌333，口感绝了！外脆里嫩，酱汁是灵魂～吃完立刻安利给闺蜜? #美食打卡 #吃货日常',NULL,NULL,NULL,NULL,NULL,'draft','unpublished','pending','pending','normal',NULL,'2025-07-21 03:18:55','2025-07-21 03:18:55','2025-07-21 03:18:55',1,NULL,NULL,'{\"mark_replacements\": {\"\\u5546\\u54c1\\u540d\\u79f0\": \"333\", \"\\u54c1\\u724c\\u540d\\u79f0\": \"555\", \"\\u5e97\\u94fa\\u5730\\u5740\": \"111\"}}',0,NULL,NULL);
/*!40000 ALTER TABLE `contents` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `display_schedules`
--

DROP TABLE IF EXISTS `display_schedules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `display_schedules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `display_date` date NOT NULL,
  `display_time` time NOT NULL,
  `is_fixed_time` tinyint(1) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `display_order` int(11) DEFAULT NULL,
  `actual_display_time` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `client_id` (`client_id`),
  KEY `content_id` (`content_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `display_schedules`
--

LOCK TABLES `display_schedules` WRITE;
/*!40000 ALTER TABLE `display_schedules` DISABLE KEYS */;
/*!40000 ALTER TABLE `display_schedules` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `display_settings`
--

DROP TABLE IF EXISTS `display_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `display_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `order_type` varchar(20) DEFAULT NULL,
  `custom_order` text,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `client_id` (`client_id`),
  KEY `updated_by` (`updated_by`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `display_settings`
--

LOCK TABLES `display_settings` WRITE;
/*!40000 ALTER TABLE `display_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `display_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `type` varchar(30) NOT NULL,
  `related_content_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `is_read` tinyint(1) DEFAULT '0',
  `recipient_id` int(11) NOT NULL,
  `priority` varchar(10) DEFAULT 'normal',
  PRIMARY KEY (`id`),
  KEY `related_content_id` (`related_content_id`),
  KEY `recipient_id` (`recipient_id`),
  KEY `ix_notifications_created_at` (`created_at`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`related_content_id`) REFERENCES `contents` (`id`),
  CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`recipient_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications`
--

LOCK TABLES `notifications` WRITE;
/*!40000 ALTER TABLE `notifications` DISABLE KEYS */;
/*!40000 ALTER TABLE `notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=63 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES (1,'user.view','查看用户'),(2,'user.manage','管理用户'),(3,'client.view','查看客户'),(4,'client.manage','管理客户'),(5,'template.view','查看模板'),(6,'template.manage','管理模板'),(7,'content.view','查看文案'),(8,'content.manage','管理文案'),(9,'review.first','初审管理'),(10,'image.view','查看图片'),(11,'image.manage','管理图片'),(12,'review.final','终审管理'),(13,'publish.view','查看发布'),(14,'publish.manage','管理发布'),(15,'system.settings','系统设置'),(16,'notification.view','查看通知'),(17,'notification.manage','管理通知'),(48,'dashboard_access','控制面板'),(49,'user_manage','用户管理'),(50,'client_manage','客户管理'),(51,'template_manage','模板管理'),(52,'content_manage','文案管理'),(53,'content_generate','生成文案'),(54,'topic_manage','话题管理'),(55,'task_manage','任务管理'),(56,'publish_manage','发布管理'),(57,'supplement_manage','文案补充'),(58,'display_manage','文案展示'),(59,'notification_manage','通知中心'),(60,'stats_view','数据统计'),(61,'export_manage','导入导出'),(62,'system_settings','系统设置');
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions_backup`
--

DROP TABLE IF EXISTS `permissions_backup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `permissions_backup` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions_backup`
--

LOCK TABLES `permissions_backup` WRITE;
/*!40000 ALTER TABLE `permissions_backup` DISABLE KEYS */;
INSERT INTO `permissions_backup` VALUES (1,'查看用户列表','查看系统用户列表'),(2,'新增用户','创建新的系统用户'),(3,'编辑用户','编辑系统用户信息'),(4,'删除用户','删除系统用户'),(5,'分配用户角色','为用户分配系统角色'),(6,'查看客户列表','查看所有客户'),(7,'新增客户','添加新客户'),(8,'编辑客户','编辑客户信息'),(9,'删除客户','删除客户'),(10,'创建客户分享链接','为客户创建分享链接'),(11,'管理客户审核权限','管理客户的审核权限'),(12,'查看模板列表','查看所有模板'),(13,'新增模板','创建新的模板'),(14,'编辑模板','编辑现有模板'),(15,'删除模板','删除模板'),(16,'管理模板分类','管理模板分类'),(17,'查看文案列表','查看所有文案'),(18,'新建文案任务','创建新的文案生成任务'),(19,'批量生成文案','批量生成文案内容'),(20,'编辑文案','编辑文案内容'),(21,'预览文案','预览文案效果'),(22,'删除文案','删除文案'),(23,'查看待初审文案','查看待初审的文案列表'),(24,'初审通过','将文案标记为初审通过'),(25,'初审编辑','在初审阶段编辑文案'),(26,'初审驳回','驳回初审文案'),(27,'批量初审操作','批量处理初审文案'),(28,'查看待上传图片文案','查看需要上传图片的文案'),(29,'上传图片','为文案上传图片'),(30,'编辑图片','编辑文案图片'),(31,'删除图片','删除文案图片'),(32,'提交图片审核','提交图片进入下一审核流程'),(33,'查看待终审文案','查看待终审的文案'),(34,'终审通过','将文案标记为终审通过'),(35,'终审编辑','在终审阶段编辑文案'),(36,'终审驳回','驳回终审文案'),(37,'批量终审操作','批量处理终审文案'),(38,'查看待发布文案','查看待发布的文案'),(39,'设置发布优先级','设置文案发布优先级'),(40,'查看发布状态','查看文案发布状态'),(41,'手动更新发布状态','手动更新文案发布状态'),(42,'发布失败处理','处理发布失败的文案'),(43,'查看系统设置','查看系统配置选项'),(44,'修改系统配置','修改系统配置选项'),(45,'管理审核流程','管理文案审核流程'),(46,'管理快捷理由','管理拒绝快捷理由'),(47,'查看系统日志','查看系统操作日志'),(48,'user.view','查看用户'),(49,'user.create','创建用户'),(50,'user.edit','编辑用户'),(51,'user.delete','删除用户'),(52,'user.manage','管理用户'),(53,'client.view','查看客户'),(54,'client.create','创建客户'),(55,'client.edit','编辑客户'),(56,'client.delete','删除客户'),(57,'client.manage','管理客户'),(58,'template.view','查看模板'),(59,'template.create','创建模板'),(60,'template.edit','编辑模板'),(61,'template.delete','删除模板'),(62,'template.manage','管理模板'),(63,'content.view','查看文案'),(64,'content.create','创建文案'),(65,'content.edit','编辑文案'),(66,'content.delete','删除文案'),(67,'content.manage','管理文案'),(68,'review.first.view','查看初审列表'),(69,'review.first.approve','初审通过'),(70,'review.first.reject','初审驳回'),(71,'review.first.manage','管理初审(包含所有初审相关权限)'),(72,'image.view','查看图片'),(73,'image.upload','上传图片'),(74,'image.edit','编辑图片'),(75,'image.delete','删除图片'),(76,'image.manage','管理图片'),(77,'review.final.view','查看终审列表'),(78,'review.final.approve','终审通过'),(79,'review.final.reject','终审驳回'),(80,'review.final.manage','管理终审(包含所有终审相关权限)'),(81,'publish.view','查看发布'),(82,'publish.approve','发布文案'),(83,'publish.manage','管理发布'),(84,'system.view','查看系统设置'),(85,'system.edit','编辑系统设置'),(86,'system.manage','管理系统(包含所有系统相关权限)'),(87,'review.first','初审管理'),(88,'review.final','终审管理'),(89,'system.settings','系统设置'),(90,'notification.view','查看通知'),(91,'notification.manage','管理通知');
/*!40000 ALTER TABLE `permissions_backup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `publish_records`
--

DROP TABLE IF EXISTS `publish_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `publish_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `status` varchar(20) DEFAULT NULL,
  `platform` varchar(50) DEFAULT NULL,
  `account` varchar(100) DEFAULT NULL,
  `publish_url` varchar(255) DEFAULT NULL,
  `publish_time` datetime DEFAULT NULL,
  `error_message` text,
  `ext_info` text,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `content_id` (`content_id`),
  CONSTRAINT `publish_records_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `publish_records`
--

LOCK TABLES `publish_records` WRITE;
/*!40000 ALTER TABLE `publish_records` DISABLE KEYS */;
/*!40000 ALTER TABLE `publish_records` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `publish_timeouts`
--

DROP TABLE IF EXISTS `publish_timeouts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `publish_timeouts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `timeout_minutes` int(11) DEFAULT NULL,
  `action` varchar(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `updated_by` (`updated_by`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `publish_timeouts`
--

LOCK TABLES `publish_timeouts` WRITE;
/*!40000 ALTER TABLE `publish_timeouts` DISABLE KEYS */;
/*!40000 ALTER TABLE `publish_timeouts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `quick_reasons`
--

DROP TABLE IF EXISTS `quick_reasons`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `quick_reasons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content` varchar(200) NOT NULL,
  `sort_order` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `quick_reasons`
--

LOCK TABLES `quick_reasons` WRITE;
/*!40000 ALTER TABLE `quick_reasons` DISABLE KEYS */;
INSERT INTO `quick_reasons` VALUES (1,'内容质量不符合要求',1,'2025-07-13 10:39:29'),(2,'标题不够吸引人',2,'2025-07-13 10:39:29'),(3,'图片质量需要提升',3,'2025-07-13 10:39:29'),(4,'文案长度需要调整',4,'2025-07-13 10:39:29'),(5,'话题标签需要优化',5,'2025-07-13 10:39:29'),(6,'发布时间需要调整',6,'2025-07-13 10:39:29'),(7,'内容重复度过高',7,'2025-07-13 10:39:29'),(8,'品牌露出过多',8,'2025-07-13 10:39:29'),(9,'文案风格需要调整',9,'2025-07-13 10:39:29'),(10,'其他原因',10,'2025-07-13 10:39:29');
/*!40000 ALTER TABLE `quick_reasons` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rejection_reasons`
--

DROP TABLE IF EXISTS `rejection_reasons`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `rejection_reasons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `reason` text NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `is_client` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `content_id` (`content_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `rejection_reasons_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`),
  CONSTRAINT `rejection_reasons_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rejection_reasons`
--

LOCK TABLES `rejection_reasons` WRITE;
/*!40000 ALTER TABLE `rejection_reasons` DISABLE KEYS */;
/*!40000 ALTER TABLE `rejection_reasons` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_permissions`
--

DROP TABLE IF EXISTS `role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `role_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`),
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=72 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permissions`
--

LOCK TABLES `role_permissions` WRITE;
/*!40000 ALTER TABLE `role_permissions` DISABLE KEYS */;
INSERT INTO `role_permissions` VALUES (1,2,1),(2,2,2),(3,2,3),(4,2,4),(5,2,5),(6,2,6),(7,2,7),(8,2,8),(9,2,9),(10,2,10),(11,2,11),(12,2,12),(13,2,13),(14,2,14),(15,2,15),(16,2,16),(17,2,17),(32,3,7),(33,3,8),(35,7,7),(36,7,10),(37,7,11),(38,4,3),(39,4,4),(41,6,5),(42,6,7),(44,8,7),(45,8,12),(47,9,5),(48,9,6),(50,5,13),(51,5,14),(52,5,16),(53,5,17),(57,2,48),(58,2,49),(59,2,50),(60,2,51),(61,2,52),(62,2,53),(63,2,54),(64,2,55),(65,2,56),(66,2,57),(67,2,58),(68,2,59),(69,2,60),(70,2,61),(71,2,62);
/*!40000 ALTER TABLE `role_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_permissions_backup`
--

DROP TABLE IF EXISTS `role_permissions_backup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `role_permissions_backup` (
  `id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permissions_backup`
--

LOCK TABLES `role_permissions_backup` WRITE;
/*!40000 ALTER TABLE `role_permissions_backup` DISABLE KEYS */;
INSERT INTO `role_permissions_backup` VALUES (186,2,1),(187,2,2),(188,2,3),(189,2,4),(190,2,5),(191,2,6),(192,2,7),(193,2,8),(194,2,9),(195,2,10),(196,2,11),(197,2,12),(198,2,13),(199,2,14),(200,2,15),(201,2,16),(202,2,17),(203,2,18),(204,2,19),(205,2,20),(206,2,21),(207,2,22),(208,2,23),(209,2,24),(210,2,25),(211,2,26),(212,2,27),(213,2,28),(214,2,29),(215,2,30),(216,2,31),(217,2,32),(218,2,33),(219,2,34),(220,2,35),(221,2,36),(222,2,37),(223,2,38),(224,2,39),(225,2,40),(226,2,41),(227,2,42),(228,2,43),(229,2,44),(230,2,45),(231,2,46),(232,2,47),(233,2,48),(234,2,49),(235,2,50),(236,2,51),(237,2,52),(238,2,53),(239,2,54),(240,2,55),(241,2,56),(242,2,57),(243,2,58),(244,2,59),(245,2,60),(246,2,61),(247,2,62),(248,2,63),(249,2,64),(250,2,65),(251,2,66),(252,2,67),(253,2,68),(254,2,69),(255,2,70),(256,2,71),(257,2,72),(258,2,73),(259,2,74),(260,2,75),(261,2,76),(262,2,77),(263,2,78),(264,2,79),(265,2,80),(266,2,81),(267,2,82),(268,2,83),(269,2,84),(270,2,85),(271,2,86),(272,2,87),(273,2,88),(274,2,89),(275,2,90),(276,2,91),(277,3,63),(278,3,67),(279,7,63),(280,7,72),(281,7,76),(282,4,53),(283,4,57),(284,6,63),(285,6,58),(286,8,63),(287,8,88),(288,9,58),(289,9,62),(290,5,81),(291,5,83),(292,5,90),(293,5,91);
/*!40000 ALTER TABLE `role_permissions_backup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES (2,'超级管理员','系统超级管理员，拥有所有权限','2025-07-13 02:49:12'),(3,'内容编辑','负责编辑文案内容','2025-07-13 10:39:29'),(4,'客户经理','负责管理客户','2025-07-13 10:39:29'),(5,'运营专员','负责内容发布和运营','2025-07-13 10:39:29'),(6,'普通用户','普通系统用户','2025-07-13 10:39:29'),(7,'图文编辑','负责管理图片和文案','2025-07-15 15:55:41'),(8,'最终审核员','负责内容终审','2025-07-15 15:55:41'),(9,'模板管理员','负责管理模板','2025-07-15 15:59:04');
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_settings`
--

DROP TABLE IF EXISTS `system_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(50) NOT NULL,
  `value` text,
  `description` varchar(200) DEFAULT NULL,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `system_settings_ibfk_1` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_settings`
--

LOCK TABLES `system_settings` WRITE;
/*!40000 ALTER TABLE `system_settings` DISABLE KEYS */;
INSERT INTO `system_settings` VALUES (1,'default_content_count','5','默认每日文案数量','2025-07-13 10:39:29',NULL),(2,'review_timeout_hours','24','审核超时时间（小时）','2025-07-13 10:39:29',NULL),(3,'auto_publish_enabled','false','是否启用自动发布','2025-07-13 10:39:29',NULL),(4,'client_share_enabled','true','是否启用客户分享功能','2025-07-13 10:39:29',NULL),(5,'notification_enabled','true','是否启用通知功能','2025-07-13 10:39:29',NULL),(6,'content_backup_enabled','true','是否启用文案备份','2025-07-13 10:39:29',NULL),(7,'max_upload_size','10485760','最大上传文件大小（字节）','2025-07-13 10:39:29',NULL),(8,'allowed_image_types','jpg,jpeg,png,gif','允许的图片类型','2025-07-13 10:39:29',NULL),(9,'default_publish_interval_min','30','默认发布间隔最小值（分钟）','2025-07-13 10:39:29',NULL),(10,'default_publish_interval_max','120','默认发布间隔最大值（分钟）','2025-07-13 10:39:29',NULL);
/*!40000 ALTER TABLE `system_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tasks`
--

DROP TABLE IF EXISTS `tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `status` varchar(20) DEFAULT 'processing',
  `target_count` int(11) DEFAULT '0',
  `actual_count` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `allow_template_duplicate` tinyint(1) DEFAULT '0' COMMENT '是否允许在当前任务中重复使用模板',
  PRIMARY KEY (`id`),
  KEY `client_id` (`client_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `tasks_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`),
  CONSTRAINT `tasks_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tasks`
--

LOCK TABLES `tasks` WRITE;
/*!40000 ALTER TABLE `tasks` DISABLE KEYS */;
INSERT INTO `tasks` VALUES (4,1,'2025年07月14日任务','自动创建于 2025-07-14 23:16','in_progress',1,2,'2025-07-14 23:16:25','2025-07-14 23:29:00',1,0),(6,1,'2025年07月15日任务','自动创建于 2025-07-15 01:03','in_progress',1,67,'2025-07-15 01:03:46','2025-07-15 15:21:47',1,0),(7,1,'2025年07月21日任务','自动创建于 2025-07-21 02:34','in_progress',1,9,'2025-07-21 02:34:44','2025-07-21 03:18:55',1,0);
/*!40000 ALTER TABLE `tasks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `template_categories`
--

DROP TABLE IF EXISTS `template_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `template_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `sort_order` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`),
  CONSTRAINT `template_categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `template_categories` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `template_categories`
--

LOCK TABLES `template_categories` WRITE;
/*!40000 ALTER TABLE `template_categories` DISABLE KEYS */;
INSERT INTO `template_categories` VALUES (1,'美妆护肤',NULL,1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(2,'时尚穿搭',NULL,2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(3,'美食探店',NULL,3,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(4,'旅游攻略',NULL,4,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(5,'生活分享',NULL,5,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(6,'护肤心得',1,1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(7,'彩妆教程',1,2,'2025-07-13 10:39:29','2025-07-17 02:38:25'),(8,'穿搭搭配',2,1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(9,'潮流趋势',2,2,'2025-07-13 10:39:29','2025-07-19 01:25:22'),(10,'餐厅推荐',3,1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(11,'美食制作',3,2,'2025-07-13 10:39:29','2025-07-13 10:39:29');
/*!40000 ALTER TABLE `template_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `template_marks`
--

DROP TABLE IF EXISTS `template_marks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `template_marks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  `type` varchar(20) DEFAULT 'text',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `template_marks`
--

LOCK TABLES `template_marks` WRITE;
/*!40000 ALTER TABLE `template_marks` DISABLE KEYS */;
INSERT INTO `template_marks` VALUES (1,'品牌名称','','text','2025-07-13 17:32:29'),(2,'店铺地址','','text','2025-07-13 17:33:24'),(4,'标记2','','text','2025-07-13 17:34:01'),(5,'标记3','','text','2025-07-13 17:34:05'),(7,'标记5','','text','2025-07-13 17:34:13'),(8,'商品名称','','text','2025-07-13 18:24:44'),(9,'标记6','','text','2025-07-17 02:45:13'),(11,'标记8','','text','2025-07-17 02:59:22');
/*!40000 ALTER TABLE `template_marks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `templates`
--

DROP TABLE IF EXISTS `templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL,
  `title` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `creator_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` tinyint(1) DEFAULT '1',
  `marks` json DEFAULT NULL COMMENT '模板中的标记列表，JSON格式存储',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `creator_id` (`creator_id`),
  CONSTRAINT `templates_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `template_categories` (`id`),
  CONSTRAINT `templates_ibfk_2` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=103 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `templates`
--

LOCK TABLES `templates` WRITE;
/*!40000 ALTER TABLE `templates` DISABLE KEYS */;
INSERT INTO `templates` VALUES (93,11,'{品牌名称}必点单品！{商品名称}一口就爱上?','?{店铺地址}\r\n今天终于吃到{品牌名称}的招牌{商品名称}，口感绝了！外脆里嫩，酱汁是灵魂～吃完立刻安利给闺蜜? #美食打卡 #吃货日常',1,'2025-07-20 17:54:35','2025-07-20 17:54:35',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(94,11,'挖到宝了✨{品牌名称}的{商品名称}也太香了吧！','? {店铺地址}\r\n路过被香味吸引进店，{商品名称}直接封神！分量超足，人均30+吃到撑～姐妹们快冲 #高性价比美食 #周末探店',1,'2025-07-20 17:54:53','2025-07-20 17:54:53',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(95,1,'本地人才知道的隐藏美味！{品牌名称}{商品名称}?','?{店铺地址}\r\n同事强烈安利的{商品名称}，果然没让我失望！独家秘制配方，吃完还想打包十份带走！ #地方美食 #必吃清单',1,'2025-07-20 17:55:05','2025-07-20 17:55:05',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(96,1,'早餐新选择！{品牌名称}{商品名称}开启元气一天☀️','? {店铺地址}\r\n早起就是为了这口{商品名称}！现点现做，酥脆爆浆，搭配咖啡绝绝子～打工人早餐首选 #早餐推荐 #美食日记',1,'2025-07-20 17:55:18','2025-07-20 17:55:18',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(97,11,'深夜放毒时间到！{品牌名称}{商品名称}太罪恶了?','? {店铺地址}\r\n半夜饿到不行发现{品牌名称}还营业！{商品名称}热乎乎的超治愈，减肥什么的明天再说啦～ #夜宵美食 #深夜食堂',1,'2025-07-20 17:55:36','2025-07-20 17:55:36',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(98,11,'闺蜜下午茶首选?{品牌名称}{商品名称}颜值味道双在线！','? {店铺地址}\r\n和姐妹约会的秘密基地！{商品名称}不仅拍照好看，甜度也刚刚好～搭配花茶完美 #高颜值甜品 #闺蜜约会',1,'2025-07-20 17:55:56','2025-07-20 23:27:13',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(99,11,'打工人午餐救星！{品牌名称}{商品名称}15分钟上菜⚡','? {店铺地址}\r\n工作日中午的快乐源泉～{商品名称}量大实惠，上菜速度感人！已经连续吃一周了 #工作餐推荐 #快餐美食',1,'2025-07-20 17:56:12','2025-07-20 23:27:12',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(100,11,'家庭聚餐就选这！{品牌名称}{商品名称}老少皆宜????','? {店铺地址}\r\n带全家来吃{品牌名称}，{商品名称}获得一致好评！爸妈都说下次还要来～ #家庭餐厅 #聚餐推荐',1,'2025-07-20 17:56:32','2025-07-20 17:56:32',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(101,11,'减肥也能吃！{品牌名称}轻食系列{商品名称}太?了','? {店铺地址}\r\n健身教练推荐的{商品名称}，低卡高蛋白，吃完毫无负担！已经加入我的每周必点清单 #健康餐 #减脂美食',1,'2025-07-20 17:56:47','2025-07-20 17:56:47',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(102,11,'节日限定?{品牌名称}{商品名称}错过等一年！','? {店铺地址}\r\n圣诞季必吃的限定款{商品名称}！节日氛围拉满，味道更是惊喜～建议提前预约哦 #季节限定 #节日美食',1,'2025-07-20 17:57:00','2025-07-20 23:05:44',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]');
/*!40000 ALTER TABLE `templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `topic_relations`
--

DROP TABLE IF EXISTS `topic_relations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `topic_relations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `topic_id` int(11) NOT NULL,
  `related_topic_id` int(11) NOT NULL,
  `weight` int(11) DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `topic_id` (`topic_id`),
  KEY `related_topic_id` (`related_topic_id`),
  CONSTRAINT `topic_relations_ibfk_1` FOREIGN KEY (`topic_id`) REFERENCES `topics` (`id`),
  CONSTRAINT `topic_relations_ibfk_2` FOREIGN KEY (`related_topic_id`) REFERENCES `topics` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `topic_relations`
--

LOCK TABLES `topic_relations` WRITE;
/*!40000 ALTER TABLE `topic_relations` DISABLE KEYS */;
/*!40000 ALTER TABLE `topic_relations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `topics`
--

DROP TABLE IF EXISTS `topics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `topics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `type` varchar(20) DEFAULT 'random',
  `priority` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `topics`
--

LOCK TABLES `topics` WRITE;
/*!40000 ALTER TABLE `topics` DISABLE KEYS */;
INSERT INTO `topics` VALUES (1,'#美妆分享','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(2,'#护肤心得','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(3,'#穿搭搭配','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(4,'#美食探店','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(5,'#旅游攻略','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(6,'#生活分享','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(7,'#好物推荐','random',2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(8,'#购物分享','random',2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(9,'#职场穿搭','random',2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(10,'#约会穿搭','random',2,'2025-07-13 10:39:29','2025-07-13 10:39:29');
/*!40000 ALTER TABLE `topics` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_permissions`
--

DROP TABLE IF EXISTS `user_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `user_permissions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `user_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_permissions`
--

LOCK TABLES `user_permissions` WRITE;
/*!40000 ALTER TABLE `user_permissions` DISABLE KEYS */;
INSERT INTO `user_permissions` VALUES (1,1,48),(2,1,49),(3,1,50),(4,1,51),(5,1,52),(6,1,53),(7,1,54),(8,1,55),(9,1,56),(10,1,57),(11,1,58),(12,1,59),(13,1,60),(14,1,61),(15,1,62),(16,7,51),(17,7,51),(18,7,53),(19,7,53);
/*!40000 ALTER TABLE `user_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_permissions_backup`
--

DROP TABLE IF EXISTS `user_permissions_backup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_permissions_backup` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_permissions_backup`
--

LOCK TABLES `user_permissions_backup` WRITE;
/*!40000 ALTER TABLE `user_permissions_backup` DISABLE KEYS */;
INSERT INTO `user_permissions_backup` VALUES (12,7,46),(13,7,29),(14,7,42),(15,7,54),(16,7,47),(17,7,58),(18,7,27),(19,7,53),(20,7,57),(21,7,30),(22,7,48);
/*!40000 ALTER TABLE `user_permissions_backup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_role`
--

DROP TABLE IF EXISTS `user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`),
  KEY `user_id` (`user_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_role`
--

LOCK TABLES `user_role` WRITE;
/*!40000 ALTER TABLE `user_role` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_roles`
--

DROP TABLE IF EXISTS `user_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `user_roles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `user_roles_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_roles`
--

LOCK TABLES `user_roles` WRITE;
/*!40000 ALTER TABLE `user_roles` DISABLE KEYS */;
INSERT INTO `user_roles` VALUES (1,1,2),(2,1,2),(3,2,3),(4,3,7),(5,4,8),(6,5,6),(7,6,5);
/*!40000 ALTER TABLE `user_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `real_name` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `last_login` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_users_username` (`username`),
  UNIQUE KEY `ix_users_email` (`email`),
  KEY `ix_users_is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'admin','scrypt:32768:8:1$jC2NoFyNXqBCgj0W$30873b394b1d325e5c8472c1b79a89b7a5635c99c13893ef02871425f996ce3a04c316eff7fb715eeb7742f225e91bc2178793f675d4ca1f1e4843e682b1c663','<EMAIL>','管理员',NULL,'2025-07-13 02:49:12','2025-07-21 02:22:47',1),(2,'reviewer','scrypt:32768:8:1$jsBrbhGtdX8IxNWH$65485bac3a84d93dd5e73f907766155701906693dfcc46af15a1cc6617851c4fc51f354cf513d2c4b56440347438b4ecdd7e83dadcc198355fc478c28fcd9bcf','<EMAIL>','文案审核员',NULL,'2025-07-15 15:55:41',NULL,1),(3,'editor','scrypt:32768:8:1$jC2NoFyNXqBCgj0W$30873b394b1d325e5c8472c1b79a89b7a5635c99c13893ef02871425f996ce3a04c316eff7fb715eeb7742f225e91bc2178793f675d4ca1f1e4843e682b1c663','<EMAIL>','图文编辑',NULL,'2025-07-15 15:55:41',NULL,1),(4,'final_reviewer','scrypt:32768:8:1$jC2NoFyNXqBCgj0W$30873b394b1d325e5c8472c1b79a89b7a5635c99c13893ef02871425f996ce3a04c316eff7fb715eeb7742f225e91bc2178793f675d4ca1f1e4843e682b1c663','<EMAIL>','最终审核员',NULL,'2025-07-15 15:55:41',NULL,1),(5,'client','scrypt:32768:8:1$jC2NoFyNXqBCgj0W$30873b394b1d325e5c8472c1b79a89b7a5635c99c13893ef02871425f996ce3a04c316eff7fb715eeb7742f225e91bc2178793f675d4ca1f1e4843e682b1c663','<EMAIL>','客户用户',NULL,'2025-07-15 15:55:41',NULL,1),(6,'publisher','scrypt:32768:8:1$jC2NoFyNXqBCgj0W$30873b394b1d325e5c8472c1b79a89b7a5635c99c13893ef02871425f996ce3a04c316eff7fb715eeb7742f225e91bc2178793f675d4ca1f1e4843e682b1c663','<EMAIL>','发布管理员',NULL,'2025-07-15 15:55:41',NULL,1),(7,'template_manager','scrypt:32768:8:1$jC2NoFyNXqBCgj0W$30873b394b1d325e5c8472c1b79a89b7a5635c99c13893ef02871425f996ce3a04c316eff7fb715eeb7742f225e91bc2178793f675d4ca1f1e4843e682b1c663','<EMAIL>','模板管理员','','2025-07-15 15:59:04','2025-07-15 16:02:44',1);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-21  3:44:22
