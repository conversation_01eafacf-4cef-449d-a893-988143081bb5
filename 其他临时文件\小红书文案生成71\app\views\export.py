"""
导入导出功能视图
"""
import os
import csv
import json
import io
import zipfile
import datetime
from flask import Blueprint, render_template, request, jsonify, send_file, current_app, Response, abort, flash, redirect, url_for
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
import pandas as pd

from app.models import db
from app.models.template import Template, TemplateCategory
from app.models.topic import Topic, TopicRelation
from app.models.client import Client
from app.models.content import Content
from app.models.task import Task, Batch
from app.utils.decorators import permission_required, ajax_aware

# 创建蓝图
export_bp = Blueprint('export', __name__)


@export_bp.route('/')
@login_required
@permission_required('admin_access')
@ajax_aware
def index():
    """导入导出功能首页"""
    context = {
        'title': '导入导出 - 小红书文案生成系统'
    }
    return 'export/index.html', context


@export_bp.route('/template/export', methods=['GET'])
@login_required
@permission_required('template_manage')
def export_templates():
    """导出模板数据"""
    format_type = request.args.get('format', 'csv')
    category_id = request.args.get('category_id', type=int)
    
    # 查询模板数据
    query = Template.query.filter_by(status=True)
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    templates = query.all()
    
    if format_type == 'json':
        # 导出为JSON格式
        data = []
        for template in templates:
            category = TemplateCategory.query.get(template.category_id)
            data.append({
                'id': template.id,
                'title': template.title,
                'content': template.content,
                'category': category.name if category else '',
                'category_id': template.category_id,
                'created_at': template.created_at.strftime('%Y-%m-%d %H:%M:%S') if template.created_at else '',
                'updated_at': template.updated_at.strftime('%Y-%m-%d %H:%M:%S') if template.updated_at else '',
                'status': template.status
            })
        
        # 创建响应
        response = Response(
            json.dumps(data, ensure_ascii=False),
            mimetype='application/json',
            headers={'Content-Disposition': 'attachment;filename=templates.json'}
        )
        return response
    
    else:  # 默认CSV格式
        # 创建CSV内存文件
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入表头
        writer.writerow(['ID', '标题', '内容', '分类', '分类ID', '创建时间', '更新时间', '状态'])
        
        # 写入数据
        for template in templates:
            category = TemplateCategory.query.get(template.category_id)
            writer.writerow([
                template.id,
                template.title,
                template.content,
                category.name if category else '',
                template.category_id,
                template.created_at.strftime('%Y-%m-%d %H:%M:%S') if template.created_at else '',
                template.updated_at.strftime('%Y-%m-%d %H:%M:%S') if template.updated_at else '',
                '启用' if template.status else '禁用'
            ])
        
        # 创建响应
        output.seek(0)
        return Response(
            output,
            mimetype='text/csv',
            headers={'Content-Disposition': 'attachment;filename=templates.csv'}
        )


@export_bp.route('/topic/export', methods=['GET'])
@login_required
@permission_required('topic_manage')
def export_topics():
    """导出话题数据"""
    format_type = request.args.get('format', 'csv')
    
    # 查询话题数据
    topics = Topic.query.all()
    
    if format_type == 'json':
        # 导出为JSON格式
        data = []
        for topic in topics:
            # 获取关联话题
            related_topics = []
            relations = TopicRelation.query.filter_by(topic_id=topic.id).all()
            for relation in relations:
                related_topic = Topic.query.get(relation.related_topic_id)
                if related_topic:
                    related_topics.append({
                        'id': related_topic.id,
                        'name': related_topic.name,
                        'weight': relation.weight
                    })
            
            data.append({
                'id': topic.id,
                'name': topic.name,
                'type': topic.type,
                'priority': topic.priority,
                'created_at': topic.created_at.strftime('%Y-%m-%d %H:%M:%S') if topic.created_at else '',
                'updated_at': topic.updated_at.strftime('%Y-%m-%d %H:%M:%S') if topic.updated_at else '',
                'related_topics': related_topics
            })
        
        # 创建响应
        response = Response(
            json.dumps(data, ensure_ascii=False),
            mimetype='application/json',
            headers={'Content-Disposition': 'attachment;filename=topics.json'}
        )
        return response
    
    else:  # 默认CSV格式
        # 创建CSV内存文件
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入表头
        writer.writerow(['ID', '名称', '类型', '优先级', '创建时间', '更新时间', '关联话题'])
        
        # 写入数据
        for topic in topics:
            # 获取关联话题
            related_topics = []
            relations = TopicRelation.query.filter_by(topic_id=topic.id).all()
            for relation in relations:
                related_topic = Topic.query.get(relation.related_topic_id)
                if related_topic:
                    related_topics.append(f"{related_topic.name}({relation.weight})")
            
            writer.writerow([
                topic.id,
                topic.name,
                '必选' if topic.type == 'required' else '随机',
                topic.priority,
                topic.created_at.strftime('%Y-%m-%d %H:%M:%S') if topic.created_at else '',
                topic.updated_at.strftime('%Y-%m-%d %H:%M:%S') if topic.updated_at else '',
                ', '.join(related_topics)
            ])
        
        # 创建响应
        output.seek(0)
        return Response(
            output,
            mimetype='text/csv',
            headers={'Content-Disposition': 'attachment;filename=topics.csv'}
        )


@export_bp.route('/client/export', methods=['GET'])
@login_required
@permission_required('client_manage')
def export_clients():
    """导出客户数据"""
    format_type = request.args.get('format', 'csv')
    
    # 查询客户数据
    clients = Client.query.all()
    
    if format_type == 'json':
        # 导出为JSON格式
        data = []
        for client in clients:
            data.append({
                'id': client.id,
                'name': client.name,
                'contact': client.contact,
                'phone': client.phone,
                'email': client.email,
                'need_review': client.need_review,
                'daily_content_count': client.daily_content_count,
                'display_start_time': client.display_start_time.strftime('%H:%M:%S') if client.display_start_time else '',
                'interval_min': client.interval_min,
                'interval_max': client.interval_max,
                'created_at': client.created_at.strftime('%Y-%m-%d %H:%M:%S') if client.created_at else '',
                'updated_at': client.updated_at.strftime('%Y-%m-%d %H:%M:%S') if client.updated_at else '',
                'status': client.status,
                'ext_data': client.ext_data
            })
        
        # 创建响应
        response = Response(
            json.dumps(data, ensure_ascii=False),
            mimetype='application/json',
            headers={'Content-Disposition': 'attachment;filename=clients.json'}
        )
        return response
    
    else:  # 默认CSV格式
        # 创建CSV内存文件
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入表头
        writer.writerow(['ID', '名称', '联系人', '电话', '邮箱', '需要审核', '每日展示数量', '展示开始时间', '最小间隔(分钟)', '最大间隔(分钟)', '创建时间', '更新时间', '状态'])
        
        # 写入数据
        for client in clients:
            writer.writerow([
                client.id,
                client.name,
                client.contact,
                client.phone,
                client.email,
                '是' if client.need_review else '否',
                client.daily_content_count,
                client.display_start_time.strftime('%H:%M:%S') if client.display_start_time else '',
                client.interval_min,
                client.interval_max,
                client.created_at.strftime('%Y-%m-%d %H:%M:%S') if client.created_at else '',
                client.updated_at.strftime('%Y-%m-%d %H:%M:%S') if client.updated_at else '',
                '启用' if client.status else '禁用'
            ])
        
        # 创建响应
        output.seek(0)
        return Response(
            output,
            mimetype='text/csv',
            headers={'Content-Disposition': 'attachment;filename=clients.csv'}
        )


@export_bp.route('/content/export', methods=['GET'])
@login_required
@permission_required('content_manage')
def export_contents():
    """导出文案数据"""
    format_type = request.args.get('format', 'csv')
    task_id = request.args.get('task_id', type=int)
    client_id = request.args.get('client_id', type=int)
    status = request.args.get('status')
    
    # 查询文案数据
    query = Content.query
    
    if task_id:
        query = query.filter_by(task_id=task_id)
    if client_id:
        query = query.filter_by(client_id=client_id)
    if status:
        query = query.filter_by(workflow_status=status)
    
    contents = query.all()
    
    if format_type == 'json':
        # 导出为JSON格式
        data = []
        for content in contents:
            client = Client.query.get(content.client_id)
            task = Task.query.get(content.task_id)
            
            data.append({
                'id': content.id,
                'title': content.title,
                'content': content.content,
                'topics': content.topics_list,
                'location': content.location,
                'client': client.name if client else '',
                'task': task.name if task else '',
                'workflow_status': content.workflow_status,
                'publish_status': content.publish_status,
                'publish_priority': content.publish_priority,
                'created_at': content.created_at.strftime('%Y-%m-%d %H:%M:%S') if content.created_at else '',
                'updated_at': content.updated_at.strftime('%Y-%m-%d %H:%M:%S') if content.updated_at else ''
            })
        
        # 创建响应
        response = Response(
            json.dumps(data, ensure_ascii=False),
            mimetype='application/json',
            headers={'Content-Disposition': 'attachment;filename=contents.json'}
        )
        return response
    
    else:  # 默认CSV格式
        # 创建CSV内存文件
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入表头
        writer.writerow(['ID', '标题', '内容', '话题', '定位', '客户', '任务', '工作流状态', '发布状态', '发布优先级', '创建时间', '更新时间'])
        
        # 写入数据
        for content in contents:
            client = Client.query.get(content.client_id)
            task = Task.query.get(content.task_id)
            
            # 状态映射
            status_map = {
                'draft': '草稿',
                'pending_review': '待初审',
                'first_reviewed': '初审通过',
                'pending_image': '待上传图片',
                'image_uploaded': '图片已上传',
                'pending_final_review': '待最终审核',
                'pending_client_review': '待客户审核',
                'client_rejected': '客户已拒绝',
                'client_approved': '客户已通过',
                'pending_publish': '待发布',
                'published': '已发布'
            }
            
            publish_status_map = {
                'unpublished': '未发布',
                'publishing': '发布中',
                'published': '发布成功',
                'failed': '发布失败',
                'partial_published': '部分发布',
                'publish_timeout': '发布超时'
            }
            
            priority_map = {
                'high': '高',
                'normal': '中',
                'low': '低'
            }
            
            writer.writerow([
                content.id,
                content.title,
                content.content,
                ', '.join(content.topics_list) if content.topics else '',
                content.location,
                client.name if client else '',
                task.name if task else '',
                status_map.get(content.workflow_status, content.workflow_status),
                publish_status_map.get(content.publish_status, content.publish_status),
                priority_map.get(content.publish_priority, content.publish_priority),
                content.created_at.strftime('%Y-%m-%d %H:%M:%S') if content.created_at else '',
                content.updated_at.strftime('%Y-%m-%d %H:%M:%S') if content.updated_at else ''
            ])
        
        # 创建响应
        output.seek(0)
        return Response(
            output,
            mimetype='text/csv',
            headers={'Content-Disposition': 'attachment;filename=contents.csv'}
        )


@export_bp.route('/template/import', methods=['POST'])
@login_required
@permission_required('template_manage')
def import_templates():
    """导入模板数据"""
    if 'file' not in request.files:
        flash('没有上传文件', 'danger')
        return redirect(url_for('export.index'))
    
    file = request.files['file']
    if file.filename == '':
        flash('没有选择文件', 'danger')
        return redirect(url_for('export.index'))
    
    # 检查文件扩展名
    _, ext = os.path.splitext(file.filename)
    if ext.lower() not in ['.csv', '.json']:
        flash('不支持的文件格式，请上传CSV或JSON文件', 'danger')
        return redirect(url_for('export.index'))
    
    try:
        # 处理CSV文件
        if ext.lower() == '.csv':
            # 读取CSV文件
            df = pd.read_csv(file)
            
            # 导入数据
            imported_count = 0
            for _, row in df.iterrows():
                # 检查必要字段
                if 'title' not in row or 'content' not in row or 'category_id' not in row:
                    continue
                
                # 检查分类是否存在
                category_id = int(row['category_id'])
                category = TemplateCategory.query.get(category_id)
                if not category:
                    continue
                
                # 创建或更新模板
                template = Template(
                    title=row['title'],
                    content=row['content'],
                    category_id=category_id,
                    creator_id=current_user.id,
                    status=True
                )
                
                db.session.add(template)
                imported_count += 1
            
            db.session.commit()
            flash(f'成功导入 {imported_count} 条模板数据', 'success')
        
        # 处理JSON文件
        else:
            # 读取JSON文件
            data = json.load(file)
            
            # 导入数据
            imported_count = 0
            for item in data:
                # 检查必要字段
                if 'title' not in item or 'content' not in item or 'category_id' not in item:
                    continue
                
                # 检查分类是否存在
                category_id = int(item['category_id'])
                category = TemplateCategory.query.get(category_id)
                if not category:
                    continue
                
                # 创建或更新模板
                template = Template(
                    title=item['title'],
                    content=item['content'],
                    category_id=category_id,
                    creator_id=current_user.id,
                    status=True
                )
                
                db.session.add(template)
                imported_count += 1
            
            db.session.commit()
            flash(f'成功导入 {imported_count} 条模板数据', 'success')
    
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'导入模板数据失败: {str(e)}')
        flash(f'导入失败: {str(e)}', 'danger')
    
    return redirect(url_for('export.index'))


@export_bp.route('/topic/import', methods=['POST'])
@login_required
@permission_required('topic_manage')
def import_topics():
    """导入话题数据"""
    if 'file' not in request.files:
        flash('没有上传文件', 'danger')
        return redirect(url_for('export.index'))
    
    file = request.files['file']
    if file.filename == '':
        flash('没有选择文件', 'danger')
        return redirect(url_for('export.index'))
    
    # 检查文件扩展名
    _, ext = os.path.splitext(file.filename)
    if ext.lower() not in ['.csv', '.json']:
        flash('不支持的文件格式，请上传CSV或JSON文件', 'danger')
        return redirect(url_for('export.index'))
    
    try:
        # 处理CSV文件
        if ext.lower() == '.csv':
            # 读取CSV文件
            df = pd.read_csv(file)
            
            # 导入数据
            imported_count = 0
            for _, row in df.iterrows():
                # 检查必要字段
                if 'name' not in row:
                    continue
                
                # 检查话题是否已存在
                existing_topic = Topic.query.filter_by(name=row['name']).first()
                if existing_topic:
                    continue
                
                # 创建话题
                topic_type = 'required' if '必选' in str(row.get('type', '')) else 'random'
                topic = Topic(
                    name=row['name'],
                    type=topic_type,
                    priority=int(row.get('priority', 0))
                )
                
                db.session.add(topic)
                imported_count += 1
            
            db.session.commit()
            flash(f'成功导入 {imported_count} 条话题数据', 'success')
        
        # 处理JSON文件
        else:
            # 读取JSON文件
            data = json.load(file)
            
            # 导入数据
            imported_count = 0
            relation_count = 0
            
            # 第一遍导入所有话题
            topic_map = {}  # 用于存储导入的话题ID映射
            for item in data:
                # 检查必要字段
                if 'name' not in item:
                    continue
                
                # 检查话题是否已存在
                existing_topic = Topic.query.filter_by(name=item['name']).first()
                if existing_topic:
                    topic_map[item.get('id')] = existing_topic.id
                    continue
                
                # 创建话题
                topic = Topic(
                    name=item['name'],
                    type=item.get('type', 'random'),
                    priority=item.get('priority', 0)
                )
                
                db.session.add(topic)
                db.session.flush()  # 获取自增ID
                
                topic_map[item.get('id')] = topic.id
                imported_count += 1
            
            # 第二遍处理关联关系
            for item in data:
                if 'related_topics' not in item or not item['related_topics']:
                    continue
                
                topic_id = topic_map.get(item.get('id'))
                if not topic_id:
                    continue
                
                for related in item['related_topics']:
                    related_id = topic_map.get(related.get('id'))
                    if not related_id:
                        continue
                    
                    # 创建关联关系
                    relation = TopicRelation(
                        topic_id=topic_id,
                        related_topic_id=related_id,
                        weight=related.get('weight', 1)
                    )
                    
                    db.session.add(relation)
                    relation_count += 1
            
            db.session.commit()
            flash(f'成功导入 {imported_count} 条话题数据和 {relation_count} 条关联关系', 'success')
    
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'导入话题数据失败: {str(e)}')
        flash(f'导入失败: {str(e)}', 'danger')
    
    return redirect(url_for('export.index'))


@export_bp.route('/client/import', methods=['POST'])
@login_required
@permission_required('client_manage')
def import_clients():
    """导入客户数据"""
    if 'file' not in request.files:
        flash('没有上传文件', 'danger')
        return redirect(url_for('export.index'))
    
    file = request.files['file']
    if file.filename == '':
        flash('没有选择文件', 'danger')
        return redirect(url_for('export.index'))
    
    # 检查文件扩展名
    _, ext = os.path.splitext(file.filename)
    if ext.lower() not in ['.csv', '.json']:
        flash('不支持的文件格式，请上传CSV或JSON文件', 'danger')
        return redirect(url_for('export.index'))
    
    try:
        # 处理CSV文件
        if ext.lower() == '.csv':
            # 读取CSV文件
            df = pd.read_csv(file)
            
            # 导入数据
            imported_count = 0
            for _, row in df.iterrows():
                # 检查必要字段
                if 'name' not in row:
                    continue
                
                # 检查客户是否已存在
                existing_client = Client.query.filter_by(name=row['name']).first()
                if existing_client:
                    continue
                
                # 创建客户
                client = Client(
                    name=row['name'],
                    contact=row.get('contact', ''),
                    phone=row.get('phone', ''),
                    email=row.get('email', ''),
                    need_review=True if str(row.get('need_review', '是')) == '是' else False,
                    daily_content_count=int(row.get('daily_content_count', 5)),
                    interval_min=int(row.get('interval_min', 30)),
                    interval_max=int(row.get('interval_max', 120)),
                    status=True
                )
                
                # 处理时间字段
                if 'display_start_time' in row and row['display_start_time']:
                    try:
                        client.display_start_time = datetime.datetime.strptime(row['display_start_time'], '%H:%M:%S').time()
                    except:
                        pass
                
                db.session.add(client)
                imported_count += 1
            
            db.session.commit()
            flash(f'成功导入 {imported_count} 条客户数据', 'success')
        
        # 处理JSON文件
        else:
            # 读取JSON文件
            data = json.load(file)
            
            # 导入数据
            imported_count = 0
            for item in data:
                # 检查必要字段
                if 'name' not in item:
                    continue
                
                # 检查客户是否已存在
                existing_client = Client.query.filter_by(name=item['name']).first()
                if existing_client:
                    continue
                
                # 创建客户
                client = Client(
                    name=item['name'],
                    contact=item.get('contact', ''),
                    phone=item.get('phone', ''),
                    email=item.get('email', ''),
                    need_review=item.get('need_review', True),
                    daily_content_count=item.get('daily_content_count', 5),
                    interval_min=item.get('interval_min', 30),
                    interval_max=item.get('interval_max', 120),
                    status=item.get('status', True)
                )
                
                # 处理时间字段
                if 'display_start_time' in item and item['display_start_time']:
                    try:
                        client.display_start_time = datetime.datetime.strptime(item['display_start_time'], '%H:%M:%S').time()
                    except:
                        pass
                
                # 处理扩展数据
                if 'ext_data' in item and item['ext_data']:
                    client.ext_data = item['ext_data']
                
                db.session.add(client)
                imported_count += 1
            
            db.session.commit()
            flash(f'成功导入 {imported_count} 条客户数据', 'success')
    
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'导入客户数据失败: {str(e)}')
        flash(f'导入失败: {str(e)}', 'danger')
    
    return redirect(url_for('export.index')) 