# -*- coding: utf-8 -*-
"""
发布管理表单
"""
from datetime import datetime
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, DateField, IntegerField, RadioField
from wtforms.validators import DataRequired, Optional, NumberRange

from app.models import Client

class PublishFilterForm(FlaskForm):
    """发布管理筛选表单"""
    client_id = SelectField('客户', validators=[Optional()])
    status = SelectField('发布状态', choices=[
        ('', '全部状态'),
        ('unpublished', '未发布'),
        ('publishing', '发布中'),
        ('published', '已发布'),
        ('failed', '发布失败'),
        ('partial_published', '部分发布'),
        ('publish_timeout', '发布超时')
    ], validators=[Optional()])
    priority = SelectField('优先级', choices=[
        ('', '全部优先级'),
        ('high', '高'),
        ('normal', '中'),
        ('low', '低')
    ], validators=[Optional()])
    start_date = DateField('开始日期', format='%Y-%m-%d', validators=[Optional()])
    end_date = DateField('结束日期', format='%Y-%m-%d', validators=[Optional()])
    sort_by = SelectField('排序方式', choices=[
        ('created_at', '创建时间'),
        ('priority', '优先级'),
        ('status', '发布状态')
    ], default='created_at', validators=[Optional()])
    
    def __init__(self, *args, **kwargs):
        super(PublishFilterForm, self).__init__(*args, **kwargs)
        # 动态加载客户列表
        self.client_id.choices = [('', '全部客户')] + [
            (str(client.id), client.name) for client in Client.query.order_by(Client.name).all()
        ]
        
    def validate_client_id(self, field):
        """验证客户ID"""
        if field.data == '':
            field.data = None
        else:
            try:
                field.data = int(field.data)
            except (ValueError, TypeError):
                field.data = None

class PublishBatchForm(FlaskForm):
    """发布批量操作表单"""
    action = RadioField('操作类型', choices=[
        ('status', '更新状态'),
        ('priority', '更新优先级')
    ], default='status')
    status = SelectField('发布状态', choices=[
        ('unpublished', '未发布'),
        ('publishing', '发布中'),
        ('published', '已发布'),
        ('failed', '发布失败'),
        ('partial_published', '部分发布'),
        ('publish_timeout', '发布超时')
    ], default='unpublished')
    priority = SelectField('优先级', choices=[
        ('high', '高'),
        ('normal', '中'),
        ('low', '低')
    ], default='normal')

class PublishTimeoutForm(FlaskForm):
    """发布超时设置表单"""
    timeout = IntegerField('超时时间（小时）', validators=[
        DataRequired('请输入超时时间'),
        NumberRange(min=1, max=72, message='超时时间必须在1-72小时之间')
    ])
    action = SelectField('超时处理策略', choices=[
        ('keep_timeout', '保持超时状态（需手动处理）'),
        ('auto_reset', '自动重置为待发布'),
        ('auto_fail', '自动标记为发布失败')
    ], default='keep_timeout', validators=[DataRequired('请选择超时处理策略')])

class PublishApiKeyForm(FlaskForm):
    """API密钥设置表单"""
    api_key = StringField('API密钥', validators=[DataRequired('请输入API密钥')])
    generate_new = RadioField('是否生成新密钥', choices=[
        ('0', '保持现有密钥'),
        ('1', '生成新密钥')
    ], default='0') 