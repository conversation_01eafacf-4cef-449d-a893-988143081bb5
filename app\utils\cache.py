"""
缓存工具模块
用于提供缓存功能，优化性能
"""
import time
import functools
from flask import current_app, request
from werkzeug.contrib.cache import SimpleCache

# 创建一个简单的内存缓存
cache = SimpleCache()


def cached(timeout=5 * 60, key_prefix='view'):
    """
    缓存装饰器，用于缓存视图函数的返回结果
    
    参数:
        timeout: 缓存过期时间（秒），默认5分钟
        key_prefix: 缓存键前缀
    
    使用方法:
    @cached(timeout=300, key_prefix='some_view')
    def some_view():
        # 视图函数代码
        return render_template('template.html')
    """
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # 如果是调试模式，不使用缓存
            if current_app.debug:
                return f(*args, **kwargs)
            
            # 生成缓存键
            cache_key = f"{key_prefix}:{request.path}:{request.query_string.decode('utf-8')}"
            
            # 尝试从缓存获取
            rv = cache.get(cache_key)
            if rv is not None:
                return rv
            
            # 执行原始函数
            rv = f(*args, **kwargs)
            
            # 存入缓存
            cache.set(cache_key, rv, timeout=timeout)
            
            return rv
        return decorated_function
    return decorator


def memoize(timeout=5 * 60):
    """
    记忆化装饰器，用于缓存函数调用结果
    
    参数:
        timeout: 缓存过期时间（秒），默认5分钟
    
    使用方法:
    @memoize(timeout=300)
    def expensive_function(param1, param2):
        # 耗时操作
        return result
    """
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # 生成缓存键
            cache_key = f"memoize:{f.__module__}:{f.__name__}:{str(args)}:{str(kwargs)}"
            
            # 尝试从缓存获取
            rv = cache.get(cache_key)
            if rv is not None:
                return rv
            
            # 执行原始函数
            rv = f(*args, **kwargs)
            
            # 存入缓存
            cache.set(cache_key, rv, timeout=timeout)
            
            return rv
        return decorated_function
    return decorator


def clear_cache(key_pattern=None):
    """
    清除缓存
    
    参数:
        key_pattern: 缓存键模式，如果提供，只清除匹配的键
    """
    # 由于SimpleCache不支持模式匹配删除，这里只能清除所有缓存
    cache.clear()
    
    return True


class QueryCache:
    """
    查询缓存类，用于缓存数据库查询结果
    """
    def __init__(self, model, timeout=5 * 60):
        """
        初始化查询缓存
        
        参数:
            model: 数据库模型类
            timeout: 缓存过期时间（秒），默认5分钟
        """
        self.model = model
        self.timeout = timeout
        self.model_name = model.__name__
    
    def get(self, id):
        """
        根据ID获取记录，优先从缓存获取
        
        参数:
            id: 记录ID
        
        返回:
            记录对象
        """
        cache_key = f"db:{self.model_name}:{id}"
        
        # 尝试从缓存获取
        rv = cache.get(cache_key)
        if rv is not None:
            return rv
        
        # 从数据库查询
        rv = self.model.query.get(id)
        
        # 存入缓存
        if rv is not None:
            cache.set(cache_key, rv, timeout=self.timeout)
        
        return rv
    
    def invalidate(self, id):
        """
        使指定ID的缓存失效
        
        参数:
            id: 记录ID
        """
        cache_key = f"db:{self.model_name}:{id}"
        cache.delete(cache_key)
        
        return True 