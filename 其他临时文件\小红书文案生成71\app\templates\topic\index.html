{% extends "base.html" %}

{% block title %}话题管理 - 小红书文案生成系统{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
<style>
    .topic-actions {
        white-space: nowrap;
    }
    .topic-type {
        width: 100px;
    }
    .topic-priority {
        width: 80px;
    }
</style>
{% endblock %}

{% block content_auth %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>话题管理</h2>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('topic.add_topic') }}" class="btn btn-primary">
                <i class="bi bi-plus-lg"></i> 添加话题
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>话题名称</th>
                            <th class="topic-type">类型</th>
                            <th class="topic-priority">优先级</th>
                            <th>创建时间</th>
                            <th class="topic-actions">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for topic in topics %}
                        <tr>
                            <td>{{ topic.id }}</td>
                            <td>{{ topic.name }}</td>
                            <td class="topic-type">
                                {% if topic.type == 'required' %}
                                <span class="badge bg-primary">必选话题</span>
                                {% else %}
                                <span class="badge bg-secondary">随机话题</span>
                                {% endif %}
                            </td>
                            <td class="topic-priority">{{ topic.priority }}</td>
                            <td>{{ topic.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td class="topic-actions">
                                <a href="{{ url_for('topic.relations', id=topic.id) }}" class="btn btn-sm btn-info" title="关联管理">
                                    <i class="bi bi-diagram-3"></i>
                                </a>
                                <a href="{{ url_for('topic.edit_topic', id=topic.id) }}" class="btn btn-sm btn-primary" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <button class="btn btn-sm btn-danger delete-topic" data-id="{{ topic.id }}" title="删除">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="6" class="text-center">暂无话题数据</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个话题吗？此操作不可恢复。如果有关联的话题关系，也会一并删除。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 删除话题
        let deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        let topicId = null;
        
        // 点击删除按钮
        document.querySelectorAll('.delete-topic').forEach(function(button) {
            button.addEventListener('click', function() {
                topicId = this.getAttribute('data-id');
                deleteModal.show();
            });
        });
        
        // 确认删除
        document.getElementById('confirmDelete').addEventListener('click', function() {
            if (topicId) {
                fetch(`/topics/delete/${topicId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token() }}'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    deleteModal.hide();
                    if (data.success) {
                        // 删除成功，刷新页面
                        location.reload();
                    } else {
                        // 删除失败，显示错误信息
                        alert(data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除失败，请重试');
                });
            }
        });
    });
</script>
{% endblock %} 