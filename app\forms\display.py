"""
文案展示系统表单
"""
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, IntegerField, TimeField, BooleanField, SelectMultipleField, RadioField, TextAreaField
from wtforms.validators import DataRequired, Optional, NumberRange, ValidationError


class DisplayRuleForm(FlaskForm):
    """展示规则配置表单"""
    client_id = SelectField('客户', coerce=int, validators=[DataRequired('请选择客户')])
    daily_content_count = IntegerField('每日展示数量', validators=[
        DataRequired('请输入每日展示数量'),
        NumberRange(min=1, max=100, message='每日展示数量必须在1-100之间')
    ])
    display_start_time = TimeField('展示开始时间', format='%H:%M', validators=[Optional()])
    interval_min = IntegerField('最小间隔时间(分钟)', validators=[
        DataRequired('请输入最小间隔时间'),
        NumberRange(min=5, max=300, message='最小间隔时间必须在5-300分钟之间')
    ])
    interval_max = IntegerField('最大间隔时间(分钟)', validators=[
        DataRequired('请输入最大间隔时间'),
        NumberRange(min=10, max=600, message='最大间隔时间必须在10-600分钟之间')
    ])
    
    def validate_interval_max(self, field):
        """验证最大间隔时间必须大于最小间隔时间"""
        if field.data <= self.interval_min.data:
            raise ValidationError('最大间隔时间必须大于最小间隔时间')


class DisplayTimeForm(FlaskForm):
    """展示时间管理表单"""
    content_id = SelectField('文案', coerce=int, validators=[DataRequired('请选择文案')])
    display_date = StringField('展示日期', validators=[DataRequired('请选择展示日期')])
    display_time = TimeField('展示时间', format='%H:%M', validators=[DataRequired('请选择展示时间')])
    is_fixed_time = BooleanField('固定时间展示', default=False)
    status = SelectField('状态', choices=[
        ('scheduled', '已安排'),
        ('displayed', '已展示'),
        ('canceled', '已取消')
    ], default='scheduled')


class DisplayOrderForm(FlaskForm):
    """展示顺序设置表单"""
    client_id = SelectField('客户', coerce=int, validators=[DataRequired('请选择客户')])
    order_type = RadioField('排序方式', choices=[
        ('priority', '按优先级'),
        ('time', '按时间'),
        ('random', '随机排序'),
        ('custom', '自定义顺序')
    ], default='priority')
    content_ids = TextAreaField('自定义顺序(每行一个ID)', validators=[Optional()])
    
    def validate_content_ids(self, field):
        """验证自定义顺序格式"""
        if self.order_type.data == 'custom' and not field.data:
            raise ValidationError('使用自定义顺序时必须输入文案ID列表')
        
        if field.data:
            try:
                ids = [int(line.strip()) for line in field.data.split('\n') if line.strip()]
                if len(ids) < 1:
                    raise ValidationError('至少需要一个文案ID')
            except ValueError:
                raise ValidationError('文案ID必须是整数')


class DisplayFilterForm(FlaskForm):
    """展示筛选表单"""
    client_id = SelectField('客户', coerce=int)
    date_from = StringField('开始日期')
    date_to = StringField('结束日期')
    status = SelectField('状态', choices=[
        ('', '全部状态'),
        ('scheduled', '已安排'),
        ('displayed', '已展示'),
        ('canceled', '已取消')
    ], default='')
    search = StringField('搜索') 