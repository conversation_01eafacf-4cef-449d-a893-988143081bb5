-- 创建客户标记默认值表
CREATE TABLE IF NOT EXISTS client_mark_defaults (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_id INT NOT NULL,
    mark_name VARCHAR(100) NOT NULL COMMENT '标记名称，如 "品牌"、"产品"',
    default_values TEXT COMMENT 'JSON格式存储多个默认值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_client_mark (client_id, mark_name),
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    INDEX idx_client_id (client_id),
    INDEX idx_mark_name (mark_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户标记默认值表';

-- 插入示例数据（可选）
-- INSERT INTO client_mark_defaults (client_id, mark_name, default_values) VALUES 
-- (1, '品牌', '["小红书", "抖音", "快手"]'),
-- (1, '产品', '["口红", "粉底液", "眼影盘"]'),
-- (1, '定位', '["上海", "北京", "广州"]');
