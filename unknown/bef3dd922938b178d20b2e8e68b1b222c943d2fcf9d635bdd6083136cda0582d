"""
拒绝理由管理视图
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from sqlalchemy import desc

from app.models import db
from app.models.content import QuickReason, RejectionReason
from app.utils.decorators import permission_required, ajax_aware

# 创建蓝图
reason_bp = Blueprint('reason', __name__)


@reason_bp.route('/')
@login_required
@permission_required('admin_access')
@ajax_aware
def reason_list():
    """快捷理由列表"""
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    # 查询快捷理由
    query = QuickReason.query.order_by(QuickReason.sort_order)
    pagination = query.paginate(page=page, per_page=per_page)
    reasons = pagination.items
    
    context = {
        'title': '快捷理由管理 - 小红书文案生成系统',
        'reasons': reasons,
        'pagination': pagination
    }
    
    return 'reason/list.html', context


@reason_bp.route('/create', methods=['GET', 'POST'])
@login_required
@permission_required('admin_access')
@ajax_aware
def reason_create():
    """创建快捷理由"""
    if request.method == 'POST':
        content = request.form.get('content')
        sort_order = request.form.get('sort_order', 0, type=int)
        
        if not content:
            flash('理由内容不能为空', 'danger')
            return redirect(url_for('reason.reason_create'))
        
        # 创建快捷理由
        reason = QuickReason(
            content=content,
            sort_order=sort_order
        )
        
        db.session.add(reason)
        db.session.commit()
        
        flash('快捷理由创建成功', 'success')
        return redirect(url_for('reason.reason_list'))
    
    context = {
        'title': '创建快捷理由 - 小红书文案生成系统'
    }
    
    return 'reason/create.html', context


@reason_bp.route('/<int:reason_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('admin_access')
@ajax_aware
def reason_edit(reason_id):
    """编辑快捷理由"""
    reason = QuickReason.query.get_or_404(reason_id)
    
    if request.method == 'POST':
        content = request.form.get('content')
        sort_order = request.form.get('sort_order', 0, type=int)
        
        if not content:
            flash('理由内容不能为空', 'danger')
            return redirect(url_for('reason.reason_edit', reason_id=reason_id))
        
        # 更新快捷理由
        reason.content = content
        reason.sort_order = sort_order
        
        db.session.commit()
        
        flash('快捷理由更新成功', 'success')
        return redirect(url_for('reason.reason_list'))
    
    context = {
        'title': '编辑快捷理由 - 小红书文案生成系统',
        'reason': reason
    }
    
    return 'reason/edit.html', context


@reason_bp.route('/<int:reason_id>/delete', methods=['POST'])
@login_required
@permission_required('admin_access')
def reason_delete(reason_id):
    """删除快捷理由"""
    reason = QuickReason.query.get_or_404(reason_id)
    
    db.session.delete(reason)
    db.session.commit()
    
    flash('快捷理由删除成功', 'success')
    return redirect(url_for('reason.reason_list'))


@reason_bp.route('/rejection-stats')
@login_required
@permission_required('admin_access')
@ajax_aware
def rejection_stats():
    """拒绝理由统计"""
    # 获取最常用的拒绝理由
    top_reasons = db.session.query(
        RejectionReason.reason, 
        db.func.count(RejectionReason.id).label('count')
    ).group_by(RejectionReason.reason).order_by(desc('count')).limit(10).all()
    
    # 按用户统计拒绝次数
    user_stats = db.session.query(
        db.func.count(RejectionReason.id).label('count'),
        'users.username'
    ).join(RejectionReason.creator).group_by('users.id').order_by(desc('count')).limit(10).all()
    
    # 按客户统计被拒绝次数
    client_stats = db.session.query(
        db.func.count(RejectionReason.id).label('count'),
        'clients.name'
    ).join(RejectionReason.content).join(Content.client).group_by('clients.id').order_by(desc('count')).limit(10).all()
    
    context = {
        'title': '拒绝理由统计 - 小红书文案生成系统',
        'top_reasons': top_reasons,
        'user_stats': user_stats,
        'client_stats': client_stats
    }
    
    return 'reason/stats.html', context 