/**
 * AJAX导航系统
 * 实现菜单点击不刷新页面，通过AJAX加载内容
 */
$(document).ready(function() {
    // 初始状态保存当前URL
    let currentUrl = window.location.href;
    
    // 添加CSS样式到head
    $('head').append(`
        <style>
            #loading-indicator {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 9999;
                background: rgba(255, 255, 255, 0.8);
                padding: 20px;
                border-radius: 5px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
            }
            
            .ajax-fade {
                opacity: 0.6;
                transition: opacity 0.3s;
            }
        </style>
    `);
    
    // 处理所有带有data-ajax-link属性的链接点击事件
    $(document).on('click', 'a[data-ajax-link]', function(e) {
        e.preventDefault();
        const url = $(this).attr('href');
        
        // 如果是当前页面，不重新加载
        if (url === currentUrl) {
            return;
        }
        
        // 更新浏览器历史记录
        window.history.pushState({url: url}, '', url);
        
        // 加载新页面内容
        loadContent(url);
    });

    // 处理关闭当前标签页按钮
    $(document).on('click', '#closeCurrentTab', function() {
        // 返回到控制面板
        window.history.pushState({url: '/dashboard'}, '', '/dashboard');
        loadContent('/dashboard');
    });
    
    // 处理浏览器前进/后退按钮
    $(window).on('popstate', function(e) {
        if (e.originalEvent.state && e.originalEvent.state.url) {
            loadContent(e.originalEvent.state.url);
        } else {
            // 如果没有状态，则加载当前URL
            loadContent(window.location.href);
        }
    });
    
    // 处理表单提交
    $(document).on('submit', 'form[data-ajax-form]', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const url = form.attr('action') || window.location.href;
        const method = form.attr('method') || 'GET';  // 默认使用GET而不是POST
        
        console.log("AJAX表单提交:", url, method);
        
        // 如果有文件上传，不使用AJAX
        if (form.find('input[type="file"]').length > 0) {
            console.log("检测到文件上传，使用传统表单提交");
            form.submit();
            return;
        }
        
        // 显示加载指示器
        $('#loading-indicator').show();
        $('#main-content').addClass('ajax-fade');
        
        // 收集表单数据
        const formData = form.serialize();
        console.log("表单数据:", formData);
        
        // 使用AJAX提交表单
        $.ajax({
            url: url,
            type: method,
            data: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response, textStatus, xhr) {
                console.log('表单提交成功，状态:', xhr.status);
                console.log('响应数据:', response);
                
                // 检查是否有重定向头
                const redirectUrl = xhr.getResponseHeader('X-Redirect-To');
                if (redirectUrl) {
                    console.log('检测到重定向头:', redirectUrl);
                    window.history.pushState({url: redirectUrl}, '', redirectUrl);
                    loadContent(redirectUrl);
                    return;
                }
                
                // 如果返回的是JSON
                if (typeof response === 'object') {
                    console.log('收到JSON响应:', response);
                    
                    // 检查是否需要强制完整页面加载
                    if (response.force_reload && response.redirect) {
                        console.log('检测到强制页面加载标志，执行完整页面加载:', response.redirect);
                        window.location.href = response.redirect;
                        return;
                    }
                    
                    // 如果有消息，显示消息
                    if (response.message) {
                        const messageType = response.success ? 'success' : 'error';
                        const title = response.success ? '成功' : '错误';
                        
                        // 使用中央弹窗显示消息
                        showCenterModal(title, response.message, function() {
                            // 如果有重定向URL，则加载该URL
                            if (response.redirect) {
                                console.log('检测到JSON重定向:', response.redirect);
                                window.history.pushState({url: response.redirect}, '', response.redirect);
                                loadContent(response.redirect);
                            }
                        });
                        
                        // 如果没有重定向，则继续处理其他响应内容
                        if (!response.redirect) {
                            // 更新页面标题
                            if (response.title) {
                                document.title = response.title;
                            }
                            
                            // 更新主内容区域
                            if (response.html) {
                                // 确保只加载内容区域，不包含导航菜单
                                const contentOnly = extractContentOnly(response.html);
                                $('#main-content').html(contentOnly).removeClass('ajax-fade');
                            }
                            
                            // 更新消息提示
                            if (response.flash_messages) {
                                $('#flash-messages').html(response.flash_messages);
                            }
                        }
                    } else {
                        // 如果有重定向URL，则加载该URL
                        if (response.redirect) {
                            console.log('检测到JSON重定向:', response.redirect);
                            window.history.pushState({url: response.redirect}, '', response.redirect);
                            loadContent(response.redirect);
                        }
                        
                        // 更新页面标题
                        if (response.title) {
                            document.title = response.title;
                        }
                        
                        // 更新主内容区域
                        if (response.html) {
                            // 确保只加载内容区域，不包含导航菜单
                            const contentOnly = extractContentOnly(response.html);
                            $('#main-content').html(contentOnly).removeClass('ajax-fade');
                        }
                        
                        // 更新消息提示
                        if (response.flash_messages) {
                            $('#flash-messages').html(response.flash_messages);
                        }
                    }
                } else {
                    // 如果返回的是HTML，则检查是否是表单提交成功的响应
                    console.log('表单提交返回HTML');
                    
                    // 根据URL判断应该加载哪个页面
                    if (url.includes('/contents/generate')) {
                        console.log('检测到文案生成页面，保持在当前页面');
                        // 不进行跳转，保持在当前页面
                        $('#main-content').removeClass('ajax-fade');
                        
                        // 尝试提取并显示可能的错误消息
                        try {
                            const responseHtml = $(response);
                            const flashMessages = responseHtml.find('#flash-messages').html();
                            if (flashMessages) {
                                $('#flash-messages').html(flashMessages);
                                console.log('提取并显示了消息:', flashMessages);
                            }
                        } catch (e) {
                            console.error('提取消息失败:', e);
                        }
                    } else if (url.includes('/user-management/')) {
                        // 用户管理相关页面，根据操作类型决定跳转
                        console.log('用户管理相关操作');

                        // 判断是否是表单提交
                        const isFormSubmit = method.toUpperCase() === 'POST';

                        if (isFormSubmit) {
                            // 表单提交成功，返回用户列表页面
                            console.log('表单提交成功，返回用户列表页面');
                            loadContent('/user-management/users');
                        } else {
                            // 非表单提交，加载当前页面
                            console.log('加载当前页面:', url);
                            loadContent(url);
                        }
                    } else if (url.includes('/clients/') || url.includes('/client-management/')) {
                        // 客户管理相关页面，保持在当前页面
                        console.log('客户管理相关操作，保持在当前页面');

                        // 判断是否是表单提交
                        const isFormSubmit = method.toUpperCase() === 'POST';

                        if (isFormSubmit) {
                            // 表单提交成功，返回客户列表页面
                            console.log('客户表单提交成功，返回客户列表页面');
                            loadContent('/clients/');
                        } else {
                            // 非表单提交（如筛选、分页），使用GET方法处理
                            console.log('客户页面筛选/分页，使用GET方法处理');
                            
                            // 构建带参数的URL
                            const queryString = formData ? '?' + formData : '';
                            const targetUrl = url.split('?')[0] + queryString;
                            
                            // 更新浏览器历史记录并加载内容
                            window.history.pushState({url: targetUrl}, '', targetUrl);
                            loadContent(targetUrl);
                        }
                    } else {
                        // 对于其他页面，加载文案列表页面
                        console.log('其他操作，加载文案列表页面');
                        loadContent('/contents/');
                    }
                }
                
                // 初始化新加载内容中的JavaScript
                initDynamicContent();
                
                // 隐藏加载指示器
                $('#loading-indicator').hide();
                
                // 更新活动菜单
                updateActiveMenu(window.location.href);
            },
            error: function(xhr, status, error) {
                console.error('表单提交失败:', error, xhr.status);
                
                // 如果是302重定向，则加载重定向的URL
                if (xhr.status === 302 || xhr.status === 301) {
                    const redirectUrl = xhr.getResponseHeader('Location');
                    if (redirectUrl) {
                        console.log('检测到302重定向:', redirectUrl);
                        window.history.pushState({url: redirectUrl}, '', redirectUrl);
                        loadContent(redirectUrl);
                        return;
                    } else {
                        // 根据当前URL决定加载哪个页面
                        if (url.includes('/clients/') || url.includes('/client-management/')) {
                            console.log('无法获取重定向URL，保持在客户管理页面');
                            window.history.pushState({url: '/clients/'}, '', '/clients/');
                            loadContent('/clients/');
                        } else if (url.includes('/user-management/')) {
                            console.log('无法获取重定向URL，保持在用户管理页面');
                            window.history.pushState({url: '/user-management/users'}, '', '/user-management/users');
                            loadContent('/user-management/users');
                        } else {
                            console.log('无法获取重定向URL，加载文案列表页面');
                            window.history.pushState({url: '/contents/'}, '', '/contents/');
                            loadContent('/contents/');
                        }
                        return;
                    }
                }
                
                // 显示错误消息
                $('#flash-messages').html(
                    `<div class="alert alert-danger alert-dismissible fade show">
                        表单提交失败: ${error}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>`
                );
                
                // 隐藏加载指示器
                $('#loading-indicator').hide();
                $('#main-content').removeClass('ajax-fade');
            }
        });
    });
    
    // 加载页面内容的函数
    function loadContent(url) {
        console.log('加载内容:', url);
        
        // 显示加载指示器
        if (!$('#loading-indicator').length) {
            $('body').append('<div id="loading-indicator"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div></div>');
        }
        $('#loading-indicator').show();
        $('#main-content').addClass('ajax-fade');
        
        // 保存当前URL
        currentUrl = url;
        
        // 更新活动菜单
        updateActiveMenu(url);
        
        // 特殊处理用户管理页面
        if (url.includes('/user-management/')) {
            console.log('特殊处理用户管理页面:', url);
            // 使用完整的URL
            const fullUrl = window.location.origin + url;
            console.log('使用完整URL:', fullUrl);
            
            $.ajax({
                url: fullUrl,
                type: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    console.log('用户管理页面加载成功，响应长度:', response.length);
                    
                    // 提取内容区域
                    const contentOnly = extractContentOnly(response);
                    console.log('提取的内容长度:', contentOnly.length);
                    
                    // 如果提取的内容为空，尝试直接使用响应
                    if (!contentOnly || contentOnly.trim() === '') {
                        console.log('提取的内容为空，尝试直接使用响应');
                        // 创建临时容器
                        const $temp = $('<div></div>').html(response);
                        // 移除不需要的元素
                        $temp.find('script, link, meta, title, header, footer, nav, aside').remove();
                        $('#main-content').html($temp.html()).removeClass('ajax-fade');
                    } else {
                        $('#main-content').html(contentOnly).removeClass('ajax-fade');
                    }
                    
                    // 更新页面标题
                    let pageTitle = '用户管理';
                    if (url.includes('/create')) {
                        pageTitle = '创建用户';
                    } else if (url.includes('/edit')) {
                        pageTitle = '编辑用户';
                    } else if (url.includes('/password')) {
                        pageTitle = '修改密码';
                    }
                    
                    document.title = pageTitle + ' - 小红书文案生成系统';
                    // 更新顶部标题
                    $('.current-page-title').text(pageTitle);
                    
                    // 初始化新加载内容中的JavaScript
                    initDynamicContent();
                    
                    // 隐藏加载指示器
                    $('#loading-indicator').hide();
                },
                error: function(xhr, status, error) {
                    console.error('加载内容失败:', error);
                    $('#main-content').removeClass('ajax-fade').html(`
                        <div class="alert alert-danger m-4">
                            <h4 class="alert-heading">加载失败</h4>
                            <p>无法加载页面内容: ${error}</p>
                            <hr>
                            <p class="mb-0">请尝试刷新页面或联系系统管理员。</p>
                        </div>
                    `);
                    $('#loading-indicator').hide();
                }
            });
            return;
        }
        
        // 使用AJAX加载页面内容
        $.ajax({
            url: url,
            type: 'GET',
            dataType: 'html',  // 改为html，因为服务器可能返回HTML而非JSON
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                console.log('AJAX请求成功，响应类型:', typeof response);
                // 更新当前URL
                currentUrl = url;
                
                // 尝试解析为JSON，如果失败则作为HTML处理
                let data = null;
                try {
                    if (typeof response === 'string' && response.trim().startsWith('{')) {
                        data = JSON.parse(response);
                        console.log('成功解析为JSON响应');
                    }
                } catch (e) {
                    console.log('响应不是有效的JSON，作为HTML处理');
                }
                
                // 如果成功解析为JSON
                if (data) {
                    // 检查是否需要强制完整页面加载
                    if (data.force_reload && data.redirect) {
                        console.log('检测到强制页面加载标志，执行完整页面加载:', data.redirect);
                        window.location.href = data.redirect;
                        return;
                    }
                    
                    // 更新页面标题
                    if (data.title) {
                        document.title = data.title;
                        // 更新顶部标题
                        $('.current-page-title').text(data.title.split(' - ')[0] || data.title);
                    }
                    
                    // 更新主内容区域
                    if (data.html) {
                        // 确保只加载内容区域，不包含导航菜单
                        const contentOnly = extractContentOnly(data.html);
                        $('#main-content').html(contentOnly).removeClass('ajax-fade');
                    }
                    
                    // 更新消息提示
                    if (data.flash_messages) {
                        $('#flash-messages').html(data.flash_messages);
                    }
                    
                    // 如果有重定向URL，则加载该URL
                    if (data.redirect && !data.force_reload) {
                        console.log('检测到JSON中的重定向URL:', data.redirect);
                        window.history.pushState({url: data.redirect}, '', data.redirect);
                        loadContent(data.redirect);
                        return;
                    }
                } else {
                    // 作为HTML处理
                    console.log('作为HTML处理响应');
                    const contentOnly = extractContentOnly(response);
                    $('#main-content').html(contentOnly).removeClass('ajax-fade');
                    
                    // 检查URL中是否包含新分享链接的参数
                    if (url.includes('new_share_id') && url.includes('new_share_url')) {
                        console.log('检测到新分享链接参数，自动复制链接');
                        // 延迟执行，确保DOM已加载
                        setTimeout(function() {
                            if ($('#fullShareLink').length > 0) {
                                copyToClipboard('fullShareLink');
                                // 显示复制成功提示
                                showToast('分享链接已复制到剪贴板');
                            }
                        }, 500);
                    }
                }
                
                // 初始化新加载内容中的任何JavaScript
                initDynamicContent();
                
                // 高亮当前活动菜单
                updateActiveMenu(url);
                
                // 隐藏加载指示器
                $('#loading-indicator').hide();
                
                // 滚动到页面顶部
                window.scrollTo(0, 0);
            },
            error: function(xhr, status, error) {
                console.error('加载内容失败:', error, xhr.status, xhr.responseText);
                
                // 如果是未授权错误，重定向到登录页面
                if (xhr.status === 401) {
                    window.location.href = '/auth/login';
                    return;
                }
                
                // 如果期望JSON但收到HTML，则回退到传统方式加载
                if (xhr.responseText && xhr.responseText.trim().startsWith('<!DOCTYPE html>')) {
                    console.log('收到HTML响应，回退到传统加载');
                    // 尝试提取内容并显示
                    try {
                        const contentOnly = extractContentOnly(xhr.responseText);
                        $('#main-content').html(contentOnly).removeClass('ajax-fade');
                        initDynamicContent();
                        updateActiveMenu(url); // 确保在错误处理中也更新菜单
                        $('#loading-indicator').hide();
                        return;
                    } catch (e) {
                        console.error('提取内容失败，回退到整页加载', e);
                        window.location.href = url;
                        return;
                    }
                }
                
                // 显示错误消息
                $('#flash-messages').html(
                    `<div class="alert alert-danger alert-dismissible fade show">
                        加载内容失败: ${error}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>`
                );
                
                // 隐藏加载指示器
                $('#loading-indicator').hide();
                $('#main-content').removeClass('ajax-fade');
            }
        });
    }
    
    // 提取纯内容，移除可能的重复导航菜单
    function extractContentOnly(htmlContent) {
        if (!htmlContent) {
            console.error('收到空的HTML内容');
            return '';
        }
        
        console.log('提取内容，HTML长度:', htmlContent.length);
        
        // 检查是否是完整的HTML文档
        if (htmlContent.includes('<!DOCTYPE html>')) {
            console.log('接收到完整HTML文档，提取内容区域');
            
            // 创建一个临时容器
            const $temp = $('<div></div>').html(htmlContent);
            
            // 尝试提取内容区域
            const $mainContent = $temp.find('#main-content');
            if ($mainContent.length > 0) {
                console.log('找到#main-content元素');
                return $mainContent.html();
            }
            
            // 如果找不到内容区域，尝试提取content块
            const $content = $temp.find('[data-content-block]');
            if ($content.length > 0) {
                console.log('找到[data-content-block]元素');
                return $content.html();
            }
            
            // 尝试提取content_auth块
            const $contentAuth = $temp.find('[data-block="content_auth"]');
            if ($contentAuth.length > 0) {
                console.log('找到[data-block="content_auth"]元素');
                return $contentAuth.html();
            }
            
            // 查找模板中的content_auth块内容
            const contentAuthRegex = /\{% block content_auth %\}([\s\S]*?)\{% endblock %\}/;
            const contentAuthMatch = htmlContent.match(contentAuthRegex);
            if (contentAuthMatch && contentAuthMatch[1]) {
                console.log('通过正则表达式找到content_auth块内容');
                return contentAuthMatch[1];
            }
            
            // 尝试直接查找内容块
            const $blockContent = $temp.find('[data-block="content"]');
            if ($blockContent.length > 0) {
                console.log('找到[data-block="content"]元素');
                return $blockContent.html();
            }
            
            // 尝试查找特定模板的内容区域
            const $templateContent = $temp.find('.container-fluid.py-4');
            if ($templateContent.length > 0) {
                console.log('找到.container-fluid.py-4元素');
                return $templateContent.parent().html() || $templateContent.html();
            }
            
            // 特殊处理用户管理页面
            if (htmlContent.includes('user-management') || htmlContent.includes('用户管理')) {
                console.log('特殊处理用户管理页面内容');
                
                // 尝试查找卡片内容
                const $card = $temp.find('.card.shadow');
                if ($card.length > 0) {
                    console.log('找到.card.shadow元素');
                    return $card.parent().parent().html();
                }
                
                // 尝试查找表单内容
                const $form = $temp.find('form');
                if ($form.length > 0) {
                    console.log('找到form元素');
                    return $form.parent().parent().parent().html();
                }
            }
            
            // 尝试查找其他可能的内容块
            console.log('尝试查找其他可能的内容块');
            const possibleContentSelectors = [
                '.container-fluid',
                '.content-wrapper',
                '.card',
                '.main-content',
                '#content_auth',
                '#content'
            ];
            
            for (const selector of possibleContentSelectors) {
                const $element = $temp.find(selector);
                if ($element.length > 0) {
                    console.log(`找到${selector}元素`);
                    return $element.html();
                }
            }
            
            // 如果所有尝试都失败，提取body内容
            console.log('无法找到特定内容块，提取body内容');
            const $body = $temp.find('body');
            if ($body.length > 0) {
                // 移除导航和页脚等元素
                $body.find('nav, .sidebar, .content-header, footer').remove();
                return $body.html();
            }
        }
        
        // 如果不是完整的HTML文档或找不到内容区域，则返回原始内容
        console.log('无法识别内容区域，返回原始内容');
        // 但仍然移除可能的导航菜单
        const $temp = $('<div></div>').html(htmlContent);
        
        // 移除任何可能的导航菜单
        $temp.find('nav.navbar').remove();
        $temp.find('#navbarNav').remove();
        
        // 移除可能的重复加载指示器
        $temp.find('#loading-indicator').remove();
        
        // 移除可能的重复页脚
        $temp.find('footer').remove();
        
        return $temp.html();
    }
    
    // 初始化动态加载内容中的JavaScript
    function initDynamicContent() {
        console.log('初始化动态内容');
        
        // 重新初始化Bootstrap组件
        // 工具提示
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // 下拉菜单
        var dropdownTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]'));
        dropdownTriggerList.map(function (dropdownTriggerEl) {
            return new bootstrap.Dropdown(dropdownTriggerEl);
        });
        
        // 模态框
        var modalTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="modal"]'));
        modalTriggerList.map(function (modalTriggerEl) {
            return new bootstrap.Modal(modalTriggerEl);
        });
        
        // 将表单标记为AJAX表单（排除客户编辑表单）
        $('#main-content form').not('[enctype="multipart/form-data"]').not('[data-no-ajax="true"]').not('[data-client-edit-form="true"]').attr('data-ajax-form', 'true');
        
        // 特殊处理用户管理页面中的表单
        if (window.location.href.includes('/user-management/')) {
            console.log('初始化用户管理页面表单');
            
            // 确保表单使用AJAX提交
            $('#main-content form').not('[enctype="multipart/form-data"]').each(function() {
                const form = $(this);
                if (!form.attr('data-ajax-form')) {
                    form.attr('data-ajax-form', 'true');
                    console.log('添加data-ajax-form属性到表单');
                }
                
                // 绑定表单提交事件
                form.off('submit').on('submit', function(e) {
                    // 检查是否有data-no-ajax属性，如果有则跳过AJAX处理
                    if ($(this).attr('data-no-ajax') === 'true') {
                        console.log('表单有data-no-ajax属性，跳过全局AJAX处理');
                        return;
                    }

                    e.preventDefault();
                    console.log('表单提交被拦截，使用AJAX提交');
                    
                    const formData = $(this).serialize();
                    let formUrl = $(this).attr('action');

                    // 如果表单没有action属性，使用当前页面URL（但确保是POST请求支持的URL）
                    if (!formUrl) {
                        formUrl = window.location.pathname;
                        console.log('表单没有action属性，使用当前路径:', formUrl);
                    }
                    
                    $.ajax({
                        url: formUrl,
                        type: 'POST',
                        data: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        success: function(response) {
                            console.log('表单提交成功');
                            // 显示成功消息
                            showToast('操作成功');
                            // 返回用户列表页面
                            loadContent('/user-management/users');
                        },
                        error: function(xhr, status, error) {
                            console.error('表单提交失败:', error);
                            showToast('操作失败: ' + error);
                        }
                    });
                });
            });
            
            // 确保所有链接使用AJAX加载
            $('#main-content a').each(function() {
                const link = $(this);
                const href = link.attr('href');
                if (href && href.includes('/user-management/') && !link.attr('data-ajax-link')) {
                    link.attr('data-ajax-link', 'true');
                    console.log('添加data-ajax-link属性到链接:', href);
                }
            });
        }
        
        // 执行页面特定的初始化脚本
        if (typeof pageInit === 'function') {
            pageInit();
        }
    }
    
    // 更新活动菜单项 - 已禁用，由标签页管理器接管
    function updateActiveMenu(url) {
        console.log('菜单激活由标签页管理器接管，跳过ajax-nav激活:', url);
        return;

        // 以下代码已禁用
        /*
        console.log('更新活动菜单，当前URL:', url);

        // 移除所有当前活动状态
        $('.nav-list .nav-link').removeClass('active');

        // 为当前URL添加活动状态
        $('.nav-list .nav-link').each(function() {
            const menuUrl = $(this).attr('href');
            console.log('检查菜单项:', menuUrl);

            // 检查URL是否匹配菜单项
            if (menuUrl && menuUrl !== '/') {
                // 对于用户管理页面的特殊处理
                if (url.includes('/user-management/users') && menuUrl.includes('/user-management/users')) {
                    $(this).addClass('active');
                    console.log('激活用户管理菜单项:', menuUrl);
                    return false; // 跳出循环
                }

                // 常规匹配
                if (url.includes(menuUrl)) {
                    $(this).addClass('active');
                    console.log('激活菜单项:', menuUrl);
                }
            }
        });

        // 确保菜单显示正常
        if ($('.navbar-collapse').length > 0) {
            $('.navbar-collapse').addClass('show');
        }
        */
    }
    
    // 在页面加载完成后立即初始化菜单
    setTimeout(function() {
        console.log('页面加载完成，初始化菜单');
        updateActiveMenu(window.location.href);
        
        // 确保菜单可见
        if ($('.navbar-collapse').length > 0 && !$('.navbar-collapse').hasClass('show')) {
            $('.navbar-collapse').addClass('show');
        }
    }, 100);
    
    // 初始化页面
    updateActiveMenu(window.location.href);
    
    // 初始化动态内容
    initDynamicContent();
}); 

// 辅助函数：复制文本到剪贴板
function copyToClipboard(elementId) {
    var copyText = document.getElementById(elementId);
    if (copyText) {
        var text = copyText.value || copyText.textContent || copyText.innerText;

        // 检查是否支持现代剪贴板API（需要HTTPS或localhost）
        if (navigator.clipboard && navigator.clipboard.writeText && (window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost')) {
            navigator.clipboard.writeText(text)
                .then(() => {
                    console.log('文本已成功复制到剪贴板');
                    showToast('内容已复制到剪贴板');
                })
                .catch(err => {
                    console.error('现代剪贴板API失败:', err);
                    // 回退到传统方法
                    copyUsingExecCommand(copyText, text);
                });
        } else {
            console.log('使用传统复制方法 (HTTP环境或不支持现代API)');
            // 回退到传统方法
            copyUsingExecCommand(copyText, text);
        }
    }
}

// 使用传统execCommand方法复制文本
function copyUsingExecCommand(element, text) {
    try {
        // 如果是输入框，直接选择
        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
            element.focus();
            element.select();
            element.setSelectionRange(0, 99999);
        } else {
            // 如果是其他元素，创建临时输入框
            const tempInput = document.createElement('textarea');
            tempInput.value = text;
            tempInput.style.position = 'fixed';
            tempInput.style.left = '-999999px';
            tempInput.style.top = '-999999px';
            document.body.appendChild(tempInput);
            tempInput.focus();
            tempInput.select();

            // 执行复制后清理
            const successful = document.execCommand('copy');
            document.body.removeChild(tempInput);

            if (successful) {
                console.log('文本已使用execCommand复制到剪贴板');
                showToast('内容已复制到剪贴板');
            } else {
                console.error('execCommand复制失败');
                showManualCopyFallback(text);
            }
            return;
        }

        // 执行复制命令
        const successful = document.execCommand('copy');
        if (successful) {
            console.log('文本已使用execCommand复制到剪贴板');
            showToast('内容已复制到剪贴板');
        } else {
            console.error('execCommand复制失败');
            showManualCopyFallback(text);
        }
    } catch (err) {
        console.error('execCommand复制出错:', err);
        showManualCopyFallback(text);
    }
}

// 手动复制降级方案
function showManualCopyFallback(text) {
    // 简单的提示用户手动复制
    const message = `复制失败，请手动复制以下内容：\n\n${text}`;

    // 尝试使用 prompt 显示文本（用户可以全选复制）
    if (window.prompt) {
        window.prompt('请复制以下内容 (Ctrl+A 全选, Ctrl+C 复制):', text);
    } else {
        // 最后的降级方案
        alert(message);
    }
}

// 显示提示信息
function showToast(message) {
    var toast = document.createElement("div");
    toast.className = "position-fixed top-0 end-0 p-3";
    toast.style.zIndex = "9999";
    toast.innerHTML = `
        <div class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check-circle me-2"></i> ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;
    document.body.appendChild(toast);
    
    var bsToast = new bootstrap.Toast(toast.querySelector('.toast'), {
        autohide: true,
        delay: 3000
    });
    bsToast.show();
    
    setTimeout(function() {
        document.body.removeChild(toast);
    }, 3500);
} 

// 显示中央弹窗提示
function showCenterModal(title, message, confirmCallback, cancelCallback) {
    // 创建模态框元素
    const modalId = 'centerConfirmModal' + Math.floor(Math.random() * 1000);
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = modalId;
    modal.setAttribute('tabindex', '-1');
    modal.setAttribute('aria-labelledby', `${modalId}Label`);
    modal.setAttribute('aria-hidden', 'true');
    
    // 设置模态框内容，添加更美观的样式
    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.3); border: none;">
                <div class="modal-header bg-primary text-white" style="border-top-left-radius: 10px; border-top-right-radius: 10px;">
                    <h5 class="modal-title" id="${modalId}Label">
                        <i class="fas fa-exclamation-circle me-2"></i>${title}
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body" style="padding: 20px; font-size: 16px;">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-info-circle text-primary" style="font-size: 32px;"></i>
                        </div>
                        <div>
                            <p style="margin-bottom: 0;">${message.replace(/\n/g, '<br>')}</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: 1px solid #eee; padding: 15px;">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="${modalId}-cancel" style="border-radius: 20px; padding: 8px 20px;">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" id="${modalId}-confirm" style="border-radius: 20px; padding: 8px 20px;">
                        <i class="fas fa-check me-1"></i>确定
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // 添加到文档中
    document.body.appendChild(modal);
    
    // 创建Bootstrap模态框实例
    const modalInstance = new bootstrap.Modal(modal);
    
    // 添加淡入效果的CSS
    const style = document.createElement('style');
    style.textContent = `
        #${modalId} .modal-content {
            transform: scale(0.7);
            opacity: 0;
            transition: all 0.3s ease-in-out;
        }
        #${modalId}.show .modal-content {
            transform: scale(1);
            opacity: 1;
        }
        #${modalId}-confirm:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        #${modalId}-cancel:hover {
            transform: translateY(-2px);
        }
    `;
    document.head.appendChild(style);
    
    // 显示模态框
    modalInstance.show();
    
    // 绑定确认按钮事件
    document.getElementById(`${modalId}-confirm`).addEventListener('click', function() {
        // 添加按钮点击效果
        this.classList.add('active');
        
        modalInstance.hide();
        if (typeof confirmCallback === 'function') {
            confirmCallback(true);
        }
        // 延迟移除DOM元素
        setTimeout(() => {
            document.body.removeChild(modal);
            document.head.removeChild(style);
        }, 500);
    });
    
    // 绑定取消按钮事件
    document.getElementById(`${modalId}-cancel`).addEventListener('click', function() {
        // 添加按钮点击效果
        this.classList.add('active');
        
        modalInstance.hide();
        if (typeof cancelCallback === 'function') {
            cancelCallback(false);
        }
        // 延迟移除DOM元素
        setTimeout(() => {
            document.body.removeChild(modal);
            document.head.removeChild(style);
        }, 500);
    });
    
    // 模态框关闭事件
    modal.addEventListener('hidden.bs.modal', function() {
        if (typeof cancelCallback === 'function') {
            cancelCallback(false);
        }
        // 延迟移除DOM元素
        setTimeout(() => {
            if (document.body.contains(modal)) {
                document.body.removeChild(modal);
            }
            if (document.head.contains(style)) {
                document.head.removeChild(style);
            }
        }, 500);
    });
    
    // 返回一个Promise，以支持异步操作
    return new Promise((resolve) => {
        document.getElementById(`${modalId}-confirm`).addEventListener('click', () => resolve(true));
        document.getElementById(`${modalId}-cancel`).addEventListener('click', () => resolve(false));
        modal.addEventListener('hidden.bs.modal', () => resolve(false));
    });
} 