{% extends "base.html" %}

{% block title %}基础设置{% endblock %}

{% block content_auth %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">基础设置</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('system.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回系统设置
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5>展示设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.daily_content_count.label(class="form-label") }}
                                    {{ form.daily_content_count(class="form-control") }}
                                    {% if form.daily_content_count.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.daily_content_count.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">设置默认每日展示的文案数量</small>
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.display_start_time.label(class="form-label") }}
                                    {{ form.display_start_time(class="form-control", type="time") }}
                                    {% if form.display_start_time.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.display_start_time.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">设置默认每日开始展示文案的时间</small>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        {{ form.interval_min.label(class="form-label") }}
                                        {{ form.interval_min(class="form-control") }}
                                        {% if form.interval_min.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.interval_min.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                        <small class="form-text text-muted">设置文案之间的最小间隔时间（分钟）</small>
                                    </div>
                                    <div class="col-md-6">
                                        {{ form.interval_max.label(class="form-label") }}
                                        {{ form.interval_max(class="form-control") }}
                                        {% if form.interval_max.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.interval_max.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                        <small class="form-text text-muted">设置文案之间的最大间隔时间（分钟）</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5>链接设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.link_expiration_days.label(class="form-label") }}
                                    {{ form.link_expiration_days(class="form-control") }}
                                    {% if form.link_expiration_days.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.link_expiration_days.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">设置分享链接的有效期（天），0表示永久有效</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                            <a href="{{ url_for('system.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 