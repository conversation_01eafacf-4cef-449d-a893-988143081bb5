{% extends "base.html" %}

{% block title %}审核流程设置{% endblock %}

{% block content_auth %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">审核流程设置</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('system.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回系统设置
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5>审核自动化设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-check form-switch mb-3">
                                    {{ form.auto_review_first(class="form-check-input") }}
                                    {{ form.auto_review_first.label(class="form-check-label") }}
                                    <small class="form-text text-muted d-block">启用后，文案提交后将自动通过初审</small>
                                </div>
                                
                                <div class="form-check form-switch mb-3">
                                    {{ form.auto_review_final(class="form-check-input") }}
                                    {{ form.auto_review_final.label(class="form-check-label") }}
                                    <small class="form-text text-muted d-block">启用后，初审通过的文案将自动通过最终审核</small>
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.auto_review_delay.label(class="form-label") }}
                                    {{ form.auto_review_delay(class="form-control") }}
                                    {% if form.auto_review_delay.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.auto_review_delay.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">设置自动通过的延迟时间（分钟），0表示立即通过</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5>拒绝理由设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-check form-switch mb-3">
                                    {{ form.rejection_reason_required(class="form-check-input") }}
                                    {{ form.rejection_reason_required.label(class="form-check-label") }}
                                    <small class="form-text text-muted d-block">启用后，拒绝文案时必须填写拒绝理由</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5>自动确认设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.auto_confirm_minutes.label(class="form-label") }}
                                    {{ form.auto_confirm_minutes(class="form-control") }}
                                    {% if form.auto_confirm_minutes.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.auto_confirm_minutes.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">设置文案自动确认的时间（分钟），0表示不自动确认</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                            <a href="{{ url_for('system.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 审核流程可视化 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h3 class="card-title">审核流程可视化</h3>
                </div>
                <div class="card-body">
                    <div class="workflow-visualization">
                        <div class="row text-center">
                            <div class="col">
                                <div class="workflow-step">
                                    <div class="workflow-icon bg-primary">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <div class="workflow-label">文案创建</div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="workflow-arrow">
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                            </div>
                            <div class="col">
                                <div class="workflow-step">
                                    <div class="workflow-icon {% if form.auto_review_first.data %}bg-success{% else %}bg-warning{% endif %}">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div class="workflow-label">初审{% if form.auto_review_first.data %}(自动){% endif %}</div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="workflow-arrow">
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                            </div>
                            <div class="col">
                                <div class="workflow-step">
                                    <div class="workflow-icon {% if form.auto_review_final.data %}bg-success{% else %}bg-warning{% endif %}">
                                        <i class="fas fa-check-double"></i>
                                    </div>
                                    <div class="workflow-label">最终审核{% if form.auto_review_final.data %}(自动){% endif %}</div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="workflow-arrow">
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                            </div>
                            <div class="col">
                                <div class="workflow-step">
                                    <div class="workflow-icon bg-success">
                                        <i class="fas fa-paper-plane"></i>
                                    </div>
                                    <div class="workflow-label">发布</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .workflow-visualization {
        padding: 20px 0;
    }
    .workflow-step {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .workflow-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 20px;
        margin-bottom: 10px;
    }
    .workflow-label {
        font-size: 14px;
    }
    .workflow-arrow {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 50px;
        color: #aaa;
        font-size: 20px;
    }
</style>
{% endblock %} 