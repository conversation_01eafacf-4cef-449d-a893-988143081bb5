{% extends "base.html" %}

{% block title %}展示规则设置{% endblock %}

{% block page_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">展示规则设置</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('display.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回展示计划
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead>
                                <tr>
                                    <th>客户</th>
                                    <th>每日展示数量</th>
                                    <th>展示开始时间</th>
                                    <th>最小间隔(分钟)</th>
                                    <th>最大间隔(分钟)</th>
                                    <th>排序方式</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rule in client_rules %}
                                <tr>
                                    <td>{{ rule.client.name }}</td>
                                    <td>{{ rule.daily_count }}</td>
                                    <td>{{ rule.start_time.strftime('%H:%M') if rule.start_time else '未设置' }}</td>
                                    <td>{{ rule.interval_min }}</td>
                                    <td>{{ rule.interval_max }}</td>
                                    <td>
                                        {% if rule.order_type == 'priority' %}
                                        <span class="badge bg-primary">按优先级</span>
                                        {% elif rule.order_type == 'time' %}
                                        <span class="badge bg-info">按时间</span>
                                        {% elif rule.order_type == 'random' %}
                                        <span class="badge bg-warning">随机排序</span>
                                        {% elif rule.order_type == 'custom' %}
                                        <span class="badge bg-success">自定义顺序</span>
                                        {% else %}
                                        <span class="badge bg-secondary">未设置</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('display.edit_rule', client_id=rule.client.id) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i> 编辑规则
                                            </a>
                                            <a href="{{ url_for('display.display_order', client_id=rule.client.id) }}" class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-sort"></i> 设置顺序
                                            </a>
                                            <a href="{{ url_for('display.schedule', client_id=rule.client.id) }}" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-calendar-plus"></i> 安排展示
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">没有找到客户展示规则</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 