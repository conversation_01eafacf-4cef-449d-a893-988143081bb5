<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}文案查看系统{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% block styles %}{% endblock %}
    <style>
        body {
            background-color: #f8f9fa;
        }
        .client-header {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 15px 0;
            margin-bottom: 30px;
        }
        .client-logo {
            max-height: 40px;
        }
        .client-footer {
            background-color: #f8f9fa;
            padding: 20px 0;
            margin-top: 50px;
            border-top: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <!-- 简化的顶部导航栏 -->
    <header class="client-header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <img src="{{ url_for('static', filename='images/logo.png') }}" alt="Logo" class="client-logo me-2">
                    <span class="fs-4 fw-bold text-primary">文案查看系统</span>
                </div>
                <div>
                    <span class="badge bg-primary">客户专用通道</span>
                    {% if client %}
                    <span class="ms-2 fw-bold">{{ client.name }}</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </header>

    <!-- 加载指示器 -->
    <div id="loading-indicator" style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="container mt-4" id="flash-messages">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
            <div class="alert alert-{{ category }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        {% endif %}
        {% endwith %}
    </div>

    <!-- 主内容区 -->
    <main class="container my-4">
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="client-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; {{ now.year }} 小红书文案生成系统 - 客户专用通道</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="text-muted mb-0">如有问题请联系管理员</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html> 