<!-- 系统设置页面 -->
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="bi bi-gear"></i> 系统设置</h2>
            <p class="text-muted">配置系统的各项参数和功能开关</p>
        </div>
        <div class="col-md-4 text-end">
            <button type="button" class="btn btn-outline-warning" onclick="resetSettings()">
                <i class="bi bi-arrow-clockwise"></i> 重置为默认值
            </button>
            <button type="button" class="btn btn-primary" onclick="saveAllSettings()">
                <i class="bi bi-check"></i> 保存所有设置
            </button>
        </div>
    </div>

    <!-- 工作流程设置 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0"><i class="bi bi-diagram-3"></i> 工作流程设置</h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% for setting in workflow_settings %}
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">
                                        {% if setting.key == 'ENABLE_FIRST_REVIEW' %}
                                            是否启用初审
                                        {% elif setting.key == 'ENABLE_FINAL_REVIEW' %}
                                            是否启用最终审核
                                        {% else %}
                                            {{ setting.description or setting.key }}
                                        {% endif %}
                                    </h6>
                                    <p class="card-text text-muted small">
                                        {% if setting.key == 'ENABLE_FIRST_REVIEW' %}
                                            关闭后，生成的文案将直接进入图片上传阶段
                                        {% elif setting.key == 'ENABLE_FINAL_REVIEW' %}
                                            关闭后，图片上传后将直接进入客户审核阶段
                                        {% else %}
                                            {{ setting.description }}
                                        {% endif %}
                                    </p>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="{{ setting.key }}"
                                           data-setting-key="{{ setting.key }}"
                                           {% if setting.value == '1' %}checked{% endif %}>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- 图片上传设置 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0"><i class="bi bi-image"></i> 图片上传设置</h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% for setting in upload_settings %}
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">
                                {% if setting.key == 'IMAGE_UPLOAD_MAX_SIZE' %}
                                    图片最大大小
                                {% elif setting.key == 'IMAGE_UPLOAD_ALLOWED_TYPES' %}
                                    允许的图片格式
                                {% elif setting.key == 'MAX_IMAGES_PER_CONTENT' %}
                                    每篇文案最大图片数量
                                {% else %}
                                    {{ setting.description or setting.key }}
                                {% endif %}
                            </h6>
                            <p class="card-text text-muted small">
                                {% if setting.key == 'IMAGE_UPLOAD_MAX_SIZE' %}
                                    单个图片文件的最大大小（字节）
                                {% elif setting.key == 'IMAGE_UPLOAD_ALLOWED_TYPES' %}
                                    支持的图片文件格式，用逗号分隔
                                {% elif setting.key == 'MAX_IMAGES_PER_CONTENT' %}
                                    每篇文案最多可以上传的图片数量
                                {% else %}
                                    {{ setting.description }}
                                {% endif %}
                            </p>
                            
                            {% if setting.key == 'IMAGE_UPLOAD_MAX_SIZE' %}
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control" 
                                           id="{{ setting.key }}"
                                           data-setting-key="{{ setting.key }}"
                                           value="{{ (setting.value|int / 1048576)|round(1) }}"
                                           min="1" max="100" step="0.1">
                                    <span class="input-group-text">MB</span>
                                </div>
                                <small class="text-muted">当前: {{ (setting.value|int / 1048576)|round(1) }} MB</small>
                            {% elif setting.key == 'MAX_IMAGES_PER_CONTENT' %}
                                <input type="number" 
                                       class="form-control" 
                                       id="{{ setting.key }}"
                                       data-setting-key="{{ setting.key }}"
                                       value="{{ setting.value }}"
                                       min="1" max="20">
                            {% else %}
                                <input type="text" 
                                       class="form-control" 
                                       id="{{ setting.key }}"
                                       data-setting-key="{{ setting.key }}"
                                       value="{{ setting.value }}">
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- 其他设置 -->
    {% if other_settings %}
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0"><i class="bi bi-sliders"></i> 其他设置</h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% for setting in other_settings %}
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">
                                {% if setting.key == 'CLIENT_SHARE_LINK_EXPIRES_DAYS' %}
                                    客户分享链接有效期
                                {% else %}
                                    {{ setting.description or setting.key }}
                                {% endif %}
                            </h6>
                            <p class="card-text text-muted small">
                                {% if setting.key == 'CLIENT_SHARE_LINK_EXPIRES_DAYS' %}
                                    客户审核分享链接的有效期（天）
                                {% else %}
                                    {{ setting.description }}
                                {% endif %}
                            </p>
                            
                            {% if setting.key == 'CLIENT_SHARE_LINK_EXPIRES_DAYS' %}
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control" 
                                           id="{{ setting.key }}"
                                           data-setting-key="{{ setting.key }}"
                                           value="{{ setting.value }}"
                                           min="1" max="365">
                                    <span class="input-group-text">天</span>
                                </div>
                            {% else %}
                                <input type="text" 
                                       class="form-control" 
                                       id="{{ setting.key }}"
                                       data-setting-key="{{ setting.key }}"
                                       value="{{ setting.value }}">
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 设置说明 -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0"><i class="bi bi-info-circle"></i> 设置说明</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>工作流程说明</h6>
                    <ul class="list-unstyled">
                        <li><strong>启用初审：</strong> 文案生成后需要经过初审才能进入图片上传</li>
                        <li><strong>启用最终审核：</strong> 图片上传后需要经过最终审核才能进入客户审核</li>
                        <li><strong>客户审核：</strong> 在客户管理中可以设置客户是否需要审核</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>图片上传说明</h6>
                    <ul class="list-unstyled">
                        <li><strong>文件大小：</strong> 建议设置为5-10MB，过大会影响上传速度</li>
                        <li><strong>文件格式：</strong> 支持常见的图片格式，建议包含jpg,png,gif</li>
                        <li><strong>图片数量：</strong> 每篇文案的最大图片数量，建议6-9张</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 确认模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmButton">确认</button>
            </div>
        </div>
    </div>
</div>

<script>
// 保存所有设置
function saveAllSettings() {
    const settings = {};
    
    // 收集所有设置值
    document.querySelectorAll('[data-setting-key]').forEach(element => {
        const key = element.getAttribute('data-setting-key');
        let value;
        
        if (element.type === 'checkbox') {
            value = element.checked ? '1' : '0';
        } else if (key === 'IMAGE_UPLOAD_MAX_SIZE') {
            // 将MB转换为字节
            value = Math.round(parseFloat(element.value) * 1048576).toString();
        } else {
            value = element.value;
        }
        
        settings[key] = value;
    });
    
    // 发送请求
    fetch('/simple/api/system/settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('保存设置失败: ' + error.message, 'error');
    });
}

// 重置设置
function resetSettings() {
    showConfirm('确定要重置所有设置为默认值吗？此操作不可撤销。', function() {
        fetch('/simple/api/system/settings/reset', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('重置设置失败: ' + error.message, 'error');
        });
    });
}

// 显示确认对话框
function showConfirm(message, callback) {
    document.getElementById('confirmMessage').textContent = message;
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    
    document.getElementById('confirmButton').onclick = function() {
        modal.hide();
        callback();
    };
    
    modal.show();
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 这里可以使用Bootstrap的Toast组件或其他提示组件
    alert(message);
}

// 监听设置变化，实时保存
document.addEventListener('change', function(e) {
    if (e.target.hasAttribute('data-setting-key')) {
        const key = e.target.getAttribute('data-setting-key');
        let value;
        
        if (e.target.type === 'checkbox') {
            value = e.target.checked ? '1' : '0';
        } else if (key === 'IMAGE_UPLOAD_MAX_SIZE') {
            value = Math.round(parseFloat(e.target.value) * 1048576).toString();
        } else {
            value = e.target.value;
        }
        
        // 自动保存单个设置
        fetch(`/simple/api/system/settings/${key}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ value: value })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log(`设置 ${key} 已保存`);
            } else {
                console.error(`保存设置 ${key} 失败:`, data.message);
            }
        })
        .catch(error => {
            console.error(`保存设置 ${key} 失败:`, error);
        });
    }
});

console.log('系统设置页面已加载');
</script>
