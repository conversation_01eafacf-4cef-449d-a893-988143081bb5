<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小红书文案发布API测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .api-section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .api-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #34495e;
        }
        
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.2);
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        
        .btn-success:hover {
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .btn-warning:hover {
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
        }

        .btn-lg {
            padding: 20px 40px;
            font-size: 18px;
        }
        
        .response-area {
            margin-top: 20px;
            padding: 20px;
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            border: 2px solid #34495e;
        }
        
        .content-display {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border: 2px solid #3498db;
            border-radius: 8px;
        }
        
        .content-item {
            margin-bottom: 15px;
            padding: 10px;
            background: #ecf0f1;
            border-radius: 5px;
        }
        
        .content-item strong {
            color: #2c3e50;
        }
        
        .image-gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        
        .image-gallery img {
            max-width: 150px;
            max-height: 150px;
            border-radius: 8px;
            border: 2px solid #ddd;
            cursor: pointer;
            transition: transform 0.3s;
        }
        
        .image-gallery img:hover {
            transform: scale(1.1);
        }
        
        .status-indicator {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .row {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .col {
            flex: 1;
            min-width: 300px;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .api-section {
                padding: 20px;
            }
            
            .row {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 小红书文案发布API测试</h1>
            <p>测试第三方软件调用API获取和更新发布状态</p>
        </div>
        
        <div class="content">
            <!-- API密钥配置 -->
            <div class="api-section">
                <h2>🔑 API密钥配置</h2>
                <div class="form-group">
                    <label for="apiKey">API密钥 (X-API-Key):</label>
                    <input type="text" id="apiKey" placeholder="请输入API密钥" value="">
                </div>
                <button class="btn" onclick="testConnection()">测试连接</button>
                <button class="btn btn-warning" onclick="checkTimeouts()">检查超时</button>
                <div id="connectionResult" class="response-area" style="display: none;"></div>
            </div>
            
            <div class="row">
                <!-- 获取待发布文案 -->
                <div class="col">
                    <div class="api-section">
                        <h2>📥 获取待发布文案</h2>
                        <p class="text-muted">自动按优先级返回最高优先级的待发布文案（高 > 中 > 低）</p>
                        <button class="btn btn-lg" onclick="getContent()">🚀 获取文案</button>
                        <div class="loading" id="getLoading">正在获取文案...</div>
                        <div id="contentDisplay" class="content-display" style="display: none;"></div>
                        <div id="getResponse" class="response-area" style="display: none;"></div>
                    </div>
                </div>
                
                <!-- 更新发布状态 -->
                <div class="col">
                    <div class="api-section">
                        <h2>📤 更新发布状态</h2>
                        <div class="form-group">
                            <label for="contentId">文案ID:</label>
                            <input type="number" id="contentId" placeholder="请输入文案ID" required>
                        </div>
                        <div class="form-group">
                            <label for="status">发布状态:</label>
                            <select id="status" required>
                                <option value="">请选择状态</option>
                                <option value="success">发布成功</option>
                                <option value="failed">发布失败</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="publishUrl">发布链接 (可选):</label>
                            <input type="url" id="publishUrl" placeholder="https://example.com/post/123">
                        </div>
                        <div class="form-group">
                            <label for="platform">发布平台 (可选):</label>
                            <input type="text" id="platform" placeholder="小红书" value="小红书">
                        </div>
                        <div class="form-group">
                            <label for="account">发布账号 (可选):</label>
                            <input type="text" id="account" placeholder="账号名称">
                        </div>
                        <div class="form-group">
                            <label for="errorMessage">提示信息 (可选):</label>
                            <textarea id="errorMessage" rows="3" placeholder="发布成功或失败的详细信息，如：发布成功到小红书平台 或 网络连接超时"></textarea>
                        </div>
                        <button class="btn btn-success" onclick="updateStatus()">更新状态</button>
                        <div class="loading" id="updateLoading">正在更新状态...</div>
                        <div id="updateResponse" class="response-area" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://127.0.0.1:5000/api/v1';
        let currentContent = null;

        // 检查发布超时
        async function checkTimeouts() {
            const apiKey = document.getElementById('apiKey').value;
            const resultDiv = document.getElementById('connectionResult');

            if (!apiKey) {
                showResult(resultDiv, '请先输入API密钥', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/check-timeouts`, {
                    method: 'POST',
                    headers: {
                        'X-API-Key': apiKey,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    const message = `✅ 超时检查完成！\n处理了 ${data.timeout_count} 篇超时文案\n超时时间: ${data.timeout_seconds}秒\n处理策略: ${data.timeout_action}\n\n详细信息:\n${JSON.stringify(data, null, 2)}`;
                    showResult(resultDiv, message, 'success');
                } else {
                    showResult(resultDiv, '❌ 超时检查失败：\n' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                showResult(resultDiv, '❌ 网络错误：' + error.message, 'error');
            }
        }

        // 测试API连接
        async function testConnection() {
            const apiKey = document.getElementById('apiKey').value;
            const resultDiv = document.getElementById('connectionResult');

            if (!apiKey) {
                showResult(resultDiv, '请输入API密钥', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/settings`, {
                    method: 'GET',
                    headers: {
                        'X-API-Key': apiKey,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    showResult(resultDiv, '✅ API连接成功！\n' + JSON.stringify(data, null, 2), 'success');
                } else {
                    showResult(resultDiv, '❌ API连接失败：\n' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                showResult(resultDiv, '❌ 网络错误：' + error.message, 'error');
            }
        }

        // 获取待发布文案（自动按优先级返回）
        async function getContent() {
            const apiKey = document.getElementById('apiKey').value;
            const loadingDiv = document.getElementById('getLoading');
            const responseDiv = document.getElementById('getResponse');
            const displayDiv = document.getElementById('contentDisplay');

            if (!apiKey) {
                showResult(responseDiv, '请先输入API密钥', 'error');
                return;
            }

            // 直接调用API，不需要任何参数，自动返回优先级最高的文案
            const url = `${API_BASE_URL}/content`;

            showLoading(loadingDiv, true);
            hideResult(responseDiv);
            hideResult(displayDiv);

            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'X-API-Key': apiKey,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                showLoading(loadingDiv, false);

                if (response.ok && data.success) {
                    currentContent = data.content;
                    displayContent(data.content);
                    document.getElementById('contentId').value = data.content.id;
                    showResult(responseDiv, `✅ ${data.message || '获取成功！'}\n` + JSON.stringify(data, null, 2), 'success');
                } else {
                    showResult(responseDiv, '❌ 获取失败：\n' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                showLoading(loadingDiv, false);
                showResult(responseDiv, '❌ 网络错误：' + error.message, 'error');
            }
        }

        // 更新发布状态
        async function updateStatus() {
            const apiKey = document.getElementById('apiKey').value;
            const contentId = document.getElementById('contentId').value;
            const status = document.getElementById('status').value;
            const publishUrl = document.getElementById('publishUrl').value;
            const platform = document.getElementById('platform').value;
            const account = document.getElementById('account').value;
            const message = document.getElementById('errorMessage').value;
            const loadingDiv = document.getElementById('updateLoading');
            const responseDiv = document.getElementById('updateResponse');

            if (!apiKey) {
                showResult(responseDiv, '请先输入API密钥', 'error');
                return;
            }

            if (!contentId || !status) {
                showResult(responseDiv, '请填写文案ID和发布状态', 'error');
                return;
            }

            const requestData = {
                status: status,
                publish_url: publishUrl,
                platform: platform,
                account: account,
                message: message,  // 使用新的通用提示信息字段
                ext_info: JSON.stringify({
                    test_source: 'api_test_page',
                    timestamp: new Date().toISOString()
                })
            };

            showLoading(loadingDiv, true);
            hideResult(responseDiv);

            try {
                const response = await fetch(`${API_BASE_URL}/content/${contentId}/status`, {
                    method: 'POST',
                    headers: {
                        'X-API-Key': apiKey,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();
                showLoading(loadingDiv, false);

                if (response.ok && data.success) {
                    showResult(responseDiv, '✅ 状态更新成功！\n' + JSON.stringify(data, null, 2), 'success');
                    // 清空表单
                    document.getElementById('contentId').value = '';
                    document.getElementById('status').value = '';
                    document.getElementById('publishUrl').value = '';
                    document.getElementById('account').value = '';
                    document.getElementById('errorMessage').value = '';
                } else {
                    showResult(responseDiv, '❌ 状态更新失败：\n' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                showLoading(loadingDiv, false);
                showResult(responseDiv, '❌ 网络错误：' + error.message, 'error');
            }
        }

        // 显示文案内容
        function displayContent(content) {
            const displayDiv = document.getElementById('contentDisplay');

            let html = `
                <div class="content-item">
                    <strong>文案ID:</strong> ${content.id}
                    <span class="status-indicator status-success">优先级: ${content.priority_display || content.priority || 'normal'}</span>
                </div>
                <div class="content-item">
                    <strong>标题:</strong> ${content.title || '无标题'}
                </div>
                <div class="content-item">
                    <strong>内容:</strong><br>${(content.content || '').replace(/\n/g, '<br>')}
                </div>
                <div class="content-item">
                    <strong>话题:</strong> ${Array.isArray(content.topics) ? content.topics.join(', ') : (content.topics || '无话题')}
                </div>
                <div class="content-item">
                    <strong>位置:</strong> ${content.location || '无位置信息'}
                </div>
                <div class="content-item">
                    <strong>客户信息:</strong> ${content.client_name || '未知客户'} (ID: ${content.client_id})
                </div>
                <div class="content-item">
                    <strong>任务信息:</strong> ${content.task_name || '未知任务'} (ID: ${content.task_id || 'N/A'})
                </div>
                <div class="content-item">
                    <strong>批次信息:</strong> ${content.batch_name || '未知批次'} (ID: ${content.batch_id || 'N/A'})
                </div>
                <div class="content-item">
                    <strong>模板信息:</strong> ${content.template_title || '未知模板'} (ID: ${content.template_id || 'N/A'})
                </div>
                <div class="content-item">
                    <strong>状态信息:</strong>
                    <span class="status-indicator status-info">工作流: ${content.workflow_status}</span>
                    <span class="status-indicator status-info">发布: ${content.publish_status}</span>
                </div>
                <div class="content-item">
                    <strong>时间信息:</strong><br>
                    创建时间: ${content.created_at ? new Date(content.created_at).toLocaleString('zh-CN') : '未知'}<br>
                    获取时间: ${content.publish_time ? new Date(content.publish_time).toLocaleString('zh-CN') : '未知'}
                    ${content.display_date ? `<br>展示日期: ${content.display_date}` : ''}
                    ${content.display_time ? ` ${content.display_time}` : ''}
                </div>
            `;

            // 显示图片
            const imageCount = content.image_count || (content.images ? content.images.length : 0);
            if (content.images && content.images.length > 0) {
                html += `
                    <div class="content-item">
                        <strong>图片 (${imageCount}张):</strong>
                        <div class="image-gallery">
                `;
                content.images.forEach((imageUrl, index) => {
                    html += `<img src="${imageUrl}" alt="图片${index + 1}" onclick="window.open('${imageUrl}', '_blank')" title="点击查看大图" onerror="this.style.display='none'; console.log('图片加载失败: ${imageUrl}')">`;
                });
                html += `
                        </div>
                    </div>
                `;
            } else {
                html += `
                    <div class="content-item">
                        <strong>图片:</strong> 无图片 (图片数量: ${imageCount})
                    </div>
                `;
            }

            displayDiv.innerHTML = html;
            displayDiv.style.display = 'block';
        }

        // 显示结果
        function showResult(element, message, type) {
            element.textContent = message;
            element.style.display = 'block';
            element.className = `response-area status-${type}`;
        }

        // 隐藏结果
        function hideResult(element) {
            element.style.display = 'none';
        }

        // 显示/隐藏加载状态
        function showLoading(element, show) {
            element.style.display = show ? 'block' : 'none';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认的API密钥
            document.getElementById('apiKey').value = 'fETkRLwJQJkp29hzFIPuzaPWTcMcdWUW';
        });
    </script>
</body>
</html>
