#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查admin用户是否存在，如果不存在则创建，并确保它拥有所有权限
"""

from app import create_app
from app.models import db, User, Role, Permission

def main():
    app = create_app()
    with app.app_context():
        # 检查admin角色是否存在
        admin_role = Role.query.filter_by(name='admin').first()
        if not admin_role:
            admin_role = Role(name='admin', description='管理员角色')
            db.session.add(admin_role)
            print('已创建admin角色')
        
        # 确保admin角色拥有所有权限
        all_permissions = Permission.query.all()
        for perm in all_permissions:
            if perm not in admin_role.permissions:
                admin_role.permissions.append(perm)
                print(f'已将权限 {perm.name} 添加到admin角色')
        
        # 检查admin用户是否存在
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                real_name='系统管理员',
                is_active=True
            )
            admin_user.password = 'admin123'  # 设置初始密码
            db.session.add(admin_user)
            print('已创建admin用户')
        
        # 确保admin用户拥有admin角色
        if admin_role not in admin_user.roles:
            admin_user.roles.append(admin_role)
            print('已将admin角色分配给admin用户')
        
        # 提交更改
        db.session.commit()
        print('检查完成')
        
        # 打印admin用户的权限
        print('\nadmin用户拥有的权限:')
        for perm in all_permissions:
            if admin_user.has_permission(perm.name):
                print(f'- {perm.name}: {perm.description}')
            else:
                print(f'- [缺失] {perm.name}: {perm.description}')

if __name__ == '__main__':
    main()

 