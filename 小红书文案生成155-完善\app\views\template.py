# -*- coding: utf-8 -*-
"""
模板管理视图
"""

from datetime import datetime
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app as app
from flask_login import login_required, current_user
from sqlalchemy import desc

from app.models import db
from app.models.template import Template, TemplateCategory, TemplateMark
from app.models.content import Content
from app.forms.template import TemplateCategoryForm, TemplateForm, TemplateMarkForm
from app.utils.decorators import permission_required, ajax_aware, ajax_aware
import json

# 创建模板管理蓝图
template_bp = Blueprint('template', __name__, url_prefix='/templates')


@template_bp.route('/')
@login_required
@permission_required('template_manage')
@ajax_aware
def index():
    """模板管理首页"""

    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    # 限制每页显示数量的范围
    if per_page not in [10, 20, 30, 50, 80, 100]:
        per_page = 20

    # 获取筛选参数
    category_id = request.args.get('category_id', type=int)
    status = request.args.get('status')

    # 构建查询
    query = Template.query

    # 应用筛选条件
    if category_id:
        query = query.filter(Template.category_id == category_id)

    if status is not None and status != '':
        query = query.filter(Template.status == (status == '1'))

    # 按更新时间排序并分页
    pagination = query.order_by(Template.updated_at.desc()).paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

    # 获取所有分类用于筛选下拉框
    categories = TemplateCategory.query.all()

    return render_template('template/index.html',
                         templates=pagination.items,
                         pagination=pagination,
                         per_page=per_page,
                         categories=categories)


@template_bp.route('/categories')
@login_required
@ajax_aware
def categories():
    """模板分类管理"""
    if not current_user.has_permission('template_manage'):
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': '您没有权限访问此页面'}), 403
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('main_simple.dashboard'))

    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    # 限制每页显示数量的范围
    if per_page not in [10, 20, 30, 50, 80, 100]:
        per_page = 20

    # 为了保持层级显示的用户体验，我们需要特殊处理分页
    # 1. 先获取顶级分类进行分页
    # 2. 然后获取这些顶级分类的所有子分类
    # 3. 计算总的显示行数（顶级分类 + 子分类）进行正确的分页统计

    # 获取所有顶级分类
    top_categories = TemplateCategory.query.filter_by(parent_id=None).order_by(
        TemplateCategory.sort_order,
        TemplateCategory.id
    ).all()

    # 计算每个顶级分类及其子分类的总行数
    category_rows = []
    for category in top_categories:
        category_rows.append(category)
        # 添加子分类
        children = category.children.order_by('sort_order', 'id').all()
        category_rows.extend(children)

    # 手动分页
    total_rows = len(category_rows)
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    page_categories = category_rows[start_idx:end_idx]

    # 创建分页对象 - 使用简单的分页信息字典
    total_pages = (total_rows + per_page - 1) // per_page if total_rows > 0 else 1

    # 创建一个简单的分页信息对象
    class SimplePagination:
        def __init__(self, page, per_page, total, items):
            self.page = page
            self.per_page = per_page
            self.total = total
            self.items = items
            self.pages = (total + per_page - 1) // per_page if total > 0 else 1
            self.has_prev = page > 1
            self.has_next = page < self.pages
            self.prev_num = page - 1 if self.has_prev else None
            self.next_num = page + 1 if self.has_next else None

        def iter_pages(self, left_edge=2, right_edge=2, left_current=2, right_current=3):
            """生成分页页码的迭代器，与Flask-SQLAlchemy的Pagination兼容"""
            last = self.pages
            for num in range(1, last + 1):
                if num <= left_edge or \
                   (self.page - left_current - 1 < num < self.page + right_current) or \
                   num > last - right_edge:
                    yield num

    pagination = SimplePagination(
        page=page,
        per_page=per_page,
        total=total_rows,
        items=page_categories
    )

    return render_template('template/categories.html',
                         categories=pagination.items,
                         pagination=pagination,
                         per_page=per_page)


@template_bp.route('/category/add', methods=['GET', 'POST'])
@login_required
@ajax_aware
def add_category():
    """添加模板分类"""
    if not current_user.has_permission('template_manage'):
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': '您没有权限访问此页面'}), 403
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('main_simple.dashboard'))
    
    form = TemplateCategoryForm()
    # 获取所有分类作为父分类选项
    form.parent_id.choices = [(0, '无（顶级分类）')] + [(c.id, c.name) for c in TemplateCategory.query.all()]
    
    if form.validate_on_submit():
        try:
            category = TemplateCategory(
                name=form.name.data,
                parent_id=form.parent_id.data if form.parent_id.data != 0 else None,
                sort_order=form.sort_order.data
            )
            db.session.add(category)
            db.session.commit()

            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': True, 'message': '分类添加成功'})
            else:
                flash('分类添加成功', 'success')
                return redirect(url_for('template.categories'))
        except Exception as e:
            db.session.rollback()
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': f'添加失败: {str(e)}'})
            else:
                flash(f'添加失败: {str(e)}', 'danger')
    
    return render_template('template/category_form.html', form=form, title='添加分类')


@template_bp.route('/category/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@ajax_aware
def edit_category(id):
    """编辑模板分类"""
    if not current_user.has_permission('template_manage'):
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': '您没有权限访问此页面'}), 403
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('main.dashboard'))
    
    category = TemplateCategory.query.get_or_404(id)
    form = TemplateCategoryForm(obj=category)
    
    # 获取所有分类作为父分类选项（排除自己及其子分类）
    exclude_ids = [id]
    children = TemplateCategory.query.filter_by(parent_id=id).all()
    for child in children:
        exclude_ids.append(child.id)
    
    form.parent_id.choices = [(0, '无（顶级分类）')] + [
        (c.id, c.name) for c in TemplateCategory.query.filter(~TemplateCategory.id.in_(exclude_ids)).all()
    ]
    
    if form.validate_on_submit():
        try:
            category.name = form.name.data
            category.parent_id = form.parent_id.data if form.parent_id.data != 0 else None
            category.sort_order = form.sort_order.data
            db.session.commit()

            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': True, 'message': '分类更新成功'})
            else:
                flash('分类更新成功', 'success')
                return redirect(url_for('template.categories'))
        except Exception as e:
            db.session.rollback()
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': f'更新失败: {str(e)}'})
            else:
                flash(f'更新失败: {str(e)}', 'danger')
    
    # 设置当前值
    if category.parent_id:
        form.parent_id.data = category.parent_id
    else:
        form.parent_id.data = 0
    
    return render_template('template/category_form.html', form=form, title='编辑分类')


@template_bp.route('/category/delete/<int:id>', methods=['POST'])
@login_required
def delete_category(id):
    """删除模板分类"""
    if not current_user.has_permission('template_manage'):
        return jsonify({'success': False, 'message': '您没有权限执行此操作'})
    
    category = TemplateCategory.query.get_or_404(id)
    
    # 检查是否有子分类
    if TemplateCategory.query.filter_by(parent_id=id).count() > 0:
        return jsonify({'success': False, 'message': '无法删除，该分类下有子分类'})
    
    # 检查是否有关联的模板
    if Template.query.filter_by(category_id=id).count() > 0:
        return jsonify({'success': False, 'message': '无法删除，该分类下有模板'})
    
    db.session.delete(category)
    db.session.commit()
    return jsonify({'success': True, 'message': '分类删除成功'})


@template_bp.route('/categories')
@login_required
def get_categories():
    """获取所有模板分类（JSON API）"""
    categories = TemplateCategory.query.all()
    result = []
    
    for category in categories:
        result.append({
            'id': category.id,
            'name': category.name,
            'parent_id': category.parent_id
        })
    
    return jsonify(result)


@template_bp.route('/add', methods=['GET', 'POST'])
@login_required
@ajax_aware
def add_template():
    """添加模板"""
    if not current_user.has_permission('template_manage'):
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': '您没有权限访问此页面'}), 403
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('main_simple.dashboard'))

    form = TemplateForm()
    # 获取所有分类作为选项
    form.category_id.choices = [(c.id, c.name) for c in TemplateCategory.query.all()]
    
    if form.validate_on_submit():
        try:
            template = Template(
                title=form.title.data,
                content=form.content.data,
                category_id=form.category_id.data,
                creator_id=current_user.id,
                status=form.status.data
            )
            # 保存模板，update_marks方法会自动处理marks字段
            print(f"DEBUG - 开始保存模板: {template.title}")
            marks_list = template.update_marks()
            print(f"DEBUG - 提取的标记: {marks_list}")
            print(f"DEBUG - 设置的marks字段: {template.marks}")

            db.session.add(template)
            db.session.commit()

            print(f"DEBUG - 模板保存成功，ID: {template.id}")


            flash('模板添加成功', 'success')
            
            # 检查是否是AJAX请求
            if request.headers.get('X-No-AJAX') or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                # 获取分类名称
                category_name = None
                if template.category_id:
                    category = TemplateCategory.query.get(template.category_id)
                    category_name = category.name if category else None

                # 返回完整的模板数据
                template_data = {
                    'id': template.id,
                    'title': template.title,
                    'category_name': category_name,
                    'status': template.status,
                    'created_at': template.created_at.isoformat() if template.created_at else None
                }
                return jsonify({'success': True, 'message': '模板添加成功', 'template': template_data})
            
            # 检查是否有重定向请求
            redirect_url = request.form.get('redirect_after_submit')
            if redirect_url:
                return redirect(redirect_url)
            return redirect(url_for('template.index'))
        except Exception as e:
            db.session.rollback()
            app.logger.error(f"添加模板失败: {str(e)}")
            if request.headers.get('X-No-AJAX') or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': f'添加失败: {str(e)}'})
            flash('添加失败，请重试', 'danger')
    elif request.method == 'POST':
        # 表单验证失败，返回错误信息
        errors = []
        for field, field_errors in form.errors.items():
            field_name = getattr(form, field).label.text
            errors.append(f"{field_name}: {', '.join(field_errors)}")
        
        if request.headers.get('X-No-AJAX') or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': '表单验证失败', 'errors': errors})
    
    # 获取所有标记
    marks = TemplateMark.query.all()
    
    return render_template('template/template_form.html', form=form, marks=marks, title='添加模板')


@template_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@ajax_aware
def edit_template(id):
    """编辑模板"""
    if not current_user.has_permission('template_manage'):
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': '您没有权限访问此页面'}), 403
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('main_simple.dashboard'))
    
    template = Template.query.get_or_404(id)
    form = TemplateForm(obj=template)
    
    # 获取所有分类作为选项
    form.category_id.choices = [(c.id, c.name) for c in TemplateCategory.query.all()]
    
    if form.validate_on_submit():
        try:
            template.title = form.title.data
            template.content = form.content.data
            template.category_id = form.category_id.data
            template.status = form.status.data
            template.updated_at = datetime.now()
            # 先保存模板（marks为NULL）
            template.update_marks()
            db.session.commit()

            # 模板保存成功后，尝试更新marks字段（使用UTF-8编码）
            try:
                marks_list = template.extract_marks()
                if marks_list:
                    # 使用ensure_ascii=False，直接存储中文
                    import json
                    marks_json = json.dumps(marks_list, ensure_ascii=False, separators=(',', ':'))

                    # 使用原生SQL更新
                    from sqlalchemy import text
                    db.session.execute(
                        text("UPDATE templates SET marks = :marks WHERE id = :id"),
                        {"marks": marks_json, "id": template.id}
                    )
                    db.session.commit()
                    print(f"DEBUG - 成功更新marks字段（UTF-8编码）: {marks_json}")
                else:
                    print(f"DEBUG - 没有提取到标记")
            except Exception as e:
                print(f"WARNING - 更新marks字段失败: {e}")
                print(f"INFO - 模板已保存成功，marks字段为NULL，功能通过实时提取实现")

            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                # 获取分类名称
                category_name = None
                if template.category_id:
                    category = TemplateCategory.query.get(template.category_id)
                    category_name = category.name if category else None

                # 返回完整的模板数据
                template_data = {
                    'id': template.id,
                    'title': template.title,
                    'category_name': category_name,
                    'status': template.status,
                    'created_at': template.created_at.isoformat() if template.created_at else None
                }
                return jsonify({'success': True, 'message': '模板更新成功', 'template': template_data})
            else:
                flash('模板更新成功', 'success')
                # 检查是否有重定向请求
                redirect_url = request.form.get('redirect_after_submit')
                if redirect_url:
                    return redirect(redirect_url)
                return redirect(url_for('template.index'))
        except Exception as e:
            db.session.rollback()
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': f'更新失败: {str(e)}'})
            else:
                flash(f'更新失败: {str(e)}', 'danger')
    
    # 获取所有标记
    marks = TemplateMark.query.all()
    
    return render_template('template/template_form.html', form=form, marks=marks, title='编辑模板')


@template_bp.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete_template(id):
    """删除模板"""
    if not current_user.has_permission('template_manage'):
        return jsonify({'success': False, 'message': '您没有权限执行此操作'})
    
    template = Template.query.get_or_404(id)
    
    # 检查是否有关联的文案
    if template.contents.count() > 0:
        return jsonify({'success': False, 'message': '无法删除，该模板已被文案使用'})
    
    db.session.delete(template)
    db.session.commit()
    return jsonify({'success': True, 'message': '模板删除成功'})


@template_bp.route('/toggle_status/<int:id>', methods=['POST'])
@login_required
def toggle_status(id):
    """切换模板状态"""
    if not current_user.has_permission('template_manage'):
        return jsonify({'success': False, 'message': '您没有权限执行此操作'})
    
    template = Template.query.get_or_404(id)
    
    # 获取请求中的新状态
    data = request.get_json() or {}
    new_status = data.get('status', not template.status)

    print(f"DEBUG - 切换模板状态: template_id={id}")
    print(f"DEBUG - 当前用户: {current_user.username}")
    print(f"DEBUG - 模板: {template.title}")
    print(f"DEBUG - 当前状态: {template.status}")
    print(f"DEBUG - 新状态: {new_status}")
    
    try:
        template.status = bool(new_status)
        template.updated_at = datetime.now()
        db.session.commit()

        status_text = '启用' if template.status else '禁用'
        print(f"DEBUG - 模板状态切换成功: {status_text}")

        return jsonify({
            'success': True,
            'message': f'模板状态已更新为{status_text}',
            'status': template.status
        })
    except Exception as e:
        print(f"ERROR - 切换模板状态失败: {str(e)}")
        db.session.rollback()
        app.logger.error(f"更新模板状态失败: {str(e)}")
        return jsonify({'success': False, 'message': f'状态更新失败: {str(e)}'}), 500


@template_bp.route('/marks')
@login_required
@ajax_aware
def marks():
    """标记管理"""
    if not current_user.has_permission('template_manage'):
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': '您没有权限访问此页面'}), 403
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('main.dashboard'))

    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    # 限制每页显示数量的范围
    if per_page not in [10, 20, 30, 50, 80, 100]:
        per_page = 20

    # 分页查询所有标记
    pagination = TemplateMark.query.order_by(TemplateMark.id).paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

    return render_template('template/marks.html',
                         marks=pagination.items,
                         pagination=pagination,
                         per_page=per_page)


@template_bp.route('/mark/add', methods=['GET', 'POST'])
@login_required
@ajax_aware
def add_mark():
    """添加标记"""
    if not current_user.has_permission('template_manage'):
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': '您没有权限访问此页面'}), 403
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('main.dashboard'))
    
    form = TemplateMarkForm()
    
    if form.validate_on_submit():
        try:
            mark = TemplateMark(
                name=form.name.data,
                description=form.description.data,
                type=form.type.data
            )
            db.session.add(mark)
            db.session.commit()

            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': True, 'message': '标记添加成功'})
            else:
                flash('标记添加成功', 'success')
                return redirect(url_for('template.marks'))
        except Exception as e:
            db.session.rollback()
            error_msg = str(e)
            # 处理数据库唯一性约束错误
            if 'UNIQUE constraint failed' in error_msg or 'duplicate key' in error_msg.lower():
                error_msg = '标记名称已存在，请使用其他名称'

            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': f'添加失败: {error_msg}'})
            else:
                flash(f'添加失败: {error_msg}', 'danger')
    elif request.method == 'POST':
        # 表单验证失败，返回错误信息
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            errors = []
            for field, field_errors in form.errors.items():
                field_name = getattr(form, field).label.text
                errors.extend([f"{field_name}: {error}" for error in field_errors])
            return jsonify({'success': False, 'message': '; '.join(errors)})
    
    return render_template('template/mark_form.html', form=form, title='添加标记')


@template_bp.route('/mark/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@ajax_aware
def edit_mark(id):
    """编辑标记"""
    if not current_user.has_permission('template_manage'):
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': '您没有权限访问此页面'}), 403
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('main.dashboard'))
    
    mark = TemplateMark.query.get_or_404(id)
    form = TemplateMarkForm(obj=mark, original_name=mark.name)
    
    if form.validate_on_submit():
        try:
            # 只允许更新描述和类型，不允许更新名称
            mark.description = form.description.data
            mark.type = form.type.data
            db.session.commit()

            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': True, 'message': '标记更新成功！'})
            else:
                flash('标记更新成功！', 'success')
                return redirect(url_for('template.marks'))
        except Exception as e:
            db.session.rollback()
            error_msg = str(e)
            # 处理数据库唯一性约束错误
            if 'UNIQUE constraint failed' in error_msg or 'duplicate key' in error_msg.lower():
                error_msg = '标记名称已存在，请使用其他名称'

            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': f'更新失败: {error_msg}'})
            else:
                flash(f'更新失败: {error_msg}', 'danger')
    elif request.method == 'POST':
        # 表单验证失败，返回错误信息
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            errors = []
            for field, field_errors in form.errors.items():
                field_name = getattr(form, field).label.text
                errors.extend([f"{field_name}: {error}" for error in field_errors])
            return jsonify({'success': False, 'message': '; '.join(errors)})
    
    return render_template('template/mark_form.html', form=form, title='编辑标记')


@template_bp.route('/mark/delete/<int:id>', methods=['POST'])
@login_required
def delete_mark(id):
    """删除标记"""
    if not current_user.has_permission('template_manage'):
        return jsonify({'success': False, 'message': '您没有权限执行此操作'})
    
    mark = TemplateMark.query.get_or_404(id)
    db.session.delete(mark)
    db.session.commit()
    return jsonify({'success': True, 'message': '标记删除成功'})


@template_bp.route('/preview/<int:id>')
@login_required
def preview_template(id):
    """预览模板"""
    template = Template.query.get_or_404(id)
    
    # 处理内容中的换行符，确保在前端正确显示
    raw_content = template.content
    
    # 将内容中的标记符号 {标记} 转换为 《标记》
    display_content = raw_content.replace('{', '《').replace('}', '》')
    
    return render_template('template/preview.html', 
                          template=template, 
                          raw_content=raw_content,
                          display_content=display_content)


@template_bp.route('/test_nl2br')
@login_required
def test_nl2br():
    """测试nl2br过滤器"""
    # 创建一个包含多种换行情况的测试文本
    test_content = "这是第一行\n这是第二行\n\n这是空行后的内容\n\n\n这是两个空行后的内容\n这是最后一行\n\n\n\n"
    
    # 添加一个计数测试
    count_test = "1\n2\n\n3\n\n\n4\n\n\n\n5"
    
    # 添加一个末尾换行测试
    end_test = "这行后面有一个换行\n"
    
    return render_template('template/test_nl2br.html', 
                          test_content=test_content,
                          count_test=count_test,
                          end_test=end_test) 