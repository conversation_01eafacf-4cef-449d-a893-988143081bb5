"""
模板管理相关表单
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, IntegerField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, Optional, ValidationError
from app.utils.simplemde_field import SimpleMDEField


class TemplateCategoryForm(FlaskForm):
    """模板分类表单"""
    name = StringField('分类名称', validators=[
        DataRequired(message='分类名称不能为空'),
        Length(1, 50, message='分类名称长度不正确')
    ])
    parent_id = SelectField('父分类', coerce=int, validators=[Optional()])
    sort_order = IntegerField('排序', default=0)
    submit = SubmitField('保存')


class TemplateForm(FlaskForm):
    """模板表单"""
    title = StringField('标题', validators=[
        DataRequired(message='标题不能为空'),
        Length(1, 100, message='标题长度不正确')
    ])
    category_id = SelectField('分类', coerce=int, validators=[
        DataRequired(message='请选择分类')
    ])
    content = SimpleMDEField('内容', validators=[
        DataRequired(message='内容不能为空')
    ])
    status = BooleanField('启用', default=True)
    submit = SubmitField('保存')


class TemplateMarkForm(FlaskForm):
    """标记表单"""
    name = StringField('标记名称', validators=[
        DataRequired(message='标记名称不能为空'),
        Length(1, 50, message='标记名称长度不正确')
    ])
    description = StringField('描述', validators=[
        Optional(),
        Length(max=200, message='描述长度不正确')
    ])
    type = SelectField('类型', choices=[
        ('text', '文本'),
        ('topic', '话题'),
        ('location', '位置'),
        ('user', '用户')
    ])
    submit = SubmitField('保存')

    def __init__(self, original_name=None, *args, **kwargs):
        super(TemplateMarkForm, self).__init__(*args, **kwargs)
        self.original_name = original_name

    def validate_name(self, field):
        """验证标记名称是否重复"""
        from app.models.template import TemplateMark

        # 如果是编辑模式且名称没有改变，则跳过验证
        if self.original_name and field.data == self.original_name:
            return

        # 检查是否存在相同名称的标记
        existing_mark = TemplateMark.query.filter_by(name=field.data).first()
        if existing_mark:
            raise ValidationError('标记名称已存在，请使用其他名称')