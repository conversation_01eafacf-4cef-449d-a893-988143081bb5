"""
系统设置表单
"""
from flask_wtf import FlaskForm
from wtforms import StringField, IntegerField, BooleanField, SelectField, TimeField, TextAreaField, RadioField
from wtforms.validators import DataRequired, Optional, NumberRange, ValidationError


class BasicSettingForm(FlaskForm):
    """基础设置表单"""
    daily_content_count = IntegerField('默认每日展示数量', validators=[
        DataRequired('请输入默认每日展示数量'),
        NumberRange(min=1, max=100, message='每日展示数量必须在1-100之间')
    ])
    
    display_start_time = TimeField('默认展示开始时间', format='%H:%M', validators=[Optional()])
    
    interval_min = IntegerField('默认最小间隔时间(分钟)', validators=[
        DataRequired('请输入默认最小间隔时间'),
        NumberRange(min=5, max=300, message='最小间隔时间必须在5-300分钟之间')
    ])
    
    interval_max = IntegerField('默认最大间隔时间(分钟)', validators=[
        DataRequired('请输入默认最大间隔时间'),
        NumberRange(min=10, max=600, message='最大间隔时间必须在10-600分钟之间')
    ])
    
    link_expiration_days = IntegerField('链接有效期(天)', validators=[
        Optional(),
        NumberRange(min=1, message='链接有效期必须大于等于1天')
    ])
    
    def validate_interval_max(self, field):
        """验证最大间隔时间必须大于最小间隔时间"""
        if field.data <= self.interval_min.data:
            raise ValidationError('最大间隔时间必须大于最小间隔时间')


class ReviewSettingForm(FlaskForm):
    """审核流程设置表单"""
    auto_review_first = BooleanField('自动通过初审', default=False)
    auto_review_final = BooleanField('自动通过最终审核', default=False)
    auto_review_delay = IntegerField('自动通过延迟时间(分钟)', validators=[
        Optional(),
        NumberRange(min=1, max=1440, message='延迟时间必须在1-1440分钟之间')
    ])
    rejection_reason_required = BooleanField('拒绝理由必填', default=True)
    auto_confirm_minutes = IntegerField('自动确认时间(分钟)', validators=[
        Optional(),
        NumberRange(min=5, max=1440, message='自动确认时间必须在5-1440分钟之间')
    ])


class PublishSettingForm(FlaskForm):
    """发布设置表单"""
    publish_timeout = IntegerField('发布超时时间(小时)', validators=[
        DataRequired('请输入发布超时时间'),
        NumberRange(min=1, max=72, message='发布超时时间必须在1-72小时之间')
    ])
    
    timeout_action = SelectField('超时处理策略', choices=[
        ('notify', '仅通知'),
        ('retry', '自动重试'),
        ('mark_failed', '标记为失败')
    ], default='notify')
    
    timeout_notify_roles = SelectField('超时通知角色', choices=[
        ('all', '所有角色'),
        ('admin', '仅管理员'),
        ('publisher', '发布管理员')
    ], default='publisher')


class LogSettingForm(FlaskForm):
    """日志设置表单"""
    log_level = SelectField('日志级别', choices=[
        ('DEBUG', '调试'),
        ('INFO', '信息'),
        ('WARNING', '警告'),
        ('ERROR', '错误'),
        ('CRITICAL', '严重错误')
    ], default='INFO')
    
    log_retention_days = IntegerField('日志保留天数', validators=[
        Optional(),
        NumberRange(min=1, max=365, message='日志保留天数必须在1-365天之间')
    ])


class ArchiveSettingForm(FlaskForm):
    """归档设置表单"""
    archive_content_days = IntegerField('文案归档天数', validators=[
        Optional(),
        NumberRange(min=30, message='文案归档天数必须大于等于30天')
    ])
    
    archive_log_days = IntegerField('日志归档天数', validators=[
        Optional(),
        NumberRange(min=7, message='日志归档天数必须大于等于7天')
    ])
    
    archive_notification_days = IntegerField('通知归档天数', validators=[
        Optional(),
        NumberRange(min=7, message='通知归档天数必须大于等于7天')
    ]) 


class ApiSettingForm(FlaskForm):
    """API设置表单"""
    api_key = StringField('API密钥', validators=[DataRequired('请输入API密钥')])
    generate_new = RadioField('是否生成新密钥', choices=[
        ('0', '保持现有密钥'),
        ('1', '生成新密钥')
    ], default='0') 