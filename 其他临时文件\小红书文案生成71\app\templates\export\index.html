{% extends "base.html" %}

{% block title %}导入导出 - 小红书文案生成系统{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
<style>
    .export-card {
        transition: all 0.3s;
    }
    .export-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <h2 class="mb-4">数据导入导出</h2>
    
    <div class="row">
        <!-- 模板导出 -->
        <div class="col-md-6 mb-4">
            <div class="card export-card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">模板导出</h5>
                </div>
                <div class="card-body">
                    <p>导出系统中的模板数据，支持CSV和JSON格式。</p>
                    <form action="{{ url_for('export.export_templates') }}" method="get">
                        <div class="mb-3">
                            <label for="template_category" class="form-label">模板分类</label>
                            <select class="form-select" id="template_category" name="category_id">
                                <option value="">全部分类</option>
                                <!-- 这里可以添加模板分类选项 -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">导出格式</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="format" id="format_csv" value="csv" checked>
                                <label class="form-check-label" for="format_csv">CSV格式</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="format" id="format_json" value="json">
                                <label class="form-check-label" for="format_json">JSON格式</label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-download me-2"></i>导出模板
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 模板导入 -->
        <div class="col-md-6 mb-4">
            <div class="card export-card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">模板导入</h5>
                </div>
                <div class="card-body">
                    <p>导入模板数据，支持CSV和JSON格式。</p>
                    <form action="{{ url_for('export.import_templates') }}" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="mb-3">
                            <label for="template_file" class="form-label">选择文件</label>
                            <input class="form-control" type="file" id="template_file" name="file" accept=".csv,.json">
                            <div class="form-text">支持CSV和JSON格式文件</div>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-upload me-2"></i>导入模板
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 话题导出 -->
        <div class="col-md-6 mb-4">
            <div class="card export-card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">话题导出</h5>
                </div>
                <div class="card-body">
                    <p>导出系统中的话题数据，支持CSV和JSON格式。</p>
                    <form action="{{ url_for('export.export_topics') }}" method="get">
                        <div class="mb-3">
                            <label class="form-label">导出格式</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="format" id="topic_format_csv" value="csv" checked>
                                <label class="form-check-label" for="topic_format_csv">CSV格式</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="format" id="topic_format_json" value="json">
                                <label class="form-check-label" for="topic_format_json">JSON格式</label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-info">
                            <i class="bi bi-download me-2"></i>导出话题
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 话题导入 -->
        <div class="col-md-6 mb-4">
            <div class="card export-card h-100">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">话题导入</h5>
                </div>
                <div class="card-body">
                    <p>导入话题数据，支持CSV和JSON格式。</p>
                    <form action="{{ url_for('export.import_topics') }}" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="mb-3">
                            <label for="topic_file" class="form-label">选择文件</label>
                            <input class="form-control" type="file" id="topic_file" name="file" accept=".csv,.json">
                            <div class="form-text">支持CSV和JSON格式文件</div>
                        </div>
                        <button type="submit" class="btn btn-warning">
                            <i class="bi bi-upload me-2"></i>导入话题
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 客户导出 -->
        <div class="col-md-6 mb-4">
            <div class="card export-card h-100">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">客户导出</h5>
                </div>
                <div class="card-body">
                    <p>导出系统中的客户数据，支持CSV和JSON格式。</p>
                    <form action="{{ url_for('export.export_clients') }}" method="get">
                        <div class="mb-3">
                            <label class="form-label">导出格式</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="format" id="client_format_csv" value="csv" checked>
                                <label class="form-check-label" for="client_format_csv">CSV格式</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="format" id="client_format_json" value="json">
                                <label class="form-check-label" for="client_format_json">JSON格式</label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-danger">
                            <i class="bi bi-download me-2"></i>导出客户
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 客户导入 -->
        <div class="col-md-6 mb-4">
            <div class="card export-card h-100">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">客户导入</h5>
                </div>
                <div class="card-body">
                    <p>导入客户数据，支持CSV和JSON格式。</p>
                    <form action="{{ url_for('export.import_clients') }}" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="mb-3">
                            <label for="client_file" class="form-label">选择文件</label>
                            <input class="form-control" type="file" id="client_file" name="file" accept=".csv,.json">
                            <div class="form-text">支持CSV和JSON格式文件</div>
                        </div>
                        <button type="submit" class="btn btn-secondary">
                            <i class="bi bi-upload me-2"></i>导入客户
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 文案导出 -->
        <div class="col-md-12 mb-4">
            <div class="card export-card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">文案导出</h5>
                </div>
                <div class="card-body">
                    <p>导出系统中的文案数据，支持CSV和JSON格式。可以按任务、客户和状态筛选。</p>
                    <form action="{{ url_for('export.export_contents') }}" method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="content_task" class="form-label">任务</label>
                            <select class="form-select" id="content_task" name="task_id">
                                <option value="">全部任务</option>
                                <!-- 这里可以添加任务选项 -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="content_client" class="form-label">客户</label>
                            <select class="form-select" id="content_client" name="client_id">
                                <option value="">全部客户</option>
                                <!-- 这里可以添加客户选项 -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="content_status" class="form-label">状态</label>
                            <select class="form-select" id="content_status" name="status">
                                <option value="">全部状态</option>
                                <option value="draft">草稿</option>
                                <option value="pending_review">待初审</option>
                                <option value="first_reviewed">初审通过</option>
                                <option value="pending_image">待上传图片</option>
                                <option value="image_uploaded">图片已上传</option>
                                <option value="pending_final_review">待最终审核</option>
                                <option value="pending_client_review">待客户审核</option>
                                <option value="client_rejected">客户已拒绝</option>
                                <option value="client_approved">客户已通过</option>
                                <option value="pending_publish">待发布</option>
                                <option value="published">已发布</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">导出格式</label>
                            <div class="d-flex">
                                <div class="form-check me-3">
                                    <input class="form-check-input" type="radio" name="format" id="content_format_csv" value="csv" checked>
                                    <label class="form-check-label" for="content_format_csv">CSV格式</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="format" id="content_format_json" value="json">
                                    <label class="form-check-label" for="content_format_json">JSON格式</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-dark">
                                <i class="bi bi-download me-2"></i>导出文案
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 加载模板分类
    fetch('/templates/categories')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('template_category');
            if (select) {
                data.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => console.error('加载模板分类失败:', error));
    
    // 加载任务列表
    fetch('/tasks/list-json')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('content_task');
            if (select) {
                data.forEach(task => {
                    const option = document.createElement('option');
                    option.value = task.id;
                    option.textContent = task.name;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => console.error('加载任务列表失败:', error));
    
    // 加载客户列表
    fetch('/clients/list-json')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('content_client');
            if (select) {
                data.forEach(client => {
                    const option = document.createElement('option');
                    option.value = client.id;
                    option.textContent = client.name;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => console.error('加载客户列表失败:', error));
});
</script>
{% endblock %} 