{% extends "base.html" %}

{% block title %}文案统计 - 小红书文案生成系统{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
<style>
    .chart-container {
        height: 300px;
    }
</style>
{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">文案统计</h2>
        <div class="btn-group">
            <a href="{{ url_for('stats.index') }}" class="btn btn-outline-primary">
                <i class="bi bi-bar-chart"></i> 统计概览
            </a>
            <a href="{{ url_for('stats.task_stats') }}" class="btn btn-outline-primary">
                <i class="bi bi-list-task"></i> 任务统计
            </a>
            <a href="{{ url_for('stats.user_stats') }}" class="btn btn-outline-primary">
                <i class="bi bi-people"></i> 用户统计
            </a>
        </div>
    </div>

    <!-- 时间范围选择 -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">时间范围选择</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">开始日期</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">结束日期</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary w-100">查询</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 图表 -->
    <div class="row">
        <!-- 工作流状态分布 -->
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">工作流状态分布</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="workflowChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 发布状态分布 -->
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">发布状态分布</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="publishChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 客户文案统计 -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">客户文案数量排行</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>客户名称</th>
                                    <th>文案数量</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for client in client_stats %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ client.name }}</td>
                                    <td>{{ client.count }}</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="3" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 审核人员统计 -->
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">审核人员数量排行</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>审核人员</th>
                                    <th>审核数量</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for reviewer in reviewer_stats %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ reviewer.username }}</td>
                                    <td>{{ reviewer.count }}</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="3" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 工作流状态详情表格 -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">工作流状态详情</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>状态</th>
                                    <th>数量</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% set status_map = {
                                    'draft': '草稿',
                                    'pending_review': '待初审',
                                    'first_reviewed': '初审通过',
                                    'pending_image': '待上传图片',
                                    'image_uploaded': '图片已上传',
                                    'pending_final_review': '待最终审核',
                                    'pending_client_review': '待客户审核',
                                    'client_rejected': '客户已拒绝',
                                    'client_approved': '客户已通过',
                                    'pending_publish': '待发布',
                                    'published': '已发布'
                                } %}
                                {% for status, count in workflow_stats %}
                                <tr>
                                    <td>{{ status_map.get(status, status) }}</td>
                                    <td>{{ count }}</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="2" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 工作流状态分布饼图
    const workflowCtx = document.getElementById('workflowChart').getContext('2d');
    const workflowLabels = [];
    const workflowData = [];
    const workflowColors = [
        '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', 
        '#6f42c1', '#fd7e14', '#20c997', '#6c757d', '#dc3545', '#17a2b8'
    ];
    
    {% set status_map = {
        'draft': '草稿',
        'pending_review': '待初审',
        'first_reviewed': '初审通过',
        'pending_image': '待上传图片',
        'image_uploaded': '图片已上传',
        'pending_final_review': '待最终审核',
        'pending_client_review': '待客户审核',
        'client_rejected': '客户已拒绝',
        'client_approved': '客户已通过',
        'pending_publish': '待发布',
        'published': '已发布'
    } %}
    
    {% for status, count in workflow_stats %}
        workflowLabels.push('{{ status_map.get(status, status) }}');
        workflowData.push({{ count }});
    {% endfor %}
    
    new Chart(workflowCtx, {
        type: 'doughnut',
        data: {
            labels: workflowLabels,
            datasets: [{
                data: workflowData,
                backgroundColor: workflowColors.slice(0, workflowData.length),
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });
    
    // 发布状态分布饼图
    const publishCtx = document.getElementById('publishChart').getContext('2d');
    const publishLabels = [];
    const publishData = [];
    const publishColors = [
        '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', 
        '#6f42c1'
    ];
    
    {% set publish_map = {
        'unpublished': '未发布',
        'publishing': '发布中',
        'published': '发布成功',
        'failed': '发布失败',
        'partial_published': '部分发布',
        'publish_timeout': '发布超时'
    } %}
    
    {% for status, count in publish_stats %}
        publishLabels.push('{{ publish_map.get(status, status) }}');
        publishData.push({{ count }});
    {% endfor %}
    
    new Chart(publishCtx, {
        type: 'doughnut',
        data: {
            labels: publishLabels,
            datasets: [{
                data: publishData,
                backgroundColor: publishColors.slice(0, publishData.length),
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });
});
</script>
{% endblock %} 