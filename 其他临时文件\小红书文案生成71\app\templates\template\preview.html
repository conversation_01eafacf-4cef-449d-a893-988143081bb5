{% extends "base.html" %}

{% block title %}预览模板 - 小红书文案生成系统{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/github-markdown-css@5.1.0/github-markdown.min.css">
<style>
    .template-content {
        border: 1px solid #ddd;
        padding: 20px;
        border-radius: 5px;
        background-color: #f9f9f9;
    }
    .template-mark {
        background-color: #e9f5ff;
        border: 1px solid #b3d7ff;
        border-radius: 3px;
        padding: 2px 4px;
        font-weight: bold;
        color: #0066cc;
        display: inline-block;
        margin: 0 2px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .markdown-body {
        padding: 15px;
    }
    .markdown-body pre {
        background-color: #f6f8fa;
        border-radius: 3px;
        padding: 16px;
    }
    .markdown-body p {
        margin-bottom: 16px;
    }
    .raw-content {
        white-space: pre-wrap;
        word-wrap: break-word;
        font-family: Consolas, Monaco, 'Andale Mono', monospace;
        padding: 15px;
        background-color: #f6f8fa;
        border-radius: 3px;
        margin-top: 20px;
        border: 1px solid #ddd;
    }
    .highlight-br {
        display: none;  /* 默认隐藏 */
        background-color: #ffff99;
        padding: 2px;
        border-radius: 3px;
        font-size: 0.8em;
        color: #666;
    }
    .show-br-marks .highlight-br {
        display: inline;  /* 显示换行标记 */
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>预览模板</h2>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('template.edit_template', id=template.id) }}" class="btn btn-primary">
                <i class="bi bi-pencil"></i> 编辑模板
            </a>
            <a href="{{ url_for('template.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回模板列表
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">{{ template.title.replace('{', '《').replace('}', '》') }}</h5>
                        <span class="badge {% if template.status %}bg-success{% else %}bg-secondary{% endif %}">
                            {% if template.status %}启用{% else %}禁用{% endif %}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <p><strong>分类：</strong>{{ template.category.name }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>创建者：</strong>{{ template.creator.real_name or template.creator.username }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>创建时间：</strong>{{ template.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>更新时间：</strong>{{ template.updated_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        </div>
                    </div>
                    
                    <h5>模板内容</h5>
                    <ul class="nav nav-tabs" id="previewTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="rendered-tab" data-bs-toggle="tab" data-bs-target="#rendered" type="button" role="tab" aria-controls="rendered" aria-selected="true">渲染视图</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="raw-tab" data-bs-toggle="tab" data-bs-target="#raw" type="button" role="tab" aria-controls="raw" aria-selected="false">原始视图</button>
                        </li>
                    </ul>
                    <div class="tab-content" id="previewTabsContent">
                        <div class="tab-pane fade show active" id="rendered" role="tabpanel" aria-labelledby="rendered-tab">
                            <div class="template-content markdown-body">
                                {{ display_content|nl2br|safe }}
                            </div>
                        </div>
                        <div class="tab-pane fade" id="raw" role="tabpanel" aria-labelledby="raw-tab">
                            <div class="raw-content">{{ display_content }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 为标记添加样式
        const templateContent = document.querySelector('.template-content');
        if (templateContent) {
            let html = templateContent.innerHTML;
            html = html.replace(/《([^》]+)》/g, '<span class="template-mark">《$1》</span>');
            templateContent.innerHTML = html;
            
            // 在每个<br>后添加一个可见的标记，以便更好地查看换行位置
            const brElements = templateContent.querySelectorAll('br');
            brElements.forEach(function(br) {
                const span = document.createElement('span');
                span.className = 'highlight-br';
                span.textContent = '↵';  // 换行符号
                br.parentNode.insertBefore(span, br.nextSibling);
            });
        }
        
        // 添加一个切换按钮，用于显示/隐藏换行标记
        const tabsContainer = document.getElementById('previewTabs');
        if (tabsContainer) {
            const toggleButton = document.createElement('button');
            toggleButton.className = 'btn btn-sm btn-outline-secondary ms-3';
            toggleButton.textContent = '显示换行标记';
            toggleButton.addEventListener('click', function() {
                const content = document.querySelector('.template-content');
                if (content.classList.toggle('show-br-marks')) {
                    this.textContent = '隐藏换行标记';
                } else {
                    this.textContent = '显示换行标记';
                }
            });
            tabsContainer.parentNode.appendChild(toggleButton);
        }
    });
</script>
{% endblock %} 