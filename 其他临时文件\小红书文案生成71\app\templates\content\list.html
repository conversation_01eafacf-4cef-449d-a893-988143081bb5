{% extends "base_simple.html" %}

{% block title %}初审文案{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-clipboard-check text-primary"></i> 初审文案
            <small class="text-muted fs-6">审核生成的文案内容</small>
        </h2>
        <div class="btn-group">
            <a href="/simple/content" class="btn btn-success">
                <i class="fas fa-magic"></i> 生成文案
            </a>
            <button type="button" class="btn btn-outline-primary" onclick="refreshPage()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-light">
            <h6 class="mb-3"><i class="fas fa-filter"></i> 筛选条件</h6>
            <form method="get" class="row g-3">
                <div class="col-md-2">
                    <div class="form-group">
                        {{ form.client_id.label(class="form-label") }}
                        {{ form.client_id(class="form-select", id="client_id") }}
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        {{ form.task_id.label(class="form-label") }}
                        {{ form.task_id(class="form-select", id="task_id") }}
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label class="form-label">批次</label>
                        <select name="batch_id" id="batch_id" class="form-select">
                            <option value="0">全部批次</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        {{ form.status.label(class="form-label") }}
                        {{ form.status(class="form-select") }}
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        {{ form.search.label(class="form-label") }}
                        {{ form.search(class="form-control", value=search, placeholder="搜索标题或内容") }}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        {{ form.date_from.label(class="form-label") }}
                        {{ form.date_from(class="form-control", type="date", value=date_from if date_from else '') }}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        {{ form.date_to.label(class="form-label") }}
                        {{ form.date_to(class="form-control", type="date", value=date_to if date_to else '') }}
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        {{ form.sort_by.label(class="form-label") }}
                        {{ form.sort_by(class="form-select") }}
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        {{ form.sort_order.label(class="form-label") }}
                        {{ form.sort_order(class="form-select") }}
                    </div>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> 筛选
                    </button>
                </div>
            </form>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40px">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th width="60px">ID</th>
                            <th>标题</th>
                            <th width="100px">客户</th>
                            <th width="120px">任务</th>
                            <th width="100px">批次</th>
                            <th width="100px">状态</th>
                            <th width="120px">创建时间</th>
                            <th width="150px">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for content in contents %}
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input content-checkbox" type="checkbox" value="{{ content.id }}">
                                </div>
                            </td>
                            <td>{{ content.id }}</td>
                            <td>
                                <span class="text-decoration-none">
                                    {{ content.title[:50] }}{% if content.title|length > 50 %}...{% endif %}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">{{ content.client.name if content.client else '-' }}</span>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ content.task.name if content.task else '-' }}</span>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ content.batch.name if content.batch else '-' }}</span>
                            </td>
                            <td>
                                {% set status_map = {
                                    'draft': ('草稿', 'secondary'),
                                    'pending_review': ('待初审', 'warning'),
                                    'first_reviewed': ('初审通过', 'success'),
                                    'pending_image': ('待上传图片', 'info'),
                                    'image_uploaded': ('图片已上传', 'info'),
                                    'pending_final_review': ('待最终审核', 'warning'),
                                    'pending_client_review': ('待客户审核', 'primary'),
                                    'client_rejected': ('客户已拒绝', 'danger'),
                                    'client_approved': ('客户已通过', 'success'),
                                    'pending_publish': ('待发布', 'info'),
                                    'published': ('已发布', 'success')
                                } %}
                                {% set status_text, status_class = status_map.get(content.workflow_status, ('未知', 'secondary')) %}
                                <span class="badge bg-{{ status_class }}">{{ status_text }}</span>
                            </td>
                            <td>
                                <small>{{ content.created_at.strftime('%m-%d %H:%M') }}</small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-info btn-sm" title="查看详情" onclick="viewContent({{ content.id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>

                                    {% if content.workflow_status in ['draft', 'pending_review'] %}
                                    <button type="button" class="btn btn-outline-success btn-sm review-btn"
                                            data-content-id="{{ content.id }}"
                                            data-action="approve"
                                            title="通过初审">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-warning btn-sm review-btn"
                                            data-content-id="{{ content.id }}"
                                            data-action="reject"
                                            title="拒绝并要求修改">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}

                                    {% if current_user.has_permission('content_edit') and content.workflow_status in ['draft', 'pending_review'] %}
                                    <button type="button" class="btn btn-outline-primary btn-sm" title="编辑" onclick="editContent({{ content.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="9" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-inbox fa-2x mb-2"></i>
                                    <p class="mb-0">暂无待初审的文案</p>
                                    <small>请先到<a href="/simple/content">文案生成</a>页面生成文案</small>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        {% if contents %}
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="batch-actions">
                    <div class="d-flex align-items-center gap-3">
                        <span class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            已选择 <span id="selectedCount">0</span> 篇文案
                        </span>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success btn-sm batch-review-btn" data-action="approve">
                                <i class="fas fa-check"></i> 批量通过
                            </button>
                            <button type="button" class="btn btn-warning btn-sm batch-review-btn" data-action="reject">
                                <i class="fas fa-times"></i> 批量拒绝
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                                <i class="fas fa-times"></i> 清除选择
                            </button>
                        </div>
                    </div>
                </div>
                
                {% if pagination.pages > 1 %}
                <nav>
                    <ul class="pagination justify-content-center mb-0">
                        {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ pagination.prev_num }}&client_id={{ client_id }}&task_id={{ task_id }}&batch_id={{ batch_id }}&status={{ status }}&date_from={{ date_from }}&date_to={{ date_to }}&search={{ search }}&sort_by={{ sort_by }}&sort_order={{ sort_order }}"
                                <span>&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">&laquo;</span>
                        </li>
                        {% endif %}
                        
                        {% for page in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                            {% if page %}
                                {% if page == pagination.page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page }}</span>
                                </li>
                                {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page }}&client_id={{ client_id }}&task_id={{ task_id }}&batch_id={{ batch_id }}&status={{ status }}&date_from={{ date_from }}&date_to={{ date_to }}&search={{ search }}&sort_by={{ sort_by }}&sort_order={{ sort_order }}">{{ page }}</a>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ pagination.next_num }}&client_id={{ client_id }}&task_id={{ task_id }}&batch_id={{ batch_id }}&status={{ status }}&date_from={{ date_from }}&date_to={{ date_to }}&search={{ search }}&sort_by={{ sort_by }}&sort_order={{ sort_order }}"
                                <span>&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">&raquo;</span>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 删除确认现在使用 JavaScript confirm 对话框，不需要模态框 -->

<!-- 批量操作确认现在使用 JavaScript confirm 对话框，不需要模态框 -->
{% endblock %}

{% block styles %}
<style>
    .batch-actions {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-top: 15px;
        border: 1px solid #dee2e6;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        font-weight: 600;
        color: #495057;
        font-size: 0.9rem;
    }

    .table td {
        vertical-align: middle;
        font-size: 0.9rem;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0,123,255,.075);
    }

    .badge {
        font-size: 0.75em;
        font-weight: 500;
    }

    .btn-group-sm > .btn, .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .pagination .page-link {
        color: #0d6efd;
    }

    .pagination .page-item.active .page-link {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    /* 自定义模态框样式 */
    .custom-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1050;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .custom-modal-overlay.show {
        opacity: 1;
    }

    .custom-modal {
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        max-width: 500px;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
        transform: scale(0.9);
        transition: transform 0.3s ease;
    }

    .custom-modal-overlay.show .custom-modal {
        transform: scale(1);
    }

    .custom-modal-header {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #dee2e6;
        background-color: #f8f9fa;
        border-radius: 8px 8px 0 0;
    }

    .custom-modal-title {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .custom-modal-body {
        padding: 1.5rem;
    }

    .custom-modal-footer {
        padding: 1rem 1.5rem;
        border-top: 1px solid #dee2e6;
        background-color: #f8f9fa;
        border-radius: 0 0 8px 8px;
        display: flex;
        justify-content: flex-end;
        gap: 0.5rem;
    }

    /* 状态徽章颜色优化 */
    .badge.bg-light {
        color: #495057 !important;
        background-color: #e9ecef !important;
    }

    .badge.bg-info {
        background-color: #0dcaf0 !important;
    }

    .badge.bg-secondary {
        background-color: #6c757d !important;
    }

    /* 按钮组优化 */
    .btn-group .btn {
        border-radius: 4px !important;
        margin-right: 2px;
    }

    .btn-group .btn:last-child {
        margin-right: 0;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    // 自定义确认对话框
    function showCustomConfirm(title, message, onConfirm, onCancel) {
        // 创建对话框HTML
        const modalHtml = `
            <div class="custom-modal-overlay" id="customConfirmModal">
                <div class="custom-modal">
                    <div class="custom-modal-header">
                        <h5 class="custom-modal-title">
                            <i class="fas fa-exclamation-triangle"></i>
                            ${title}
                        </h5>
                    </div>
                    <div class="custom-modal-body">
                        ${message}
                    </div>
                    <div class="custom-modal-footer">
                        <button type="button" class="btn btn-secondary" id="customConfirmCancel">取消</button>
                        <button type="button" class="btn btn-danger" id="customConfirmOk">确认</button>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        const modal = document.getElementById('customConfirmModal');
        const okBtn = document.getElementById('customConfirmOk');
        const cancelBtn = document.getElementById('customConfirmCancel');

        // 显示对话框
        setTimeout(() => modal.classList.add('show'), 10);

        // 绑定事件
        okBtn.addEventListener('click', function() {
            hideModal();
            if (onConfirm) onConfirm();
        });

        cancelBtn.addEventListener('click', function() {
            hideModal();
            if (onCancel) onCancel();
        });

        // 点击遮罩关闭
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                hideModal();
                if (onCancel) onCancel();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', function escHandler(e) {
            if (e.key === 'Escape') {
                hideModal();
                if (onCancel) onCancel();
                document.removeEventListener('keydown', escHandler);
            }
        });

        function hideModal() {
            modal.classList.remove('show');
            setTimeout(() => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            }, 300);
        }
    }

    // 自定义提示框
    function showCustomToast(message, type = 'success', duration = 3000) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-times-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        const toastHtml = `
            <div class="custom-toast ${type}" id="customToast">
                <i class="${icons[type] || icons.success}"></i>
                <div class="custom-toast-message">${message}</div>
            </div>
        `;

        // 移除已存在的提示框
        const existingToast = document.getElementById('customToast');
        if (existingToast) {
            existingToast.parentNode.removeChild(existingToast);
        }

        // 添加新提示框
        document.body.insertAdjacentHTML('beforeend', toastHtml);

        const toast = document.getElementById('customToast');

        // 显示提示框
        setTimeout(() => toast.classList.add('show'), 10);

        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }



    // 全选/取消全选功能
    function toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const contentCheckboxes = document.querySelectorAll('.content-checkbox');

        if (selectAllCheckbox && contentCheckboxes.length > 0) {
            const isChecked = selectAllCheckbox.checked;
            contentCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            console.log('Toggle select all:', isChecked);
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM Content Loaded');
        console.log('Bootstrap version:', typeof bootstrap !== 'undefined' ? 'Available' : 'Not available');

        // 绑定全选复选框事件
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', toggleSelectAll);
        }
        
        // 客户选择变化时，获取对应的任务列表
        const clientSelect = document.getElementById('client_id');
        const taskSelect = document.getElementById('task_id');
        const batchSelect = document.getElementById('batch_id');

        if (clientSelect) {
            clientSelect.addEventListener('change', function() {
                const clientId = this.value;

                // 清空任务和批次选择
                taskSelect.innerHTML = '<option value="0">全部任务</option>';
                batchSelect.innerHTML = '<option value="0">全部批次</option>';

                if (clientId > 0) {
                    fetch(`/simple/contents/get-tasks/${clientId}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                data.tasks.forEach(task => {
                                    const option = document.createElement('option');
                                    option.value = task.id;
                                    option.textContent = task.name;
                                    taskSelect.appendChild(option);
                                });
                            }
                        })
                        .catch(error => console.error('Error:', error));
                }
            });
        }

        // 任务选择变化时，获取对应的批次列表
        if (taskSelect) {
            taskSelect.addEventListener('change', function() {
                const taskId = this.value;

                // 清空批次选择
                batchSelect.innerHTML = '<option value="0">全部批次</option>';

                if (taskId > 0) {
                    fetch(`/simple/contents/get-batches/${taskId}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                data.batches.forEach(batch => {
                                    const option = document.createElement('option');
                                    option.value = batch.id;
                                    option.textContent = batch.name;
                                    batchSelect.appendChild(option);
                                });
                            }
                        })
                        .catch(error => console.error('Error:', error));
                }
            });
        }

        // 选择计数更新
        updateSelectedCount();
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('content-checkbox') || e.target.id === 'selectAll') {
                updateSelectedCount();
            }
        });
    });

    // 更新选择计数
    function updateSelectedCount() {
        const checkboxes = document.querySelectorAll('.content-checkbox:checked');
        const count = checkboxes.length;
        const countSpan = document.getElementById('selectedCount');
        if (countSpan) {
            countSpan.textContent = count;
        }

        // 显示/隐藏批量操作按钮
        const batchActions = document.querySelectorAll('.batch-review-btn');
        batchActions.forEach(btn => {
            btn.style.display = count > 0 ? 'inline-block' : 'none';
        });
    }

    // 清除选择
    function clearSelection() {
        const checkboxes = document.querySelectorAll('.content-checkbox, #selectAll');
        checkboxes.forEach(cb => cb.checked = false);
        updateSelectedCount();
    }

    // 刷新页面
    function refreshPage() {
        window.location.reload();
    }

    // 单个文案初审
    document.addEventListener('click', function(e) {
        if (e.target.closest('.review-btn')) {
            const btn = e.target.closest('.review-btn');
            const contentId = btn.getAttribute('data-content-id');
            const action = btn.getAttribute('data-action');

            if (action === 'approve') {
                reviewContent(contentId, 'approve', '通过初审');
            } else if (action === 'reject') {
                showRejectModal(contentId);
            }
        }
    });

    // 批量初审
    document.addEventListener('click', function(e) {
        if (e.target.closest('.batch-review-btn')) {
            const btn = e.target.closest('.batch-review-btn');
            const action = btn.getAttribute('data-action');
            const checkboxes = document.querySelectorAll('.content-checkbox:checked');

            if (checkboxes.length === 0) {
                showCustomToast('请先选择要操作的文案', 'warning');
                return;
            }

            const contentIds = Array.from(checkboxes).map(cb => cb.value);

            if (action === 'approve') {
                batchReviewContent(contentIds, 'approve', '批量通过初审');
            } else if (action === 'reject') {
                showBatchRejectModal(contentIds);
            }
        }
    });

    // 执行初审操作
    function reviewContent(contentId, action, actionText) {
        const reason = action === 'reject' ? document.getElementById('rejectReason').value : '';

        // 创建表单数据
        const formData = new FormData();
        formData.append('status', action === 'approve' ? 'approved' : 'rejected');
        formData.append('reason', reason);
        // 获取CSRF token
        const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '';
        if (csrfToken) {
            formData.append('csrf_token', csrfToken);
        }

        fetch(`/simple/contents/${contentId}/review`, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                showCustomToast(`${actionText}成功`, 'success');
                setTimeout(() => window.location.reload(), 1000);
            } else {
                showCustomToast(`${actionText}失败`, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showCustomToast(`${actionText}失败，请重试`, 'error');
        });
    }

    // 批量初审操作
    function batchReviewContent(contentIds, action, actionText) {
        const reason = action === 'reject' ? document.getElementById('batchRejectReason').value : '';

        // 创建表单数据
        const formData = new FormData();
        formData.append('action', 'review');
        formData.append('content_ids', contentIds.join(','));
        formData.append('status', action === 'approve' ? 'approved' : 'rejected');
        formData.append('reason', reason);
        formData.append('confirm', '1');
        // 获取CSRF token
        const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '';
        if (csrfToken) {
            formData.append('csrf_token', csrfToken);
        }

        fetch('/simple/contents/batch-action', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                showCustomToast(`${actionText}成功`, 'success');
                setTimeout(() => window.location.reload(), 1000);
            } else {
                showCustomToast(`${actionText}失败`, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showCustomToast(`${actionText}失败，请重试`, 'error');
        });
    }

    // 显示拒绝原因模态框
    function showRejectModal(contentId) {
        const modalHtml = `
            <div class="custom-modal-overlay" id="rejectModal">
                <div class="custom-modal">
                    <div class="custom-modal-header">
                        <h5 class="custom-modal-title">
                            <i class="fas fa-times-circle text-warning"></i>
                            拒绝文案
                        </h5>
                    </div>
                    <div class="custom-modal-body">
                        <div class="form-group">
                            <label for="rejectReason" class="form-label">拒绝原因：</label>
                            <textarea id="rejectReason" class="form-control" rows="3" placeholder="请输入拒绝原因..."></textarea>
                        </div>
                    </div>
                    <div class="custom-modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeRejectModal()">取消</button>
                        <button type="button" class="btn btn-warning" onclick="confirmReject(${contentId})">确认拒绝</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        setTimeout(() => document.getElementById('rejectModal').classList.add('show'), 10);
    }

    // 显示批量拒绝原因模态框
    function showBatchRejectModal(contentIds) {
        const modalHtml = `
            <div class="custom-modal-overlay" id="batchRejectModal">
                <div class="custom-modal">
                    <div class="custom-modal-header">
                        <h5 class="custom-modal-title">
                            <i class="fas fa-times-circle text-warning"></i>
                            批量拒绝文案
                        </h5>
                    </div>
                    <div class="custom-modal-body">
                        <p>即将拒绝 <strong>${contentIds.length}</strong> 篇文案</p>
                        <div class="form-group">
                            <label for="batchRejectReason" class="form-label">拒绝原因：</label>
                            <textarea id="batchRejectReason" class="form-control" rows="3" placeholder="请输入拒绝原因..."></textarea>
                        </div>
                    </div>
                    <div class="custom-modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeBatchRejectModal()">取消</button>
                        <button type="button" class="btn btn-warning" onclick="confirmBatchReject([${contentIds.join(',')}])">确认拒绝</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        setTimeout(() => document.getElementById('batchRejectModal').classList.add('show'), 10);
    }

    // 关闭拒绝模态框
    function closeRejectModal() {
        const modal = document.getElementById('rejectModal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => modal.remove(), 300);
        }
    }

    function closeBatchRejectModal() {
        const modal = document.getElementById('batchRejectModal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => modal.remove(), 300);
        }
    }

    // 确认拒绝
    function confirmReject(contentId) {
        reviewContent(contentId, 'reject', '拒绝文案');
        closeRejectModal();
    }

    function confirmBatchReject(contentIds) {
        batchReviewContent(contentIds, 'reject', '批量拒绝文案');
        closeBatchRejectModal();
    }

    // 查看内容详情
    function viewContent(contentId) {
        showCustomToast('查看功能暂未实现', 'info');
    }

    // 编辑内容
    function editContent(contentId) {
        showCustomToast('编辑功能暂未实现', 'info');
    }


</script>
{% endblock %} 