{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content_auth %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-key me-2"></i>{{ title }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>用户信息</h6>
                            <p><strong>用户名：</strong>{{ user.username }}</p>
                            <p><strong>真实姓名：</strong>{{ user.real_name or '未设置' }}</p>
                            <p><strong>邮箱：</strong>{{ user.email }}</p>
                            <p><strong>状态：</strong>
                                {% if user.is_active %}
                                    <span class="badge bg-success">启用</span>
                                {% else %}
                                    <span class="badge bg-danger">禁用</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>

                    <form method="post">
                        {{ form.csrf_token }}
                        
                        <h6 class="mb-3">菜单权限设置</h6>
                        <p class="text-muted mb-4">选择用户可以访问的菜单功能：</p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">基础功能</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check mb-2">
                                            {{ form.dashboard_access(class="form-check-input") }}
                                            {{ form.dashboard_access.label(class="form-check-label") }}
                                        </div>
                                        <div class="form-check mb-2">
                                            {{ form.content_generate(class="form-check-input") }}
                                            {{ form.content_generate.label(class="form-check-label") }}
                                        </div>
                                        <div class="form-check mb-2">
                                            {{ form.content_manage(class="form-check-input") }}
                                            {{ form.content_manage.label(class="form-check-label") }}
                                        </div>
                                        <div class="form-check mb-2">
                                            {{ form.template_manage(class="form-check-input") }}
                                            {{ form.template_manage.label(class="form-check-label") }}
                                        </div>
                                        <div class="form-check mb-2">
                                            {{ form.topic_manage(class="form-check-input") }}
                                            {{ form.topic_manage.label(class="form-check-label") }}
                                        </div>
                                        <div class="form-check mb-2">
                                            {{ form.client_manage(class="form-check-input") }}
                                            {{ form.client_manage.label(class="form-check-label") }}
                                        </div>
                                        <div class="form-check mb-2">
                                            {{ form.task_manage(class="form-check-input") }}
                                            {{ form.task_manage.label(class="form-check-label") }}
                                        </div>
                                        <div class="form-check mb-2">
                                            {{ form.notification_manage(class="form-check-input") }}
                                            {{ form.notification_manage.label(class="form-check-label") }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">高级功能</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check mb-2">
                                            {{ form.publish_manage(class="form-check-input") }}
                                            {{ form.publish_manage.label(class="form-check-label") }}
                                        </div>
                                        <div class="form-check mb-2">
                                            {{ form.supplement_manage(class="form-check-input") }}
                                            {{ form.supplement_manage.label(class="form-check-label") }}
                                        </div>
                                        <div class="form-check mb-2">
                                            {{ form.display_manage(class="form-check-input") }}
                                            {{ form.display_manage.label(class="form-check-label") }}
                                        </div>
                                        <div class="form-check mb-2">
                                            {{ form.stats_view(class="form-check-input") }}
                                            {{ form.stats_view.label(class="form-check-label") }}
                                        </div>
                                        <div class="form-check mb-2">
                                            {{ form.export_manage(class="form-check-input") }}
                                            {{ form.export_manage.label(class="form-check-label") }}
                                        </div>
                                        <div class="form-check mb-2">
                                            {{ form.user_manage(class="form-check-input") }}
                                            {{ form.user_manage.label(class="form-check-label") }}
                                        </div>
                                        <div class="form-check mb-2">
                                            {{ form.system_settings(class="form-check-input") }}
                                            {{ form.system_settings.label(class="form-check-label") }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>保存权限
                            </button>
                            <a href="{{ url_for('user_management.user_list') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>返回
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 全选/取消全选功能
    const selectAllBtn = document.createElement('button');
    selectAllBtn.type = 'button';
    selectAllBtn.className = 'btn btn-sm btn-outline-primary me-2 mb-3';
    selectAllBtn.innerHTML = '<i class="fas fa-check-square me-1"></i>全选';
    
    const unselectAllBtn = document.createElement('button');
    unselectAllBtn.type = 'button';
    unselectAllBtn.className = 'btn btn-sm btn-outline-secondary mb-3';
    unselectAllBtn.innerHTML = '<i class="fas fa-square me-1"></i>取消全选';
    
    const form = document.querySelector('form');
    const firstCard = form.querySelector('.card');
    firstCard.insertBefore(selectAllBtn, firstCard.firstChild);
    firstCard.insertBefore(unselectAllBtn, firstCard.firstChild);
    
    selectAllBtn.addEventListener('click', function() {
        const checkboxes = form.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => cb.checked = true);
    });
    
    unselectAllBtn.addEventListener('click', function() {
        const checkboxes = form.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => cb.checked = false);
    });
});
</script>
{% endblock %}
