-- 小红书文案生成系统 - 权限修复SQL
-- 用于修复角色权限关联缺失问题

-- 1. 清空现有角色权限关联数据（如果有的话）
DELETE FROM role_permissions;

-- 2. 为超级管理员角色(id=2)分配所有权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 2, id FROM permissions;

-- 3. 为内容编辑角色(id=3)分配权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 3, id FROM permissions WHERE name IN (
    'template_manage',
    'topic_manage',
    'client_view',
    'content_create',
    'content_review',
    'content_edit',
    'content_manage',
    'stats_view'
);

-- 4. 为客户经理角色(id=4)分配权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 4, id FROM permissions WHERE name IN (
    'client_view',
    'client_manage',
    'task_manage',
    'content_view',
    'stats_view',
    'content_export'
);

-- 5. 为运营专员角色(id=5)分配权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 5, id FROM permissions WHERE name IN (
    'content_view',
    'content_review',
    'content_publish',
    'stats_view',
    'display_manage'
);

-- 6. 为普通用户角色(id=6)分配基础权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 6, id FROM permissions WHERE name IN (
    'content_view',
    'stats_view'
);

-- 7. 确保admin用户(id=1)关联到超级管理员角色(id=2)
-- 这一步通常不需要，因为user_roles表中已经有记录，但为了安全起见还是检查一下
INSERT INTO user_roles (user_id, role_id)
SELECT 1, 2
WHERE NOT EXISTS (SELECT 1 FROM user_roles WHERE user_id = 1 AND role_id = 2);

-- 8. 确保admin用户是激活状态
UPDATE users SET is_active = 1 WHERE id = 1;

-- 执行完成提示
SELECT '权限修复完成！' AS message;
SELECT '超级管理员已拥有所有权限' AS admin_info;
SELECT '所有角色的权限已正确分配' AS roles_info; 