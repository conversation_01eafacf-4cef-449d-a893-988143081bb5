"""
测试配置文件
"""
import os
import tempfile
import pytest
from flask import Flask

from app import create_app, db
from app.models.user import User, Role, Permission, UserRole
from app.models.client import Client
from app.models.template import Template, TemplateCategory
from app.models.topic import Topic
from app.models.task import Task
from app.models.content import Content


@pytest.fixture
def app():
    """创建测试应用实例"""
    app = create_app('testing')
    
    # 创建临时数据库文件
    db_fd, db_path = tempfile.mkstemp()
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    
    # 创建应用上下文
    with app.app_context():
        # 创建数据库表
        db.create_all()
        
        # 初始化测试数据
        init_test_data()
    
    yield app
    
    # 清理
    os.close(db_fd)
    os.unlink(db_path)


@pytest.fixture
def client(app):
    """创建测试客户端"""
    return app.test_client()


@pytest.fixture
def runner(app):
    """创建测试命令行运行器"""
    return app.test_cli_runner()


def init_test_data():
    """初始化测试数据"""
    # 创建权限
    permissions = {
        'admin_access': Permission(name='admin_access', description='管理员访问权限'),
        'content_create': Permission(name='content_create', description='创建文案权限'),
        'content_review': Permission(name='content_review', description='审核文案权限'),
        'content_manage': Permission(name='content_manage', description='管理文案权限'),
        'template_manage': Permission(name='template_manage', description='管理模板权限'),
        'topic_manage': Permission(name='topic_manage', description='管理话题权限'),
        'client_manage': Permission(name='client_manage', description='管理客户权限'),
        'task_manage': Permission(name='task_manage', description='管理任务权限'),
    }
    
    for permission in permissions.values():
        db.session.add(permission)
    
    # 创建角色
    roles = {
        'admin': Role(name='admin', description='管理员'),
        'editor': Role(name='editor', description='编辑'),
        'reviewer': Role(name='reviewer', description='审核员'),
        'client': Role(name='client', description='客户'),
    }
    
    for role in roles.values():
        db.session.add(role)
    
    db.session.commit()
    
    # 设置角色权限
    # 管理员拥有所有权限
    for permission in permissions.values():
        roles['admin'].permissions.append(permission)
    
    # 编辑拥有创建文案和管理模板权限
    roles['editor'].permissions.append(permissions['content_create'])
    roles['editor'].permissions.append(permissions['template_manage'])
    
    # 审核员拥有审核文案权限
    roles['reviewer'].permissions.append(permissions['content_review'])
    
    # 客户无特殊权限
    
    db.session.commit()
    
    # 创建测试用户
    users = {
        'admin': User(username='admin', email='<EMAIL>', password='password', is_active=True),
        'editor': User(username='editor', email='<EMAIL>', password='password', is_active=True),
        'reviewer': User(username='reviewer', email='<EMAIL>', password='password', is_active=True),
        'client': User(username='client', email='<EMAIL>', password='password', is_active=True),
    }
    
    for user in users.values():
        db.session.add(user)
    
    db.session.commit()
    
    # 分配角色
    db.session.add(UserRole(user_id=users['admin'].id, role_id=roles['admin'].id))
    db.session.add(UserRole(user_id=users['editor'].id, role_id=roles['editor'].id))
    db.session.add(UserRole(user_id=users['reviewer'].id, role_id=roles['reviewer'].id))
    db.session.add(UserRole(user_id=users['client'].id, role_id=roles['client'].id))
    
    # 创建测试客户
    test_client = Client(
        name='测试客户',
        contact='联系人',
        phone='13800138000',
        email='<EMAIL>',
        need_review=True,
        daily_content_count=5,
        interval_min=30,
        interval_max=120,
        status=True,
        user_id=users['client'].id
    )
    db.session.add(test_client)
    
    # 创建测试模板分类
    category = TemplateCategory(name='测试分类', parent_id=None)
    db.session.add(category)
    db.session.commit()
    
    # 创建测试模板
    template = Template(
        title='测试模板',
        content='这是一个测试模板，包含{{关键词1}}和{{关键词2}}',
        category_id=category.id,
        creator_id=users['admin'].id,
        status=True
    )
    db.session.add(template)
    
    # 创建测试话题
    topic = Topic(name='测试话题', type='required', priority=1)
    db.session.add(topic)
    
    # 创建测试任务
    task = Task(
        name='测试任务',
        description='这是一个测试任务',
        client_id=test_client.id,
        template_id=template.id,
        target_count=10,
        actual_count=0,
        status='pending',
        created_by=users['admin'].id
    )
    db.session.add(task)
    db.session.commit()
    
    # 创建测试文案
    content = Content(
        title='测试文案',
        content='这是一个测试文案内容',
        topics='测试话题',
        client_id=test_client.id,
        task_id=task.id,
        created_by=users['editor'].id,
        workflow_status='draft'
    )
    db.session.add(content)
    
    db.session.commit() 