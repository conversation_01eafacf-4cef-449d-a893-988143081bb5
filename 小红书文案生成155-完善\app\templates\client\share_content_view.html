{% extends "client/share_layout.html" %}

{% block title %}文案详情 - {{ content.title }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">{{ content.title }}</h4>
                    <div>
                        {% if content.client_review_status == 'approved' %}
                        <span class="badge bg-success">已通过</span>
                        {% elif content.client_review_status == 'rejected' %}
                        <span class="badge bg-danger">已拒绝</span>
                        {% else %}
                        <span class="badge bg-warning text-dark">待审核</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <div class="d-flex justify-content-between mb-3">
                            <div>
                                <span class="badge bg-light text-dark me-2">展示日期: {{ content.display_date.strftime('%Y-%m-%d') }}</span>
                                <span class="badge bg-light text-dark">创建时间: {{ content.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                            </div>
                            <a href="{{ url_for('client.share_access', token=share.access_token) }}{% if request.args.get('key') %}?key={{ request.args.get('key') }}{% endif %}" class="btn btn-sm btn-secondary">
                                <i class="fas fa-arrow-left"></i> 返回列表
                            </a>
                        </div>
                        
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">文案内容</h5>
                            </div>
                            <div class="card-body">
                                <div class="content-text">
                                    {{ content.content|nl2br }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">话题标签</h5>
                                    </div>
                                    <div class="card-body">
                                        {% if content.topics %}
                                            {% for topic in content.topics %}
                                                <span class="badge bg-info me-1 mb-1">#{{ topic }}</span>
                                            {% endfor %}
                                        {% else %}
                                            <span class="text-muted">无话题标签</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">位置信息</h5>
                                    </div>
                                    <div class="card-body">
                                        {% if content.location %}
                                            <span class="badge bg-secondary">{{ content.location }}</span>
                                        {% else %}
                                            <span class="text-muted">无位置信息</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        {% if content.image_urls %}
                        <div class="card mt-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">图片</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    {% for image_url in content.image_urls %}
                                    <div class="col-md-4 mb-3">
                                        <a href="{{ image_url }}" target="_blank" class="d-block">
                                            <img src="{{ image_url }}" alt="图片" class="img-fluid rounded">
                                        </a>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('client.share_access', token=share.access_token) }}{% if request.args.get('key') %}?key={{ request.args.get('key') }}{% endif %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回文案列表
                    </a>
                    
                    {% if share.review_permission and content.client_review_status == 'pending' %}
                    <div class="float-end">
                        <a href="{{ url_for('client.share_content_approve', token=share.access_token, content_id=content.id) }}{% if request.args.get('key') %}?key={{ request.args.get('key') }}{% endif %}" class="btn btn-success">
                            <i class="fas fa-check"></i> 通过
                        </a>
                        <a href="{{ url_for('client.share_content_reject', token=share.access_token, content_id=content.id) }}{% if request.args.get('key') %}?key={{ request.args.get('key') }}{% endif %}" class="btn btn-danger">
                            <i class="fas fa-times"></i> 拒绝
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 