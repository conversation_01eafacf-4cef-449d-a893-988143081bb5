{% extends "base_simple.html" %}

{% block title %}测试换行符 - 小红书文案生成系统{% endblock %}

{% block styles %}
<style>
    .test-content {
        border: 1px solid #ddd;
        padding: 20px;
        border-radius: 5px;
        background-color: #f9f9f9;
        margin-bottom: 20px;
    }
    .raw-content {
        white-space: pre-wrap;
        word-wrap: break-word;
        font-family: Consolas, Monaco, 'Andale Mono', monospace;
        padding: 15px;
        background-color: #f6f8fa;
        border-radius: 3px;
        margin-top: 20px;
        border: 1px solid #ddd;
    }
    .test-section {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }
    .highlight-br {
        background-color: #ffff99;
        padding: 2px;
        border-radius: 3px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h2>测试换行符处理</h2>
            <p>本页面用于测试nl2br过滤器对换行符的处理，特别是连续换行和末尾换行的情况。</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">测试用例</h5>
                </div>
                <div class="card-body">
                    <!-- 测试用例1：基本测试 -->
                    <div class="test-section">
                        <h5>测试用例1：基本测试</h5>
                        <div class="test-content">
                            {{ test_content|nl2br|safe }}
                        </div>
                        
                        <h6>原始内容</h6>
                        <div class="raw-content">{{ test_content }}</div>
                        
                        <h6>HTML源码</h6>
                        <div class="raw-content">{{ test_content|nl2br }}</div>
                    </div>
                    
                    <!-- 测试用例2：计数测试 -->
                    <div class="test-section">
                        <h5>测试用例2：计数测试（数字间的空行数应该精确）</h5>
                        <div class="test-content">
                            {{ count_test|nl2br|safe }}
                        </div>
                        
                        <h6>原始内容</h6>
                        <div class="raw-content">{{ count_test }}</div>
                        
                        <h6>HTML源码</h6>
                        <div class="raw-content">{{ count_test|nl2br }}</div>
                    </div>
                    
                    <!-- 测试用例3：末尾换行测试 -->
                    <div class="test-section">
                        <h5>测试用例3：末尾换行测试</h5>
                        <div class="test-content">
                            {{ end_test|nl2br|safe }}
                        </div>
                        
                        <h6>原始内容</h6>
                        <div class="raw-content">{{ end_test }}</div>
                        
                        <h6>HTML源码</h6>
                        <div class="raw-content">{{ end_test|nl2br }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 高亮显示<br>标签，使其更容易看到
        const testContents = document.querySelectorAll('.test-content');
        testContents.forEach(function(content) {
            // 在每个<br>后添加一个可见的标记，以便更好地查看换行位置
            const brElements = content.querySelectorAll('br');
            brElements.forEach(function(br) {
                const span = document.createElement('span');
                span.className = 'highlight-br';
                span.textContent = '↵';  // 换行符号
                br.parentNode.insertBefore(span, br.nextSibling);
            });
        });
    });
</script>
{% endblock %} 