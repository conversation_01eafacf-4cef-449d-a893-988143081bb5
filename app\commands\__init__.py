"""
命令模块初始化文件
"""
from flask import Flask
from flask.cli import AppGroup

# 创建命令组
content_cli = AppGroup('content', help='内容管理命令')
system_cli = AppGroup('system', help='系统管理命令')
menu_cli = AppGroup('menu', help='菜单权限管理命令')


def register_commands(app: Flask):
    """注册自定义命令"""
    # 注册命令组
    app.cli.add_command(content_cli)
    app.cli.add_command(system_cli)
    app.cli.add_command(menu_cli)
    
    # 导入命令模块，触发装饰器注册
    import app.commands.content
    import app.commands.system
    import app.commands.init_menu_permissions
    import app.commands.create_superuser 