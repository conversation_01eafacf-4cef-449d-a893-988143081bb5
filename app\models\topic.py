"""
话题相关模型
"""
from datetime import datetime
from . import db


class Topic(db.Model):
    """话题模型"""
    __tablename__ = 'topics'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(20), default='random')  # 'required'必选话题，'random'随机话题
    priority = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    def __repr__(self):
        return f'<Topic {self.name}>'


class TopicRelation(db.Model):
    """话题关联模型"""
    __tablename__ = 'topic_relations'
    
    id = db.Column(db.Integer, primary_key=True)
    topic_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>ey('topics.id'), nullable=False)
    related_topic_id = db.Column(db.Inte<PERSON>, db.<PERSON><PERSON>ey('topics.id'), nullable=False)
    weight = db.Column(db.Integer, default=1)  # 关联权重
    
    # 话题关联
    topic = db.relationship('Topic', foreign_keys=[topic_id], backref=db.backref('related_topics', lazy='dynamic'))
    related_topic = db.relationship('Topic', foreign_keys=[related_topic_id], backref=db.backref('related_by', lazy='dynamic'))
    
    def __repr__(self):
        return f'<TopicRelation {self.topic_id} -> {self.related_topic_id}>' 