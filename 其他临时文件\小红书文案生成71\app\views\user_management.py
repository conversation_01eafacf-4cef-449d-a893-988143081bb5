from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from sqlalchemy.exc import IntegrityError

from app.models import db
from app.models.user import User, Role, Permission
from app.forms.user import UserCreateForm, UserEditForm, UserPasswordForm, UserRoleForm, UserPermissionForm
from app.utils.decorators import permission_required

# 创建蓝图
user_management = Blueprint('user_management', __name__)

@user_management.route('/users')
@login_required
@permission_required('user_manage')
def user_list():
    """用户列表"""
    
    page = request.args.get('page', 1, type=int)
    per_page = 20
    pagination = User.query.order_by(User.id).paginate(page=page, per_page=per_page)
    users = pagination.items
    
    return render_template('user_management/user_list.html', 
                           users=users, 
                           pagination=pagination,
                           title='用户管理')

@user_management.route('/users/create', methods=['GET', 'POST'])
@login_required
@permission_required('user_manage')
def user_create():
    """创建用户"""
    
    form = UserCreateForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            real_name=form.real_name.data,
            phone=form.phone.data,
            is_active=form.is_active.data
        )
        # 设置密码
        user.password = form.password.data

        # 添加权限
        menu_permissions = {
            'dashboard_access': '控制面板',
            'user_manage': '用户管理',
            'client_manage': '客户管理',
            'template_manage': '模板管理',
            'content_manage': '文案管理',
            'content_generate': '生成文案',
            'topic_manage': '话题管理',
            'task_manage': '任务管理',
            'publish_manage': '发布管理',
            'supplement_manage': '文案补充',
            'display_manage': '文案展示',
            'notification_manage': '通知中心',
            'stats_view': '数据统计',
            'export_manage': '导入导出',
            'system_settings': '系统设置'
        }

        for field_name, perm_desc in menu_permissions.items():
            if getattr(form, field_name).data:
                # 查找或创建权限
                perm = Permission.query.filter_by(name=field_name).first()
                if not perm:
                    perm = Permission(name=field_name, description=perm_desc)
                    db.session.add(perm)
                    db.session.flush()

                # 添加权限到用户
                if perm not in user.permissions:
                    user.permissions.append(perm)
        try:
            db.session.add(user)
            db.session.commit()
            flash(f'用户 {user.username} 创建成功！', 'success')
            return redirect(url_for('user_management.user_list'))
        except IntegrityError:
            db.session.rollback()
            flash('用户名或邮箱已存在', 'danger')
    return render_template('user_management/user_create.html', 
                           form=form,
                           title='创建用户')

@user_management.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('user_manage')
def user_edit(user_id):
    """编辑用户"""
    user = User.query.get_or_404(user_id)
    form = UserEditForm(obj=user)
    if form.validate_on_submit():
        # 防止管理员编辑自己时禁用自己的账户
        if user.id == current_user.id and not form.is_active.data:
            flash('不能禁用当前登录的用户账户', 'danger')
            return redirect(url_for('user_management.user_edit', user_id=user_id))
        user.username = form.username.data
        user.email = form.email.data
        user.real_name = form.real_name.data
        user.phone = form.phone.data
        user.is_active = form.is_active.data
        # 更新权限
        user.permissions.clear()  # 清除现有权限

        menu_permissions = {
            'dashboard_access': '控制面板',
            'user_manage': '用户管理',
            'client_manage': '客户管理',
            'template_manage': '模板管理',
            'content_manage': '文案管理',
            'content_generate': '生成文案',
            'topic_manage': '话题管理',
            'task_manage': '任务管理',
            'publish_manage': '发布管理',
            'supplement_manage': '文案补充',
            'display_manage': '文案展示',
            'notification_manage': '通知中心',
            'stats_view': '数据统计',
            'export_manage': '导入导出',
            'system_settings': '系统设置'
        }

        for field_name, perm_desc in menu_permissions.items():
            if getattr(form, field_name).data:
                # 查找或创建权限
                perm = Permission.query.filter_by(name=field_name).first()
                if not perm:
                    perm = Permission(name=field_name, description=perm_desc)
                    db.session.add(perm)
                    db.session.flush()

                # 添加权限到用户
                if perm not in user.permissions:
                    user.permissions.append(perm)
        try:
            db.session.commit()
            flash(f'用户 {user.username} 更新成功！', 'success')
            return redirect(url_for('user_management.user_list'))
        except IntegrityError:
            db.session.rollback()
            flash('用户名或邮箱已存在', 'danger')
    else:
        # 设置表单初始值 - 权限
        menu_permissions = {
            'dashboard_access': '控制面板',
            'user_manage': '用户管理',
            'client_manage': '客户管理',
            'template_manage': '模板管理',
            'content_manage': '文案管理',
            'content_generate': '生成文案',
            'topic_manage': '话题管理',
            'task_manage': '任务管理',
            'publish_manage': '发布管理',
            'supplement_manage': '文案补充',
            'display_manage': '文案展示',
            'notification_manage': '通知中心',
            'stats_view': '数据统计',
            'export_manage': '导入导出',
            'system_settings': '系统设置'
        }

        for field_name in menu_permissions.keys():
            perm = Permission.query.filter_by(name=field_name).first()
            if perm and (perm in user.permissions or user.has_permission(field_name)):
                getattr(form, field_name).data = True
    return render_template('user_management/user_edit.html', 
                           form=form, 
                           user=user,
                           title='编辑用户')

@user_management.route('/users/<int:user_id>/password', methods=['GET', 'POST'])
@login_required
@permission_required('user_manage')
def user_password(user_id):
    """修改用户密码"""
    
    user = User.query.get_or_404(user_id)
    form = UserPasswordForm()
    
    if form.validate_on_submit():
        user.password = form.password.data
        db.session.commit()
        flash(f'用户 {user.username} 密码修改成功！', 'success')
        return redirect(url_for('user_management.user_list'))
    
    return render_template('user_management/user_password.html', 
                           form=form, 
                           user=user,
                           title='修改密码')

@user_management.route('/users/<int:user_id>/delete', methods=['POST'])
@login_required
@permission_required('user_manage')
def user_delete(user_id):
    """删除用户（标记为禁用）"""
    
    user = User.query.get_or_404(user_id)
    
    # 防止删除自己
    if user.id == current_user.id:
        flash('不能删除当前登录的用户账户', 'danger')
        return redirect(url_for('user_management.user_list'))
    
    # 实际上我们不会真正删除用户，而是将其标记为禁用
    user.is_active = False
    db.session.commit()
    
    flash(f'用户 {user.username} 已禁用', 'success')
    return redirect(url_for('user_management.user_list'))

@user_management.route('/roles')
@login_required
@permission_required('user_manage')
def role_list():
    """角色列表"""
    
    roles = Role.query.order_by(Role.id).all()
    return render_template('user_management/role_list.html',
                           roles=roles,
                           title='角色管理')

@user_management.route('/users/<int:user_id>/permissions', methods=['GET', 'POST'])
@login_required
@permission_required('user_manage')
def user_permissions(user_id):
    """用户菜单权限管理"""
    user = User.query.get_or_404(user_id)
    form = UserPermissionForm()

    # 定义菜单权限映射（匹配当前系统的权限名称）
    menu_permissions = {
        'dashboard_access': '控制面板',
        'user_manage': '用户管理',
        'client_manage': '客户管理',
        'template_manage': '模板管理',
        'content_manage': '初审文案',
        'content_generate': '生成文案',
        'topic_manage': '话题管理',
        'task_manage': '任务管理',
        'publish_manage': '发布管理',
        'supplement_manage': '文案补充',
        'display_manage': '文案展示',
        'notification_manage': '通知中心',
        'stats_view': '数据统计',
        'export_manage': '导入导出',
        'system_settings': '系统设置'
    }

    if form.validate_on_submit():
        try:
            # 清除用户现有的直接权限
            user.permissions.clear()

            # 根据表单选择添加权限
            for field_name, perm_desc in menu_permissions.items():
                if getattr(form, field_name).data:
                    # 查找或创建权限
                    perm = Permission.query.filter_by(name=field_name).first()
                    if not perm:
                        perm = Permission(name=field_name, description=perm_desc)
                        db.session.add(perm)
                        db.session.flush()

                    # 添加权限到用户
                    if perm not in user.permissions:
                        user.permissions.append(perm)

            db.session.commit()
            flash(f'用户 {user.username} 的权限已更新', 'success')
            return redirect(url_for('user_management.user_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'权限更新失败：{str(e)}', 'danger')

    # 设置表单默认值
    for field_name in menu_permissions.keys():
        perm = Permission.query.filter_by(name=field_name).first()
        if perm and (perm in user.permissions or user.has_permission(field_name)):
            getattr(form, field_name).data = True

    return render_template('user_management/user_permissions.html',
                           user=user,
                           form=form,
                           title=f'管理用户权限 - {user.username}')