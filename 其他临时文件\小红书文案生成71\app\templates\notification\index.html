{% extends "base.html" %}

{% block title %}通知中心{% endblock %}

{% block content_auth %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">通知中心 <span class="badge bg-danger">{{ unread_count }}</span></h3>
                    <div class="card-tools">
                        {% if current_user.has_permission('admin_access') %}
                        <a href="{{ url_for('system.notification') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-cog"></i> 通知设置
                        </a>
                        <a href="{{ url_for('notification.create') }}" class="btn btn-sm btn-outline-success">
                            <i class="fas fa-plus"></i> 创建通知
                        </a>
                        {% endif %}
                        <a href="{{ url_for('notification.mark_all_read') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-check-double"></i> 全部标为已读
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 筛选表单 -->
                    <form method="get" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-2">
                                {{ form.type.label(class="form-label") }}
                                {{ form.type(class="form-select") }}
                            </div>
                            <div class="col-md-2">
                                {{ form.priority.label(class="form-label") }}
                                {{ form.priority(class="form-select") }}
                            </div>
                            <div class="col-md-2">
                                {{ form.is_read.label(class="form-label") }}
                                {{ form.is_read(class="form-select") }}
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">开始日期</label>
                                <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">结束日期</label>
                                <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <div class="input-group">
                                    <input type="text" name="search" class="form-control" placeholder="搜索" value="{{ search }}">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <!-- 通知列表 -->
                    <div class="list-group">
                        {% for notification in notifications %}
                        <a href="{{ url_for('notification.view', notification_id=notification.id) }}" class="list-group-item list-group-item-action {% if not notification.is_read %}list-group-item-light{% endif %}">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">
                                    {% if not notification.is_read %}
                                    <span class="badge bg-primary">新</span>
                                    {% endif %}
                                    
                                    {% if notification.priority == 'high' %}
                                    <span class="badge bg-danger">重要</span>
                                    {% elif notification.priority == 'low' %}
                                    <span class="badge bg-secondary">低优先级</span>
                                    {% endif %}
                                    
                                    {{ notification.title }}
                                </h5>
                                <small>{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                            <p class="mb-1">{{ notification.content|truncate(100) }}</p>
                            <small>
                                {% if notification.type == 'review_status' %}
                                <span class="badge bg-info">审核状态变更</span>
                                {% elif notification.type == 'client_operation' %}
                                <span class="badge bg-warning">客户操作</span>
                                {% elif notification.type == 'publish_notice' %}
                                <span class="badge bg-success">发布通知</span>
                                {% elif notification.type == 'system_change' %}
                                <span class="badge bg-dark">系统变更</span>
                                {% endif %}
                                
                                {% if notification.related_content %}
                                <span>相关文案: <a href="{{ url_for('content.content_view', content_id=notification.related_content.id) }}" target="_blank">{{ notification.related_content.title|truncate(20) }}</a></span>
                                {% endif %}
                            </small>
                        </a>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> 没有找到符合条件的通知
                        </div>
                        {% endfor %}
                    </div>
                    
                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% for page in pagination.iter_pages() %}
                                {% if page %}
                                    {% if page != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('notification.index', page=page, type=notification_type, priority=priority, is_read=is_read, date_from=date_from, date_to=date_to, search=search) }}">
                                            {{ page }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 