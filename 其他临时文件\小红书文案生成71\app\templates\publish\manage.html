<!-- 发布管理页面 -->
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="bi bi-broadcast"></i> 发布管理</h2>
            <p class="text-muted">管理文案发布队列和发布时间安排</p>
        </div>
        <div class="col-md-4 text-end">
            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i> 刷新页面
            </button>
            <button type="button" class="btn btn-outline-primary" onclick="showBatchPublishModal()">
                <i class="bi bi-check-all"></i> 批量发布
            </button>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-warning">{{ total_ready }}</h4>
                    <small class="text-muted">待发布</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-success">{{ published_today }}</h4>
                    <small class="text-muted">今日已发布</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-info" id="highPriorityCount">-</h4>
                    <small class="text-muted">高优先级</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-primary" id="weekPublishedCount">-</h4>
                    <small class="text-muted">本周已发布</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选条件 -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="card-title mb-0">筛选条件</h6>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-2">
                    <label class="form-label">客户</label>
                    {{ form.client_id(class="form-select") }}
                </div>
                <div class="col-md-2">
                    <label class="form-label">任务</label>
                    {{ form.task_id(class="form-select") }}
                </div>
                <div class="col-md-2">
                    <label class="form-label">批次</label>
                    {{ form.batch_id(class="form-select") }}
                </div>
                <div class="col-md-2">
                    <label class="form-label">优先级</label>
                    <select name="priority" class="form-select">
                        <option value="">全部优先级</option>
                        <option value="high" {% if request.args.get('priority') == 'high' %}selected{% endif %}>高优先级</option>
                        <option value="normal" {% if request.args.get('priority') == 'normal' %}selected{% endif %}>普通</option>
                        <option value="low" {% if request.args.get('priority') == 'low' %}selected{% endif %}>低优先级</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">发布日期</label>
                    <input type="date" name="publish_date" class="form-control" value="{{ request.args.get('publish_date', '') }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="bi bi-search"></i> 筛选
                    </button>
                    <a href="{{ url_for('main_simple.publish_manage') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i> 重置
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- 文案列表 -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="card-title mb-0">发布队列</h6>
            <div>
                <input type="checkbox" id="selectAll" onchange="toggleSelectAll(this)">
                <label for="selectAll" class="ms-1">全选</label>
            </div>
        </div>
        <div class="card-body">
            {% if content_data %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAllHeader" onchange="toggleSelectAll(this)">
                                </th>
                                <th>文案信息</th>
                                <th width="120">配图</th>
                                <th width="100">客户</th>
                                <th width="120">发布时间</th>
                                <th width="80">优先级</th>
                                <th width="200">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in content_data %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="content-checkbox" value="{{ item.content.id }}">
                                </td>
                                <td>
                                    <div class="d-flex">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">{{ item.content.title[:50] }}{% if item.content.title|length > 50 %}...{% endif %}</h6>
                                            <p class="text-muted small mb-1">{{ item.content.content[:100] }}{% if item.content.content|length > 100 %}...{% endif %}</p>
                                            <div class="small text-muted">
                                                {% if item.content.task %}
                                                <span class="badge bg-light text-dark">{{ item.content.task.name }}</span>
                                                {% endif %}
                                                {% if item.content.batch %}
                                                <span class="badge bg-light text-dark">{{ item.content.batch.name }}</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if item.images %}
                                        <div class="d-flex flex-wrap gap-1">
                                            {% for image in item.images[:3] %}
                                            <img src="{{ url_for('static', filename='uploads/' + image.thumbnail_path) }}" 
                                                 class="img-thumbnail" 
                                                 style="width: 30px; height: 30px; object-fit: cover;"
                                                 title="{{ image.original_name }}">
                                            {% endfor %}
                                            {% if item.images|length > 3 %}
                                            <span class="badge bg-secondary">+{{ item.images|length - 3 }}</span>
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">{{ item.image_count }} 张</small>
                                    {% else %}
                                        <span class="text-muted">无图片</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ item.content.client.name if item.content.client else '未知' }}</span>
                                </td>
                                <td>
                                    <div class="small">
                                        {% if item.content.display_date %}
                                            <div><i class="bi bi-calendar"></i> {{ item.content.display_date.strftime('%m-%d') }}</div>
                                        {% endif %}
                                        {% if item.content.display_time %}
                                            <div><i class="bi bi-clock"></i> {{ item.content.display_time.strftime('%H:%M') }}</div>
                                        {% endif %}
                                        {% if not item.content.display_date and not item.content.display_time %}
                                            <span class="text-muted">未设置</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if item.content.publish_priority == 'high' %}
                                        <span class="badge bg-danger">高</span>
                                    {% elif item.content.publish_priority == 'low' %}
                                        <span class="badge bg-secondary">低</span>
                                    {% else %}
                                        <span class="badge bg-primary">普通</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group-vertical btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                                onclick="viewContent({{ item.content.id }})">
                                            <i class="bi bi-eye"></i> 查看
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" 
                                                onclick="schedulePublish({{ item.content.id }})">
                                            <i class="bi bi-calendar-event"></i> 排期
                                        </button>
                                        <button type="button" class="btn btn-outline-success btn-sm" 
                                                onclick="publishContent({{ item.content.id }})">
                                            <i class="bi bi-send"></i> 发布
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                {% if pagination.pages > 1 %}
                <nav aria-label="分页导航">
                    <ul class="pagination justify-content-center">
                        {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main_simple.publish_manage', page=pagination.prev_num) }}">上一页</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in pagination.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != pagination.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('main_simple.publish_manage', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main_simple.publish_manage', page=pagination.next_num) }}">下一页</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-inbox fs-1 text-muted"></i>
                    <h5 class="text-muted mt-3">暂无待发布的文案</h5>
                    <p class="text-muted">所有文案都已发布完成</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 批量发布模态框 -->
<div class="modal fade" id="batchPublishModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量发布</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <p>确定要发布选中的文案吗？</p>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        已选择 <span id="selectedCount">0</span> 篇文案
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="submitBatchPublish()">确认发布</button>
            </div>
        </div>
    </div>
</div>

<!-- 发布排期模态框 -->
<div class="modal fade" id="scheduleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">发布排期</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="scheduleForm">
                    <div class="mb-3">
                        <label for="scheduleDate" class="form-label">发布日期</label>
                        <input type="date" class="form-control" id="scheduleDate" required>
                    </div>
                    <div class="mb-3">
                        <label for="scheduleTime" class="form-label">发布时间</label>
                        <input type="time" class="form-control" id="scheduleTime" required>
                    </div>
                    <div class="mb-3">
                        <label for="schedulePriority" class="form-label">优先级</label>
                        <select class="form-select" id="schedulePriority">
                            <option value="normal">普通</option>
                            <option value="high">高优先级</option>
                            <option value="low">低优先级</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitSchedule()">保存排期</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentContentId = null;

// 页面加载完成后加载统计信息
document.addEventListener('DOMContentLoaded', function() {
    loadPublishStats();
});

// 加载发布统计
function loadPublishStats() {
    fetch('/simple/api/publish/stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stats = data.stats;
                document.getElementById('highPriorityCount').textContent = stats.high_priority;
                document.getElementById('weekPublishedCount').textContent = stats.published_this_week;
            }
        })
        .catch(error => {
            console.error('加载统计信息失败:', error);
        });
}

// 全选/取消全选
function toggleSelectAll(checkbox) {
    const checkboxes = document.querySelectorAll('.content-checkbox');
    checkboxes.forEach(cb => cb.checked = checkbox.checked);
    updateSelectedCount();
}

// 更新选中数量
function updateSelectedCount() {
    const selected = document.querySelectorAll('.content-checkbox:checked');
    document.getElementById('selectedCount').textContent = selected.length;
}

// 监听复选框变化
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('content-checkbox')) {
        updateSelectedCount();
    }
});

// 查看文案详情
function viewContent(contentId) {
    fetch(`/simple/api/contents/${contentId}/view`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        showContentModal('查看文案详情', html);
    })
    .catch(error => {
        console.error('加载文案详情失败:', error);
        alert('加载文案详情失败，请重试');
    });
}

// 发布单个文案
function publishContent(contentId) {
    if (!confirm('确定要立即发布这篇文案吗？')) {
        return;
    }
    
    fetch(`/simple/api/publish/${contentId}`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            location.reload();
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('发布失败: ' + error.message, 'error');
    });
}

// 显示批量发布模态框
function showBatchPublishModal() {
    const selected = document.querySelectorAll('.content-checkbox:checked');
    if (selected.length === 0) {
        alert('请先选择要发布的文案');
        return;
    }
    
    updateSelectedCount();
    const modal = new bootstrap.Modal(document.getElementById('batchPublishModal'));
    modal.show();
}

// 提交批量发布
function submitBatchPublish() {
    const selected = document.querySelectorAll('.content-checkbox:checked');
    if (selected.length === 0) {
        alert('请先选择要发布的文案');
        return;
    }
    
    const contentIds = Array.from(selected).map(cb => cb.value);
    
    const formData = new FormData();
    formData.append('content_ids', contentIds.join(','));
    
    fetch('/simple/api/publish/batch', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('batchPublishModal')).hide();
            location.reload();
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('批量发布失败: ' + error.message, 'error');
    });
}

// 发布排期
function schedulePublish(contentId) {
    currentContentId = contentId;
    
    // 设置默认值
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    document.getElementById('scheduleDate').value = tomorrow.toISOString().split('T')[0];
    document.getElementById('scheduleTime').value = '09:00';
    document.getElementById('schedulePriority').value = 'normal';
    
    const modal = new bootstrap.Modal(document.getElementById('scheduleModal'));
    modal.show();
}

// 提交排期
function submitSchedule() {
    const date = document.getElementById('scheduleDate').value;
    const time = document.getElementById('scheduleTime').value;
    const priority = document.getElementById('schedulePriority').value;
    
    if (!date || !time) {
        alert('请填写发布日期和时间');
        return;
    }
    
    const formData = new FormData();
    formData.append('display_date', date);
    formData.append('display_time', time);
    formData.append('priority', priority);
    
    fetch(`/simple/api/publish/${currentContentId}/schedule`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('scheduleModal')).hide();
            location.reload();
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('设置排期失败: ' + error.message, 'error');
    });
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 这里可以使用Bootstrap的Toast组件或其他提示组件
    alert(message);
}

console.log('发布管理页面已加载');
</script>
