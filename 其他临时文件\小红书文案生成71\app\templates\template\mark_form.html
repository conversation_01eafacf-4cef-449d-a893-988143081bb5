{% extends "base.html" %}

{% block title %}{{ title }} - 小红书文案生成系统{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
{% endblock %}

{% block content_auth %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h2>{{ title }}</h2>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        <div class="mb-3">
                            {{ form.name.label(class="form-label") }}
                            {% if title == '编辑标记' %}
                            {{ form.name(class="form-control", readonly=true, style="background-color: #f8f9fa;") }}
                            <div class="form-text text-info">
                                <i class="bi bi-info-circle"></i>
                                <strong>提示：</strong>为了保证系统稳定性，标记名称创建后不允许修改。如需使用新名称，请创建新标记。
                            </div>
                            {% else %}
                            {{ form.name(class="form-control", id="mark-name-field") }}
                            {% endif %}
                            {% if form.name.errors %}
                                {% for error in form.name.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control") }}
                            {% if form.description.errors %}
                                {% for error in form.description.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.type.label(class="form-label") }}
                            {{ form.type(class="form-select") }}
                            {% if form.type.errors %}
                                {% for error in form.type.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        <div class="d-grid gap-2">
                            {{ form.submit(class="btn btn-primary", id="submit-btn") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 标记编辑页面不需要特殊的JavaScript逻辑
console.log('标记表单页面加载完成');
</script>
{% endblock %}