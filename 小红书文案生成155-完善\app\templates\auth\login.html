<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 小红书文案管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="{{ url_for('static', filename='css/bootstrap-simple.min.css') }}" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="{{ url_for('static', filename='css/bootstrap-icons.min.css') }}" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            width: 100%;
            max-width: 400px;
            padding: 2rem;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header h1 {
            color: #333;
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: #666;
            font-size: 0.9rem;
            margin: 0;
        }

        .form-control {
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .form-label {
            font-weight: 500;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .invalid-feedback {
            color: #dc3545;
            font-size: 0.875rem;
        }

        .login-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-icon">
                <i class="bi bi-person-circle text-white" style="font-size: 2rem;"></i>
            </div>
            <h1>小红书文案管理系统</h1>
            <p>用户登录</p>
        </div>

        <!-- 显示flash消息 -->
{% with messages = get_flashed_messages(with_categories=true) %}
  {% if messages %}
    {% for category, message in messages %}
      <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
        {{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    {% endfor %}
  {% endif %}
{% endwith %}

<form method="post" action="{{ url_for('auth.login') }}">
            {{ form.csrf_token }}

            <div class="mb-3">
                {{ form.username.label(class="form-label") }}
                {{ form.username(class="form-control", placeholder="请输入用户名") }}
                {% if form.username.errors %}
                    {% for error in form.username.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>

            <div class="mb-3">
                {{ form.password.label(class="form-label") }}
                {{ form.password(class="form-control", placeholder="请输入密码") }}
                {% if form.password.errors %}
                    {% for error in form.password.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>

            <div class="mb-4 form-check">
                {{ form.remember_me(class="form-check-input") }}
                {{ form.remember_me.label(class="form-check-label") }}
            </div>

            {{ form.submit(class="btn btn-primary btn-login w-100") }}
        </form>
    </div>

    <!-- Bootstrap JS -->
    <script src="{{ url_for('static', filename='js/bootstrap-simple.min.js') }}"></script>
</body>
</html>