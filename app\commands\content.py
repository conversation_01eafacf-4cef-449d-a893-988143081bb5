"""
内容管理命令
"""
import click
from flask.cli import with_appcontext
from app.commands import content_cli
from app.models import db
from app.models.content import Content


@content_cli.command('cleanup')
@click.option('--days', default=30, help='清理多少天前的已删除内容')
@with_appcontext
def cleanup_content(days):
    """清理已删除的内容"""
    from datetime import datetime, timedelta
    from sqlalchemy import func
    
    # 计算截止日期
    cutoff_date = datetime.now() - timedelta(days=days)
    
    # 查询需要清理的内容
    query = Content.query.filter(
        Content.is_deleted == True,
        Content.updated_at < cutoff_date
    )
    
    # 获取数量
    count = query.count()
    
    if count == 0:
        click.echo('没有需要清理的内容')
        return
    
    # 确认操作
    if click.confirm(f'确定要永久删除 {count} 条内容吗?'):
        # 执行删除
        query.delete()
        db.session.commit()
        click.echo(f'成功清理 {count} 条内容')
    else:
        click.echo('操作已取消')


@content_cli.command('stats')
@with_appcontext
def content_stats():
    """显示内容统计信息"""
    from sqlalchemy import func
    
    # 总数量
    total_count = Content.query.count()
    
    # 按状态统计
    status_counts = db.session.query(
        Content.workflow_status,
        func.count(Content.id)
    ).group_by(Content.workflow_status).all()
    
    # 按发布状态统计
    publish_counts = db.session.query(
        Content.publish_status,
        func.count(Content.id)
    ).group_by(Content.publish_status).all()
    
    # 输出统计信息
    click.echo(f'总内容数量: {total_count}')
    
    click.echo('\n工作流状态统计:')
    for status, count in status_counts:
        click.echo(f'  {status}: {count}')
    
    click.echo('\n发布状态统计:')
    for status, count in publish_counts:
        click.echo(f'  {status}: {count}') 