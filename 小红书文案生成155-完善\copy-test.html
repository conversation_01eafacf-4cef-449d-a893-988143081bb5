<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复制功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .input-group {
            display: flex;
            margin-bottom: 10px;
        }
        .input-group input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px 0 0 4px;
        }
        .input-group button {
            border-radius: 0 4px 4px 0;
            margin: 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.info { background: #e3f2fd; color: #1976d2; }
        .status.success { background: #e8f5e8; color: #2e7d32; }
        .status.warning { background: #fff3e0; color: #f57c00; }
    </style>
</head>
<body>
    <h1>🔧 HTTP环境复制功能测试</h1>
    
    <div class="card">
        <h3>环境信息</h3>
        <div class="status info">
            <strong>当前环境：</strong><span id="envInfo"></span><br>
            <strong>剪贴板API支持：</strong><span id="clipboardSupport"></span><br>
            <strong>安全上下文：</strong><span id="secureContext"></span>
        </div>
    </div>
    
    <div class="card">
        <h3>复制测试</h3>
        <p>测试不同类型的内容复制：</p>
        
        <div class="input-group">
            <input type="text" id="testUrl" value="http://xhsw.ke999.cn/client-review/1b6d9ee2a532b293a48d6cc90d769f3d?key=NTPC" readonly>
            <button onclick="copyToClipboard(document.getElementById('testUrl').value)">复制链接</button>
        </div>
        
        <div class="input-group">
            <input type="text" id="testKey" value="NTPC" readonly>
            <button onclick="copyToClipboard(document.getElementById('testKey').value)">复制密钥</button>
        </div>
        
        <div class="input-group">
            <input type="text" id="testText" value="这是一段测试文本内容" readonly>
            <button onclick="copyToClipboard(document.getElementById('testText').value)">复制文本</button>
        </div>
    </div>
    
    <div class="card">
        <h3>粘贴测试</h3>
        <p>在下面的输入框中粘贴，验证复制是否成功：</p>
        <input type="text" placeholder="在这里粘贴测试复制效果..." style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
    </div>
    
    <div class="card">
        <h3>测试日志</h3>
        <div id="testLog" style="background: #f8f9fa; padding: 10px; border-radius: 4px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
    </div>

    <!-- 引入修复方案 -->
    <script src="/static/js/http-clipboard-fix.js"></script>
    
    <script>
        // 显示环境信息
        function updateEnvInfo() {
            const isHttp = location.protocol === 'http:';
            const isLocalhost = location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            
            document.getElementById('envInfo').textContent = 
                `${location.protocol}//${location.hostname} ${isHttp && !isLocalhost ? '(HTTP环境)' : '(安全环境)'}`;
            
            document.getElementById('clipboardSupport').textContent = 
                (navigator.clipboard && navigator.clipboard.writeText) ? '支持' : '不支持';
            
            document.getElementById('secureContext').textContent = 
                window.isSecureContext ? '是' : '否';
        }
        
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('testLog');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        // Toast 提示函数（简化版）
        function showToast(message, type = 'info') {
            log(`Toast (${type}): ${message}`);
            
            // 创建简单的提示
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};
                color: white;
                padding: 12px 20px;
                border-radius: 4px;
                z-index: 10001;
                box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            updateEnvInfo();
            log('页面加载完成');
            log(`当前环境: ${location.protocol}//${location.hostname}`);
            
            const isHttpEnvironment = location.protocol === 'http:' && 
                                     location.hostname !== 'localhost' && 
                                     location.hostname !== '127.0.0.1';
            
            if (isHttpEnvironment) {
                log('✅ HTTP环境检测成功，修复方案应该已激活');
                showToast('HTTP环境复制修复方案已激活', 'success');
            } else {
                log('ℹ️ 当前为安全环境，使用标准复制方案');
                showToast('当前为安全环境', 'info');
            }
        });
        
        // 测试按钮点击事件
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'BUTTON') {
                log(`点击了按钮: ${e.target.textContent}`);
            }
        });
    </script>
</body>
</html>
