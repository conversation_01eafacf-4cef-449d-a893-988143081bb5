{% extends "base.html" %}

{% block title %}数据统计 - 小红书文案生成系统{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
<style>
    .stat-card {
        transition: all 0.3s;
    }
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .chart-container {
        height: 300px;
    }
</style>
{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">数据统计概览</h2>
        <div class="btn-group">
            <a href="{{ url_for('stats.content_stats') }}" class="btn btn-outline-primary">
                <i class="bi bi-file-text"></i> 文案统计
            </a>
            <a href="{{ url_for('stats.task_stats') }}" class="btn btn-outline-primary">
                <i class="bi bi-list-task"></i> 任务统计
            </a>
            <a href="{{ url_for('stats.user_stats') }}" class="btn btn-outline-primary">
                <i class="bi bi-people"></i> 用户统计
            </a>
        </div>
    </div>

    <!-- 数据卡片 -->
    <div class="row row-cols-1 row-cols-md-2 row-cols-xl-5 g-4 mb-4">
        <div class="col">
            <div class="card stat-card h-100 border-primary">
                <div class="card-body text-center">
                    <h1 class="display-4 text-primary mb-0">{{ content_count }}</h1>
                    <p class="text-muted">文案总数</p>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card stat-card h-100 border-success">
                <div class="card-body text-center">
                    <h1 class="display-4 text-success mb-0">{{ task_count }}</h1>
                    <p class="text-muted">任务总数</p>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card stat-card h-100 border-info">
                <div class="card-body text-center">
                    <h1 class="display-4 text-info mb-0">{{ client_count }}</h1>
                    <p class="text-muted">客户总数</p>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card stat-card h-100 border-warning">
                <div class="card-body text-center">
                    <h1 class="display-4 text-warning mb-0">{{ user_count }}</h1>
                    <p class="text-muted">用户总数</p>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card stat-card h-100 border-danger">
                <div class="card-body text-center">
                    <h1 class="display-4 text-danger mb-0">{{ template_count }}</h1>
                    <p class="text-muted">模板总数</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表 -->
    <div class="row">
        <!-- 文案状态分布 -->
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">文案状态分布</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文案创建趋势 -->
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">最近7天文案创建趋势</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="trendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 状态详情表格 -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">文案状态详情</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>状态</th>
                                    <th>数量</th>
                                    <th>百分比</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% set status_map = {
                                    'draft': '草稿',
                                    'pending_review': '待初审',
                                    'first_reviewed': '初审通过',
                                    'pending_image': '待上传图片',
                                    'image_uploaded': '图片已上传',
                                    'pending_final_review': '待最终审核',
                                    'pending_client_review': '待客户审核',
                                    'client_rejected': '客户已拒绝',
                                    'client_approved': '客户已通过',
                                    'pending_publish': '待发布',
                                    'published': '已发布'
                                } %}
                                {% for status, count in status_data.items() %}
                                <tr>
                                    <td>{{ status_map.get(status, status) }}</td>
                                    <td>{{ count }}</td>
                                    <td>
                                        {% if content_count > 0 %}
                                            {{ (count / content_count * 100) | round(1) }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="3" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 文案状态分布饼图
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    const statusLabels = [];
    const statusData = [];
    const statusColors = [
        '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', 
        '#6f42c1', '#fd7e14', '#20c997', '#6c757d', '#dc3545', '#17a2b8'
    ];
    
    {% set status_map = {
        'draft': '草稿',
        'pending_review': '待初审',
        'first_reviewed': '初审通过',
        'pending_image': '待上传图片',
        'image_uploaded': '图片已上传',
        'pending_final_review': '待最终审核',
        'pending_client_review': '待客户审核',
        'client_rejected': '客户已拒绝',
        'client_approved': '客户已通过',
        'pending_publish': '待发布',
        'published': '已发布'
    } %}
    
    {% for status, count in status_data.items() %}
        statusLabels.push('{{ status_map.get(status, status) }}');
        statusData.push({{ count }});
    {% endfor %}
    
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: statusLabels,
            datasets: [{
                data: statusData,
                backgroundColor: statusColors.slice(0, statusData.length),
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });
    
    // 文案创建趋势图
    const trendCtx = document.getElementById('trendChart').getContext('2d');
    const trendLabels = [];
    const trendData = [];
    
    {% for item in trend_data %}
        trendLabels.push('{{ item.date }}');
        trendData.push({{ item.count }});
    {% endfor %}
    
    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: trendLabels,
            datasets: [{
                label: '文案数量',
                data: trendData,
                fill: false,
                borderColor: '#4e73df',
                tension: 0.1,
                backgroundColor: '#4e73df'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
});
</script>
{% endblock %} 