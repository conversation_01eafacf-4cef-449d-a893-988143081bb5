"""
文案展示系统视图
"""
from datetime import datetime, timedelta
import random
import json
from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from sqlalchemy import func, desc, and_, or_

from app import db
from app.models import Content, Client
from app.models.display import DisplaySchedule, DisplaySetting
from app.utils.decorators import permission_required
from app.forms.display import DisplayRuleForm, DisplayTimeForm, DisplayOrderForm, DisplayFilterForm

# 创建蓝图
display_bp = Blueprint('display', __name__)

@display_bp.route('/')
@login_required
@permission_required('content_manage')
def index():
    """展示系统首页"""
    # 获取筛选参数
    form = DisplayFilterForm()
    
    # 初始化表单选择项
    form.client_id.choices = [(0, '全部客户')] + [(c.id, c.name) for c in Client.query.filter_by(status=True).all()]
    
    # 获取筛选参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    client_id = request.args.get('client_id', 0, type=int)
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    status = request.args.get('status', '')
    search = request.args.get('search', '')
    
    # 构建查询
    query = DisplaySchedule.query
    
    # 应用筛选条件
    if client_id > 0:
        query = query.filter(DisplaySchedule.client_id == client_id)
    
    if status:
        query = query.filter(DisplaySchedule.status == status)
    
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(DisplaySchedule.display_date >= date_from)
        except:
            pass
    
    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(DisplaySchedule.display_date <= date_to)
        except:
            pass
    
    if search:
        # 通过关联查询文案内容
        query = query.join(Content).filter(
            or_(
                Content.title.like(f'%{search}%'),
                Content.content.like(f'%{search}%')
            )
        )
    
    # 默认按展示日期和时间排序
    query = query.order_by(DisplaySchedule.display_date.desc(), DisplaySchedule.display_time.desc())
    
    # 分页
    pagination = query.paginate(page=page, per_page=per_page)
    schedules = pagination.items
    
    # 获取客户展示设置
    client_settings = {}
    if client_id > 0:
        setting = DisplaySetting.query.filter_by(client_id=client_id).first()
        if setting:
            client_settings = {
                'order_type': setting.order_type,
                'custom_order': setting.custom_order_list if setting.order_type == 'custom' else []
            }
    
    return render_template(
        'display/index.html',
        schedules=schedules,
        pagination=pagination,
        form=form,
        client_id=client_id,
        status=status,
        date_from=date_from,
        date_to=date_to,
        search=search,
        client_settings=client_settings
    )

@display_bp.route('/rules')
@login_required
@permission_required('content_manage')
def rules():
    """展示规则列表"""
    clients = Client.query.filter_by(status=True).all()
    client_rules = []
    
    for client in clients:
        setting = DisplaySetting.query.filter_by(client_id=client.id).first()
        order_type = setting.order_type if setting else 'priority'
        
        client_rules.append({
            'client': client,
            'daily_count': client.daily_content_count,
            'start_time': client.display_start_time,
            'interval_min': client.interval_min,
            'interval_max': client.interval_max,
            'order_type': order_type
        })
    
    return render_template(
        'display/rules.html',
        client_rules=client_rules
    )

@display_bp.route('/rule/<int:client_id>', methods=['GET', 'POST'])
@login_required
@permission_required('content_manage')
def edit_rule(client_id):
    """编辑展示规则"""
    client = Client.query.get_or_404(client_id)
    form = DisplayRuleForm(obj=client)
    
    if form.validate_on_submit():
        # 更新客户展示规则
        client.daily_content_count = form.daily_content_count.data
        client.display_start_time = form.display_start_time.data
        client.interval_min = form.interval_min.data
        client.interval_max = form.interval_max.data
        
        db.session.commit()
        flash('展示规则已更新', 'success')
        return redirect(url_for('display.rules'))
    
    return render_template(
        'display/edit_rule.html',
        form=form,
        client=client
    )

@display_bp.route('/order/<int:client_id>', methods=['GET', 'POST'])
@login_required
@permission_required('content_manage')
def display_order(client_id):
    """设置展示顺序"""
    client = Client.query.get_or_404(client_id)
    setting = DisplaySetting.query.filter_by(client_id=client_id).first()
    
    if not setting:
        setting = DisplaySetting(client_id=client_id, order_type='priority')
        db.session.add(setting)
        db.session.commit()
    
    form = DisplayOrderForm(obj=setting)
    
    # 如果是自定义顺序，填充文案ID列表
    if setting.order_type == 'custom' and setting.custom_order:
        form.content_ids.data = '\n'.join(map(str, setting.custom_order_list))
    
    if form.validate_on_submit():
        setting.order_type = form.order_type.data
        setting.updated_by = current_user.id
        
        # 如果是自定义顺序，解析文案ID列表
        if form.order_type.data == 'custom' and form.content_ids.data:
            content_ids = []
            for line in form.content_ids.data.split('\n'):
                try:
                    content_id = int(line.strip())
                    # 验证文案是否存在
                    content = Content.query.get(content_id)
                    if content and content.client_id == client_id:
                        content_ids.append(content_id)
                except:
                    pass
            
            setting.custom_order_list = content_ids
        
        db.session.commit()
        flash('展示顺序已更新', 'success')
        return redirect(url_for('display.rules'))
    
    # 获取客户的文案列表，用于自定义排序参考
    contents = Content.query.filter(
        Content.client_id == client_id,
        Content.is_deleted == False,
        Content.workflow_status == 'published'
    ).order_by(Content.id.desc()).limit(50).all()
    
    return render_template(
        'display/order.html',
        form=form,
        client=client,
        setting=setting,
        contents=contents
    )

@display_bp.route('/schedule/<int:client_id>', methods=['GET', 'POST'])
@login_required
@permission_required('content_manage')
def schedule(client_id):
    """安排展示计划"""
    client = Client.query.get_or_404(client_id)
    
    # 获取可用于展示的文案
    available_contents = Content.query.filter(
        Content.client_id == client_id,
        Content.is_deleted == False,
        Content.workflow_status == 'published'
    ).all()
    
    if request.method == 'POST':
        # 获取表单数据
        display_date = request.form.get('display_date')
        auto_schedule = request.form.get('auto_schedule') == 'true'
        
        if not display_date:
            flash('请选择展示日期', 'danger')
            return redirect(url_for('display.schedule', client_id=client_id))
        
        try:
            display_date = datetime.strptime(display_date, '%Y-%m-%d').date()
        except:
            flash('日期格式不正确', 'danger')
            return redirect(url_for('display.schedule', client_id=client_id))
        
        # 检查是否已有该日期的安排
        existing = DisplaySchedule.query.filter(
            DisplaySchedule.client_id == client_id,
            DisplaySchedule.display_date == display_date
        ).count()
        
        if existing > 0:
            flash(f'该日期已有{existing}条展示安排，请选择其他日期或编辑现有安排', 'warning')
            return redirect(url_for('display.schedule', client_id=client_id))
        
        if auto_schedule:
            # 自动安排展示计划
            count = client.daily_content_count
            if count > len(available_contents):
                count = len(available_contents)
                flash(f'可用文案数量不足，只能安排{count}条展示计划', 'warning')
            
            # 获取排序设置
            setting = DisplaySetting.query.filter_by(client_id=client_id).first()
            order_type = setting.order_type if setting else 'priority'
            
            # 根据排序方式选择文案
            selected_contents = []
            if order_type == 'priority':
                # 按优先级排序
                selected_contents = sorted(
                    available_contents, 
                    key=lambda x: 0 if x.publish_priority == 'high' else (1 if x.publish_priority == 'normal' else 2)
                )[:count]
            elif order_type == 'time':
                # 按时间排序
                selected_contents = sorted(
                    available_contents,
                    key=lambda x: x.created_at,
                    reverse=True
                )[:count]
            elif order_type == 'random':
                # 随机排序
                selected_contents = random.sample(available_contents, min(count, len(available_contents)))
            elif order_type == 'custom' and setting and setting.custom_order:
                # 自定义顺序
                custom_ids = setting.custom_order_list
                # 按自定义顺序筛选文案
                content_dict = {c.id: c for c in available_contents}
                selected_contents = [content_dict[cid] for cid in custom_ids if cid in content_dict][:count]
            else:
                # 默认按优先级
                selected_contents = sorted(
                    available_contents, 
                    key=lambda x: 0 if x.publish_priority == 'high' else (1 if x.publish_priority == 'normal' else 2)
                )[:count]
            
            # 生成展示时间
            start_time = client.display_start_time or datetime.strptime('09:00', '%H:%M').time()
            interval_min = client.interval_min or 30
            interval_max = client.interval_max or 120
            
            # 创建展示计划
            for i, content in enumerate(selected_contents):
                # 计算展示时间
                if i == 0:
                    display_time = start_time
                else:
                    # 随机间隔
                    interval = random.randint(interval_min, interval_max)
                    # 将前一个时间转换为datetime，然后加上间隔
                    prev_datetime = datetime.combine(display_date, display_time)
                    next_datetime = prev_datetime + timedelta(minutes=interval)
                    display_time = next_datetime.time()
                
                # 创建展示计划
                schedule = DisplaySchedule(
                    content_id=content.id,
                    client_id=client_id,
                    display_date=display_date,
                    display_time=display_time,
                    is_fixed_time=False,
                    status='scheduled',
                    display_order=i + 1,
                    created_by=current_user.id
                )
                db.session.add(schedule)
            
            db.session.commit()
            flash(f'成功为{display_date}安排了{len(selected_contents)}条展示计划', 'success')
            return redirect(url_for('display.index', client_id=client_id))
        
        # 如果不是自动安排，跳转到手动安排页面
        return redirect(url_for('display.manual_schedule', client_id=client_id, display_date=display_date.strftime('%Y-%m-%d')))
    
    return render_template(
        'display/schedule.html',
        client=client,
        available_count=len(available_contents)
    )

@display_bp.route('/manual_schedule/<int:client_id>', methods=['GET', 'POST'])
@login_required
@permission_required('content_manage')
def manual_schedule(client_id):
    """手动安排展示计划"""
    client = Client.query.get_or_404(client_id)
    display_date = request.args.get('display_date')
    
    try:
        display_date = datetime.strptime(display_date, '%Y-%m-%d').date()
    except:
        flash('日期格式不正确', 'danger')
        return redirect(url_for('display.schedule', client_id=client_id))
    
    # 获取可用于展示的文案
    contents = Content.query.filter(
        Content.client_id == client_id,
        Content.is_deleted == False,
        Content.workflow_status == 'published'
    ).all()
    
    if request.method == 'POST':
        content_ids = request.form.getlist('content_ids')
        display_times = request.form.getlist('display_times')
        is_fixed = request.form.getlist('is_fixed')
        
        if not content_ids:
            flash('请至少选择一条文案', 'danger')
            return redirect(url_for('display.manual_schedule', client_id=client_id, display_date=display_date.strftime('%Y-%m-%d')))
        
        # 创建展示计划
        for i, content_id in enumerate(content_ids):
            try:
                content_id = int(content_id)
                display_time = datetime.strptime(display_times[i], '%H:%M').time() if i < len(display_times) else None
                is_fixed_time = str(i) in is_fixed
                
                if not display_time:
                    continue
                
                # 创建展示计划
                schedule = DisplaySchedule(
                    content_id=content_id,
                    client_id=client_id,
                    display_date=display_date,
                    display_time=display_time,
                    is_fixed_time=is_fixed_time,
                    status='scheduled',
                    display_order=i + 1,
                    created_by=current_user.id
                )
                db.session.add(schedule)
            except:
                pass
        
        db.session.commit()
        flash(f'成功为{display_date}安排了展示计划', 'success')
        return redirect(url_for('display.index', client_id=client_id))
    
    # 获取默认展示时间
    start_time = client.display_start_time or datetime.strptime('09:00', '%H:%M').time()
    
    return render_template(
        'display/manual_schedule.html',
        client=client,
        contents=contents,
        display_date=display_date,
        start_time=start_time
    )

@display_bp.route('/edit_schedule/<int:schedule_id>', methods=['GET', 'POST'])
@login_required
@permission_required('content_manage')
def edit_schedule(schedule_id):
    """编辑展示计划"""
    schedule = DisplaySchedule.query.get_or_404(schedule_id)
    client = Client.query.get(schedule.client_id)
    
    form = DisplayTimeForm(obj=schedule)
    
    # 获取可用于展示的文案
    contents = Content.query.filter(
        Content.client_id == schedule.client_id,
        Content.is_deleted == False,
        Content.workflow_status == 'published'
    ).all()
    
    form.content_id.choices = [(c.id, f"{c.id} - {c.title}") for c in contents]
    
    if form.validate_on_submit():
        schedule.content_id = form.content_id.data
        schedule.display_date = datetime.strptime(form.display_date.data, '%Y-%m-%d').date()
        schedule.display_time = form.display_time.data
        schedule.is_fixed_time = form.is_fixed_time.data
        schedule.status = form.status.data
        
        db.session.commit()
        flash('展示计划已更新', 'success')
        return redirect(url_for('display.index', client_id=schedule.client_id))
    
    # 初始化表单数据
    if request.method == 'GET':
        form.display_date.data = schedule.display_date.strftime('%Y-%m-%d')
    
    return render_template(
        'display/edit_schedule.html',
        form=form,
        schedule=schedule,
        client=client
    )

@display_bp.route('/delete_schedule/<int:schedule_id>', methods=['POST'])
@login_required
@permission_required('content_manage')
def delete_schedule(schedule_id):
    """删除展示计划"""
    schedule = DisplaySchedule.query.get_or_404(schedule_id)
    client_id = schedule.client_id
    
    db.session.delete(schedule)
    db.session.commit()
    
    flash('展示计划已删除', 'success')
    return redirect(url_for('display.index', client_id=client_id))

@display_bp.route('/api/get_contents/<int:client_id>')
@login_required
@permission_required('content_manage')
def api_get_contents(client_id):
    """API：获取客户可用文案"""
    contents = Content.query.filter(
        Content.client_id == client_id,
        Content.is_deleted == False,
        Content.workflow_status == 'published'
    ).all()
    
    return jsonify({
        'success': True,
        'contents': [{'id': c.id, 'title': c.title} for c in contents]
    }) 