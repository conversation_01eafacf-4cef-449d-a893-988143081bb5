#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
初始化菜单权限
"""

from flask import current_app
from app.models import db
from app.models.user import Role, Permission
from app.models.menu import MenuItem
from app.commands import menu_cli

@menu_cli.command('init')
def init_menu_permissions():
    """初始化菜单和权限"""
    print("开始初始化菜单权限...")
    
    # 创建权限
    permissions_data = [
        ('dashboard.view', '控制台查看'),
        ('template.manage', '模板管理'),
        ('client.manage', '客户管理'),
        ('content.generate', '内容生成'),
        ('content.review', '内容审核'),
        ('image.upload', '图片上传'),
        ('content.final_review', '最终审核'),
        ('client.review', '客户审核'),
        ('publish.manage', '发布管理'),
        ('publish.status', '发布状态'),
        ('user.manage', '用户管理'),
        ('system.settings', '系统设置')
    ]
    
    permissions = {}
    for perm_name, perm_desc in permissions_data:
        permission = Permission.query.filter_by(name=perm_name).first()
        if not permission:
            permission = Permission(name=perm_name, description=perm_desc)
            db.session.add(permission)
            print(f"创建权限: {perm_name}")
        permissions[perm_name] = permission
    
    # 创建菜单项
    menu_data = [
        ('控制台', '/simple/dashboard', 'bi bi-speedometer2', 'dashboard.view', 1),
        ('模板管理', '/simple/templates', 'bi bi-layer-group', 'template.manage', 2),
        ('客户管理', '/simple/clients', 'bi bi-people', 'client.manage', 3),
        ('内容生成', '/simple/content', 'bi bi-pencil-square', 'content.generate', 4),
        ('初审文案', '/simple/review-content', 'bi bi-clipboard-check', 'content.review', 5),
        ('图片上传', '/simple/image-upload', 'bi bi-image', 'image.upload', 6),
        ('最终审核', '/simple/final-review', 'bi bi-check2-square', 'content.final_review', 7),
        ('客户审核', '/simple/client-review', 'bi bi-person-check', 'client.review', 8),
        ('发布管理', '/simple/publish-manage', 'bi bi-send', 'publish.manage', 9),
        ('发布状态', '/simple/publish-status-manage', 'bi bi-list-check', 'publish.status', 10),
        ('用户管理', '/simple/users', 'bi bi-person-gear', 'user.manage', 11),
        ('系统设置', '/simple/system', 'bi bi-gear', 'system.settings', 12)
    ]
    
    for name, url, icon, permission_name, order in menu_data:
        menu_item = MenuItem.query.filter_by(url=url).first()
        if not menu_item:
            permission = permissions.get(permission_name)
            menu_item = MenuItem(
                name=name,
                url=url,
                icon=icon,
                permission=permission_name,
                order=order,
                is_active=True
            )
            db.session.add(menu_item)
            print(f"创建菜单: {name}")
    
    # 创建默认角色
    roles_data = [
        ('超级管理员', '拥有所有权限'),
        ('管理员', '拥有大部分管理权限'),
        ('编辑', '拥有内容编辑权限'),
        ('审核员', '拥有内容审核权限'),
        ('普通用户', '基础查看权限')
    ]
    
    roles = {}
    for role_name, role_desc in roles_data:
        role = Role.query.filter_by(name=role_name).first()
        if not role:
            role = Role(name=role_name, description=role_desc)
            db.session.add(role)
            print(f"创建角色: {role_name}")
        roles[role_name] = role
    
    # 提交数据库更改
    db.session.commit()
    
    # 为角色分配权限
    role_permissions = {
        '超级管理员': list(permissions.keys()),  # 所有权限
        '管理员': [
            'dashboard.view', 'template.manage', 'client.manage', 'content.generate',
            'content.review', 'image.upload', 'content.final_review', 'client.review',
            'publish.manage', 'publish.status', 'user.manage', 'system.settings'
        ],
        '编辑': [
            'dashboard.view', 'content.generate', 'content.review', 'image.upload'
        ],
        '审核员': [
            'dashboard.view', 'content.review', 'content.final_review', 'client.review'
        ],
        '普通用户': [
            'dashboard.view'
        ]
    }
    
    for role_name, perm_names in role_permissions.items():
        role = roles.get(role_name)
        if role:
            # 清除现有权限
            role.permissions.clear()
            # 添加新权限
            for perm_name in perm_names:
                permission = permissions.get(perm_name)
                if permission and permission not in role.permissions:
                    role.permissions.append(permission)
            print(f"为角色 {role_name} 分配权限: {', '.join(perm_names)}")
    
    db.session.commit()
    print("菜单权限初始化完成！")

if __name__ == '__main__':
    from app import create_app
    app = create_app()
    with app.app_context():
        init_menu_permissions() 