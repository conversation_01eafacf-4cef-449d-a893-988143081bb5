"""rename_status_to_is_active

Revision ID: f53868527c9d
Revises: 2bd56124dce2
Create Date: 2025-07-13 15:42:00.212849

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'f53868527c9d'
down_revision = '2bd56124dce2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_role',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('role_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('display_schedules', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'users', ['created_by'], ['id'])
        batch_op.create_foreign_key(None, 'clients', ['client_id'], ['id'])
        batch_op.create_foreign_key(None, 'contents', ['content_id'], ['id'])

    with op.batch_alter_table('display_settings', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'users', ['updated_by'], ['id'])
        batch_op.create_foreign_key(None, 'clients', ['client_id'], ['id'])

    with op.batch_alter_table('permissions', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('code'))
        batch_op.drop_column('code')

    with op.batch_alter_table('publish_timeouts', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'users', ['updated_by'], ['id'])

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_active', sa.Boolean(), nullable=True))
        
    # 将status字段的值复制到is_active字段
    connection = op.get_bind()
    connection.execute(sa.text("UPDATE users SET is_active = (status = 1)"))
        
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_users_status'))
        batch_op.create_index(batch_op.f('ix_users_is_active'), ['is_active'], unique=False)
        batch_op.drop_column('status')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('status', mysql.TINYINT(display_width=1), server_default=sa.text("'1'"), autoincrement=False, nullable=True))
        
    # 将is_active字段的值复制到status字段
    connection = op.get_bind()
    connection.execute(sa.text("UPDATE users SET status = CASE WHEN is_active THEN 1 ELSE 0 END"))
        
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_users_is_active'))
        batch_op.create_index(batch_op.f('ix_users_status'), ['status'], unique=False)
        batch_op.drop_column('is_active')

    with op.batch_alter_table('publish_timeouts', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')

    with op.batch_alter_table('permissions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('code', mysql.VARCHAR(length=50), nullable=False))
        batch_op.create_index(batch_op.f('code'), ['code'], unique=True)

    with op.batch_alter_table('display_settings', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')

    with op.batch_alter_table('display_schedules', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')

    op.drop_table('user_role')
    # ### end Alembic commands ###
