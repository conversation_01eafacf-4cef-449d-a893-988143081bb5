{% extends "base.html" %}

{% block title %}任务补充详情{% endblock %}

{% block page_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">任务补充详情</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('supplement.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                        <a href="{{ url_for('supplement.supplement_task', task_id=task.id) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-plus"></i> 补充文案
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 任务信息 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">任务信息</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th width="120px">ID</th>
                                            <td>{{ task.id }}</td>
                                        </tr>
                                        <tr>
                                            <th>任务名称</th>
                                            <td>{{ task.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>客户</th>
                                            <td>{{ task.client.name if task.client else '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>状态</th>
                                            <td>
                                                {% if task.status == 'in_progress' %}
                                                <span class="badge bg-primary">进行中</span>
                                                {% elif task.status == 'completed' %}
                                                <span class="badge bg-success">已完成</span>
                                                {% else %}
                                                <span class="badge bg-secondary">{{ task.status }}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>目标数量</th>
                                            <td>{{ task.target_count }}</td>
                                        </tr>
                                        <tr>
                                            <th>实际数量</th>
                                            <td>{{ task.actual_count }}</td>
                                        </tr>
                                        <tr>
                                            <th>创建时间</th>
                                            <td>{{ task.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">补充信息</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <h5><i class="fas fa-info-circle"></i> 当前补充来源设置：</h5>
                                        {% if supplement_source == 'unassigned' %}
                                        <p>从未分配文案池补充</p>
                                        {% else %}
                                        <p>生成新文案</p>
                                        {% endif %}
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="info-box bg-light">
                                                <div class="info-box-content">
                                                    <span class="info-box-text">已删除文案数量</span>
                                                    <span class="info-box-number text-danger">{{ deleted_count }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-box bg-light">
                                                <div class="info-box-content">
                                                    <span class="info-box-text">可用于补充的文案数量</span>
                                                    <span class="info-box-number text-success">{{ available_count }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 批次列表 -->
                    <h5>批次列表</h5>
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>批次名称</th>
                                    <th>文案数量</th>
                                    <th>已删除数量</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for batch in batches %}
                                <tr>
                                    <td>{{ batch.id }}</td>
                                    <td>{{ batch.name }}</td>
                                    <td>{{ batch.content_count }}</td>
                                    <td>{{ batch_deleted_counts.get(batch.id, 0) }}</td>
                                    <td>{{ batch.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        {% if batch_deleted_counts.get(batch.id, 0) > 0 %}
                                        <button type="button" class="btn btn-sm btn-primary batch-supplement" 
                                                data-batch-id="{{ batch.id }}" 
                                                data-task-id="{{ task.id }}" 
                                                data-deleted-count="{{ batch_deleted_counts.get(batch.id, 0) }}">
                                            <i class="fas fa-sync"></i> 批量补充
                                        </button>
                                        {% else %}
                                        <button type="button" class="btn btn-sm btn-secondary" disabled>
                                            <i class="fas fa-sync"></i> 批量补充
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="6" class="text-center">没有找到批次</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量补充模态框 -->
<div class="modal fade" id="batchSupplementModal" tabindex="-1" aria-labelledby="batchSupplementModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchSupplementModalLabel">批量补充文案</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="batchSupplementForm">
                    <input type="hidden" id="modalTaskId" name="task_id">
                    <input type="hidden" id="modalBatchId" name="batch_id">
                    <div class="mb-3">
                        <label for="modalDeletedCount" class="form-label">已删除数量</label>
                        <input type="text" class="form-control" id="modalDeletedCount" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="modalCount" class="form-label">补充数量</label>
                        <input type="number" class="form-control" id="modalCount" name="count" min="1" max="100">
                        <small class="form-text text-muted">最大不超过已删除数量</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmBatchSupplement">确认补充</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 批量补充按钮点击事件
    document.querySelectorAll('.batch-supplement').forEach(button => {
        button.addEventListener('click', function() {
            const taskId = this.dataset.taskId;
            const batchId = this.dataset.batchId;
            const deletedCount = this.dataset.deletedCount;
            
            // 设置模态框数据
            document.getElementById('modalTaskId').value = taskId;
            document.getElementById('modalBatchId').value = batchId;
            document.getElementById('modalDeletedCount').value = deletedCount;
            document.getElementById('modalCount').max = deletedCount;
            document.getElementById('modalCount').value = deletedCount;
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('batchSupplementModal'));
            modal.show();
        });
    });
    
    // 确认补充按钮点击事件
    document.getElementById('confirmBatchSupplement').addEventListener('click', function() {
        const form = document.getElementById('batchSupplementForm');
        const formData = new FormData(form);
        
        // 发送AJAX请求
        fetch('{{ url_for("supplement.batch_supplement") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 显示成功消息
                alert(data.message);
                // 刷新页面
                location.reload();
            } else {
                // 显示错误消息
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('操作失败，请重试');
        });
    });
</script>
{% endblock %} 