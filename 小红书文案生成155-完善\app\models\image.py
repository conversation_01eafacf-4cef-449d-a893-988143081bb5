#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图片模型
"""

from datetime import datetime
from app.models import db

class ContentImage(db.Model):
    """文案图片模型"""
    __tablename__ = 'content_images'
    
    id = db.Column(db.Integer, primary_key=True)
    content_id = db.Column(db.Integer, db.ForeignKey('contents.id'), nullable=False)
    image_path = db.Column(db.String(500), nullable=False, comment='图片路径')
    thumbnail_path = db.Column(db.String(500), comment='缩略图路径')
    original_name = db.Column(db.String(255), comment='原始文件名')
    file_size = db.Column(db.Integer, comment='文件大小（字节）')
    image_order = db.Column(db.Integer, default=1, comment='图片排序')
    upload_time = db.Column(db.DateTime, default=datetime.now, comment='上传时间')
    is_deleted = db.Column(db.<PERSON>, default=False, comment='是否已删除')
    deleted_at = db.Column(db.DateTime, comment='删除时间')
    
    # 关联关系
    content = db.relationship('Content', backref=db.backref('images', lazy='dynamic'))
    
    def __repr__(self):
        return f'<ContentImage {self.id}: {self.original_name}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'content_id': self.content_id,
            'image_path': self.image_path,
            'thumbnail_path': self.thumbnail_path,
            'original_name': self.original_name,
            'file_size': self.file_size,
            'image_order': self.image_order,
            'upload_time': self.upload_time.isoformat() if self.upload_time else None,
            'is_deleted': self.is_deleted
        }
    
    @property
    def file_size_mb(self):
        """文件大小（MB）"""
        if self.file_size:
            return round(self.file_size / (1024 * 1024), 2)
        return 0
    
    def mark_deleted(self):
        """标记为已删除"""
        self.is_deleted = True
        self.deleted_at = datetime.now()
    
    @classmethod
    def get_by_content(cls, content_id, include_deleted=False):
        """根据文案ID获取图片列表"""
        query = cls.query.filter_by(content_id=content_id)
        if not include_deleted:
            query = query.filter_by(is_deleted=False)
        return query.order_by(cls.image_order, cls.upload_time).all()
    
    @classmethod
    def get_active_count(cls, content_id):
        """获取文案的有效图片数量"""
        return cls.query.filter_by(content_id=content_id, is_deleted=False).count()
    
    @classmethod
    def get_next_order(cls, content_id):
        """获取下一个排序号"""
        max_order = db.session.query(db.func.max(cls.image_order)).filter_by(
            content_id=content_id, is_deleted=False
        ).scalar()
        return (max_order or 0) + 1
