{% extends 'base.html' %}

{% block title %}创建用户{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-md-8 col-lg-6 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">创建新用户</h5>
                </div>
                <div class="card-body">
                    <form method="post" data-ajax-form>
                        {{ form.csrf_token }}
                        
                        <div class="mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control") }}
                            {% if form.username.errors %}
                            <div class="text-danger">
                                {% for error in form.username.errors %}
                                <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control", type="email") }}
                            {% if form.email.errors %}
                            <div class="text-danger">
                                {% for error in form.email.errors %}
                                <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.password.label(class="form-label") }}
                                {{ form.password(class="form-control") }}
                                {% if form.password.errors %}
                                <div class="text-danger">
                                    {% for error in form.password.errors %}
                                    <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                {{ form.confirm_password.label(class="form-label") }}
                                {{ form.confirm_password(class="form-control") }}
                                {% if form.confirm_password.errors %}
                                <div class="text-danger">
                                    {% for error in form.confirm_password.errors %}
                                    <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.real_name.label(class="form-label") }}
                                {{ form.real_name(class="form-control") }}
                                {% if form.real_name.errors %}
                                <div class="text-danger">
                                    {% for error in form.real_name.errors %}
                                    <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                {{ form.phone.label(class="form-label") }}
                                {{ form.phone(class="form-control") }}
                                {% if form.phone.errors %}
                                <div class="text-danger">
                                    {% for error in form.phone.errors %}
                                    <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">菜单权限</label>
                            <p class="text-muted mb-3">选择用户可以访问的菜单功能：</p>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">基础功能</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-check mb-2">
                                                {{ form.dashboard_access(class="form-check-input") }}
                                                {{ form.dashboard_access.label(class="form-check-label") }}
                                            </div>
                                            <div class="form-check mb-2">
                                                {{ form.content_generate(class="form-check-input") }}
                                                {{ form.content_generate.label(class="form-check-label") }}
                                            </div>
                                            <div class="form-check mb-2">
                                                {{ form.content_manage(class="form-check-input") }}
                                                {{ form.content_manage.label(class="form-check-label") }}
                                            </div>
                                            <div class="form-check mb-2">
                                                {{ form.template_manage(class="form-check-input") }}
                                                {{ form.template_manage.label(class="form-check-label") }}
                                            </div>
                                            <div class="form-check mb-2">
                                                {{ form.topic_manage(class="form-check-input") }}
                                                {{ form.topic_manage.label(class="form-check-label") }}
                                            </div>
                                            <div class="form-check mb-2">
                                                {{ form.client_manage(class="form-check-input") }}
                                                {{ form.client_manage.label(class="form-check-label") }}
                                            </div>
                                            <div class="form-check mb-2">
                                                {{ form.task_manage(class="form-check-input") }}
                                                {{ form.task_manage.label(class="form-check-label") }}
                                            </div>
                                            <div class="form-check mb-2">
                                                {{ form.notification_manage(class="form-check-input") }}
                                                {{ form.notification_manage.label(class="form-check-label") }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">高级功能</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-check mb-2">
                                                {{ form.publish_manage(class="form-check-input") }}
                                                {{ form.publish_manage.label(class="form-check-label") }}
                                            </div>
                                            <div class="form-check mb-2">
                                                {{ form.supplement_manage(class="form-check-input") }}
                                                {{ form.supplement_manage.label(class="form-check-label") }}
                                            </div>
                                            <div class="form-check mb-2">
                                                {{ form.display_manage(class="form-check-input") }}
                                                {{ form.display_manage.label(class="form-check-label") }}
                                            </div>
                                            <div class="form-check mb-2">
                                                {{ form.stats_view(class="form-check-input") }}
                                                {{ form.stats_view.label(class="form-check-label") }}
                                            </div>
                                            <div class="form-check mb-2">
                                                {{ form.export_manage(class="form-check-input") }}
                                                {{ form.export_manage.label(class="form-check-label") }}
                                            </div>
                                            <div class="form-check mb-2">
                                                {{ form.user_manage(class="form-check-input") }}
                                                {{ form.user_manage.label(class="form-check-label") }}
                                            </div>
                                            <div class="form-check mb-2">
                                                {{ form.system_settings(class="form-check-input") }}
                                                {{ form.system_settings.label(class="form-check-label") }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('user_management.user_list') }}" class="btn btn-secondary" data-ajax-link>
                                <i class="fas fa-arrow-left"></i> 返回
                            </a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 