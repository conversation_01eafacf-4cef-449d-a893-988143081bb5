"""
模型模块
"""
from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

def init_app(app):
    """初始化数据库"""
    db.init_app(app)

# 导入模型，避免循环导入
from app.models.user import User, Role, Permission
from app.models.template import Template, TemplateCategory
from app.models.topic import Topic, TopicRelation
from app.models.client import Client, ClientShare, ClientShareLink
from app.models.task import Task, Batch
from app.models.content import Content, ContentHistory, RejectionReason, QuickReason
from app.models.publish import PublishRecord, PublishTimeout
from app.models.display import DisplaySchedule, DisplaySetting
from app.models.image import ContentImage
from app.models.system_setting import SystemSetting