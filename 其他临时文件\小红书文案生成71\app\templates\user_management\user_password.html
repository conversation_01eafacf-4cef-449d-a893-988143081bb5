{% extends 'base.html' %}

{% block title %}修改密码{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-md-6 col-lg-4 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-warning text-white">
                    <h5 class="card-title mb-0">修改用户密码：{{ user.username }}</h5>
                </div>
                <div class="card-body">
                    <form method="post" data-ajax-form>
                        {{ form.csrf_token }}
                        
                        <div class="mb-3">
                            {{ form.password.label(class="form-label") }}
                            {{ form.password(class="form-control") }}
                            {% if form.password.errors %}
                            <div class="text-danger">
                                {% for error in form.password.errors %}
                                <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.confirm_password.label(class="form-label") }}
                            {{ form.confirm_password(class="form-control") }}
                            {% if form.confirm_password.errors %}
                            <div class="text-danger">
                                {% for error in form.confirm_password.errors %}
                                <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('user_management.user_edit', user_id=user.id) }}" class="btn btn-secondary" data-ajax-link>
                                <i class="fas fa-arrow-left"></i> 返回
                            </a>
                            {{ form.submit(class="btn btn-warning") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 