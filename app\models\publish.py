"""
发布相关模型
"""
from datetime import datetime
import json
from . import db


class PublishRecord(db.Model):
    """发布记录模型"""
    __tablename__ = 'publish_records'
    
    id = db.Column(db.Integer, primary_key=True)
    content_id = db.Column(db.Integer, db.<PERSON>ey('contents.id'), nullable=False)
    status = db.Column(db.String(20), default='pending')  # 发布状态：pending待发布, success成功, failed失败
    platform = db.Column(db.String(50))  # 发布平台
    account = db.Column(db.String(100))  # 发布账号
    publish_url = db.Column(db.String(255))  # 发布链接
    publish_time = db.Column(db.DateTime)  # 发布时间
    error_message = db.Column(db.Text)  # 失败原因
    ext_info = db.Column(db.Text)  # 扩展信息（JSON格式）
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 注意：Content模型中已经定义了publish_records关系，这里不再重复定义
    # content = db.relationship('Content', backref=db.backref('publish_records', lazy='dynamic'))
    
    @property
    def ext_data(self):
        """获取扩展数据"""
        if self.ext_info:
            try:
                return json.loads(self.ext_info)
            except:
                return {}
        return {}
    
    @ext_data.setter
    def ext_data(self, value):
        """设置扩展数据"""
        if value:
            self.ext_info = json.dumps(value)
        else:
            self.ext_info = None
    
    def __repr__(self):
        return f'<PublishRecord {self.id} for Content {self.content_id}>'


class PublishTimeout(db.Model):
    """发布超时设置模型"""
    __tablename__ = 'publish_timeouts'
    
    id = db.Column(db.Integer, primary_key=True)
    timeout_minutes = db.Column(db.Integer, default=60)  # 超时时间（分钟）
    action = db.Column(db.String(20), default='notify')  # 超时动作：notify通知, retry重试, mark_failed标记失败
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # 关联
    updater = db.relationship('User', backref=db.backref('publish_timeouts', lazy='dynamic'))
    
    def __repr__(self):
        return f'<PublishTimeout {self.id}>' 