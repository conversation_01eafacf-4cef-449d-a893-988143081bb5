<!-- 专用于编辑模板的独立表单页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <form method="post" id="edit-template-form">
                        {{ form.csrf_token }}
                        <input type="hidden" name="redirect_after_submit" value="{{ url_for('template.index') }}">
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    {{ form.title.label(class="form-label") }}
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-card-heading"></i>
                                        </span>
                                        {{ form.title(class="form-control", id="edit_title_field", placeholder="请输入模板标题，点击下方标记按钮可插入标记") }}
                                    </div>
                                    {% if form.title.errors %}
                                        {% for error in form.title.errors %}
                                        <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        提示：点击下方的标记按钮可以快速插入标记到标题中 | 当前焦点：<span id="edit-title-focus-indicator" class="text-success">点击此处输入标题</span>
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    {{ form.category_id.label(class="form-label") }}
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-folder"></i>
                                        </span>
                                        {{ form.category_id(class="form-select") }}
                                    </div>
                                    {% if form.category_id.errors %}
                                        {% for error in form.category_id.errors %}
                                        <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">状态</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="edit_status_switch" name="status" {{ 'checked' if form.status.data else '' }}>
                                        <label class="form-check-label" for="edit_status_switch">
                                            <span id="edit_status_text">{{ '启用' if form.status.data else '禁用' }}</span>
                                        </label>
                                    </div>
                                    {% if form.status.errors %}
                                        {% for error in form.status.errors %}
                                        <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- 可用标记按钮 -->
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="bi bi-tags"></i> 可用标记
                            </label>
                            <div class="d-flex flex-wrap gap-2">
                                {% for mark in marks %}
                                <button type="button" class="btn btn-outline-primary btn-sm mark-button" 
                                        onclick="editInsertMark('{{ mark.name }}')" 
                                        title="{{ mark.description }}">
                                    <i class="bi bi-tag"></i> {{ mark.name }}
                                </button>
                                {% endfor %}
                            </div>
                            <small class="form-text text-muted">
                                <i class="bi bi-lightbulb"></i>
                                点击标记按钮将在当前焦点位置插入标记
                            </small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="bi bi-file-text"></i> {{ form.content.label.text }}
                            </label>
                            {{ form.content(class="form-control", id="edit_content", rows="15", style="min-height: 400px; font-family: 'Courier New', monospace;", placeholder="请输入模板内容，点击上方标记按钮可插入标记...") }}
                            {% if form.content.errors %}
                                {% for error in form.content.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                            <small class="form-text text-muted">
                                <i class="bi bi-info-circle"></i>
                                提示：点击上方的标记按钮可以快速插入标记到内容中 | 当前焦点：<span id="edit-content-focus-indicator" class="text-primary">点击此处输入内容</span>
                            </small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle"></i> 取消
                            </button>
                            <button type="button" class="btn btn-primary" id="edit-submit-btn">
                                <i class="bi bi-check-circle"></i> 保存修改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .mark-button {
        margin: 5px;
    }
    
    /* 标记的通用样式 */
    .template-mark {
        background-color: #e9f5ff;
        border: 1px solid #b3d7ff;
        border-radius: 3px;
        padding: 2px 4px;
        font-weight: bold;
        color: #0066cc;
        display: inline-block;
        margin: 0 2px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* 输入框焦点状态指示 */
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .current-focus {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    }

    .has-marks {
        background-color: #f8f9fa;
    }
</style>

<script>
// 立即执行的测试
console.log('🚀 编辑模板JavaScript文件开始加载');

// 使用延迟执行，确保DOM元素已经渲染
setTimeout(function() {
    console.log('🔄 开始初始化编辑模板表单');

    // 获取表单元素
    const editTitleField = document.getElementById('edit_title_field');
    const editContentField = document.getElementById('edit_content');
    const editSubmitBtn = document.getElementById('edit-submit-btn');

    console.log('🔍 查找表单元素:', {
        editTitleField: editTitleField,
        editContentField: editContentField,
        editSubmitBtn: editSubmitBtn
    });

    if (!editTitleField || !editContentField || !editSubmitBtn) {
        console.error('❌ 找不到编辑表单元素');
        console.error('🔍 页面中所有input元素:', document.querySelectorAll('input'));
        console.error('🔍 页面中所有textarea元素:', document.querySelectorAll('textarea'));
        console.error('🔍 页面中所有button元素:', document.querySelectorAll('button'));
        return;
    }

    console.log('✅ 找到编辑表单元素');
    
    // 全局变量（用于编辑表单）
    window.editTemplateCurrentFocus = editContentField; // 默认焦点在内容区域
    
    // 更新焦点指示器
    function updateEditFocusIndicator() {
        console.log('🔄 更新编辑表单焦点指示器');
        
        // 移除所有焦点指示
        editTitleField.classList.remove('current-focus');
        editContentField.classList.remove('current-focus');

        // 更新焦点指示文本
        const titleIndicator = document.getElementById('edit-title-focus-indicator');
        const contentIndicator = document.getElementById('edit-content-focus-indicator');

        if (window.editTemplateCurrentFocus === editTitleField) {
            console.log('📍 设置标题为当前焦点');
            editTitleField.classList.add('current-focus');
            if (titleIndicator) titleIndicator.textContent = '当前活动 ✓';
            if (contentIndicator) contentIndicator.textContent = '点击此处输入内容';
        } else if (window.editTemplateCurrentFocus === editContentField) {
            console.log('📍 设置内容为当前焦点');
            editContentField.classList.add('current-focus');
            if (titleIndicator) titleIndicator.textContent = '点击此处输入标题';
            if (contentIndicator) contentIndicator.textContent = '当前活动 ✓';
        }
    }
    
    // 设置事件监听器（添加详细调试）
    console.log('🔧 设置编辑表单事件监听器');

    editTitleField.addEventListener('focus', function(e) {
        console.log('🎯 编辑-标题字段获得焦点');
        console.log('🎯 焦点事件详情:', { target: e.target, currentTarget: e.currentTarget });
        window.editTemplateCurrentFocus = this;
        updateEditFocusIndicator();
    });

    editTitleField.addEventListener('click', function(e) {
        console.log('🖱️ 编辑-标题字段被点击');
        console.log('🖱️ 点击事件详情:', { target: e.target, currentTarget: e.currentTarget });
        window.editTemplateCurrentFocus = this;
        updateEditFocusIndicator();
    });

    // 添加 mousedown 事件确保捕获
    editTitleField.addEventListener('mousedown', function(e) {
        console.log('🖱️ 编辑-标题字段 mousedown');
        window.editTemplateCurrentFocus = this;
        updateEditFocusIndicator();
    });

    editContentField.addEventListener('focus', function(e) {
        console.log('🎯 编辑-内容字段获得焦点');
        console.log('🎯 焦点事件详情:', { target: e.target, currentTarget: e.currentTarget });
        window.editTemplateCurrentFocus = this;
        updateEditFocusIndicator();
    });

    editContentField.addEventListener('click', function(e) {
        console.log('🖱️ 编辑-内容字段被点击');
        console.log('🖱️ 点击事件详情:', { target: e.target, currentTarget: e.currentTarget });
        window.editTemplateCurrentFocus = this;
        updateEditFocusIndicator();
    });

    // 添加 mousedown 事件确保捕获
    editContentField.addEventListener('mousedown', function(e) {
        console.log('🖱️ 编辑-内容字段 mousedown');
        window.editTemplateCurrentFocus = this;
        updateEditFocusIndicator();
    });
    
    // 初始化焦点指示器
    updateEditFocusIndicator();

    // 状态开关事件
    const statusSwitch = document.getElementById('edit_status_switch');
    const statusText = document.getElementById('edit_status_text');
    if (statusSwitch && statusText) {
        statusSwitch.addEventListener('change', function() {
            statusText.textContent = this.checked ? '启用' : '禁用';
            console.log('📊 状态开关变更:', this.checked ? '启用' : '禁用');
        });
    }

    // 提交按钮事件
    editSubmitBtn.addEventListener('click', function() {
        console.log('🔄 编辑表单提交');
        submitEditForm();
    });

    console.log('✅ 编辑模板表单初始化完成');
});

// 编辑模板专用的插入标记函数
function editInsertMark(markName) {
    console.log('🏷️ 编辑模板插入标记:', markName);
    
    const markText = '{' + markName + '}';
    const targetField = window.editTemplateCurrentFocus;
    
    if (!targetField) {
        console.error('❌ 没有焦点字段');
        return;
    }
    
    console.log('📍 插入到字段:', targetField.id);
    
    // 获取当前光标位置
    const startPos = targetField.selectionStart || 0;
    const endPos = targetField.selectionEnd || 0;

    // 在光标位置插入标记
    targetField.value =
        targetField.value.substring(0, startPos) +
        markText +
        targetField.value.substring(endPos);

    // 将光标位置设置到插入的标记之后
    const newPos = startPos + markText.length;
    targetField.selectionStart = targetField.selectionEnd = newPos;

    // 保持焦点在目标输入框
    targetField.focus();
    
    console.log('✅ 标记插入成功');
}

// 编辑表单提交函数
function submitEditForm() {
    console.log('🔄 开始提交编辑表单');
    
    const form = document.getElementById('edit-template-form');
    const editTitleField = document.getElementById('edit_title_field');
    const editContentField = document.getElementById('edit_content');
    
    if (!form || !editTitleField || !editContentField) {
        console.error('❌ 找不到表单元素');
        return;
    }
    
    // 保存原始值
    const originalTitle = editTitleField.value;
    const originalContent = editContentField.value;
    
    // 标记格式已经是{标记名}，不需要转换
    // 如果有其他格式，统一转换为{标记名}
    editTitleField.value = editTitleField.value.replace(/《([^》]+)》/g, '{$1}');
    editContentField.value = editContentField.value.replace(/《([^》]+)》/g, '{$1}');
    
    console.log('📝 转换后的值:', {
        title: editTitleField.value,
        content: editContentField.value
    });
    
    // 获取模板ID
    const modalElement = document.getElementById('editTemplateModal');
    const templateId = modalElement?.dataset?.templateId;
    
    if (!templateId) {
        console.error('❌ 找不到模板ID');
        return;
    }
    
    const submitUrl = `/templates/edit/${templateId}`;
    console.log('🎯 提交URL:', submitUrl);
    
    // 创建FormData
    const formData = new FormData(form);
    
    // 提交表单
    fetch(submitUrl, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('📨 服务器响应:', data);
        
        if (data.success) {
            console.log('✅ 编辑保存成功');
            showToast('保存成功！', 'success');

            // 关闭模态框并清理遮罩层
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();

                // 确保遮罩层被移除
                setTimeout(() => {
                    // 移除所有可能的遮罩层
                    const backdrops = document.querySelectorAll('.modal-backdrop');
                    backdrops.forEach(backdrop => backdrop.remove());

                    // 移除body上的modal相关类
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';

                    console.log('🧹 已清理模态框遮罩层');
                }, 300); // 等待模态框动画完成
            }

            // 更新模板行
            updateTemplateRow(data.template);
        } else {
            console.log('❌ 编辑保存失败:', data.message);
            showToast('保存失败：' + (data.message || '未知错误'), 'danger');
        }
        
        // 恢复原始值
        editTitleField.value = originalTitle;
        editContentField.value = originalContent;
    })
    .catch(error => {
        console.error('❌ 提交失败:', error);
        showToast('提交失败：' + error.message, 'danger');

        // 确保遮罩层被移除（即使出错也要清理）
        setTimeout(() => {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());

            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';

            console.log('🧹 错误情况下也已清理模态框遮罩层');
        }, 300);

        // 恢复原始值
        editTitleField.value = originalTitle;
        editContentField.value = originalContent;
    });
}

}, 100); // setTimeout 延迟100ms执行
</script>
