"""
任务管理相关表单
"""
from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, IntegerField, DateField, HiddenField, BooleanField
from wtforms.validators import DataRequired, Optional, Length, NumberRange, ValidationError


class TaskForm(FlaskForm):
    """任务表单"""
    name = StringField('任务名称', validators=[DataRequired(), Length(max=100)])
    client_id = SelectField('所属客户', coerce=int, validators=[DataRequired()])
    description = TextAreaField('任务描述', validators=[Optional(), Length(max=500)])
    target_count = IntegerField('目标文案数量', validators=[Optional(), NumberRange(min=1)])
    status = SelectField('状态', choices=[
        ('processing', '进行中'),
        ('completed', '已完成')
    ], default='processing')


class BatchForm(FlaskForm):
    """批次表单"""
    task_id = SelectField('所属任务', coerce=int, validators=[DataRequired()])
    name = StringField('批次名称', validators=[DataRequired(), Length(max=100)])
    content_count = IntegerField('文案数量', validators=[Optional(), NumberRange(min=0)])


class TaskFilterForm(FlaskForm):
    """任务筛选表单"""
    client_id = SelectField('客户', coerce=int, validators=[Optional()])
    status = SelectField('状态', choices=[
        ('', '全部状态'),
        ('processing', '进行中'),
        ('completed', '已完成')
    ], validators=[Optional()])
    
    date_from = DateField('开始日期', validators=[Optional()])
    date_to = DateField('结束日期', validators=[Optional()])
    search = StringField('搜索', validators=[Optional(), Length(max=100)])
    
    sort_by = SelectField('排序方式', choices=[
        ('created_at', '创建时间'),
        ('name', '任务名称'),
        ('client_id', '客户')
    ], default='created_at')
    
    sort_order = SelectField('排序顺序', choices=[
        ('desc', '降序'),
        ('asc', '升序')
    ], default='desc')


class BatchFilterForm(FlaskForm):
    """批次筛选表单"""
    task_id = SelectField('任务', coerce=int, validators=[Optional()])
    search = StringField('搜索', validators=[Optional(), Length(max=100)])
    
    sort_by = SelectField('排序方式', choices=[
        ('created_at', '创建时间'),
        ('name', '批次名称'),
        ('content_count', '文案数量')
    ], default='created_at')
    
    sort_order = SelectField('排序顺序', choices=[
        ('desc', '降序'),
        ('asc', '升序')
    ], default='desc')


class TaskBatchActionForm(FlaskForm):
    """任务批量操作表单"""
    action = SelectField('操作', choices=[
        ('complete', '标记完成'),
        ('delete', '批量删除')
    ], validators=[DataRequired()])
    
    task_ids = HiddenField('任务ID列表', validators=[DataRequired()])
    confirm = BooleanField('确认操作', validators=[DataRequired()]) 