# -*- coding: utf-8 -*-
"""
视图模块
"""

from app.views.main import main_bp
from app.views.auth import auth_bp
from app.views.template import template_bp
from app.views.topic import topic_bp
from app.views.client import client_bp
from app.views.task import task_bp
from app.views.content import content_bp
from app.views.upload import upload_bp
from app.views.reason import reason_bp
from app.views.stats import stats_bp
from app.views.export import export_bp
from app.views.publish import publish_bp
from app.views.supplement import supplement_bp
from app.views.display import display_bp
from app.views.notification import notification_bp
from app.views.system import system_bp
from app.views.api import api_bp
from app.views.user_management import user_management
from app.views.navigation import navigation
from app.views.first_review import first_review_bp
from app.views.image_management import image_management_bp
from app.views.final_review import final_review_bp
from app.views.main_simple import main_simple_bp
from app.views.client_review import client_review_bp

def register_blueprints(app):
    """注册蓝图"""
    app.register_blueprint(main_bp)
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(template_bp, url_prefix='/templates')
    app.register_blueprint(topic_bp, url_prefix='/topics')
    app.register_blueprint(client_bp, url_prefix='/clients')
    app.register_blueprint(task_bp, url_prefix='/tasks')
    app.register_blueprint(content_bp, url_prefix='/contents')
    app.register_blueprint(upload_bp, url_prefix='/upload')
    app.register_blueprint(reason_bp, url_prefix='/reasons')
    app.register_blueprint(stats_bp, url_prefix='/stats')
    app.register_blueprint(export_bp, url_prefix='/export')
    app.register_blueprint(publish_bp, url_prefix='/publish')
    app.register_blueprint(supplement_bp, url_prefix='/supplement')
    app.register_blueprint(display_bp, url_prefix='/display')
    app.register_blueprint(notification_bp, url_prefix='/notifications')
    app.register_blueprint(system_bp, url_prefix='/system')
    app.register_blueprint(user_management, url_prefix='/user-management')
    app.register_blueprint(navigation, url_prefix='/nav')
    app.register_blueprint(first_review_bp)
    app.register_blueprint(image_management_bp)
    app.register_blueprint(final_review_bp)

    # 注册简化版主页面蓝图
    app.register_blueprint(main_simple_bp, url_prefix='/simple')

    # 注册客户审核蓝图
    app.register_blueprint(client_review_bp)

    # 注册API蓝图
    app.register_blueprint(api_bp, url_prefix='/api')