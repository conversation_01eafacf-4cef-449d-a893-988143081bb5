{% extends "client/share_layout.html" %}

{% block title %}编辑文案 - {{ client.name }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="card mb-4">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h3 class="mb-0">{{ client.name }} - 文案编辑</h3>
                <span class="badge bg-warning">编辑模式</span>
            </div>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="fas fa-info-circle me-2"></i> 您可以在此页面查看和编辑文案内容。注意：已发布的文案不可编辑。
            </div>
            
            <!-- 文案列表 -->
            <div class="content-list mt-4">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label for="statusFilter" class="form-label">状态筛选</label>
                            <select id="statusFilter" class="form-select">
                                <option value="all">全部状态</option>
                                <option value="pending">待审核</option>
                                <option value="approved">已通过</option>
                                <option value="rejected">已拒绝</option>
                                <option value="published">已发布</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label for="dateFilter" class="form-label">日期筛选</label>
                            <input type="date" id="dateFilter" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label for="searchFilter" class="form-label">搜索</label>
                            <input type="text" id="searchFilter" class="form-control" placeholder="搜索文案内容">
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive mt-3">
                    <table class="table table-hover table-striped">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>标题</th>
                                <th>内容预览</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="contentTableBody">
                            <!-- 这里将通过AJAX加载文案数据 -->
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2">正在加载文案数据...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页控件 -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <span class="text-muted">共 <span id="totalCount">0</span> 条记录</span>
                    </div>
                    <nav aria-label="Page navigation">
                        <ul class="pagination" id="pagination">
                            <!-- 分页将通过JS动态生成 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 文案详情模态框 -->
<div class="modal fade" id="contentDetailModal" tabindex="-1" aria-labelledby="contentDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contentDetailModalLabel">文案详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="contentDetailBody">
                <!-- 文案详情将通过JS动态加载 -->
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载文案详情...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="editContentBtn" onclick="showEditForm()">
                    <i class="fas fa-edit me-1"></i> 编辑文案
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑文案模态框 -->
<div class="modal fade" id="editContentModal" tabindex="-1" aria-labelledby="editContentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editContentModalLabel">编辑文案</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editContentForm">
                    <div class="mb-3">
                        <label for="editTitle" class="form-label">标题</label>
                        <input type="text" class="form-control" id="editTitle" required>
                        <div class="form-text">
                            字符统计: <span id="titleLength">0</span>/20 
                            <span id="titleStatus" class="text-success">✓ 符合要求</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editContent" class="form-label">内容</label>
                        <textarea class="form-control" id="editContent" rows="6" required></textarea>
                        <div class="form-text">
                            字符统计: <span id="contentLength">0</span>/1000 
                            <span id="contentStatus" class="text-success">✓ 符合要求</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editTopics" class="form-label">话题标签</label>
                        <input type="text" class="form-control" id="editTopics" placeholder="多个话题用逗号分隔">
                        <div class="form-text">例如：美食,旅行,生活</div>
                    </div>
                    <div class="mb-3">
                        <label for="editLocation" class="form-label">位置信息</label>
                        <input type="text" class="form-control" id="editLocation">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveContentEdit()">保存修改</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 字符统计函数
    function calculateTitleLength(text) {
        let length = 0;
        for (let char of text) {
            // 检查是否是中文字符（包括中文标点符号）
            if (/[\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]/.test(char)) {
                length += 2; // 中文字符算2个字符
            } else {
                length += 1; // 英文、数字、符号算1个字符
            }
        }
        return length;
    }

    function calculateContentLength(text) {
        let length = 0;
        for (let char of text) {
            // 检查是否是中文字符
            if (/[\u4e00-\u9fff]/.test(char)) {
                length += 2; // 中文字符算2个
            } else {
                length += 1; // 其他字符算1个
            }
        }
        return length;
    }

    // 更新标题字符统计
    function updateTitleLength() {
        const titleInput = document.getElementById('editTitle');
        const titleLength = document.getElementById('titleLength');
        const titleStatus = document.getElementById('titleStatus');
        
        if (titleInput && titleLength && titleStatus) {
            const text = titleInput.value;
            const length = calculateTitleLength(text);
            
            console.log('客户端编辑标题统计:', { text, length, originalLength: text.length });
            
            titleLength.textContent = length;
            
            if (length > 20) {
                titleLength.className = 'text-danger';
                titleStatus.className = 'text-danger';
                titleStatus.textContent = '✗ 超出限制';
            } else {
                titleLength.className = 'text-success';
                titleStatus.className = 'text-success';
                titleStatus.textContent = '✓ 符合要求';
            }
        } else {
            console.error('客户端编辑找不到标题相关元素:', { titleInput, titleLength, titleStatus });
        }
    }

    // 更新内容字符统计
    function updateContentLength() {
        const contentInput = document.getElementById('editContent');
        const contentLength = document.getElementById('contentLength');
        const contentStatus = document.getElementById('contentStatus');
        
        if (contentInput && contentLength && contentStatus) {
            const text = contentInput.value;
            const length = calculateContentLength(text);
            
            console.log('客户端编辑内容统计:', { text, length, originalLength: text.length });
            
            contentLength.textContent = length;
            
            if (length > 1000) {
                contentLength.className = 'text-danger';
                contentStatus.className = 'text-danger';
                contentStatus.textContent = '✗ 超出限制';
            } else {
                contentLength.className = 'text-success';
                contentStatus.className = 'text-success';
                contentStatus.textContent = '✓ 符合要求';
            }
        } else {
            console.error('客户端编辑找不到内容相关元素:', { contentInput, contentLength, contentStatus });
        }
    }

    // 当前选中的文案ID和数据
    let currentContentId = null;
    let currentContentData = null;

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 加载文案数据
        loadContents(1);
        
        // 绑定筛选事件
        document.getElementById('statusFilter').addEventListener('change', function() {
            loadContents(1);
        });
        
        document.getElementById('dateFilter').addEventListener('change', function() {
            loadContents(1);
        });
        
        document.getElementById('searchFilter').addEventListener('input', function() {
            if (this.value.length === 0 || this.value.length >= 2) {
                // 当输入为空或至少2个字符时触发搜索
                loadContents(1);
            }
        });
    });
    
    // 加载文案数据
    function loadContents(page) {
        const statusFilter = document.getElementById('statusFilter').value;
        const dateFilter = document.getElementById('dateFilter').value;
        const searchFilter = document.getElementById('searchFilter').value;
        
        // 显示加载中状态
        document.getElementById('contentTableBody').innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载文案数据...</p>
                </td>
            </tr>
        `;
        
        // 构建查询参数
        const params = new URLSearchParams({
            client_id: '{{ client.id }}',
            page: page,
            status: statusFilter,
            date: dateFilter,
            search: searchFilter,
            share_token: '{{ share.access_token }}'
        });
        
        // 发送AJAX请求获取数据
        fetch('/api/client/contents?' + params.toString())
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应不正常');
                }
                return response.json();
            })
            .then(data => {
                renderContents(data);
            })
            .catch(error => {
                console.error('获取数据失败:', error);
                document.getElementById('contentTableBody').innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center py-4 text-danger">
                            <i class="fas fa-exclamation-circle fa-2x mb-3"></i>
                            <p>加载数据失败，请稍后再试</p>
                        </td>
                    </tr>
                `;
            });
    }
    
    // 渲染文案数据
    function renderContents(data) {
        const tbody = document.getElementById('contentTableBody');
        const pagination = document.getElementById('pagination');
        const totalCount = document.getElementById('totalCount');
        
        // 更新总记录数
        totalCount.textContent = data.total;
        
        // 清空表格
        tbody.innerHTML = '';
        
        // 添加数据行
        if (data.contents.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4">暂无符合条件的文案数据</td>
                </tr>
            `;
        } else {
            data.contents.forEach(function(content) {
                // 根据状态设置不同的标签样式
                let statusBadge = '';
                switch (content.status) {
                    case 'pending':
                        statusBadge = '<span class="badge bg-warning">待审核</span>';
                        break;
                    case 'approved':
                        statusBadge = '<span class="badge bg-success">已通过</span>';
                        break;
                    case 'rejected':
                        statusBadge = '<span class="badge bg-danger">已拒绝</span>';
                        break;
                    case 'published':
                        statusBadge = '<span class="badge bg-primary">已发布</span>';
                        break;
                    default:
                        statusBadge = '<span class="badge bg-secondary">未知</span>';
                }
                
                // 截取内容预览
                const contentPreview = content.content.length > 50 ? 
                    content.content.substring(0, 50) + '...' : content.content;
                
                // 添加表格行
                tbody.innerHTML += `
                    <tr>
                        <td>${content.id}</td>
                        <td>${content.title}</td>
                        <td>${contentPreview}</td>
                        <td>${statusBadge}</td>
                        <td>${content.created_at}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-info" onclick="showContentDetail(${content.id})">
                                    <i class="fas fa-eye"></i> 查看
                                </button>
                                ${content.status !== 'published' ? `
                                <button class="btn btn-outline-primary" onclick="showEditForm(${content.id})">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
            });
        }
        
        // 更新分页
        renderPagination(data.page, data.pages);
    }
    
    // 渲染分页控件
    function renderPagination(currentPage, totalPages) {
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = '';
        
        if (totalPages <= 1) {
            return;
        }
        
        // 上一页按钮
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
        prevLi.innerHTML = `
            <a class="page-link" href="javascript:void(0)" onclick="loadContents(${currentPage - 1})" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
            </a>
        `;
        pagination.appendChild(prevLi);
        
        // 页码按钮
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);
        
        if (endPage - startPage < 4) {
            startPage = Math.max(1, endPage - 4);
        }
        
        for (let i = startPage; i <= endPage; i++) {
            const pageLi = document.createElement('li');
            pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
            pageLi.innerHTML = `
                <a class="page-link" href="javascript:void(0)" onclick="loadContents(${i})">${i}</a>
            `;
            pagination.appendChild(pageLi);
        }
        
        // 下一页按钮
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
        nextLi.innerHTML = `
            <a class="page-link" href="javascript:void(0)" onclick="loadContents(${currentPage + 1})" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
            </a>
        `;
        pagination.appendChild(nextLi);
    }
    
    // 显示文案详情
    function showContentDetail(contentId) {
        currentContentId = contentId;
        const modal = new bootstrap.Modal(document.getElementById('contentDetailModal'));
        modal.show();
        
        // 显示加载中状态
        document.getElementById('contentDetailBody').innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载文案详情...</p>
            </div>
        `;
        
        // 发送AJAX请求获取文案详情
        fetch(`/api/client/content/${contentId}?share_token={{ share.access_token }}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应不正常');
                }
                return response.json();
            })
            .then(data => {
                // 保存当前文案数据
                currentContentData = data;
                
                // 渲染文案详情
                renderContentDetail(data);
            })
            .catch(error => {
                console.error('获取文案详情失败:', error);
                document.getElementById('contentDetailBody').innerHTML = `
                    <div class="text-center py-5 text-danger">
                        <i class="fas fa-exclamation-circle fa-3x mb-3"></i>
                        <p>加载文案详情失败，请稍后再试</p>
                    </div>
                `;
            });
    }
    
    // 渲染文案详情
    function renderContentDetail(content) {
        // 根据状态设置不同的标签样式
        let statusBadge = '';
        switch (content.status) {
            case 'pending':
                statusBadge = '<span class="badge bg-warning">待审核</span>';
                break;
            case 'approved':
                statusBadge = '<span class="badge bg-success">已通过</span>';
                break;
            case 'rejected':
                statusBadge = '<span class="badge bg-danger">已拒绝</span>';
                break;
            case 'published':
                statusBadge = '<span class="badge bg-primary">已发布</span>';
                break;
            default:
                statusBadge = '<span class="badge bg-secondary">未知</span>';
        }
        
        // 更新模态框标题
        document.getElementById('contentDetailModalLabel').textContent = `文案详情 - ${content.title}`;
        
        // 更新模态框内容
        document.getElementById('contentDetailBody').innerHTML = `
            <div class="mb-4">
                <div class="d-flex justify-content-between mb-3">
                    <h5 class="card-title">${content.title}</h5>
                    ${statusBadge}
                </div>
                <div class="text-muted mb-3">
                    <small>创建时间: ${content.created_at}</small>
                </div>
                <div class="card">
                    <div class="card-body">
                        <div class="content-text">
                            ${content.content.replace(/\n/g, '<br>')}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <h6 class="mb-3">附加信息</h6>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">话题标签</h6>
                            </div>
                            <div class="card-body">
                                ${content.topics ? content.topics.map(topic => 
                                    `<span class="badge bg-info me-1 mb-1">#${topic}</span>`
                                ).join('') : '<span class="text-muted">无话题标签</span>'}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">位置信息</h6>
                            </div>
                            <div class="card-body">
                                ${content.location ? content.location : '<span class="text-muted">无位置信息</span>'}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 根据状态控制编辑按钮的显示
        const editBtn = document.getElementById('editContentBtn');
        if (content.status !== 'published') {
            editBtn.style.display = 'block';
        } else {
            editBtn.style.display = 'none';
        }
    }
    
    // 显示编辑表单
    function showEditForm(contentId) {
        if (contentId) {
            currentContentId = contentId;
            
            // 如果是从列表直接点击编辑，需要先获取文案详情
            if (!currentContentData || currentContentData.id !== contentId) {
                fetch(`/api/client/content/${contentId}?share_token={{ share.access_token }}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('网络响应不正常');
                        }
                        return response.json();
                    })
                    .then(data => {
                        currentContentData = data;
                        openEditModal(data);
                    })
                    .catch(error => {
                        console.error('获取文案详情失败:', error);
                        showToast('获取文案详情失败，请稍后再试', 'danger');
                    });
            } else {
                openEditModal(currentContentData);
            }
        } else if (currentContentData) {
            // 从详情页点击编辑
            openEditModal(currentContentData);
        }
    }
    
    // 打开编辑模态框
    function openEditModal(content) {
        // 关闭详情模态框
        const detailModal = bootstrap.Modal.getInstance(document.getElementById('contentDetailModal'));
        if (detailModal) detailModal.hide();
        
        // 填充表单数据
        document.getElementById('editTitle').value = content.title || '';
        document.getElementById('editContent').value = content.content || '';
        document.getElementById('editLocation').value = content.location || '';
        
        // 话题标签
        if (content.topics && Array.isArray(content.topics)) {
            document.getElementById('editTopics').value = content.topics.join(',');
        } else {
            document.getElementById('editTopics').value = '';
        }
        
        // 显示编辑模态框
        document.getElementById('editContentModalLabel').textContent = `编辑文案 - ${content.title}`;
        const editModal = new bootstrap.Modal(document.getElementById('editContentModal'));
        editModal.show();
        
        // 延迟初始化字符统计，确保模态框完全显示后再计算
        setTimeout(() => {
            console.log('初始化客户端编辑字符统计...');
            updateTitleLength();
            updateContentLength();
            
            // 添加输入事件监听器
            document.getElementById('editTitle').addEventListener('input', updateTitleLength);
            document.getElementById('editContent').addEventListener('input', updateContentLength);
        }, 200);
    }
    
    // 保存文案编辑
    function saveContentEdit() {
        if (!currentContentId) return;
        
        // 获取表单数据
        const title = document.getElementById('editTitle').value.trim();
        const content = document.getElementById('editContent').value.trim();
        const topicsStr = document.getElementById('editTopics').value.trim();
        const location = document.getElementById('editLocation').value.trim();
        
        // 表单验证
        if (!title) {
            alert('请输入标题');
            return;
        }
        
        if (!content) {
            alert('请输入内容');
            return;
        }
        
        // 字符限制验证
        const titleLength = calculateTitleLength(title);
        if (titleLength > 20) {
            alert(`标题超出限制！当前${titleLength}个字符，最多20个字符`);
            return;
        }
        
        const contentLength = calculateContentLength(content);
        if (contentLength > 1000) {
            alert(`内容超出限制！当前${contentLength}个字符，最多1000个字符`);
            return;
        }
        
        // 处理话题标签
        let topics = [];
        if (topicsStr) {
            topics = topicsStr.split(',').map(t => t.trim()).filter(t => t);
        }
        
        // 构建请求数据
        const requestData = {
            title: title,
            content: content,
            location: location,
            topics: topics,
            share_token: '{{ share.access_token }}'
        };
        
        // 发送更新请求
        fetch(`/api/client/content/${currentContentId}/update`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应不正常');
            }
            return response.json();
        })
        .then(data => {
            // 关闭编辑模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('editContentModal'));
            if (modal) modal.hide();
            
            // 显示成功提示
            showToast('文案更新成功', 'success');
            
            // 重新加载文案列表
            loadContents(1);
        })
        .catch(error => {
            console.error('更新文案失败:', error);
            showToast('更新文案失败，请稍后再试', 'danger');
        });
    }
    
    // 显示提示信息
    function showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = 'position-fixed top-0 end-0 p-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check' : 'exclamation'}-circle me-2"></i> ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;
        document.body.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast.querySelector('.toast'), {
            autohide: true,
            delay: 3000
        });
        bsToast.show();
        
        setTimeout(function() {
            document.body.removeChild(toast);
        }, 3500);
    }
</script>
{% endblock %} 