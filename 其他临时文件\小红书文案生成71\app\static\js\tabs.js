/**
 * 标签页管理功能
 */

// 标签页管理器
class TabManager {
    constructor() {
        this.tabs = new Map();
        this.activeTab = null;
        this.init();
    }

    init() {
        console.log('初始化标签管理器');

        // 处理现有标签
        const existingTabs = document.querySelectorAll('.tab-item');
        existingTabs.forEach(tab => {
            const url = tab.getAttribute('data-url');
            if (url && !this.tabs.has(url)) {
                this.tabs.set(url, {
                    element: tab,
                    title: tab.getAttribute('data-title') || '标签页',
                    url: url
                });

                // 为现有标签绑定简单的点击事件
                this.bindSimpleTabClick(tab, url);
            }
        });

        // 获取当前URL，并确定应该激活哪个标签
        const currentUrl = window.location.pathname;
        console.log('当前URL:', currentUrl);
        
        // 获取主菜单信息
        const mainMenuInfo = this.getMainMenuInfo(currentUrl);
        const targetTabUrl = mainMenuInfo.url;
        console.log('目标标签URL:', targetTabUrl, '标题:', mainMenuInfo.title);
        
        // 确保有控制面板标签
        if (!this.tabs.has('/dashboard')) {
            this.addTab('/dashboard', '控制面板', targetTabUrl === '/dashboard');
        }
        
        // 如果当前URL对应的主菜单标签不存在，创建它
        if (targetTabUrl !== '/dashboard' && !this.tabs.has(targetTabUrl)) {
            console.log('创建当前URL对应的标签:', targetTabUrl, mainMenuInfo.title);
            this.addTab(targetTabUrl, mainMenuInfo.title, true);
        } else if (targetTabUrl !== '/dashboard') {
            // 如果标签已存在，激活它
            this.activateTab(targetTabUrl);
        } else {
            // 默认激活控制面板
            this.activateTab('/dashboard');
        }
        
        // 如果当前URL不是主菜单URL，需要加载对应内容
        if (currentUrl !== targetTabUrl) {
            console.log('加载子页面内容:', currentUrl);
            this.loadContent(currentUrl);
        }

        this.bindEvents();
        this.setupUrlMonitoring();
        
        // 特殊处理客户管理页面
        if (currentUrl === '/clients/' || currentUrl.startsWith('/clients')) {
            console.log('检测到客户管理页面，进行特殊处理');
            setTimeout(() => {
                this.hideClientPageLoadingIndicators();
                this.tryBindClientPageEvents();
            }, 100);
            
            // 多次尝试绑定事件，确保页面完全加载后按钮可用
            setTimeout(() => this.tryBindClientPageEvents(), 300);
            setTimeout(() => this.tryBindClientPageEvents(), 500);
            setTimeout(() => this.tryBindClientPageEvents(), 1000);
        }
    }

    // 设置URL监听，处理页面跳转和弹窗
    setupUrlMonitoring() {
        console.log('设置URL监听');

        // 监听浏览器前进后退
        window.addEventListener('popstate', (e) => {
            console.log('浏览器前进后退:', window.location.pathname);
            this.syncTabWithCurrentUrl();
        });

        // 监听URL变化（用于处理程序化导航）
        let lastUrl = window.location.pathname;
        const urlCheckInterval = setInterval(() => {
            const currentUrl = window.location.pathname;
            if (currentUrl !== lastUrl) {
                console.log('URL变化检测:', lastUrl, '->', currentUrl);
                lastUrl = currentUrl;
                this.syncTabWithCurrentUrl();
            }
        }, 500);

        // 监听模态框和弹窗事件
        this.setupModalMonitoring();
    }

    // 设置模态框监听
    setupModalMonitoring() {
        // 监听模态框显示事件
        document.addEventListener('show.bs.modal', (e) => {
            console.log('模态框显示:', e.target);
            this.handleModalShow(e.target);
        });

        // 监听模态框隐藏事件
        document.addEventListener('hidden.bs.modal', (e) => {
            console.log('模态框隐藏:', e.target);
            this.handleModalHide(e.target);
        });

        // 监听自定义弹窗和页面跳转
        document.addEventListener('click', (e) => {
            const button = e.target.closest('button, a');
            if (button) {
                const onclick = button.getAttribute('onclick');
                const href = button.getAttribute('href');

                // 检测可能导致页面跳转的操作
                if (onclick && (onclick.includes('window.open') || onclick.includes('location.href'))) {
                    console.log('检测到页面跳转操作:', onclick);
                    setTimeout(() => this.syncTabWithCurrentUrl(), 100);
                }

                if (href && href.startsWith('javascript:')) {
                    console.log('检测到JavaScript链接:', href);
                    setTimeout(() => this.syncTabWithCurrentUrl(), 100);
                }

                // 检测表单提交按钮（排除客户管理筛选表单）
                if (button.type === 'submit' || button.closest('form')) {
                    // 排除客户管理筛选表单，避免筛选后页面刷新
                    if (button.closest('#clientFilterForm')) {
                        console.log('跳过客户管理筛选表单的页面同步');
                        return;
                    }
                    console.log('检测到表单提交按钮');
                    setTimeout(() => this.syncTabWithCurrentUrl(), 500);
                }
            }
        });

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                console.log('页面重新可见，同步标签页状态');
                setTimeout(() => this.syncTabWithCurrentUrl(), 100);
            }
        });

        // 监听窗口焦点变化
        window.addEventListener('focus', () => {
            console.log('窗口获得焦点，同步标签页状态');
            setTimeout(() => this.syncTabWithCurrentUrl(), 100);
        });
    }

    // 处理模态框显示
    handleModalShow(modal) {
        // 保存当前状态
        this.modalState = {
            activeTab: this.activeTab,
            currentUrl: window.location.pathname
        };
    }

    // 处理模态框隐藏
    handleModalHide(modal) {
        // 检查URL是否发生变化
        setTimeout(() => {
            this.syncTabWithCurrentUrl();
        }, 100);
    }

    // 同步标签页与当前URL
    syncTabWithCurrentUrl() {
        const currentUrl = window.location.pathname;
        console.log('同步标签页与URL:', currentUrl);

        // 获取主菜单信息
        const mainMenuInfo = this.getMainMenuInfo(currentUrl);
        const targetTabUrl = mainMenuInfo.url;

        // 检查是否需要创建新标签
        if (!this.tabs.has(targetTabUrl)) {
            console.log('创建新标签以匹配URL:', targetTabUrl, mainMenuInfo.title);
            this.addTab(targetTabUrl, mainMenuInfo.title, true);
        } else if (this.activeTab !== targetTabUrl) {
            console.log('激活现有标签以匹配URL:', targetTabUrl);
            this.activateTab(targetTabUrl);
        }

        // 如果当前URL与标签URL不同，加载对应内容
        if (currentUrl !== targetTabUrl) {
            console.log('加载子页面内容:', currentUrl);
            this.loadContent(currentUrl);
        } else if (currentUrl === '/clients/' || currentUrl.startsWith('/clients')) {
            // 客户管理页面：不重新加载内容，只确保事件绑定
            console.log('客户管理页面：确保事件绑定，不重新加载内容');

            // 确保客户管理页面的按钮事件被正确绑定
            setTimeout(() => {
                this.hideClientPageLoadingIndicators();
                this.tryBindClientPageEvents();
            }, 100);
        }
        
        // 确保菜单状态与标签一致
        this.updateSidebarActiveMenu(targetTabUrl);
    }

    // 简化的标签点击事件绑定
    bindSimpleTabClick(tabElement, url) {
        console.log('绑定简单点击事件:', url);

        tabElement.addEventListener('click', (e) => {
            if (!e.target.closest('.tab-close')) {
                e.preventDefault();
                e.stopPropagation();
                console.log('标签被点击:', url);
                this.activateTab(url);
                this.loadContent(url);
            }
        });
    }

    // 为标签绑定点击事件
    bindTabClickEvent(tabElement, url) {
        console.log('为标签绑定点击事件:', url);

        // 移除可能存在的旧事件监听器
        const newTabElement = tabElement.cloneNode(true);
        tabElement.parentNode.replaceChild(newTabElement, tabElement);

        // 更新tabs映射中的元素引用
        if (this.tabs.has(url)) {
            const tabInfo = this.tabs.get(url);
            tabInfo.element = newTabElement;
            this.tabs.set(url, tabInfo);
        }

        // 绑定新的点击事件
        newTabElement.addEventListener('click', (e) => {
            if (!e.target.closest('.tab-close')) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Tab clicked:', url);
                this.activateTab(url);
                this.loadContent(url);

                // 特别处理控制面板
                if (url === '/dashboard') {
                    console.log('控制面板标签被点击，确保菜单激活');
                    setTimeout(() => {
                        this.ensureDashboardMenuActive();
                    }, 100);
                }
            }
        });

        console.log('标签点击事件绑定完成:', url);
        return newTabElement;
    }

    bindEvents() {
        // 监听菜单点击
        document.addEventListener('click', (e) => {
            const link = e.target.closest('[data-ajax-link]');
            if (link && link.href) {
                e.preventDefault();
                const url = new URL(link.href).pathname;
                const title = link.textContent.trim() || link.getAttribute('title') || '新标签页';

                console.log('菜单点击:', url, title);

                // 判断是否为主菜单项
                const isMainMenu = this.isMainMenuUrl(url);

                if (isMainMenu) {
                    // 主菜单：检查标签是否已存在
                    if (this.tabs.has(url)) {
                        // 标签已存在，直接激活
                        console.log('标签已存在，激活:', url);
                        this.activateTab(url);
                        this.loadContent(url);
                    } else {
                        // 创建新标签页并自动激活
                        console.log('创建新标签:', url, title);
                        this.addTab(url, title, true);
                        this.loadContent(url);
                    }
                } else {
                    // 子菜单：在当前标签页内加载，但要确保对应的主标签页是激活的
                    const mainMenuInfo = this.getMainMenuInfo(url);
                    console.log('子菜单点击，主菜单信息:', mainMenuInfo);

                    if (this.tabs.has(mainMenuInfo.url)) {
                        this.activateTab(mainMenuInfo.url);
                    } else {
                        // 如果主标签页不存在，创建并激活
                        this.addTab(mainMenuInfo.url, mainMenuInfo.title, true);
                    }
                    this.loadContent(url);
                }
            }
        });
    }

    isMainMenuUrl(url) {
        // 定义主菜单URL模式
        const mainMenuPatterns = [
            '^/dashboard$',
            '^/user-management/users$',
            '^/clients/$',
            '^/templates/$',
            '^/contents/$',
            '^/contents/generate$',
            '^/topics/$',
            '^/tasks/$',
            '^/publish/$',
            '^/supplement/$',
            '^/display/$',
            '^/notifications/$',
            '^/stats/$',
            '^/export/$',
            '^/system/$'
        ];

        return mainMenuPatterns.some(pattern => {
            const regex = new RegExp(pattern);
            return regex.test(url);
        });
    }

    addTab(url, title, isActive = false) {
        console.log('添加标签:', url, title, '是否激活:', isActive);

        // 获取主菜单URL和标题
        const mainMenuInfo = this.getMainMenuInfo(url);
        const tabUrl = mainMenuInfo.url;
        const tabTitle = mainMenuInfo.title;

        console.log('映射后的标签:', tabUrl, tabTitle);

        // 如果标签已存在，直接激活
        if (this.tabs.has(tabUrl)) {
            console.log('标签已存在，直接激活:', tabUrl);
            this.activateTab(tabUrl);
            return;
        }

        // 创建新标签
        const tabElement = this.createTabElement(tabUrl, tabTitle);
        const tabList = document.getElementById('tabList');
        tabList.appendChild(tabElement);

        // 保存标签信息
        this.tabs.set(tabUrl, {
            element: tabElement,
            title: tabTitle,
            url: tabUrl
        });

        console.log('新标签创建完成:', tabUrl, '总标签数:', this.tabs.size);

        // 激活标签
        if (isActive || this.tabs.size === 1) {
            console.log('激活新标签:', tabUrl);
            this.activateTab(tabUrl);
        }
    }

    getMainMenuInfo(url) {
        // 根据URL确定主菜单 - 精确匹配，避免冲突
        const menuMappings = [
            { pattern: '^/dashboard$', url: '/dashboard', title: '控制面板' },
            { pattern: '^/user-management', url: '/user-management/users', title: '用户管理' },
            { pattern: '^/clients', url: '/clients/', title: '客户管理' },
            { pattern: '^/templates', url: '/templates/', title: '模板管理' },
            // 区分初审文案和生成文案
            { pattern: '^/contents/generate', url: '/contents/generate', title: '生成文案' },
            { pattern: '^/contents', url: '/contents/', title: '初审文案' },
            { pattern: '^/topics', url: '/topics/', title: '话题管理' },
            { pattern: '^/tasks', url: '/tasks/', title: '任务管理' },
            { pattern: '^/publish', url: '/publish/', title: '发布管理' },
            { pattern: '^/supplement', url: '/supplement/', title: '文案补充' },
            { pattern: '^/display', url: '/display/', title: '文案展示' },
            { pattern: '^/notifications', url: '/notifications/', title: '通知中心' },
            { pattern: '^/stats', url: '/stats/', title: '数据统计' },
            { pattern: '^/export', url: '/export/', title: '导入导出' },
            { pattern: '^/system', url: '/system/', title: '系统设置' }
        ];

        // 查找匹配的主菜单 - 按顺序匹配，更具体的模式在前
        for (const mapping of menuMappings) {
            const regex = new RegExp(mapping.pattern);
            if (regex.test(url)) {
                return { url: mapping.url, title: mapping.title };
            }
        }

        // 默认返回原始信息
        return { url: url, title: '新标签页' };
    }

    createTabElement(url, title) {
        const tabItem = document.createElement('div');
        tabItem.className = 'tab-item';
        tabItem.setAttribute('data-url', url);
        tabItem.setAttribute('data-title', title);

        const canClose = url !== '/dashboard'; // 控制面板不能关闭

        tabItem.innerHTML = `
            <span class="tab-title">${title}</span>
            ${canClose ? `
                <button class="tab-close" onclick="tabManager.closeTab('${url}')" title="关闭标签">
                    <i class="fas fa-times"></i>
                </button>
            ` : ''}
        `;

        // 使用简化的事件绑定
        this.bindSimpleTabClick(tabItem, url);
        return tabItem;
    }

    activateTab(url) {
        console.log('激活标签:', url);

        // 移除所有活动状态
        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.classList.remove('active');
        });

        // 激活指定标签
        const tab = this.tabs.get(url);
        if (tab) {
            tab.element.classList.add('active');
            this.activeTab = url;
            console.log('标签激活成功:', url);

            // 简化的菜单激活逻辑
            this.activateCorrespondingMenu(url);

            // 更新浏览器地址栏
            if (window.location.pathname !== url) {
                window.history.pushState({}, '', url);
            }
        } else {
            console.error('找不到标签:', url, '现有标签:', Array.from(this.tabs.keys()));
        }
    }

    // 简化的菜单激活逻辑
    activateCorrespondingMenu(url) {
        console.log('激活对应菜单:', url);

        // 移除所有菜单激活状态
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // 查找对应的菜单项并激活
        const menuLink = document.querySelector(`.sidebar .nav-link[href="${url}"]`);
        if (menuLink) {
            menuLink.classList.add('active');
            console.log('菜单激活成功:', url);
        } else {
            // 如果没找到精确匹配，尝试文本匹配（特别是控制面板）
            if (url === '/dashboard') {
                const dashboardMenu = Array.from(document.querySelectorAll('.sidebar .nav-link'))
                    .find(link => link.textContent.trim().includes('控制面板'));
                if (dashboardMenu) {
                    dashboardMenu.classList.add('active');
                    console.log('通过文本匹配激活控制面板菜单');
                }
            }
        }
    }

    // 更新左侧菜单的激活状态
    updateSidebarActiveMenu(url) {
        console.log('更新左侧菜单激活状态:', url);

        // 移除所有菜单项的激活状态
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // 根据URL找到对应的菜单项并激活
        const menuMappings = [
            { tabUrl: '/dashboard', menuUrl: '/dashboard' },
            { tabUrl: '/user-management/users', menuUrl: '/user-management/users' },
            { tabUrl: '/clients/', menuUrl: '/clients/' },
            { tabUrl: '/templates/', menuUrl: '/templates/' },
            { tabUrl: '/contents/generate', menuUrl: '/contents/generate' }, // 生成文案
            { tabUrl: '/contents/', menuUrl: '/contents/' }, // 文案管理
            { tabUrl: '/topics/', menuUrl: '/topics/' },
            { tabUrl: '/tasks/', menuUrl: '/tasks/' },
            { tabUrl: '/publish/', menuUrl: '/publish/' },
            { tabUrl: '/supplement/', menuUrl: '/supplement/' },
            { tabUrl: '/display/', menuUrl: '/display/' },
            { tabUrl: '/notifications/', menuUrl: '/notifications/' },
            { tabUrl: '/stats/', menuUrl: '/stats/' },
            { tabUrl: '/export/', menuUrl: '/export/' },
            { tabUrl: '/system/', menuUrl: '/system/' }
        ];

        // 查找匹配的菜单项
        const mapping = menuMappings.find(m => m.tabUrl === url);
        if (mapping) {
            // 尝试多种选择器来找到菜单项
            const selectors = [
                `.sidebar .nav-link[href="${mapping.menuUrl}"]`,
                `.sidebar .nav-link[href="${mapping.menuUrl}/"]`,
                `.sidebar a[href="${mapping.menuUrl}"]`,
                `.sidebar a[href="${mapping.menuUrl}/"]`
            ];

            let menuLink = null;
            for (const selector of selectors) {
                menuLink = document.querySelector(selector);
                if (menuLink) {
                    console.log('找到菜单项，使用选择器:', selector);
                    break;
                }
            }

            // 如果是控制面板且没找到，使用特殊处理
            if (!menuLink && url === '/dashboard') {
                console.log('控制面板菜单未找到，使用文本匹配');
                const allLinks = document.querySelectorAll('.sidebar .nav-link, .sidebar a');
                for (const link of allLinks) {
                    if (link.textContent.trim().includes('控制面板')) {
                        menuLink = link;
                        console.log('通过文本找到控制面板菜单:', link.getAttribute('href'));
                        break;
                    }
                }
            }

            if (menuLink) {
                menuLink.classList.add('active');
                console.log('激活左侧菜单项:', mapping.menuUrl);
            } else {
                console.warn('找不到对应的菜单项:', mapping.menuUrl);
                // 调试：列出所有可用的菜单链接
                const allLinks = document.querySelectorAll('.sidebar .nav-link, .sidebar a');
                console.log('所有菜单链接:', Array.from(allLinks).map(link => ({
                    href: link.getAttribute('href'),
                    text: link.textContent.trim()
                })));
            }
        } else {
            console.warn('没有找到URL映射:', url);
        }
    }





    closeTab(url) {
        const tab = this.tabs.get(url);
        if (!tab || url === '/dashboard') {
            return; // 不能关闭控制面板
        }

        // 如果关闭的是当前活动标签，需要激活其他标签
        if (this.activeTab === url) {
            const tabUrls = Array.from(this.tabs.keys());
            const currentIndex = tabUrls.indexOf(url);
            
            // 激活前一个或后一个标签
            let nextUrl = '/dashboard'; // 默认回到控制面板
            if (currentIndex > 0) {
                nextUrl = tabUrls[currentIndex - 1];
            } else if (currentIndex < tabUrls.length - 1) {
                nextUrl = tabUrls[currentIndex + 1];
            }
            
            this.activateTab(nextUrl);
            this.loadContent(nextUrl);
        }

        // 移除标签
        tab.element.remove();
        this.tabs.delete(url);
    }

    closeAllTabs() {
        const tabUrls = Array.from(this.tabs.keys());
        tabUrls.forEach(url => {
            if (url !== '/dashboard') {
                this.closeTab(url);
            }
        });
        
        // 激活控制面板
        this.activateTab('/dashboard');
        this.loadContent('/dashboard');
    }

    // 修改loadContent函数，添加特殊处理客户管理页面的逻辑
    loadContent(url) {
        console.log('加载内容:', url);

        const contentArea = document.querySelector('.content-area');
        if (!contentArea) {
            console.error('Content area not found');
            return;
        }

        // 特殊处理控制面板
        if (url === '/dashboard') {
            console.log('加载控制面板内容');
            this.loadDashboardContent();
            return;
        }
        
        // 特殊处理客户管理页面
        const isClientPage = url === '/clients/' || url.startsWith('/clients');
        
        // 显示加载状态
        if (!isClientPage) {
            contentArea.innerHTML = `
                <div class="d-flex justify-content-center align-items-center" style="height: 300px;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            `;
        } else {
            console.log('客户管理页面，使用无加载指示器的占位');
            contentArea.innerHTML = `
                <div class="d-flex justify-content-center align-items-center" style="height: 300px;">
                    <div class="visually-hidden">加载客户管理页面...</div>
                </div>
            `;
        }

        // 发送AJAX请求
        fetch(url, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            console.log('响应状态:', response.status, response.statusText);
            if (!response.ok) {
                if (response.status === 403) {
                    throw new Error('权限不足，无法访问此页面');
                } else if (response.status === 404) {
                    throw new Error('页面不存在');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            }
            return response.text();
        })
        .then(html => {
            console.log('收到HTML响应，长度:', html.length);

            // 解析HTML并提取内容
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // 查找页面主要内容
            let content = null;

            // 尝试多种选择器来找到页面内容
            const selectors = [
                '#main-content .content-area',
                '#main-content',
                '.content-wrapper',
                '.container-fluid',
                '.container',
                'main .container-fluid',
                'main .container',
                'main',
                'body .container-fluid',
                'body .container',
                'body'
            ];

            for (const selector of selectors) {
                content = doc.querySelector(selector);
                if (content && content.innerHTML.trim()) {
                    console.log('找到内容，使用选择器:', selector, '内容长度:', content.innerHTML.length);
                    break;
                }
            }

            if (content) {
                // 清理不需要的元素
                const cleanContent = this.cleanContent(content.cloneNode(true));

                // 确保有内容区域
                const contentArea = document.querySelector('.content-area');
                if (contentArea) {
                    contentArea.innerHTML = cleanContent.innerHTML;
                    
                    // 特殊处理客户管理页面，确保加载指示器隐藏
                    if (isClientPage) {
                        console.log('客户管理页面加载完成，确保加载指示器隐藏');
                        this.hideClientPageLoadingIndicators();
                        
                        // 使用多次延迟尝试绑定客户管理页面的按钮事件
                        // 立即尝试绑定
                        this.tryBindClientPageEvents();
                        
                        // 100ms后再次尝试
                        setTimeout(() => this.tryBindClientPageEvents(), 100);
                        
                        // 300ms后再次尝试
                        setTimeout(() => this.tryBindClientPageEvents(), 300);
                        
                        // 500ms后再次尝试
                        setTimeout(() => this.tryBindClientPageEvents(), 500);
                        
                        // 1000ms后再次尝试
                        setTimeout(() => this.tryBindClientPageEvents(), 1000);
                    }
                } else {
                    console.error('Content area not found for content insertion');
                }
            } else {
                console.error('No content found in response');
                const contentArea = document.querySelector('.content-area');
                if (contentArea) {
                    contentArea.innerHTML = '<div class="alert alert-warning">无法加载页面内容</div>';
                }
            }

            // 重新初始化页面脚本
            this.initPageScripts();
        })
        .catch(error => {
            console.error('加载页面失败:', error);
            const contentArea = document.querySelector('.content-area');
            if (contentArea) {
                contentArea.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <h4 class="alert-heading">加载失败</h4>
                        <p>无法加载页面内容: ${error.message}</p>
                        <hr>
                        <p class="mb-0">
                            <button class="btn btn-outline-danger" onclick="tabManager.loadContent('${url}')">
                                <i class="fas fa-redo"></i> 重试
                            </button>
                        </p>
                    </div>
                `;
            }
        });
    }

    // 专门加载控制面板内容
    loadDashboardContent() {
        console.log('加载控制面板完整内容');

        const contentArea = document.querySelector('.content-area');
        if (!contentArea) {
            console.error('Content area not found');
            return;
        }

        // 显示加载状态
        contentArea.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 300px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载控制面板...</span>
                </div>
            </div>
        `;

        // 发送AJAX请求获取完整的控制面板内容
        fetch('/dashboard', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.text();
        })
        .then(html => {
            console.log('控制面板内容加载成功');

            // 解析HTML并提取内容
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // 查找控制面板的主要内容
            let content = null;
            const selectors = [
                '.content-area',
                '.container-fluid',
                'main .container-fluid',
                '#main-content',
                'main'
            ];

            for (const selector of selectors) {
                content = doc.querySelector(selector);
                if (content && content.innerHTML.trim()) {
                    console.log('找到控制面板内容，选择器:', selector);
                    break;
                }
            }

            if (content) {
                // 清理并插入内容
                const cleanContent = this.cleanContent(content.cloneNode(true));
                contentArea.innerHTML = cleanContent.innerHTML;

                // 确保菜单激活
                setTimeout(() => {
                    this.updateSidebarActiveMenu('/dashboard');
                    this.ensureDashboardMenuActive();
                }, 100);

                // 再次确保菜单激活（双重保险）
                setTimeout(() => {
                    this.ensureDashboardMenuActive();
                }, 500);

                console.log('控制面板内容插入完成');
            } else {
                console.error('未找到控制面板内容');
                contentArea.innerHTML = `
                    <div class="alert alert-warning">
                        <h4>内容加载问题</h4>
                        <p>无法找到控制面板内容，请刷新页面重试。</p>
                        <button class="btn btn-primary" onclick="window.location.reload()">刷新页面</button>
                    </div>
                `;
            }

            // 重新初始化页面脚本
            this.initPageScripts();
        })
        .catch(error => {
            console.error('控制面板加载失败:', error);
            contentArea.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-heading">控制面板加载失败</h4>
                    <p>无法加载控制面板内容: ${error.message}</p>
                    <hr>
                    <p class="mb-0">
                        <button class="btn btn-outline-danger" onclick="tabManager.loadDashboardContent()">
                            <i class="fas fa-redo"></i> 重试
                        </button>
                        <button class="btn btn-primary ms-2" onclick="window.location.reload()">
                            <i class="fas fa-refresh"></i> 刷新页面
                        </button>
                    </p>
                </div>
            `;
        });
    }

    cleanContent(content) {
        console.log('清理内容，原始内容长度:', content.innerHTML.length);

        // 只移除明确不需要的导航元素
        const elementsToRemove = [
            '.tab-navigation',
            '.sidebar',
            '.navbar',
            'nav.navbar',
            'header.navbar',
            'script'
        ];

        elementsToRemove.forEach(selector => {
            const elements = content.querySelectorAll(selector);
            elements.forEach(el => {
                console.log('移除元素:', selector);
                el.remove();
            });
        });

        console.log('清理后内容长度:', content.innerHTML.length);

        // 移除所有页面标题
        const titleElements = content.querySelectorAll('h1, h2, h3, .h3, .page-title');
        titleElements.forEach(el => el.remove());

        // 简化容器结构
        const containers = content.querySelectorAll('.container-fluid');
        containers.forEach(container => {
            // 如果容器只有一个子元素且也是容器，则展开
            if (container.children.length === 1) {
                const child = container.children[0];
                if (child.classList.contains('container-fluid') || child.classList.contains('row')) {
                    container.replaceWith(...child.children);
                }
            }
        });

        return content;
    }

    initPageScripts() {
        console.log('=== initPageScripts 被调用 ===');

        // 检查是否在客户管理页面
        const isClientPage = window.location.pathname === '/clients/' ||
                             window.location.pathname.startsWith('/clients');

        if (isClientPage) {
            console.log('检测到客户管理页面，开始初始化');

            // 隐藏加载指示器
            setTimeout(() => this.hideClientPageLoadingIndicators(), 0);
            setTimeout(() => this.hideClientPageLoadingIndicators(), 100);
            setTimeout(() => this.hideClientPageLoadingIndicators(), 500);

            // 执行AJAX加载的页面中的script标签
            this.executePageScripts();

            // 多次尝试绑定事件，确保函数已加载
            setTimeout(() => this.tryBindClientEvents(), 100);
            setTimeout(() => this.tryBindClientEvents(), 300);
            setTimeout(() => this.tryBindClientEvents(), 500);
            setTimeout(() => this.tryBindClientEvents(), 1000);

            // 添加强制的事件委托作为备用方案
            this.setupFallbackEventHandlers();
        }

        // 重新初始化Bootstrap组件
        if (typeof bootstrap !== 'undefined') {
            // 初始化工具提示
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // 初始化弹出框
            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        }
    }

    // 执行AJAX加载页面中的script标签
    executePageScripts() {
        console.log('执行页面中的script标签');

        const contentArea = document.querySelector('.content-area');
        if (!contentArea) {
            console.log('未找到content-area');
            return;
        }

        // 查找所有script标签
        const scripts = contentArea.querySelectorAll('script');
        console.log('找到script标签数量:', scripts.length);

        scripts.forEach((script, index) => {
            try {
                console.log(`准备执行script ${index}`);

                if (script.src) {
                    console.log(`跳过外部script: ${script.src}`);
                    return;
                }

                const scriptContent = script.textContent || script.innerHTML;
                if (!scriptContent.trim()) {
                    console.log(`跳过空script ${index}`);
                    return;
                }

                console.log(`执行内联script ${index}:`, scriptContent.substring(0, 100) + '...');

                // 使用eval执行脚本内容，这样可以在当前作用域中执行
                eval(scriptContent);

                console.log(`script ${index} 执行完成`);
            } catch (error) {
                console.error(`执行script ${index} 失败:`, error);
                console.error('错误详情:', error.message);
                console.error('错误堆栈:', error.stack);
            }
        });
    }

    // 尝试绑定客户管理页面事件
    tryBindClientEvents() {
        console.log('尝试绑定客户管理页面事件');

        // 检查必要的函数是否已加载
        const requiredFunctions = [
            'rebindAllButtonEvents',
            'toggleClientStatus',
            'toggleClientReview',
            'deleteClient',
            'showEditModal',
            'showSharesModal',
            'showUsageModal'
        ];

        const missingFunctions = requiredFunctions.filter(funcName => typeof window[funcName] !== 'function');

        if (missingFunctions.length > 0) {
            console.log('以下函数尚未加载:', missingFunctions);
            return false;
        }

        console.log('所有必要函数已加载，调用rebindAllButtonEvents');

        try {
            window.rebindAllButtonEvents();
            console.log('成功调用rebindAllButtonEvents');
            return true;
        } catch (error) {
            console.error('调用rebindAllButtonEvents失败:', error);
            return false;
        }

        try {
            // 重新绑定表单提交事件
            const forms = document.querySelectorAll('form:not([data-no-ajax])');
            forms.forEach(form => {
                if (typeof this.handleFormSubmit === 'function') {
                    form.removeEventListener('submit', this.handleFormSubmit);
                    form.addEventListener('submit', this.handleFormSubmit.bind(this));
                }
            });

            // 初始化AJAX链接
            const ajaxLinks = document.querySelectorAll('[data-ajax-link]');
            if (typeof this.handleAjaxLink === 'function') {
                ajaxLinks.forEach(link => {
                    link.removeEventListener('click', this.handleAjaxLink);
                    link.addEventListener('click', this.handleAjaxLink.bind(this));
                });
            }
        } catch (error) {
            console.error('绑定事件处理函数时出错:', error);
        }

        // 初始化其他页面脚本
        if (typeof initDynamicContent === 'function') {
            initDynamicContent();
        }
    }
    
    // 处理AJAX链接点击事件
    handleAjaxLink(e) {
        e.preventDefault();
        const link = e.currentTarget;
        const url = link.href;
        
        if (url) {
            console.log('AJAX链接被点击:', url);
            this.loadContent(new URL(url).pathname);
        }
    }

    handleFormSubmit(e) {
        e.preventDefault();
        const form = e.target;
        const formData = new FormData(form);
        const url = form.action || window.location.pathname;

        fetch(url, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            // 检查是否有重定向
            if (response.redirected) {
                console.log('表单提交后重定向到:', response.url);
                this.handleRedirect(new URL(response.url).pathname);
                return;
            }
            return response.text();
        })
        .then(html => {
            if (!html) return; // 如果是重定向，html为undefined

            // 处理表单响应
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const content = doc.querySelector('#main-content, .content-wrapper, main, .container-fluid');

            if (content) {
                document.getElementById('main-content').innerHTML = content.innerHTML;
                this.initPageScripts();

                // 同步标签页状态
                setTimeout(() => {
                    this.syncTabWithCurrentUrl();
                }, 100);
            }
        })
        .catch(error => {
            console.error('表单提交失败:', error);
        });
    }

    // 处理重定向
    handleRedirect(redirectUrl) {
        console.log('处理重定向:', redirectUrl);

        // 更新浏览器URL
        window.history.pushState({}, '', redirectUrl);

        // 同步标签页状态
        this.syncTabWithCurrentUrl();

        // 加载新内容
        this.loadContent(redirectUrl);
    }

    // 添加一个新方法，专门用于隐藏客户管理页面的加载指示器
    hideClientPageLoadingIndicators() {
        console.log('隐藏客户管理页面的所有加载指示器');
        
        // 隐藏可能存在的主页面加载指示器，但保留按钮和功能组件的加载状态
        const loadingIndicators = document.querySelectorAll('#loading-indicator, .content-area > .spinner-border, .content-area [role="status"]:not(button .spinner-border):not(button [role="status"])');
        loadingIndicators.forEach(indicator => {
            if (indicator) {
                indicator.style.display = 'none';
                indicator.style.opacity = '0';
                indicator.style.visibility = 'hidden';
                console.log('隐藏加载指示器:', indicator);
            }
        });
        
        // 添加CSS规则仅隐藏主页面加载指示器，不影响按钮内的加载状态
        const style = document.createElement('style');
        style.textContent = `
            #loading-indicator, 
            .content-area > .spinner-border, 
            .content-area > .d-flex > .spinner-border {
                display: none !important;
                opacity: 0 !important;
                visibility: hidden !important;
            }
            /* 确保按钮内的加载状态正常显示 */
            button .spinner-border, 
            .btn .spinner-border {
                display: inline-block !important;
                opacity: 1 !important;
                visibility: visible !important;
            }
        `;
        document.head.appendChild(style);
        
        // 移除可能存在的页面级加载中类，但保留按钮的加载状态
        document.querySelectorAll('.content-area.loading, .content-area .is-loading:not(button):not(.btn)').forEach(el => {
            el.classList.remove('loading', 'is-loading');
        });
    }

    // 尝试重新绑定客户管理页面的按钮事件
    tryBindClientPageEvents() {
        console.log('尝试重新绑定客户管理页面的按钮事件');
        
        // 首先尝试使用全局rebindAllButtonEvents函数
        if (typeof window.rebindAllButtonEvents === 'function') {
            try {
                window.rebindAllButtonEvents();
                console.log('成功调用全局rebindAllButtonEvents函数');
                return; // 如果成功调用了全局函数，就不需要继续执行下面的代码
            } catch (error) {
                console.error('调用全局rebindAllButtonEvents函数失败:', error);
            }
        } else {
            console.log('全局rebindAllButtonEvents函数不存在，尝试直接绑定事件');
        }
        
        // 如果全局函数不存在或调用失败，尝试直接绑定事件
        try {
            // 查找客户管理页面内容
            const clientPageContent = document.querySelector('.content-area');
            if (!clientPageContent) {
                console.error('找不到客户管理页面内容');
                return;
            }
            
            // 重新绑定状态切换按钮
            clientPageContent.querySelectorAll('.toggle-status-btn').forEach(btn => {
                const clientId = btn.getAttribute('data-client-id');
                const status = btn.getAttribute('data-status');
                if (clientId && status) {
                    btn.onclick = function() {
                        console.log('切换客户状态:', clientId, status);
                        if (typeof window.toggleClientStatus === 'function') {
                            window.toggleClientStatus(clientId, JSON.parse(status));
                        }
                    };
                    console.log('重新绑定状态切换按钮:', clientId);
                }
            });
            
            // 重新绑定审核状态切换按钮
            clientPageContent.querySelectorAll('.toggle-review-btn').forEach(btn => {
                const clientId = btn.getAttribute('data-client-id');
                const status = btn.getAttribute('data-status');
                if (clientId && status) {
                    btn.onclick = function() {
                        console.log('切换客户审核状态:', clientId, status);
                        if (typeof window.toggleClientReview === 'function') {
                            window.toggleClientReview(clientId, JSON.parse(status));
                        }
                    };
                    console.log('重新绑定审核状态切换按钮:', clientId);
                }
            });
            
            // 重新绑定编辑按钮
            clientPageContent.querySelectorAll('.btn-outline-primary[title="编辑"]').forEach(btn => {
                btn.onclick = function() {
                    const row = this.closest('tr');
                    if (row) {
                        const clientId = row.querySelector('td:first-child').textContent.trim();
                        console.log('编辑客户:', clientId);
                        if (typeof window.showEditModal === 'function') {
                            window.showEditModal(clientId);
                        }
                    }
                };
                console.log('重新绑定编辑按钮');
            });
            
            // 重新绑定分享链接按钮
            clientPageContent.querySelectorAll('.btn-outline-info[title="分享链接"]').forEach(btn => {
                btn.onclick = function() {
                    const row = this.closest('tr');
                    if (row) {
                        const clientId = row.querySelector('td:first-child').textContent.trim();
                        console.log('查看分享链接:', clientId);
                        if (typeof window.showSharesModal === 'function') {
                            window.showSharesModal(clientId);
                        }
                    }
                };
                console.log('重新绑定分享链接按钮');
            });
            
            // 重新绑定使用记录按钮
            clientPageContent.querySelectorAll('.btn-outline-secondary[title="使用记录"]').forEach(btn => {
                btn.onclick = function() {
                    const row = this.closest('tr');
                    if (row) {
                        const clientId = row.querySelector('td:first-child').textContent.trim();
                        console.log('查看使用记录:', clientId);
                        if (typeof window.showUsageModal === 'function') {
                            window.showUsageModal(clientId);
                        }
                    }
                };
                console.log('重新绑定使用记录按钮');
            });
            
            // 重新绑定删除按钮
            clientPageContent.querySelectorAll('.btn-outline-danger[title="删除"]').forEach(btn => {
                btn.onclick = function() {
                    const row = this.closest('tr');
                    if (row) {
                        const clientId = row.querySelector('td:first-child').textContent.trim();
                        const clientName = row.querySelector('td:nth-child(2)').textContent.trim();
                        console.log('删除客户:', clientId, clientName);
                        if (typeof window.deleteClient === 'function') {
                            window.deleteClient(clientId, clientName);
                        }
                    }
                };
                console.log('重新绑定删除按钮');
            });
            
            // 重新绑定新增客户按钮
            const submitCreateClientBtn = document.getElementById('submitCreateClientBtn');
            if (submitCreateClientBtn) {
                submitCreateClientBtn.onclick = function() {
                    console.log('提交新增客户表单');
                    const form = document.getElementById('createClientForm');
                    if (form && typeof window.submitCreateClientForm === 'function') {
                        window.submitCreateClientForm(form);
                    } else if (form) {
                        // 如果没有全局提交函数，使用默认提交逻辑
                        const formData = new FormData(form);
                        const createUrl = form.getAttribute('action') || '/clients/create';
                        
                        fetch(createUrl, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest',
                                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (data.success) {
                                // 关闭模态框
                                const modal = bootstrap.Modal.getInstance(document.getElementById('createClientModal'));
                                if (modal) modal.hide();
                                
                                // 刷新页面
                                window.location.reload();
                            } else {
                                alert('创建失败: ' + (data.message || '未知错误'));
                            }
                        })
                        .catch(error => {
                            console.error('提交失败:', error);
                            alert('提交失败，请重试: ' + error.message);
                        });
                    }
                };
                console.log('已绑定新增客户按钮事件');
            }
            
            console.log('直接绑定事件完成');
        } catch (error) {
            console.error('直接绑定事件失败:', error);
        }
    }

    // 设置备用事件处理器
    setupFallbackEventHandlers() {
        console.log('设置备用事件处理器');

        // 移除可能存在的旧监听器
        if (this.fallbackClickHandler) {
            document.removeEventListener('click', this.fallbackClickHandler);
        }

        // 创建新的监听器
        this.fallbackClickHandler = (e) => {
            // 只处理客户管理页面
            if (!window.location.pathname.includes('/clients')) {
                return;
            }

            console.log('备用事件处理器被触发:', e.target);

            const button = e.target.closest('button');
            if (!button) return;

            // 状态切换按钮
            if (button.classList.contains('toggle-status-btn')) {
                e.preventDefault();
                const clientId = button.getAttribute('data-client-id');
                const status = JSON.parse(button.getAttribute('data-status') || 'false');

                if (clientId) {
                    console.log('备用处理器：切换客户状态', clientId, status);
                    this.fallbackToggleClientStatus(clientId, status);
                }
                return;
            }

            // 审核状态切换按钮
            if (button.classList.contains('toggle-review-btn')) {
                e.preventDefault();
                const clientId = button.getAttribute('data-client-id');
                const status = JSON.parse(button.getAttribute('data-status') || 'false');

                if (clientId) {
                    console.log('备用处理器：切换审核状态', clientId, status);
                    this.fallbackToggleClientReview(clientId, status);
                }
                return;
            }

            // 编辑按钮
            if (button.classList.contains('btn-outline-primary') && button.getAttribute('title') === '编辑') {
                e.preventDefault();
                const row = button.closest('tr');
                if (row) {
                    const clientId = row.querySelector('td:first-child').textContent.trim();
                    console.log('备用处理器：编辑客户', clientId);
                    this.fallbackShowEditModal(clientId);
                }
                return;
            }

            // 删除按钮
            if (button.classList.contains('btn-outline-danger') && button.getAttribute('title') === '删除') {
                e.preventDefault();
                const row = button.closest('tr');
                if (row) {
                    const clientId = row.querySelector('td:first-child').textContent.trim();
                    const clientName = row.querySelector('td:nth-child(2)').textContent.trim();
                    console.log('备用处理器：删除客户', clientId, clientName);
                    this.fallbackDeleteClient(clientId, clientName);
                }
                return;
            }
        };

        document.addEventListener('click', this.fallbackClickHandler);
        console.log('备用事件处理器已设置');
    }

    // 备用的客户状态切换函数
    fallbackToggleClientStatus(clientId, currentStatus) {
        console.log('备用函数：切换客户状态', clientId, currentStatus);

        fetch(`/clients/${clientId}/toggle_status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 刷新页面内容
                this.loadContent(window.location.pathname);
                this.showToast(`客户状态已${data.status ? '启用' : '禁用'}`);
            } else {
                this.showToast('操作失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('请求失败:', error);
            this.showToast('操作失败，请重试', 'danger');
        });
    }

    // 备用的审核状态切换函数
    fallbackToggleClientReview(clientId, currentStatus) {
        console.log('备用函数：切换审核状态', clientId, currentStatus);

        fetch(`/clients/${clientId}/toggle_review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 刷新页面内容
                this.loadContent(window.location.pathname);
                this.showToast(`客户审核状态已${data.need_review ? '开启' : '关闭'}`);
            } else {
                this.showToast('操作失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('请求失败:', error);
            this.showToast('操作失败，请重试', 'danger');
        });
    }

    // 备用的编辑模态框函数
    fallbackShowEditModal(clientId) {
        console.log('备用函数：显示编辑模态框', clientId);
        // 简单的重定向到编辑页面
        window.location.href = `/clients/${clientId}/edit`;
    }

    // 备用的删除客户函数
    fallbackDeleteClient(clientId, clientName) {
        console.log('备用函数：删除客户', clientId, clientName);

        if (confirm(`确定要删除客户 "${clientName}" 吗？此操作不可恢复。`)) {
            fetch(`/clients/${clientId}/delete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 刷新页面内容
                    this.loadContent(window.location.pathname);
                    this.showToast('客户删除成功');
                } else {
                    this.showToast('删除失败: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
                this.showToast('删除失败，请重试', 'danger');
            });
        }
    }

    // 显示Toast提示
    showToast(message, type = 'success') {
        // 尝试使用全局的showToast函数
        if (typeof window.showToast === 'function') {
            window.showToast(message, type);
            return;
        }

        // 如果没有全局函数，创建简单的提示
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'danger' ? 'danger' : 'success'} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.textContent = message;

        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
}

// 全局标签管理器实例
let tabManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，初始化标签管理器');
    tabManager = new TabManager();
    
    // 确保在页面完全加载后同步标签状态
    setTimeout(() => {
        if (tabManager) {
            console.log('页面完全加载，确保标签状态同步');
            tabManager.syncTabWithCurrentUrl();
            
            // 特殊处理客户管理页面
            const currentUrl = window.location.pathname;
            if (currentUrl === '/clients/' || currentUrl.startsWith('/clients')) {
                console.log('检测到客户管理页面，DOMContentLoaded后特殊处理');
                tabManager.hideClientPageLoadingIndicators();
                tabManager.tryBindClientPageEvents();
            }
        }
    }, 300);
});

// 添加页面加载状态检测
window.addEventListener('load', function() {
    console.log('页面资源完全加载完成');
    if (tabManager) {
        // 再次同步标签状态
        tabManager.syncTabWithCurrentUrl();
        
        // 特殊处理客户管理页面
        const currentUrl = window.location.pathname;
        if (currentUrl === '/clients/' || currentUrl.startsWith('/clients')) {
            console.log('检测到客户管理页面，window.load后特殊处理');
            tabManager.hideClientPageLoadingIndicators();
            tabManager.tryBindClientPageEvents();
            
            // 多次尝试绑定事件，确保页面完全加载后按钮可用
            setTimeout(() => tabManager.tryBindClientPageEvents(), 300);
            setTimeout(() => tabManager.tryBindClientPageEvents(), 600);
            setTimeout(() => tabManager.tryBindClientPageEvents(), 1000);
        }
    }
});

// 全局函数供HTML调用
function closeTab(url) {
    if (tabManager) {
        tabManager.closeTab(url);
    }
}

function closeAllTabs() {
    if (tabManager) {
        tabManager.closeAllTabs();
    }
}
