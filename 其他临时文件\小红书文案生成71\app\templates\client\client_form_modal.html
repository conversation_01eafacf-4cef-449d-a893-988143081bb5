<!-- 客户表单（用于模态框） -->
<form method="post" id="client-form" data-ajax-form
      {% if edit_mode %}data-edit-mode="true" data-client-id="{{ client.id }}"{% endif %}>
    {{ form.csrf_token }}
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                {{ form.name.label(class="form-label") }}
                {{ form.name(class="form-control", placeholder="请输入客户名称") }}
                {% if form.name.errors %}
                    {% for error in form.name.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                {{ form.contact.label(class="form-label") }}
                {{ form.contact(class="form-control", placeholder="请输入联系人") }}
                {% if form.contact.errors %}
                    {% for error in form.contact.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                {{ form.phone.label(class="form-label") }}
                {{ form.phone(class="form-control", placeholder="请输入联系电话") }}
                {% if form.phone.errors %}
                    {% for error in form.phone.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                {{ form.email.label(class="form-label") }}
                {{ form.email(class="form-control", placeholder="请输入电子邮箱") }}
                {% if form.email.errors %}
                    {% for error in form.email.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                {{ form.need_review.label(class="form-label") }}
                <div class="form-check form-switch">
                    {{ form.need_review(class="form-check-input", id="need_review_switch") }}
                    <label class="form-check-label" for="need_review_switch">
                        <span id="need_review_text">{{ '需要审核' if form.need_review.data else '无需审核' }}</span>
                    </label>
                </div>
                {% if form.need_review.errors %}
                    {% for error in form.need_review.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                {{ form.status.label(class="form-label") }}
                <div class="form-check form-switch">
                    {{ form.status(class="form-check-input", id="status_switch") }}
                    <label class="form-check-label" for="status_switch">
                        <span id="status_text">{{ '启用' if form.status.data else '禁用' }}</span>
                    </label>
                </div>
                {% if form.status.errors %}
                    {% for error in form.status.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-4">
            <div class="mb-3">
                {{ form.daily_content_count.label(class="form-label") }}
                {{ form.daily_content_count(class="form-control", min="1", max="100") }}
                {% if form.daily_content_count.errors %}
                    {% for error in form.daily_content_count.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                {{ form.interval_min.label(class="form-label") }}
                {{ form.interval_min(class="form-control", min="1", max="1440") }}
                {% if form.interval_min.errors %}
                    {% for error in form.interval_min.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                {{ form.interval_max.label(class="form-label") }}
                {{ form.interval_max(class="form-control", min="1", max="1440") }}
                {% if form.interval_max.errors %}
                    {% for error in form.interval_max.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                {{ form.display_start_time.label(class="form-label") }}
                {{ form.display_start_time(class="form-control") }}
                {% if form.display_start_time.errors %}
                    {% for error in form.display_start_time.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                {{ form.address.label(class="form-label") }}
                {{ form.address(class="form-control", placeholder="请输入地址") }}
                {% if form.address.errors %}
                    {% for error in form.address.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="mb-3">
        {{ form.remark.label(class="form-label") }}
        {{ form.remark(class="form-control", rows="3", placeholder="请输入备注信息") }}
        {% if form.remark.errors %}
            {% for error in form.remark.errors %}
            <div class="invalid-feedback d-block">{{ error }}</div>
            {% endfor %}
        {% endif %}
    </div>
    
    <div class="d-flex justify-content-between">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle"></i> 取消
        </button>
        <button type="button" class="btn btn-primary" id="submit-btn">
            <i class="bi bi-check-circle"></i> {% if edit_mode %}更新{% else %}保存{% endif %}
        </button>
    </div>
</form>

<!-- JavaScript已移至主页面处理，因为动态加载的script标签不会执行 -->
