{% extends "base.html" %}

{% block title %}文案详情{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="card mb-4">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h3 class="mb-0">文案详情</h3>
                <div>
                    <a href="{{ url_for('content.content_list') }}" class="btn btn-sm btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                    {% if current_user.has_permission('content_edit') and content.workflow_status not in ['pending_client_review', 'client_approved', 'published'] %}
                    <a href="{{ url_for('content.content_edit', content_id=content.id) }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-edit"></i> 编辑文案
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <div class="content-header mb-4">
                        <h2 class="mb-3" id="formatted-title">{{ content.title }}</h2>
                        <div id="original-title" style="display: none;">{{ content.title }}</div>

                        <!-- 标签化信息显示 -->
                        <div class="content-tags mb-4">
                            <div class="row g-3">
                                <!-- 话题标签 -->
                                {% if content.topics_list %}
                                <div class="col-12">
                                    <div class="d-flex align-items-center flex-wrap gap-2">
                                        <span class="fw-bold text-muted me-2">
                                            <i class="fas fa-hashtag"></i> 话题：
                                        </span>
                                        {% for topic in content.topics_list %}
                                        <span class="badge bg-primary fs-6 px-3 py-2">
                                            <i class="fas fa-hashtag me-1"></i>{{ topic }}
                                        </span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                <!-- 定位标签 -->
                                {% if content.location %}
                                <div class="col-12">
                                    <div class="d-flex align-items-center flex-wrap gap-2">
                                        <span class="fw-bold text-muted me-2">
                                            <i class="fas fa-map-marker-alt"></i> 定位：
                                        </span>
                                        <span class="badge bg-success px-3 py-2 me-2 mb-2">
                                            <i class="fas fa-map-marker-alt me-1"></i>{{ content.location }}
                                        </span>
                                    </div>
                                </div>
                                {% endif %}

                                <!-- @用户标签 -->
                                {% if content.ext_data and content.ext_data.get('at_users') %}
                                <div class="col-12">
                                    <div class="d-flex align-items-center flex-wrap gap-2">
                                        <span class="fw-bold text-muted me-2">
                                            <i class="fas fa-at"></i> @用户：
                                        </span>
                                        {% for user in content.ext_data.get('at_users', []) %}
                                        <span class="badge bg-warning text-dark px-3 py-2 me-2 mb-2">
                                            <i class="fas fa-user me-1"></i>{{ user if user.startswith('@') else '@' + user }}
                                        </span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                <!-- 从文案内容中提取的@用户 -->
                                <div class="col-12" id="at-users-container">
                                    <!-- 这里将通过JavaScript动态添加从内容中提取的@用户标签 -->
                                </div>

                                <!-- 关键词标签 -->
                                <div class="col-12" id="keywords-container">
                                    <!-- 这里将通过JavaScript动态添加关键词标签 -->
                                </div>
                            </div>
                        </div>

                        <!-- 发布优先级 -->
                        <div class="mb-4">
                            <div class="d-flex align-items-center gap-2">
                                <span class="fw-bold text-muted">
                                    <i class="fas fa-flag"></i> 发布优先级：
                                </span>
                                {% if content.publish_priority == 'high' %}
                                <span class="badge bg-danger">优先级高</span>
                                {% elif content.publish_priority == 'normal' %}
                                <span class="badge bg-primary">优先级中</span>
                                {% else %}
                                <span class="badge bg-secondary">优先级低</span>
                                {% endif %}
                            </div>
                        </div>

                        <div class="content-meta text-muted small mb-3">
                            <div><strong>客户：</strong>{{ content.client.name }}</div>
                            <div><strong>任务：</strong>{{ content.task.name }}</div>
                            {% if content.template %}
                            <div><strong>使用模板：</strong>{{ content.template.title }}</div>
                            {% endif %}
                            <div><strong>创建时间：</strong>{{ content.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                            <div><strong>更新时间：</strong>{{ content.updated_at.strftime('%Y-%m-%d %H:%M') }}</div>
                            {% if content.display_date %}
                            <div><strong>展示日期：</strong>{{ content.display_date.strftime('%Y-%m-%d') }}
                                {% if content.display_time %}{{ content.display_time.strftime('%H:%M') }}{% endif %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="content-body mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">文案内容</h5>
                            </div>
                            <div class="card-body">
                                <div class="content-html" id="formatted-content">
                                    <!-- 原始内容（隐藏） -->
                                    <div id="original-content" style="display: none;">{{ content.content|safe }}</div>
                                    <!-- 格式化后的内容将在这里显示 -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {% if content.image_urls_list %}
                    <div class="content-images mb-4">
                        <h5 class="mb-3">文案图片</h5>
                        <div class="row">
                            {% for image_url in content.image_urls_list %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <a href="{{ image_url }}" target="_blank" class="d-block">
                                    <img src="{{ image_url }}" class="img-fluid rounded" alt="文案图片">
                                </a>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if history_records %}
                    <div class="content-history mb-4">
                        <h5 class="mb-3">编辑历史</h5>
                        <div class="accordion" id="historyAccordion">
                            {% for history in history_records %}
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading{{ history.id }}">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ history.id }}" aria-expanded="false" aria-controls="collapse{{ history.id }}">
                                        {{ history.edit_time.strftime('%Y-%m-%d %H:%M') }} - 
                                        {% if history.is_client_edit %}
                                        <span class="badge bg-info ms-2">客户编辑</span>
                                        {% else %}
                                        <span class="badge bg-secondary ms-2">{{ history.editor.username if history.editor else '未知' }}</span>
                                        {% endif %}
                                    </button>
                                </h2>
                                <div id="collapse{{ history.id }}" class="accordion-collapse collapse" aria-labelledby="heading{{ history.id }}" data-bs-parent="#historyAccordion">
                                    <div class="accordion-body">
                                        <h6>{{ history.title }}</h6>
                                        <div class="content-html mt-3">
                                            {{ history.content|safe }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
                
                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">文案状态</h5>
                        </div>
                        <div class="card-body">
                            {% set status_map = {
                                'draft': ('草稿', 'secondary'),
                                'pending_review': ('待初审', 'warning'),
                                'first_reviewed': ('初审通过', 'info'),
                                'pending_image': ('待上传图片', 'warning'),
                                'image_uploaded': ('图片已上传', 'info'),
                                'pending_final_review': ('待最终审核', 'warning'),
                                'pending_client_review': ('待客户审核', 'primary'),
                                'client_rejected': ('客户已拒绝', 'danger'),
                                'client_approved': ('客户已通过', 'success'),
                                'pending_publish': ('待发布', 'info'),
                                'published': ('已发布', 'success')
                            } %}
                            {% set status_text, status_class = status_map.get(content.workflow_status, ('未知', 'secondary')) %}
                            
                            <div class="d-flex justify-content-center mb-4">
                                <div class="text-center">
                                    <div class="display-6 mb-2">
                                        <span class="badge bg-{{ status_class }} p-3">{{ status_text }}</span>
                                    </div>
                                    {% if content.reviewer %}
                                    <div class="text-muted small">
                                        审核人：{{ content.reviewer.username }}
                                        {% if content.review_time %}
                                        <br>审核时间：{{ content.review_time.strftime('%Y-%m-%d %H:%M') }}
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="workflow-actions">
                                {% if content.workflow_status in ['draft', 'image_uploaded'] and current_user.has_permission('content_edit') %}
                                <form method="post" action="{{ url_for('content.content_submit', content_id=content.id) }}" class="mb-2">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-paper-plane"></i> 提交审核
                                    </button>
                                </form>
                                {% endif %}
                                
                                {% if content.workflow_status not in ['pending_client_review', 'client_approved', 'pending_publish', 'published'] and current_user.has_permission('content_delete') %}
                                <div class="d-flex gap-2 mb-2">
                                    <button type="button" class="btn btn-danger flex-grow-1" onclick="deleteContent({{ content.id }}, false)">
                                        <i class="fas fa-trash"></i> 删除文案
                                    </button>
                                    <button type="button" class="btn btn-warning flex-grow-1" onclick="deleteContent({{ content.id }}, true)">
                                        <i class="fas fa-sync"></i> 删除并补充
                                    </button>
                                </div>
                                {% endif %}
                                
                                {% if content.workflow_status == 'pending_image' and current_user.has_permission('content_edit') %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i> 请先上传图片后再提交审核
                                </div>
                                {% endif %}
                                
                                {% if content.workflow_status in ['pending_review', 'pending_final_review'] and current_user.has_permission('content_review') %}
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">审核操作</h6>
                                    </div>
                                    <div class="card-body">
                                        <form method="post" action="{{ url_for('content.content_review', content_id=content.id) }}">
                                            {{ review_form.csrf_token }}
                                            {{ review_form.content_id }}
                                            
                                            <div class="mb-3">
                                                <button type="submit" class="btn btn-success w-100" onclick="document.getElementById('action').value='approve';">
                                                    <i class="fas fa-check"></i> 审核通过
                                                </button>
                                            </div>
                                            
                                            <hr>
                                            
                                            <div class="mb-3">
                                                {{ review_form.quick_reason_id.label(class="form-label") }}
                                                {{ review_form.quick_reason_id(class="form-select") }}
                                            </div>
                                            
                                            <div class="mb-3">
                                                {{ review_form.reason.label(class="form-label") }}
                                                {{ review_form.reason(class="form-control", rows=3) }}
                                            </div>
                                            
                                            <div class="mb-3">
                                                <button type="submit" class="btn btn-danger w-100" onclick="document.getElementById('action').value='reject';">
                                                    <i class="fas fa-times"></i> 拒绝并退回
                                                </button>
                                            </div>
                                            
                                            {{ review_form.action(id="action") }}
                                        </form>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    {% if rejection_reasons %}
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">拒绝记录</h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                {% for reason in rejection_reasons %}
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span class="badge bg-{{ 'info' if reason.is_client else 'danger' }}">
                                            {{ '客户拒绝' if reason.is_client else '内部拒绝' }}
                                        </span>
                                        <small class="text-muted">{{ reason.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                    </div>
                                    <div class="mb-1">{{ reason.reason|nl2br }}</div>
                                    {% if not reason.is_client and reason.creator %}
                                    <div class="text-end">
                                        <small class="text-muted">审核人：{{ reason.creator.username }}</small>
                                    </div>
                                    {% endif %}
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<style>
    /* 内容格式化样式 */
    .formatted-content {
        line-height: 1.8;
        font-size: 16px;
    }

    .formatted-content p {
        margin-bottom: 1rem;
    }

    /* 关键词标签样式 */
    .keyword-tag {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.9em;
        font-weight: 500;
        display: inline-block;
        margin: 0 2px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* 标记替换标签样式 */
    .mark-replacement-tag {
        background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
        color: #d32f2f;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.9em;
        font-weight: 500;
        display: inline-block;
        margin: 0 2px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        cursor: help;
        position: relative;
    }

    .mark-replacement-tag:hover {
        background: linear-gradient(135deg, #ff8a8e 0%, #f8c0b4 100%);
    }

    /* 标记替换标签的提示效果 */
    .mark-replacement-tag::after {
        content: attr(title);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background-color: #333;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s;
        z-index: 1000;
    }

    .mark-replacement-tag:hover::after {
        opacity: 1;
        visibility: visible;
    }

    /* 标题中的标签样式调整 */
    h2 .mark-replacement-tag {
        font-size: 0.8em;
        padding: 3px 10px;
        vertical-align: middle;
    }

    h2 .at-user-tag {
        font-size: 0.8em;
        padding: 3px 10px;
        vertical-align: middle;
    }

    /* @用户标签样式 */
    .at-user-tag {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.9em;
        font-weight: 500;
        display: inline-block;
        margin: 0 2px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* 标签区域样式 */
    .content-tags .badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
    }

    /* 换行保持 */
    .formatted-content {
        white-space: pre-wrap;
        word-wrap: break-word;
    }
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 页面加载完成后格式化内容
    document.addEventListener('DOMContentLoaded', function() {
        formatContent();
        formatTitle();
        extractAndDisplayTags();
    });

    // 格式化文案内容
    function formatContent() {
        const originalContent = document.getElementById('original-content');
        const formattedContainer = document.getElementById('formatted-content');

        if (!originalContent || !formattedContainer) return;

        let content = originalContent.textContent || originalContent.innerText || '';

        // 获取标记替换信息
        let markReplacements = {};
        {% if content.ext_data %}
            {% if content.ext_data.get('mark_replacements') %}
                markReplacements = {{ content.ext_data.get('mark_replacements', {}) | tojson | safe }};
            {% endif %}
        {% endif %}
        console.log('标记替换信息:', markReplacements);

        // 移除HTML标签，保留纯文本
        content = content.replace(/<[^>]*>/g, '');

        // 处理换行
        content = content.replace(/\n/g, '<br>');

        // 高亮显示替换的标记内容
        if (markReplacements && Object.keys(markReplacements).length > 0) {
            for (const [markName, replacement] of Object.entries(markReplacements)) {
                // 创建正则表达式来匹配替换后的内容
                const regex = new RegExp(escapeRegExp(replacement), 'g');
                content = content.replace(regex, `<span class="mark-replacement-tag" data-mark="${markName}" title="标记: {${markName}}">${replacement}</span>`);
            }
        }

        // 识别并标记关键词（假设关键词是被特殊符号包围的，如【关键词】）
        content = content.replace(/【([^】]+)】/g, '<span class="keyword-tag">$1</span>');

        // 识别并标记@用户
        content = content.replace(/@([^\s@]+)/g, '<span class="at-user-tag">@$1</span>');

        // 设置格式化后的内容
        formattedContainer.innerHTML = `<div class="formatted-content">${content}</div>`;
    }

    // 格式化标题
    function formatTitle() {
        const originalTitle = document.getElementById('original-title');
        const formattedTitle = document.getElementById('formatted-title');

        if (!originalTitle || !formattedTitle) return;

        let title = originalTitle.textContent || originalTitle.innerText || '';

        // 获取标记替换信息
        let markReplacements = {};
        {% if content.ext_data %}
            {% if content.ext_data.get('mark_replacements') %}
                markReplacements = {{ content.ext_data.get('mark_replacements', {}) | tojson | safe }};
            {% endif %}
        {% endif %}
        console.log('标题标记替换信息:', markReplacements);

        // 高亮显示替换的标记内容
        if (markReplacements && Object.keys(markReplacements).length > 0) {
            for (const [markName, replacement] of Object.entries(markReplacements)) {
                // 创建正则表达式来匹配替换后的内容
                const regex = new RegExp(escapeRegExp(replacement), 'g');
                title = title.replace(regex, `<span class="mark-replacement-tag" data-mark="${markName}" title="标记: {${markName}}">${replacement}</span>`);
            }
        }

        // 识别并标记@用户
        title = title.replace(/@([^\s@]+)/g, '<span class="at-user-tag">@$1</span>');

        // 设置格式化后的标题
        formattedTitle.innerHTML = title;
    }

    // 转义正则表达式特殊字符
    function escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    // 提取并显示标签
    function extractAndDisplayTags() {
        const originalContent = document.getElementById('original-content');
        if (!originalContent) return;

        let content = originalContent.textContent || originalContent.innerText || '';

        // 移除HTML标签，保留纯文本
        content = content.replace(/<[^>]*>/g, '');

        console.log('提取标签的内容:', content);

        // 提取@用户 - 改进正则表达式
        const atUsers = content.match(/@[^\s@，。！？\n\r]+/g);
        console.log('提取到的@用户:', atUsers);
        if (atUsers && atUsers.length > 0) {
            displayAtUsers(atUsers);
        }

        // 提取关键词 - 支持多种括号格式
        const keywords = content.match(/【([^】]+)】/g) || content.match(/\[([^\]]+)\]/g) || content.match(/\{([^}]+)\}/g);
        console.log('提取到的关键词:', keywords);
        if (keywords && keywords.length > 0) {
            displayKeywords(keywords);
        }
    }

    // 显示@用户标签
    function displayAtUsers(atUsers) {
        const container = document.getElementById('at-users-container');
        if (!container) return;

        const uniqueUsers = [...new Set(atUsers)]; // 去重

        let html = `
            <div class="d-flex align-items-center flex-wrap gap-2">
                <span class="fw-bold text-muted me-2">
                    <i class="fas fa-at"></i> @用户：
                </span>
        `;

        uniqueUsers.forEach(user => {
            if (user.trim()) { // 确保用户名不为空
                html += `
                    <span class="badge bg-warning text-dark px-3 py-2 me-2 mb-2">
                        <i class="fas fa-user me-1"></i>${user.trim()}
                    </span>
                `;
            }
        });

        html += '</div>';
        container.innerHTML = html;
    }

    // 显示关键词标签
    function displayKeywords(keywords) {
        const container = document.getElementById('keywords-container');
        if (!container) return;

        // 移除各种括号并去重
        const uniqueKeywords = [...new Set(keywords.map(k => k.replace(/【|】|\[|\]|\{|\}/g, '')))];

        let html = `
            <div class="d-flex align-items-center flex-wrap gap-2">
                <span class="fw-bold text-muted me-2">
                    <i class="fas fa-tags"></i> 关键词：
                </span>
        `;

        uniqueKeywords.forEach(keyword => {
            if (keyword.trim()) { // 确保关键词不为空
                html += `
                    <span class="badge bg-info px-3 py-2 me-2 mb-2">
                        <i class="fas fa-tag me-1"></i>${keyword.trim()}
                    </span>
                `;
            }
        });

        html += '</div>';
        container.innerHTML = html;
    }

    function deleteContent(contentId, autoSupplement) {
        if (confirm('确定要删除这篇文案吗？' + (autoSupplement ? '删除后将自动从未分配文案池中补充一篇新文案。' : ''))) {
            // 创建表单数据
            const formData = new FormData();
            formData.append('csrf_token', '{{ csrf_token() }}');
            formData.append('auto_supplement', autoSupplement ? 'true' : 'false');
            
            // 发送AJAX请求
            fetch(`/contents/${contentId}/delete`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    // 跳转到文案列表页
                    window.location.href = '{{ url_for("content.content_list") }}';
                } else {
                    alert('操作失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败，请重试');
            });
        }
    }
</script>
{% endblock %} 