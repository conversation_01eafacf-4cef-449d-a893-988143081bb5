/* 简化版Bootstrap JavaScript - 只包含必要的功能 */

// 基本的工具函数
(function() {
    'use strict';
    
    // 简单的事件处理
    function on(element, event, handler) {
        if (element.addEventListener) {
            element.addEventListener(event, handler, false);
        } else if (element.attachEvent) {
            element.attachEvent('on' + event, handler);
        }
    }
    
    // 简单的元素查找
    function find(selector, context) {
        context = context || document;
        return context.querySelectorAll(selector);
    }
    
    function findOne(selector, context) {
        context = context || document;
        return context.querySelector(selector);
    }
    
    // 简单的类操作
    function addClass(element, className) {
        if (element.classList) {
            element.classList.add(className);
        } else {
            element.className += ' ' + className;
        }
    }
    
    function removeClass(element, className) {
        if (element.classList) {
            element.classList.remove(className);
        } else {
            element.className = element.className.replace(new RegExp('(^|\\b)' + className.split(' ').join('|') + '(\\b|$)', 'gi'), ' ');
        }
    }
    
    function hasClass(element, className) {
        if (element.classList) {
            return element.classList.contains(className);
        } else {
            return new RegExp('(^| )' + className + '( |$)', 'gi').test(element.className);
        }
    }
    
    // 简单的下拉菜单功能
    function initDropdowns() {
        var dropdowns = find('[data-bs-toggle="dropdown"]');
        
        for (var i = 0; i < dropdowns.length; i++) {
            (function(dropdown) {
                on(dropdown, 'click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    var menu = dropdown.nextElementSibling;
                    if (menu && hasClass(menu, 'dropdown-menu')) {
                        if (hasClass(menu, 'show')) {
                            removeClass(menu, 'show');
                        } else {
                            // 关闭其他下拉菜单
                            var allMenus = find('.dropdown-menu.show');
                            for (var j = 0; j < allMenus.length; j++) {
                                removeClass(allMenus[j], 'show');
                            }
                            addClass(menu, 'show');
                        }
                    }
                });
            })(dropdowns[i]);
        }
        
        // 点击外部关闭下拉菜单
        on(document, 'click', function() {
            var allMenus = find('.dropdown-menu.show');
            for (var i = 0; i < allMenus.length; i++) {
                removeClass(allMenus[i], 'show');
            }
        });
    }
    
    // 简单的模态框功能
    function initModals() {
        var modalTriggers = find('[data-bs-toggle="modal"]');
        
        for (var i = 0; i < modalTriggers.length; i++) {
            (function(trigger) {
                on(trigger, 'click', function(e) {
                    e.preventDefault();
                    var target = trigger.getAttribute('data-bs-target');
                    var modal = findOne(target);
                    
                    if (modal) {
                        addClass(modal, 'show');
                        modal.style.display = 'block';
                        addClass(document.body, 'modal-open');
                    }
                });
            })(modalTriggers[i]);
        }
        
        // 关闭模态框
        var closeButtons = find('[data-bs-dismiss="modal"]');
        for (var i = 0; i < closeButtons.length; i++) {
            (function(button) {
                on(button, 'click', function() {
                    var modal = button.closest('.modal');
                    if (modal) {
                        removeClass(modal, 'show');
                        modal.style.display = 'none';
                        removeClass(document.body, 'modal-open');
                    }
                });
            })(closeButtons[i]);
        }
    }
    
    // 简单的折叠功能
    function initCollapse() {
        var collapseTriggers = find('[data-bs-toggle="collapse"]');
        
        for (var i = 0; i < collapseTriggers.length; i++) {
            (function(trigger) {
                on(trigger, 'click', function(e) {
                    e.preventDefault();
                    var target = trigger.getAttribute('data-bs-target') || trigger.getAttribute('href');
                    var collapse = findOne(target);
                    
                    if (collapse) {
                        if (hasClass(collapse, 'show')) {
                            removeClass(collapse, 'show');
                            collapse.style.height = '0px';
                        } else {
                            addClass(collapse, 'show');
                            collapse.style.height = collapse.scrollHeight + 'px';
                        }
                    }
                });
            })(collapseTriggers[i]);
        }
    }
    
    // 简单的提示框功能
    function initTooltips() {
        var tooltips = find('[data-bs-toggle="tooltip"]');
        
        for (var i = 0; i < tooltips.length; i++) {
            (function(element) {
                var tooltip = null;
                
                on(element, 'mouseenter', function() {
                    var title = element.getAttribute('title') || element.getAttribute('data-bs-original-title');
                    if (title) {
                        tooltip = document.createElement('div');
                        tooltip.className = 'tooltip';
                        tooltip.innerHTML = title;
                        tooltip.style.position = 'absolute';
                        tooltip.style.background = '#000';
                        tooltip.style.color = '#fff';
                        tooltip.style.padding = '5px 10px';
                        tooltip.style.borderRadius = '4px';
                        tooltip.style.fontSize = '12px';
                        tooltip.style.zIndex = '1000';
                        
                        document.body.appendChild(tooltip);
                        
                        var rect = element.getBoundingClientRect();
                        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
                        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
                    }
                });
                
                on(element, 'mouseleave', function() {
                    if (tooltip) {
                        document.body.removeChild(tooltip);
                        tooltip = null;
                    }
                });
            })(tooltips[i]);
        }
    }
    
    // 初始化所有组件
    function init() {
        initDropdowns();
        initModals();
        initCollapse();
        initTooltips();
    }
    
    // DOM加载完成后初始化
    if (document.readyState === 'loading') {
        on(document, 'DOMContentLoaded', init);
    } else {
        init();
    }
    
    // 导出到全局
    window.Bootstrap = {
        init: init,
        addClass: addClass,
        removeClass: removeClass,
        hasClass: hasClass,
        find: find,
        findOne: findOne,
        on: on
    };
    
})();
