{% extends "base.html" %}

{% block title %}用户统计 - 小红书文案生成系统{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
<style>
    .chart-container {
        height: 300px;
    }
</style>
{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">用户统计</h2>
        <div class="btn-group">
            <a href="{{ url_for('stats.index') }}" class="btn btn-outline-primary">
                <i class="bi bi-bar-chart"></i> 统计概览
            </a>
            <a href="{{ url_for('stats.content_stats') }}" class="btn btn-outline-primary">
                <i class="bi bi-file-text"></i> 文案统计
            </a>
            <a href="{{ url_for('stats.task_stats') }}" class="btn btn-outline-primary">
                <i class="bi bi-list-task"></i> 任务统计
            </a>
        </div>
    </div>

    <!-- 时间范围选择 -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">时间范围选择</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">开始日期</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">结束日期</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary w-100">查询</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 图表 -->
    <div class="row">
        <!-- 用户文案创建统计 -->
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">用户文案创建统计</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="userContentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户审核统计 -->
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">用户审核统计</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="userReviewChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户创建文案统计 -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">用户创建文案排行</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>用户名</th>
                                    <th>文案数量</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in user_content_stats %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ user.username }}</td>
                                    <td>{{ user.count }}</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="3" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户审核文案统计 -->
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">用户审核文案排行</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>用户名</th>
                                    <th>审核数量</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in user_review_stats %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ user.username }}</td>
                                    <td>{{ user.count }}</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="3" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户创建任务统计 -->
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">用户创建任务排行</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>用户名</th>
                                    <th>任务数量</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in user_task_stats %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ user.username }}</td>
                                    <td>{{ user.count }}</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="3" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 用户文案创建统计图
    const userContentCtx = document.getElementById('userContentChart').getContext('2d');
    const userContentLabels = [];
    const userContentData = [];
    
    {% for user in user_content_stats %}
        userContentLabels.push('{{ user.username }}');
        userContentData.push({{ user.count }});
    {% endfor %}
    
    new Chart(userContentCtx, {
        type: 'bar',
        data: {
            labels: userContentLabels,
            datasets: [{
                label: '文案创建数量',
                data: userContentData,
                backgroundColor: '#4e73df',
                borderColor: '#4e73df',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
    
    // 用户审核统计图
    const userReviewCtx = document.getElementById('userReviewChart').getContext('2d');
    const userReviewLabels = [];
    const userReviewData = [];
    
    {% for user in user_review_stats %}
        userReviewLabels.push('{{ user.username }}');
        userReviewData.push({{ user.count }});
    {% endfor %}
    
    new Chart(userReviewCtx, {
        type: 'bar',
        data: {
            labels: userReviewLabels,
            datasets: [{
                label: '文案审核数量',
                data: userReviewData,
                backgroundColor: '#1cc88a',
                borderColor: '#1cc88a',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
});
</script>
{% endblock %} 