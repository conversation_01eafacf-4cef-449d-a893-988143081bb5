# -*- coding: utf-8 -*-
"""
文案补充表单
"""
from flask_wtf import FlaskForm
from wtforms import SelectField, IntegerField, RadioField
from wtforms.validators import DataRequired, NumberRange

class SupplementForm(FlaskForm):
    """文案补充表单"""
    batch_id = SelectField('选择批次', coerce=int, validators=[DataRequired('请选择批次')])
    count = IntegerField('补充数量', validators=[
        DataRequired('请输入补充数量'),
        NumberRange(min=1, max=100, message='补充数量必须在1-100之间')
    ])

class SupplementSettingForm(FlaskForm):
    """补充设置表单"""
    source = RadioField('补充来源', choices=[
        ('unassigned', '从未分配文案池补充'),
        ('generate', '生成新文案')
    ], default='unassigned', validators=[DataRequired('请选择补充来源')]) 