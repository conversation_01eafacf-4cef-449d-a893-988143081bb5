<!-- 通用占位页面 -->
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>{{ page_title }}</h2>
        </div>
        <div class="col-md-4 text-end">
            <button type="button" class="btn btn-primary">
                <i class="bi bi-plus-lg"></i> 添加{{ page_title.replace('管理', '') }}
            </button>
        </div>
    </div>

    <!-- 功能开发中提示 -->
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-tools fs-1 text-muted mb-3"></i>
            <h4 class="text-muted">{{ page_title }}功能开发中</h4>
            <p class="text-muted">{{ message if message else '该功能正在开发中，敬请期待...' }}</p>
            
            <!-- 功能预览 -->
            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="card border-light">
                        <div class="card-body">
                            <i class="bi bi-list-ul fs-2 text-primary"></i>
                            <h6 class="mt-2">数据列表</h6>
                            <p class="text-muted small">查看和管理数据</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-light">
                        <div class="card-body">
                            <i class="bi bi-plus-circle fs-2 text-success"></i>
                            <h6 class="mt-2">添加功能</h6>
                            <p class="text-muted small">创建新的数据</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-light">
                        <div class="card-body">
                            <i class="bi bi-gear fs-2 text-warning"></i>
                            <h6 class="mt-2">设置选项</h6>
                            <p class="text-muted small">配置相关设置</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <button type="button" class="btn btn-outline-primary me-2" onclick="showPage('dashboard')">
                    <i class="bi bi-house"></i> 返回控制台
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新页面
                </button>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">快速操作</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="bi bi-plus-circle me-2"></i>添加新项目
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="bi bi-upload me-2"></i>批量导入
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="bi bi-download me-2"></i>导出数据
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="bi bi-gear me-2"></i>设置选项
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">统计信息</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary">--</h4>
                                <small class="text-muted">总数量</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">--</h4>
                            <small class="text-muted">本月新增</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-warning">--</h4>
                                <small class="text-muted">待处理</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info">--</h4>
                            <small class="text-muted">已完成</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
console.log('{{ page_title }}页面已加载');

// 禁用快速操作链接
document.querySelectorAll('.list-group-item').forEach(item => {
    item.addEventListener('click', function(e) {
        e.preventDefault();
        alert('该功能正在开发中，敬请期待！');
    });
});
</script>
