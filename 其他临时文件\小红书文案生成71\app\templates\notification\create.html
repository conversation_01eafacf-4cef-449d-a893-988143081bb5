{% extends "base.html" %}

{% block title %}创建通知{% endblock %}

{% block content_auth %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">创建系统通知</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('notification.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回通知中心
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        
                        <div class="mb-3">
                            {{ form.title.label(class="form-label") }}
                            {{ form.title(class="form-control") }}
                            {% if form.title.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.title.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.content.label(class="form-label") }}
                            {{ form.content(class="form-control", rows=5) }}
                            {% if form.content.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.content.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">支持基本HTML格式</small>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.type.label(class="form-label") }}
                            {{ form.type(class="form-select") }}
                            {% if form.type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.type.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">{{ form.priority.label }}</label>
                            <div class="form-group">
                                {% for subfield in form.priority %}
                                <div class="form-check form-check-inline">
                                    {{ subfield(class="form-check-input") }}
                                    {{ subfield.label(class="form-check-label") }}
                                </div>
                                {% endfor %}
                            </div>
                            {% if form.priority.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.priority.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>接收者设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">{{ form.recipient_type.label }}</label>
                                    <div class="form-group">
                                        {% for subfield in form.recipient_type %}
                                        <div class="form-check">
                                            {{ subfield(class="form-check-input recipient-type-radio") }}
                                            {{ subfield.label(class="form-check-label") }}
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                
                                <div id="roleSelector" class="mb-3" style="display: none;">
                                    {{ form.recipient_role.label(class="form-label") }}
                                    {{ form.recipient_role(class="form-select") }}
                                </div>
                                
                                <div id="userSelector" class="mb-3" style="display: none;">
                                    {{ form.recipient_user.label(class="form-label") }}
                                    {{ form.recipient_user(class="form-select") }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> 发送通知
                            </button>
                            <a href="{{ url_for('notification.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 根据选择的接收者类型显示/隐藏相应的选择器
    document.addEventListener('DOMContentLoaded', function() {
        const recipientTypeRadios = document.querySelectorAll('.recipient-type-radio');
        const roleSelector = document.getElementById('roleSelector');
        const userSelector = document.getElementById('userSelector');
        
        function updateSelectors() {
            const selectedType = document.querySelector('.recipient-type-radio:checked').value;
            
            if (selectedType === 'role') {
                roleSelector.style.display = 'block';
                userSelector.style.display = 'none';
            } else if (selectedType === 'user') {
                roleSelector.style.display = 'none';
                userSelector.style.display = 'block';
            } else {
                roleSelector.style.display = 'none';
                userSelector.style.display = 'none';
            }
        }
        
        recipientTypeRadios.forEach(radio => {
            radio.addEventListener('change', updateSelectors);
        });
        
        // 初始化
        updateSelectors();
    });
</script>
{% endblock %} 