{% extends "base.html" %}

{% block title %}API设置 - 小红书文案生成系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">API设置</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('system.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回系统设置
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> API密钥用于第三方应用访问系统API接口。请妥善保管密钥，不要泄露给无关人员。
                    </div>
                    
                    <form method="post">
                        {{ form.csrf_token }}
                        
                        <div class="mb-3">
                            {{ form.api_key.label(class="form-label") }}
                            {{ form.api_key(class="form-control") }}
                            {% if form.api_key.errors %}
                                {% for error in form.api_key.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                            <small class="form-text text-muted">API密钥用于验证API请求，建议使用复杂的随机字符串。</small>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-label">密钥选项</div>
                            <div class="form-check">
                                {{ form.generate_new(class="form-check-input", value="0") }}
                                {{ form.generate_new.label(class="form-check-label", text="保持现有密钥") }}
                            </div>
                            <div class="form-check">
                                {{ form.generate_new(class="form-check-input", value="1") }}
                                {{ form.generate_new.label(class="form-check-label", text="生成新密钥") }}
                            </div>
                            <small class="form-text text-muted">生成新密钥会使现有密钥失效，需要更新所有使用API的应用程序。</small>
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-4">
                        <h5>API接口文档</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>接口名称</th>
                                        <th>URL</th>
                                        <th>方法</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>获取文案列表</td>
                                        <td><code>/api/v1/contents</code></td>
                                        <td><code>GET</code></td>
                                        <td>获取符合条件的文案列表，支持分页和筛选</td>
                                    </tr>
                                    <tr>
                                        <td>获取单篇文案</td>
                                        <td><code>/api/v1/content</code></td>
                                        <td><code>GET</code></td>
                                        <td>获取单篇待发布文案，自动将状态更新为"发布中"</td>
                                    </tr>
                                    <tr>
                                        <td>根据ID获取文案</td>
                                        <td><code>/api/v1/content/{content_id}</code></td>
                                        <td><code>GET</code></td>
                                        <td>根据ID获取文案详情</td>
                                    </tr>
                                    <tr>
                                        <td>更新文案状态</td>
                                        <td><code>/api/v1/content/{content_id}/status</code></td>
                                        <td><code>POST</code></td>
                                        <td>更新文案发布状态（成功/失败）</td>
                                    </tr>
                                    <tr>
                                        <td>批量更新</td>
                                        <td><code>/api/v1/contents/batch</code></td>
                                        <td><code>POST</code></td>
                                        <td>批量更新文案状态或优先级</td>
                                    </tr>
                                    <tr>
                                        <td>统计数据</td>
                                        <td><code>/api/v1/stats</code></td>
                                        <td><code>GET</code></td>
                                        <td>获取文案统计数据</td>
                                    </tr>
                                    <tr>
                                        <td>系统设置</td>
                                        <td><code>/api/v1/settings</code></td>
                                        <td><code>GET</code></td>
                                        <td>获取API相关系统设置</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="alert alert-secondary">
                            <h6>API认证方式</h6>
                            <p>所有API请求都需要在请求头中添加API密钥：</p>
                            <pre><code>X-API-Key: {{ current_api_key }}</code></pre>
                            <p>示例请求：</p>
                            <pre><code>curl -H "X-API-Key: {{ current_api_key }}" http://your-domain.com/api/v1/contents</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 