{% extends "base.html" %}

{% block title %}手动安排展示计划{% endblock %}

{% block page_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">手动安排展示计划 - {{ client.name }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('display.schedule', client_id=client.id) }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> 展示计划信息：</h5>
                        <p>为 {{ client.name }} 安排 {{ display_date }} 的展示计划</p>
                        <p>可用文案数量：{{ contents|length }}</p>
                    </div>
                    
                    <form method="post" id="manualScheduleForm">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th width="50px">选择</th>
                                        <th>ID</th>
                                        <th>标题</th>
                                        <th>展示时间</th>
                                        <th>固定时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for content in contents %}
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input content-checkbox" type="checkbox" name="content_ids" value="{{ content.id }}" id="content{{ content.id }}">
                                            </div>
                                        </td>
                                        <td>{{ content.id }}</td>
                                        <td>
                                            <label for="content{{ content.id }}">
                                                {{ content.title|truncate(50) }}
                                            </label>
                                        </td>
                                        <td>
                                            <input type="time" name="display_times" class="form-control display-time" disabled>
                                        </td>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input is-fixed" type="checkbox" name="is_fixed" value="{{ loop.index0 }}" disabled>
                                                <label class="form-check-label">固定时间</label>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存展示计划
                            </button>
                            <a href="{{ url_for('display.schedule', client_id=client.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始时间
        const startTime = '{{ start_time.strftime("%H:%M") if start_time else "09:00" }}';
        let timeInputs = document.querySelectorAll('.display-time');
        timeInputs[0].value = startTime;
        
        // 监听复选框变化
        const checkboxes = document.querySelectorAll('.content-checkbox');
        checkboxes.forEach((checkbox, index) => {
            checkbox.addEventListener('change', function() {
                const timeInput = this.closest('tr').querySelector('.display-time');
                const fixedCheckbox = this.closest('tr').querySelector('.is-fixed');
                
                if (this.checked) {
                    timeInput.disabled = false;
                    fixedCheckbox.disabled = false;
                    
                    // 设置默认时间
                    if (!timeInput.value) {
                        if (index === 0) {
                            timeInput.value = startTime;
                        } else {
                            // 查找前一个已选中项的时间
                            let prevTime = null;
                            for (let i = index - 1; i >= 0; i--) {
                                if (checkboxes[i].checked) {
                                    prevTime = timeInputs[i].value;
                                    break;
                                }
                            }
                            
                            if (prevTime) {
                                // 默认间隔30分钟
                                let [hours, minutes] = prevTime.split(':').map(Number);
                                minutes += 30;
                                if (minutes >= 60) {
                                    hours += Math.floor(minutes / 60);
                                    minutes = minutes % 60;
                                }
                                hours = hours % 24;
                                
                                timeInput.value = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                            } else {
                                timeInput.value = startTime;
                            }
                        }
                    }
                } else {
                    timeInput.disabled = true;
                    fixedCheckbox.disabled = true;
                    fixedCheckbox.checked = false;
                }
            });
        });
        
        // 表单提交前验证
        document.getElementById('manualScheduleForm').addEventListener('submit', function(e) {
            const checkedCount = document.querySelectorAll('.content-checkbox:checked').length;
            if (checkedCount === 0) {
                e.preventDefault();
                alert('请至少选择一条文案');
                return false;
            }
            
            // 验证所有选中的文案都有时间
            let valid = true;
            document.querySelectorAll('.content-checkbox:checked').forEach(checkbox => {
                const timeInput = checkbox.closest('tr').querySelector('.display-time');
                if (!timeInput.value) {
                    valid = false;
                }
            });
            
            if (!valid) {
                e.preventDefault();
                alert('请为所有选中的文案设置展示时间');
                return false;
            }
            
            return true;
        });
    });
</script>
{% endblock %} 