"""
新增user_permissions表，实现用户与权限的多对多直接关联

Revision ID: 20240715_add_user_permissions
Revises: f53868527c9d
Create Date: 2024-07-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '20240715_add_user_permissions'
down_revision = 'f53868527c9d'
branch_labels = None
depends_on = None

def upgrade():
    op.create_table(
        'user_permissions',
        sa.Column('id', sa.Integer, primary_key=True),
        sa.Column('user_id', sa.Integer, sa.Foreign<PERSON>ey('users.id'), nullable=False),
        sa.Column('permission_id', sa.Integer, sa.<PERSON>ey('permissions.id'), nullable=False)
    )
    op.create_index('ix_user_permissions_user_id', 'user_permissions', ['user_id'])
    op.create_index('ix_user_permissions_permission_id', 'user_permissions', ['permission_id'])

def downgrade():
    op.drop_index('ix_user_permissions_user_id', table_name='user_permissions')
    op.drop_index('ix_user_permissions_permission_id', table_name='user_permissions')
    op.drop_table('user_permissions') 