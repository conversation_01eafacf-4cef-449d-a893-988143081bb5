{% extends "base.html" %}

{% block title %}功能导航{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-compass me-2"></i>功能导航
                    </h5>
                    <small>根据您的权限显示可访问的功能模块</small>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        {% for func in functions %}
                        <div class="col-md-6 col-lg-4 col-xl-3">
                            <div class="card h-100 function-card">
                                <div class="card-body text-center">
                                    <div class="function-icon mb-3">
                                        <i class="{{ func.icon }} fa-3x text-primary"></i>
                                    </div>
                                    <h6 class="card-title">{{ func.name }}</h6>
                                    <p class="card-text text-muted small">{{ func.description }}</p>
                                    <a href="{{ func.url }}" 
                                       class="btn btn-primary btn-sm"
                                       {% if not func.url.startswith('http') and not func.url.startswith('/admin/') %}data-ajax-link{% endif %}>
                                        <i class="fas fa-arrow-right me-1"></i>进入
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    {% if functions|length == 0 %}
                    <div class="text-center py-5">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h5>暂无可访问的功能</h5>
                        <p class="text-muted">请联系管理员为您分配相应的权限</p>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-light">
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                共有 {{ functions|length }} 个功能可用
                            </small>
                        </div>
                        <div class="col-md-6 text-end">
                            <a href="{{ url_for('navigation.my_permissions') }}" 
                               class="btn btn-outline-secondary btn-sm" data-ajax-link>
                                <i class="fas fa-key me-1"></i>查看我的权限
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .function-card {
        transition: transform 0.2s, box-shadow 0.2s;
        border: 1px solid #e3e6f0;
    }
    
    .function-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .function-icon {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .card-title {
        font-weight: 600;
        color: #2c3e50;
    }
    
    .card-text {
        font-size: 0.875rem;
        line-height: 1.4;
        min-height: 2.8rem;
    }
</style>
{% endblock %}
