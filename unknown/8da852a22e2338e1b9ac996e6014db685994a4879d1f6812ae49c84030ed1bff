/**
 * HTTP环境剪贴板修复方案
 * 专门解决HTTP环境下复制功能失效的问题
 */

// 重写全局复制函数，针对HTTP环境优化
(function() {
    'use strict';
    
    console.log('HTTP剪贴板修复方案已加载');
    
    // 检测当前环境
    const isHttpEnvironment = location.protocol === 'http:' && 
                             location.hostname !== 'localhost' && 
                             location.hostname !== '127.0.0.1';
    
    console.log('当前环境检测:', {
        protocol: location.protocol,
        hostname: location.hostname,
        isHttpEnvironment: isHttpEnvironment,
        hasClipboardAPI: !!(navigator.clipboard && navigator.clipboard.writeText),
        isSecureContext: window.isSecureContext
    });
    
    // 如果是HTTP环境，直接使用手动复制方案
    if (isHttpEnvironment) {
        console.log('检测到HTTP环境，启用手动复制方案');
        
        // 重写copyToClipboard函数
        window.copyToClipboard = function(text, options = {}) {
            // 如果传入的是元素ID，获取元素的值
            if (typeof text === 'string' && !text.startsWith('http') && text.length < 50 && !text.includes(' ')) {
                const element = document.getElementById(text);
                if (element) {
                    text = element.value || element.textContent || element.innerText;
                }
            }
            
            console.log('HTTP环境复制:', text);
            
            // 直接显示手动复制对话框
            showHttpCopyDialog(text, options);
        };
        
        // HTTP环境专用复制对话框
        function showHttpCopyDialog(text, options = {}) {
            const message = text.startsWith('http') ? '分享链接' : '内容';
            
            // 创建一个更简单直接的复制界面
            const copyHtml = `
                <div style="
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: white;
                    border: 2px solid #007bff;
                    border-radius: 8px;
                    padding: 20px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    z-index: 10000;
                    max-width: 90%;
                    width: 500px;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                " id="httpCopyDialog">
                    <div style="text-align: center; margin-bottom: 15px;">
                        <h3 style="color: #007bff; margin: 0 0 10px 0;">📋 复制${message}</h3>
                        <p style="color: #666; margin: 0; font-size: 14px;">
                            由于浏览器安全限制，请手动复制以下内容
                        </p>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <textarea 
                            id="httpCopyText" 
                            readonly 
                            onclick="this.select()"
                            style="
                                width: 100%;
                                height: 80px;
                                padding: 10px;
                                border: 1px solid #ddd;
                                border-radius: 4px;
                                font-family: monospace;
                                font-size: 14px;
                                resize: none;
                                background: #f8f9fa;
                            "
                        >${text}</textarea>
                    </div>
                    
                    <div style="background: #e3f2fd; padding: 10px; border-radius: 4px; margin-bottom: 15px; font-size: 13px;">
                        <strong>📝 复制步骤：</strong><br>
                        1️⃣ 点击上方文本框（文本会自动选中）<br>
                        2️⃣ 按 <strong>Ctrl+C</strong> (Windows) 或 <strong>Cmd+C</strong> (Mac)<br>
                        3️⃣ 在需要的地方按 <strong>Ctrl+V</strong> 粘贴
                    </div>
                    
                    <div style="text-align: center;">
                        <button 
                            onclick="selectHttpCopyText()" 
                            style="
                                background: #007bff;
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 4px;
                                margin-right: 10px;
                                cursor: pointer;
                                font-size: 14px;
                            "
                        >🎯 选中文本</button>
                        
                        <button 
                            onclick="closeHttpCopyDialog()" 
                            style="
                                background: #28a745;
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 4px;
                                cursor: pointer;
                                font-size: 14px;
                            "
                        >✅ 已复制，关闭</button>
                    </div>
                </div>
                
                <div style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.5);
                    z-index: 9999;
                " id="httpCopyBackdrop" onclick="closeHttpCopyDialog()"></div>
            `;
            
            // 移除已存在的对话框
            const existing = document.getElementById('httpCopyDialog');
            if (existing) {
                existing.parentNode.removeChild(existing);
            }
            const existingBackdrop = document.getElementById('httpCopyBackdrop');
            if (existingBackdrop) {
                existingBackdrop.parentNode.removeChild(existingBackdrop);
            }
            
            // 添加新对话框
            document.body.insertAdjacentHTML('beforeend', copyHtml);
            
            // 自动选中文本
            setTimeout(() => {
                const textarea = document.getElementById('httpCopyText');
                if (textarea) {
                    textarea.focus();
                    textarea.select();
                }
            }, 100);
            
            // 显示成功提示
            if (typeof showToast === 'function') {
                showToast('请按照提示手动复制内容', 'info');
            }
        }
        
        // 全局函数：选中文本
        window.selectHttpCopyText = function() {
            const textarea = document.getElementById('httpCopyText');
            if (textarea) {
                textarea.focus();
                textarea.select();
                textarea.setSelectionRange(0, textarea.value.length);
                
                // 尝试复制
                try {
                    const success = document.execCommand('copy');
                    if (success) {
                        const btn = event.target;
                        const originalText = btn.textContent;
                        btn.textContent = '✅ 已选中';
                        btn.style.background = '#28a745';
                        
                        setTimeout(() => {
                            btn.textContent = originalText;
                            btn.style.background = '#007bff';
                        }, 2000);
                        
                        if (typeof showToast === 'function') {
                            showToast('文本已选中，请按 Ctrl+C 复制', 'success');
                        }
                    }
                } catch (err) {
                    console.log('execCommand失败:', err);
                }
            }
        };
        
        // 全局函数：关闭对话框
        window.closeHttpCopyDialog = function() {
            const dialog = document.getElementById('httpCopyDialog');
            const backdrop = document.getElementById('httpCopyBackdrop');
            
            if (dialog) dialog.remove();
            if (backdrop) backdrop.remove();
            
            if (typeof showToast === 'function') {
                showToast('复制对话框已关闭', 'info');
            }
        };
        
        // 键盘事件：ESC关闭对话框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && document.getElementById('httpCopyDialog')) {
                closeHttpCopyDialog();
            }
        });
        
    } else {
        console.log('非HTTP环境，使用标准复制方案');
    }
    
})();

// 页面加载完成后的提示
document.addEventListener('DOMContentLoaded', function() {
    const isHttpEnvironment = location.protocol === 'http:' && 
                             location.hostname !== 'localhost' && 
                             location.hostname !== '127.0.0.1';
    
    if (isHttpEnvironment) {
        console.log('🔧 HTTP环境剪贴板修复方案已激活');
        console.log('💡 复制功能将使用手动复制对话框');
        
        // 可选：显示一次性提示
        setTimeout(() => {
            if (typeof showToast === 'function') {
                showToast('💡 提示：由于HTTP环境限制，复制功能将显示手动复制对话框', 'info');
            }
        }, 2000);
    }
});
