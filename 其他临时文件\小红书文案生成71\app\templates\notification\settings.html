{% extends "base.html" %}

{% block title %}通知设置{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">通知系统设置</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('system.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回系统设置
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>通知开关设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-check form-switch mb-3">
                                    {{ form.enable_all(class="form-check-input") }}
                                    {{ form.enable_all.label(class="form-check-label") }}
                                    <small class="form-text text-muted d-block">启用或禁用所有通知</small>
                                </div>
                                
                                <hr>
                                <h6>通知类型设置</h6>
                                
                                <div class="form-check form-switch mb-2">
                                    {{ form.enable_review_status(class="form-check-input") }}
                                    {{ form.enable_review_status.label(class="form-check-label") }}
                                </div>
                                
                                <div class="form-check form-switch mb-2">
                                    {{ form.enable_client_operation(class="form-check-input") }}
                                    {{ form.enable_client_operation.label(class="form-check-label") }}
                                </div>
                                
                                <div class="form-check form-switch mb-2">
                                    {{ form.enable_publish_notice(class="form-check-input") }}
                                    {{ form.enable_publish_notice.label(class="form-check-label") }}
                                </div>
                                
                                <div class="form-check form-switch mb-2">
                                    {{ form.enable_system_change(class="form-check-input") }}
                                    {{ form.enable_system_change.label(class="form-check-label") }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>接收设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.recipient_roles.label(class="form-label") }}
                                    {{ form.recipient_roles(class="form-select") }}
                                    <small class="form-text text-muted">设置哪些角色可以接收通知</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>显示设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.display_count.label(class="form-label") }}
                                    {{ form.display_count(class="form-select") }}
                                    <small class="form-text text-muted">设置每页显示的通知数量</small>
                                </div>
                                
                                <div class="form-check form-switch mb-2">
                                    {{ form.auto_mark_read(class="form-check-input") }}
                                    {{ form.auto_mark_read.label(class="form-check-label") }}
                                    <small class="form-text text-muted d-block">查看通知详情后自动标记为已读</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                            <a href="{{ url_for('system.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 如果全局开关关闭，禁用其他选项
    document.addEventListener('DOMContentLoaded', function() {
        const enableAllSwitch = document.getElementById('enable_all');
        if (enableAllSwitch) {
            const typeSettings = document.querySelectorAll('.form-check-input:not(#enable_all)');
            
            function updateDisabled() {
                const isDisabled = !enableAllSwitch.checked;
                typeSettings.forEach(setting => {
                    setting.disabled = isDisabled;
                });
            }
            
            enableAllSwitch.addEventListener('change', updateDisabled);
            updateDisabled();
        }
    });
</script>
{% endblock %} 