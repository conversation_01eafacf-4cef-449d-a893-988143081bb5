"""
文案内容表单
"""
from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, IntegerField, DateField, TimeField, HiddenField, BooleanField, MultipleFileField, FieldList, FormField
from wtforms.validators import DataRequired, Optional, Length, ValidationError, NumberRange
from flask_wtf.file import FileField, FileAllowed
from app.utils.simplemde_field import SimpleMDEField


class ContentForm(FlaskForm):
    """文案表单"""
    title = StringField('标题', validators=[DataRequired(), Length(max=200)])
    content = SimpleMDEField('内容', validators=[DataRequired()])
    topics = StringField('话题标签', validators=[Optional(), Length(max=500)])
    location = StringField('定位信息', validators=[Optional(), Length(max=100)])
    
    template_id = SelectField('模板', coerce=int, validators=[Optional()])
    client_id = SelectField('客户', coerce=int, validators=[DataRequired()])
    task_id = SelectField('任务', coerce=int, validators=[DataRequired()])
    
    display_date = DateField('展示日期', validators=[Optional()])
    display_time = TimeField('展示时间', format='%H:%M', validators=[Optional()])
    
    publish_priority = SelectField('发布优先级', choices=[
        ('high', '高'),
        ('normal', '中'),
        ('low', '低')
    ], default='normal')
    
    images = MultipleFileField('上传图片', validators=[Optional(), FileAllowed(['jpg', 'png', 'jpeg', 'gif'], '只允许上传图片文件')])
    
    def validate_topics(self, field):
        """验证话题格式"""
        if field.data:
            topics = [t.strip() for t in field.data.split(',')]
            if len(topics) > 10:
                raise ValidationError('话题不能超过10个')


class ContentReviewForm(FlaskForm):
    """文案审核表单"""
    status = SelectField('审核结果', choices=[
        ('approved', '通过'),
        ('rejected', '拒绝')
    ])
    reason = TextAreaField('拒绝理由', validators=[Optional(), Length(max=500)])
    quick_reason_id = SelectField('快捷理由', coerce=int, validators=[Optional()])


class ContentFilterForm(FlaskForm):
    """文案筛选表单"""
    client_id = SelectField('客户', coerce=int)
    task_id = SelectField('任务', coerce=int)
    status = SelectField('状态', choices=[
        ('', '全部状态'),
        ('draft', '草稿'),
        ('pending_review', '待初审'),
        ('first_reviewed', '初审通过'),
        ('pending_image', '待上传图片'),
        ('image_uploaded', '图片已上传'),
        ('pending_final_review', '待最终审核'),
        ('pending_client_review', '待客户审核'),
        ('client_rejected', '客户已拒绝'),
        ('client_approved', '客户已通过'),
        ('pending_publish', '待发布'),
        ('published', '已发布')
    ])
    search = StringField('搜索')
    date_from = StringField('开始日期')
    date_to = StringField('结束日期')
    
    # 添加排序字段
    sort_by = SelectField('排序方式', choices=[
        ('updated_at', '更新时间'),
        ('created_at', '创建时间'),
        ('title', '标题'),
        ('client_id', '客户'),
        ('task_id', '任务')
    ], default='updated_at')
    
    sort_order = SelectField('排序顺序', choices=[
        ('desc', '降序'),
        ('asc', '升序')
    ], default='desc')


class ContentBatchActionForm(FlaskForm):
    """文案批量操作表单"""
    action = SelectField('操作', choices=[
        ('review', '批量审核'),
        ('publish', '批量发布'),
        ('delete', '批量删除')
    ])
    content_ids = HiddenField('文案ID列表')
    status = SelectField('状态', choices=[
        ('approved', '通过'),
        ('rejected', '拒绝'),
        ('pending_publish', '待发布'),
        ('published', '已发布')
    ])
    reason = TextAreaField('原因', validators=[Optional(), Length(max=500)])
    # 移除确认字段的必填验证
    confirm = BooleanField('我确认执行此操作', default=True)


class KeywordPairForm(FlaskForm):
    """关键词对表单"""
    mark = StringField('标记', validators=[DataRequired()])
    keywords = TextAreaField('关键词列表', validators=[DataRequired()])


class GenerateContentForm(FlaskForm):
    client_id = SelectField('选择客户', coerce=int, validators=[DataRequired()])
    task_id = SelectField('选择任务', coerce=int)  # 移除DataRequired验证器，因为0是有效值（创建新任务）
    new_task_name = StringField('任务名称', validators=[Optional(), Length(max=100)])
    batch_name = StringField('批次名称', validators=[DataRequired(), Length(max=100)])
    template_category_id = SelectField('模板分类', coerce=int, validators=[DataRequired()])
    count = IntegerField('生成数量', validators=[DataRequired(), NumberRange(min=1, max=1000)], default=1)
    max_topics_count = IntegerField('最大话题数量', validators=[NumberRange(min=1, max=10)], default=10)
    random_topics_count = IntegerField('随机话题数量', validators=[NumberRange(min=0, max=10)], default=0)
    random_at_users_count = IntegerField('随机@用户数量', validators=[NumberRange(min=0, max=10)], default=1)
    required_topics = TextAreaField('必选话题')
    random_topics = TextAreaField('随机话题')
    at_users = TextAreaField('@用户')
    location = StringField('定位信息')
    keywords = TextAreaField('关键词')
    publish_priority = SelectField('发布优先级', choices=[('high', '高'), ('normal', '中'), ('low', '低')], default='normal')
    avoid_duplicates = BooleanField('避免重复内容', default=True)
    allow_template_duplicate = HiddenField('允许模板重复', default='0')
    
    # 修改自定义验证方法，更灵活地处理task_id和new_task_name的关系
    def validate_task_id(self, field):
        """验证任务ID：如果是0（创建新任务），则new_task_name必须有值"""
        # 只有当task_id明确为0时才检查new_task_name
        if field.data == 0 and not self.new_task_name.data:
            raise ValidationError('选择创建新任务时，必须填写任务名称')


class EditContentForm(FlaskForm):
    """编辑文案表单"""
    title = StringField('标题', validators=[DataRequired(), Length(max=200)])
    content = TextAreaField('内容', validators=[DataRequired()])
    topics = StringField('话题标签', validators=[Optional(), Length(max=500)])
    location = StringField('定位信息', validators=[Optional(), Length(max=100)])

    display_date = DateField('展示日期', validators=[Optional()])
    display_time = TimeField('展示时间', format='%H:%M', validators=[Optional()])

    publish_priority = SelectField('发布优先级', choices=[
        ('high', '高'),
        ('normal', '中'),
        ('low', '低')
    ], default='normal')