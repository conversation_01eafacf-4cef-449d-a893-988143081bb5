"""
应用入口文件
"""

import os
import click
from app import create_app
from flask_migrate import Migrate
from app.models import db
from app.models.user import User, Role, Permission
from app.config.config import DB_CONFIG

# 创建应用实例
app = create_app(os.getenv('FLASK_ENV') or 'default')
migrate = Migrate(app, db)

# 打印当前使用的数据库URI
print("当前使用的数据库URI:", app.config['SQLALCHEMY_DATABASE_URI'])


@app.shell_context_processor
def make_shell_context():
    """创建Shell上下文"""
    return dict(db=db, User=User, Role=Role, Permission=Permission)


@app.cli.command('create-admin')
def create_admin():
    """创建管理员账号"""
    from app.models.user import User, Role, Permission
    
    # 创建权限
    admin_access = Permission.query.filter_by(name='admin_access').first()
    if not admin_access:
        admin_access = Permission(name='admin_access', description='允许访问管理后台')
        db.session.add(admin_access)
    
    # 创建管理员角色
    admin_role = Role.query.filter_by(name='超级管理员').first()
    if not admin_role:
        admin_role = Role(name='超级管理员', description='拥有所有权限')
        admin_role.permissions.append(admin_access)
        db.session.add(admin_role)
    
    # 创建管理员用户
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            real_name='管理员',
            is_active=True
        )
        admin.password = 'admin123'
        admin.roles.append(admin_role)
        db.session.add(admin)
    
    db.session.commit()
    print('管理员账号创建成功!')


@app.cli.command('init-mysql')
def init_mysql():
    """初始化MySQL数据库"""
    import subprocess
    import pymysql
    
    print('开始初始化MySQL数据库...')
    print(f"数据库配置: {DB_CONFIG['USER']}@{DB_CONFIG['HOST']}/{DB_CONFIG['NAME']}")
    
    # 连接数据库
    try:
        # 先尝试连接MySQL
        conn = pymysql.connect(
            host=DB_CONFIG['HOST'],
            user=DB_CONFIG['USER'],
            password=DB_CONFIG['PASSWORD'],
            charset=DB_CONFIG['CHARSET']
        )
        cursor = conn.cursor()
        
        # 创建数据库
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {DB_CONFIG['NAME']} DEFAULT CHARACTER SET {DB_CONFIG['CHARSET']} COLLATE {DB_CONFIG['CHARSET']}_unicode_ci")
        print('数据库创建成功!')
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        # 导入数据库结构
        print('导入数据库结构...')
        subprocess.run(f"mysql -u{DB_CONFIG['USER']} -p{DB_CONFIG['PASSWORD']} {DB_CONFIG['NAME']} < xhsrw666.sql", shell=True)
        print('数据库结构导入成功!')
        
        print('MySQL数据库初始化完成!')
    except Exception as e:
        print(f'初始化MySQL数据库失败: {e}')
        print('请确保MySQL服务已启动,并且用户名和密码正确')


if __name__ == '__main__':
    app.run(debug=True) 