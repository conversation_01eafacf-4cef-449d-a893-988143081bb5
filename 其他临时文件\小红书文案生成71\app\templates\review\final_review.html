<!-- 最终审核页面 -->
<style>
.content-preview-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
    margin: 5px 0;
}

.content-info {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
}

.content-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 8px;
}

.content-text {
    background: white;
    padding: 10px;
    border-radius: 4px;
    border-left: 3px solid #007bff;
    margin-bottom: 10px;
}

.content-text p {
    margin-bottom: 0;
    line-height: 1.6;
    color: #495057;
}

.content-meta {
    margin-top: 8px;
}

.content-images {
    margin-top: 15px;
}

.image-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.image-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.image-item:hover {
    transform: scale(1.05);
}

.image-order {
    position: absolute;
    top: 5px;
    left: 5px;
    background: rgba(0,0,0,0.7);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.no-images {
    text-align: center;
    padding: 20px;
    color: #6c757d;
}

.table td {
    vertical-align: top;
    padding: 15px 8px;
}

.btn-group-vertical .btn {
    margin-bottom: 2px;
}

/* 左侧信息面板样式 */
.info-panel {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.section {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
}

.section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.location-info {
    color: #6c757d;
    font-size: 14px;
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

/* 正方形图片网格样式 */
.images-preview {
    text-align: center;
}

.image-grid-square {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 8px;
    max-width: 200px;
    margin: 0 auto;
}

.image-item-square {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 2px solid #e9ecef;
}

.image-item-square:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.square-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.image-order-square {
    position: absolute;
    top: 2px;
    left: 2px;
    background: rgba(0,0,0,0.8);
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
}

/* 文案内容样式优化 */
.content-preview-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    background: white;
}

.content-title {
    color: #495057;
    font-weight: 600;
    font-size: 16px;
}

.content-text {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid #007bff;
    margin-top: 10px;
}

.content-text p {
    margin-bottom: 0;
    line-height: 1.6;
    color: #495057;
}

/* 表格行样式 */
.table td {
    vertical-align: top;
    padding: 15px 8px;
}

/* 无图片状态 */
.no-images {
    padding: 20px;
    color: #6c757d;
}
</style>

<script>
// 全局变量
let currentContentId = null;

// 查看文案详情
function viewContent(contentId) {
    console.log('查看文案详情，ID:', contentId);
    fetch(`/simple/api/contents/${contentId}/view`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        showContentModal('查看文案详情', html);
    })
    .catch(error => {
        console.error('加载文案详情失败:', error);
        alert('加载文案详情失败，请重试');
    });
}

// 确保函数在全局作用域中可访问
window.viewContent = viewContent;

// 审核通过
function approveContent(contentId) {
    if (!confirm('确定要通过这篇文案的最终审核吗？')) {
        return;
    }

    const formData = new FormData();
    formData.append('action', 'approve');

    fetch(`/simple/api/final-review/${contentId}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            location.reload();
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('审核失败: ' + error.message, 'error');
    });
}

// 确保函数在全局作用域中可访问
window.approveContent = approveContent;

// 驳回文案
function rejectContent(contentId) {
    currentContentId = contentId;
    const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
    modal.show();
}

// 确保函数在全局作用域中可访问
window.rejectContent = rejectContent;

// 显示图片模态框
function showImageModal(imageSrc, imageName) {
    const modal = document.getElementById('imageModal');
    const modalTitle = document.getElementById('imageModalTitle');
    const modalImg = document.getElementById('imageModalImg');

    modalTitle.textContent = imageName || '查看图片';
    modalImg.src = imageSrc;

    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

// 确保函数在全局作用域中可访问
window.showImageModal = showImageModal;

// 显示内容模态框
function showContentModal(title, content) {
    // 这个函数应该在父页面中定义
    if (typeof window.showContentModal === 'function') {
        window.showContentModal(title, content);
    } else {
        // 简单的模态框实现
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">${content}</div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        modal.addEventListener('hidden.bs.modal', () => modal.remove());
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 这里可以使用Bootstrap的Toast组件或其他提示组件
    alert(message);
}
</script>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="bi bi-check2-square"></i> 最终审核</h2>
            <p class="text-muted">对已上传图片的文案进行最终审核</p>
        </div>
        <div class="col-md-4 text-end">
            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i> 刷新页面
            </button>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-warning">{{ content_data|length }}</h4>
                    <small class="text-muted">待最终审核</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-info">{{ content_data|map(attribute='image_count')|sum }}</h4>
                    <small class="text-muted">总图片数量</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-primary">{{ content_data|selectattr('image_count', 'gt', 0)|list|length }}</h4>
                    <small class="text-muted">有图片文案</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="showBatchReviewModal()">
                        <i class="bi bi-check-all"></i> 批量审核
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选条件 -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="card-title mb-0">筛选条件</h6>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">客户</label>
                    {{ form.client_id(class="form-select") }}
                </div>
                <div class="col-md-3">
                    <label class="form-label">任务</label>
                    {{ form.task_id(class="form-select") }}
                </div>
                <div class="col-md-3">
                    <label class="form-label">搜索</label>
                    {{ form.search(class="form-control", placeholder="搜索标题或内容") }}
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="bi bi-search"></i> 筛选
                    </button>
                    <a href="{{ url_for('main_simple.final_review') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i> 重置
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- 文案列表 -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="card-title mb-0">待审核文案列表</h6>
            <div>
                <input type="checkbox" id="selectAll" onchange="toggleSelectAll(this)">
                <label for="selectAll" class="ms-1">全选</label>
            </div>
        </div>
        <div class="card-body">
            {% if content_data %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAllHeader" onchange="toggleSelectAll(this)">
                                </th>
                                <th width="200">信息面板</th>
                                <th>文案内容</th>
                                <th width="250">配图预览</th>
                                <th width="150">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in content_data %}
                            <tr data-content-id="{{ item.content.id }}">
                                <td>
                                    <input type="checkbox" class="content-checkbox" value="{{ item.content.id }}">
                                </td>
                                <!-- 左侧信息面板 -->
                                <td>
                                    <div class="info-panel">
                                        <!-- 话题标签 -->
                                        <div class="section mb-3">
                                            <h6 class="section-title">
                                                <i class="bi bi-tags text-primary"></i> 话题标签
                                            </h6>
                                            <div class="tags-container">
                                                {% if item.content.task %}
                                                <span class="badge bg-primary me-1">#{{ item.content.task.name }}</span>
                                                {% endif %}
                                                {% if item.content.batch %}
                                                <span class="badge bg-secondary me-1">#{{ item.content.batch.name }}</span>
                                                {% endif %}
                                                <span class="badge bg-success me-1">#文案</span>
                                                <span class="badge bg-info me-1">#审核</span>
                                            </div>
                                        </div>

                                        <!-- 位置信息 -->
                                        <div class="section mb-3">
                                            <h6 class="section-title">
                                                <i class="bi bi-geo-alt text-danger"></i> 位置信息
                                            </h6>
                                            <div class="location-info">
                                                <span class="text-muted">{{ item.content.id }}</span>
                                            </div>
                                        </div>

                                        <!-- 用户信息 -->
                                        <div class="section">
                                            <h6 class="section-title">
                                                <i class="bi bi-person text-info"></i> 用户
                                            </h6>
                                            <div class="user-info">
                                                <span class="badge bg-info">{{ item.content.client.name if item.content.client else '未知' }}</span>
                                                <div class="text-muted small mt-1">
                                                    {{ item.content.created_at.strftime('%m-%d %H:%M') if item.content.created_at else '未知' }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <!-- 文案内容 -->
                                <td>
                                    <div class="content-preview-card">
                                        <div class="content-info">
                                            <h6 class="content-title mb-2">
                                                <i class="bi bi-file-text text-primary"></i>
                                                {{ item.content.title }}
                                            </h6>
                                            <div class="content-text">
                                                <p class="mb-0">{{ item.content.content }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <!-- 配图预览 -->
                                <td>
                                    {% if item.images %}
                                    <div class="images-preview">
                                        <div class="mb-2">
                                            <span class="badge bg-success">{{ item.image_count }} 张图片</span>
                                        </div>
                                        <div class="image-grid-square">
                                            {% for image in item.images %}
                                            <div class="image-item-square" onclick="showImageModal('{{ url_for('static', filename='uploads/' + image.image_path) }}', '{{ image.original_name }}')">
                                                <img src="{{ url_for('static', filename='uploads/' + image.thumbnail_path) }}"
                                                     class="square-thumbnail"
                                                     title="{{ image.original_name }}">
                                                <div class="image-order-square">{{ loop.index }}</div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    {% else %}
                                    <div class="no-images text-center">
                                        <i class="bi bi-image text-muted"></i>
                                        <div class="text-muted small">暂无配图</div>
                                    </div>
                                    {% endif %}
                                </td>
                                <!-- 操作按钮 -->
                                <td>
                                    <div class="btn-group-vertical btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary btn-sm"
                                                onclick="viewContent({{ item.content.id }})">
                                            <i class="bi bi-eye"></i> 查看详情
                                        </button>
                                        <button type="button" class="btn btn-outline-success btn-sm"
                                                onclick="approveContent({{ item.content.id }})">
                                            <i class="bi bi-check"></i> 通过审核
                                        </button>
                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                onclick="rejectContent({{ item.content.id }})">
                                            <i class="bi bi-x"></i> 驳回
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                {% if pagination.pages > 1 %}
                <nav aria-label="分页导航">
                    <ul class="pagination justify-content-center">
                        {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main_simple.final_review', page=pagination.prev_num) }}">上一页</a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in pagination.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != pagination.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('main_simple.final_review', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main_simple.final_review', page=pagination.next_num) }}">下一页</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-inbox fs-1 text-muted"></i>
                    <h5 class="text-muted mt-3">暂无待最终审核的文案</h5>
                    <p class="text-muted">请先完成文案的图片上传</p>
                    <button type="button" class="btn btn-outline-primary" onclick="showPage('image-upload')">
                        <i class="bi bi-arrow-left"></i> 去图片上传
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 图片查看模态框 -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalTitle">查看图片</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="imageModalImg" src="" class="img-fluid" style="max-height: 70vh;">
            </div>
        </div>
    </div>
</div>

<!-- 批量审核模态框 -->
<div class="modal fade" id="batchReviewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量审核</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="batchReviewForm">
                    <div class="mb-3">
                        <label class="form-label">审核动作</label>
                        <div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="action" value="approve" id="batchApprove" checked>
                                <label class="form-check-label" for="batchApprove">
                                    <i class="bi bi-check text-success"></i> 批量通过
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="action" value="reject" id="batchReject">
                                <label class="form-check-label" for="batchReject">
                                    <i class="bi bi-x text-danger"></i> 批量驳回
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3" id="rejectReasonDiv" style="display: none;">
                        <label for="batchRejectReason" class="form-label">驳回理由</label>
                        <textarea class="form-control" id="batchRejectReason" name="review_comment" rows="3" placeholder="请输入驳回理由..."></textarea>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">已选择 <span id="selectedCount">0</span> 篇文案</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitBatchReview()">确认审核</button>
            </div>
        </div>
    </div>
</div>

<!-- 驳回理由模态框 -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">驳回文案</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="rejectForm">
                    <div class="mb-3">
                        <label for="rejectReason" class="form-label">驳回理由</label>
                        <textarea class="form-control" id="rejectReason" name="review_comment" rows="4" placeholder="请输入驳回理由..." required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="submitReject()">确认驳回</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentContentId = null;

// 全选/取消全选
function toggleSelectAll(checkbox) {
    const checkboxes = document.querySelectorAll('.content-checkbox');
    checkboxes.forEach(cb => cb.checked = checkbox.checked);
    updateSelectedCount();
}

// 更新选中数量
function updateSelectedCount() {
    const selected = document.querySelectorAll('.content-checkbox:checked');
    document.getElementById('selectedCount').textContent = selected.length;
}

// 监听复选框变化
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('content-checkbox')) {
        updateSelectedCount();
    }
});

// 注意：viewContent, approveContent, rejectContent 函数已在页面顶部定义

// 提交驳回
function submitReject() {
    const reason = document.getElementById('rejectReason').value.trim();
    if (!reason) {
        alert('请输入驳回理由');
        return;
    }
    
    const formData = new FormData();
    formData.append('action', 'reject');
    formData.append('review_comment', reason);
    
    fetch(`/simple/api/final-review/${currentContentId}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('rejectModal')).hide();
            location.reload();
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('驳回失败: ' + error.message, 'error');
    });
}

// 显示批量审核模态框
function showBatchReviewModal() {
    const selected = document.querySelectorAll('.content-checkbox:checked');
    if (selected.length === 0) {
        alert('请先选择要审核的文案');
        return;
    }
    
    updateSelectedCount();
    const modal = new bootstrap.Modal(document.getElementById('batchReviewModal'));
    modal.show();
}

// 批量审核动作变化
document.addEventListener('change', function(e) {
    if (e.target.name === 'action') {
        const rejectReasonDiv = document.getElementById('rejectReasonDiv');
        if (e.target.value === 'reject') {
            rejectReasonDiv.style.display = 'block';
        } else {
            rejectReasonDiv.style.display = 'none';
        }
    }
});

// 提交批量审核
function submitBatchReview() {
    const selected = document.querySelectorAll('.content-checkbox:checked');
    if (selected.length === 0) {
        alert('请先选择要审核的文案');
        return;
    }
    
    const action = document.querySelector('input[name="action"]:checked').value;
    const reason = document.getElementById('batchRejectReason').value.trim();
    
    if (action === 'reject' && !reason) {
        alert('批量驳回时必须填写驳回理由');
        return;
    }
    
    const contentIds = Array.from(selected).map(cb => cb.value);
    
    const formData = new FormData();
    formData.append('content_ids', contentIds.join(','));
    formData.append('action', action);
    if (reason) {
        formData.append('review_comment', reason);
    }
    
    fetch('/simple/api/final-review/batch', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('batchReviewModal')).hide();
            location.reload();
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('批量审核失败: ' + error.message, 'error');
    });
}

// 注意：showToast, showImageModal, showContentModal 函数已在页面顶部定义

console.log('最终审核页面已加载');
</script>
