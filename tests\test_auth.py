"""
认证功能测试
"""
import pytest
from flask import session, url_for
from flask_login import current_user

from app.models.user import User


def test_login_page(client):
    """测试登录页面是否可以访问"""
    response = client.get('/auth/login')
    assert response.status_code == 200
    assert '用户登录' in response.data.decode('utf-8')


def test_login_success(client, app):
    """测试登录成功"""
    response = client.post('/auth/login', data={
        'username': 'admin',
        'password': 'password',
        'remember_me': False
    }, follow_redirects=True)
    
    assert response.status_code == 200
    assert '登录成功' in response.data.decode('utf-8')
    
    with client.session_transaction() as sess:
        assert '_user_id' in sess
        
    with app.test_request_context():
        assert current_user.is_authenticated


def test_login_failed(client):
    """测试登录失败"""
    response = client.post('/auth/login', data={
        'username': 'admin',
        'password': 'wrong_password',
        'remember_me': False
    }, follow_redirects=True)
    
    assert response.status_code == 200
    assert '用户名或密码错误' in response.data.decode('utf-8')


def test_logout(client):
    """测试注销"""
    # 先登录
    client.post('/auth/login', data={
        'username': 'admin',
        'password': 'password',
        'remember_me': False
    })
    
    # 然后注销
    response = client.get('/auth/logout', follow_redirects=True)
    assert response.status_code == 200
    assert '已安全退出' in response.data.decode('utf-8')


def test_access_protected_page(client):
    """测试访问受保护页面"""
    # 未登录状态访问受保护页面
    response = client.get('/system/', follow_redirects=True)
    assert response.status_code == 200
    assert '请先登录' in response.data.decode('utf-8')
    
    # 登录后访问
    client.post('/auth/login', data={
        'username': 'admin',
        'password': 'password',
        'remember_me': False
    })
    
    response = client.get('/system/')
    assert response.status_code == 200
    assert '系统设置' in response.data.decode('utf-8')


def test_permission_required(client):
    """测试权限控制"""
    # 编辑用户登录（无管理员权限）
    client.post('/auth/login', data={
        'username': 'editor',
        'password': 'password',
        'remember_me': False
    })
    
    # 访问需要管理员权限的页面
    response = client.get('/system/', follow_redirects=True)
    assert response.status_code == 403
    assert '权限不足' in response.data.decode('utf-8') 