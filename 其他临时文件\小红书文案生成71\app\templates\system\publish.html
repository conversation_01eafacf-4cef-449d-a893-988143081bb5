{% extends "base.html" %}

{% block title %}发布设置{% endblock %}

{% block content_auth %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">发布设置</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('system.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回系统设置
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5>超时设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.publish_timeout.label(class="form-label") }}
                                    {{ form.publish_timeout(class="form-control") }}
                                    {% if form.publish_timeout.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.publish_timeout.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">设置发布超时时间（小时）</small>
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.timeout_action.label(class="form-label") }}
                                    {{ form.timeout_action(class="form-select") }}
                                    {% if form.timeout_action.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.timeout_action.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">设置发布超时后的处理策略</small>
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.timeout_notify_roles.label(class="form-label") }}
                                    {{ form.timeout_notify_roles(class="form-select") }}
                                    {% if form.timeout_notify_roles.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.timeout_notify_roles.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">设置发布超时通知的接收角色</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                            <a href="{{ url_for('system.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 超时处理流程说明 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h3 class="card-title">超时处理流程说明</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-header bg-info text-white">
                                    <h5 class="card-title mb-0">仅通知</h5>
                                </div>
                                <div class="card-body">
                                    <p>当文案发布超时时，系统将发送通知给指定角色，但不会对文案状态进行任何更改。</p>
                                    <p>适用场景：需要人工干预处理超时文案。</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="card-title mb-0">自动重试</h5>
                                </div>
                                <div class="card-body">
                                    <p>当文案发布超时时，系统将自动将文案状态重置为"待发布"，并发送通知给指定角色。</p>
                                    <p>适用场景：临时网络问题导致的发布失败。</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="card-title mb-0">标记为失败</h5>
                                </div>
                                <div class="card-body">
                                    <p>当文案发布超时时，系统将自动将文案状态标记为"发布失败"，并发送通知给指定角色。</p>
                                    <p>适用场景：严格控制发布时间，超时即视为失败。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 