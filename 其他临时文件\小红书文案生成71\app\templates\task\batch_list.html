{% extends "base.html" %}

{% block title %}批次管理{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">批次管理</h2>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-light">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <div class="form-group">
                        {{ form.task_id.label(class="form-label") }}
                        {{ form.task_id(class="form-select") }}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        {{ form.search.label(class="form-label") }}
                        {{ form.search(class="form-control", value=search, placeholder="搜索批次名称") }}
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        {{ form.sort_by.label(class="form-label") }}
                        {{ form.sort_by(class="form-select") }}
                    </div>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> 筛选
                    </button>
                </div>
            </form>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>ID</th>
                            <th>批次名称</th>
                            <th>所属任务</th>
                            <th>文案数量</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for batch in batches %}
                        <tr>
                            <td>{{ batch.id }}</td>
                            <td>{{ batch.name }}</td>
                            <td>
                                <a href="{{ url_for('task.task_view', task_id=batch.task_id) }}" class="text-decoration-none">
                                    {{ batch.task.name }}
                                </a>
                            </td>
                            <td>{{ batch.content_count }}</td>
                            <td>{{ batch.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('task.task_view', task_id=batch.task_id) }}" class="btn btn-outline-info" title="查看任务">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    {% if current_user.has_permission('batch_edit') %}
                                    <a href="{{ url_for('task.batch_edit', batch_id=batch.id) }}" class="btn btn-outline-primary" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    
                                    {% if current_user.has_permission('batch_delete') %}
                                    <button type="button" class="btn btn-outline-danger" onclick="confirmDelete({{ batch.id }})" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="6" class="text-center py-4">暂无批次数据</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        {% if batches %}
        <div class="card-footer">
            {% if pagination.pages > 1 %}
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    {% if pagination.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('task.batch_list', page=pagination.prev_num, task_id=task_id, search=search, sort_by=sort_by, sort_order=sort_order) }}">
                            <span>&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">&laquo;</span>
                    </li>
                    {% endif %}
                    
                    {% for page in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                        {% if page %}
                            {% if page == pagination.page %}
                            <li class="page-item active">
                                <span class="page-link">{{ page }}</span>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('task.batch_list', page=page, task_id=task_id, search=search, sort_by=sort_by, sort_order=sort_order) }}">{{ page }}</a>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if pagination.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('task.batch_list', page=pagination.next_num, task_id=task_id, search=search, sort_by=sort_by, sort_order=sort_order) }}">
                            <span>&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">&raquo;</span>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个批次吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" action="">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 确认删除
    function confirmDelete(batchId) {
        document.getElementById('deleteForm').action = `/batches/${batchId}/delete`;
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }
</script>
{% endblock %} 