#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统健康检查工具
"""

import os
import time
import psutil
from datetime import datetime, timedelta
from flask import current_app
from app import db
from app.models.content import Content
from app.models.image import ContentImage
from app.models.client import ClientShareLink

class HealthChecker:
    """系统健康检查器"""
    
    @staticmethod
    def check_database():
        """检查数据库连接"""
        try:
            # 执行简单查询测试连接
            db.session.execute('SELECT 1')
            return {
                'status': 'healthy',
                'message': '数据库连接正常',
                'response_time': 0
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'数据库连接失败: {str(e)}',
                'response_time': 0
            }
    
    @staticmethod
    def check_disk_space():
        """检查磁盘空间"""
        try:
            # 检查上传目录的磁盘空间
            upload_path = current_app.config.get('UPLOAD_FOLDER', 'app/static/uploads')
            if not os.path.exists(upload_path):
                os.makedirs(upload_path, exist_ok=True)
            
            disk_usage = psutil.disk_usage(upload_path)
            free_space_gb = disk_usage.free / (1024**3)
            total_space_gb = disk_usage.total / (1024**3)
            usage_percent = (disk_usage.used / disk_usage.total) * 100
            
            status = 'healthy'
            message = f'磁盘空间充足: {free_space_gb:.1f}GB 可用'
            
            if usage_percent > 90:
                status = 'critical'
                message = f'磁盘空间不足: 使用率 {usage_percent:.1f}%'
            elif usage_percent > 80:
                status = 'warning'
                message = f'磁盘空间紧张: 使用率 {usage_percent:.1f}%'
            
            return {
                'status': status,
                'message': message,
                'details': {
                    'total_gb': round(total_space_gb, 1),
                    'free_gb': round(free_space_gb, 1),
                    'usage_percent': round(usage_percent, 1)
                }
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'磁盘空间检查失败: {str(e)}',
                'details': {}
            }
    
    @staticmethod
    def check_memory_usage():
        """检查内存使用情况"""
        try:
            memory = psutil.virtual_memory()
            usage_percent = memory.percent
            available_gb = memory.available / (1024**3)
            
            status = 'healthy'
            message = f'内存使用正常: {usage_percent:.1f}%'
            
            if usage_percent > 90:
                status = 'critical'
                message = f'内存使用过高: {usage_percent:.1f}%'
            elif usage_percent > 80:
                status = 'warning'
                message = f'内存使用较高: {usage_percent:.1f}%'
            
            return {
                'status': status,
                'message': message,
                'details': {
                    'usage_percent': round(usage_percent, 1),
                    'available_gb': round(available_gb, 1),
                    'total_gb': round(memory.total / (1024**3), 1)
                }
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'内存检查失败: {str(e)}',
                'details': {}
            }
    
    @staticmethod
    def check_upload_directory():
        """检查上传目录"""
        try:
            upload_path = current_app.config.get('UPLOAD_FOLDER', 'app/static/uploads')
            
            # 检查目录是否存在
            if not os.path.exists(upload_path):
                return {
                    'status': 'unhealthy',
                    'message': '上传目录不存在',
                    'details': {'path': upload_path}
                }
            
            # 检查目录是否可写
            if not os.access(upload_path, os.W_OK):
                return {
                    'status': 'unhealthy',
                    'message': '上传目录不可写',
                    'details': {'path': upload_path}
                }
            
            # 统计文件数量和大小
            total_files = 0
            total_size = 0
            
            for root, dirs, files in os.walk(upload_path):
                total_files += len(files)
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(file_path)
                    except OSError:
                        pass
            
            total_size_mb = total_size / (1024**2)
            
            return {
                'status': 'healthy',
                'message': '上传目录正常',
                'details': {
                    'path': upload_path,
                    'total_files': total_files,
                    'total_size_mb': round(total_size_mb, 1)
                }
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'上传目录检查失败: {str(e)}',
                'details': {}
            }
    
    @staticmethod
    def check_workflow_health():
        """检查工作流健康状态"""
        try:
            # 检查是否有长时间停留在某个状态的文案
            one_week_ago = datetime.now() - timedelta(days=7)
            
            # 检查长时间待审核的文案
            long_pending_review = Content.query.filter(
                Content.workflow_status == 'pending_review',
                Content.created_at < one_week_ago
            ).count()
            
            # 检查长时间待上传图片的文案
            long_pending_upload = Content.query.filter(
                Content.workflow_status == 'pending_image_upload',
                Content.updated_at < one_week_ago
            ).count()
            
            # 检查长时间待最终审核的文案
            long_pending_final = Content.query.filter(
                Content.workflow_status == 'pending_final_review',
                Content.updated_at < one_week_ago
            ).count()
            
            # 检查长时间待客户审核的文案
            long_pending_client = Content.query.filter(
                Content.workflow_status == 'pending_client_review',
                Content.updated_at < one_week_ago
            ).count()
            
            issues = []
            if long_pending_review > 0:
                issues.append(f'{long_pending_review} 篇文案待初审超过一周')
            if long_pending_upload > 0:
                issues.append(f'{long_pending_upload} 篇文案待上传图片超过一周')
            if long_pending_final > 0:
                issues.append(f'{long_pending_final} 篇文案待最终审核超过一周')
            if long_pending_client > 0:
                issues.append(f'{long_pending_client} 篇文案待客户审核超过一周')
            
            if issues:
                return {
                    'status': 'warning',
                    'message': '工作流存在积压',
                    'details': {'issues': issues}
                }
            else:
                return {
                    'status': 'healthy',
                    'message': '工作流运行正常',
                    'details': {}
                }
                
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'工作流检查失败: {str(e)}',
                'details': {}
            }
    
    @staticmethod
    def check_expired_share_links():
        """检查过期的分享链接"""
        try:
            # 查找过期但仍活跃的分享链接
            expired_active_links = ClientShareLink.query.filter(
                ClientShareLink.expires_at < datetime.now(),
                ClientShareLink.is_active == True
            ).count()
            
            # 查找即将过期的分享链接（3天内）
            three_days_later = datetime.now() + timedelta(days=3)
            expiring_soon_links = ClientShareLink.query.filter(
                ClientShareLink.expires_at < three_days_later,
                ClientShareLink.expires_at > datetime.now(),
                ClientShareLink.is_active == True
            ).count()
            
            issues = []
            if expired_active_links > 0:
                issues.append(f'{expired_active_links} 个分享链接已过期但仍活跃')
            if expiring_soon_links > 0:
                issues.append(f'{expiring_soon_links} 个分享链接即将过期')
            
            if issues:
                status = 'warning' if expired_active_links == 0 else 'unhealthy'
                return {
                    'status': status,
                    'message': '分享链接需要维护',
                    'details': {'issues': issues}
                }
            else:
                return {
                    'status': 'healthy',
                    'message': '分享链接状态正常',
                    'details': {}
                }
                
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'分享链接检查失败: {str(e)}',
                'details': {}
            }
    
    @staticmethod
    def check_orphaned_images():
        """检查孤立的图片文件"""
        try:
            # 查找数据库中的图片记录
            db_images = set()
            for image in ContentImage.query.all():
                if image.image_path:
                    db_images.add(image.image_path)
                if image.thumbnail_path:
                    db_images.add(image.thumbnail_path)
            
            # 扫描上传目录中的文件
            upload_path = current_app.config.get('UPLOAD_FOLDER', 'app/static/uploads')
            if not os.path.exists(upload_path):
                return {
                    'status': 'healthy',
                    'message': '上传目录不存在，无孤立文件',
                    'details': {}
                }
            
            file_system_images = set()
            for root, dirs, files in os.walk(upload_path):
                for file in files:
                    if file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
                        rel_path = os.path.relpath(os.path.join(root, file), upload_path)
                        file_system_images.add(rel_path.replace('\\', '/'))
            
            # 找出孤立的文件
            orphaned_files = file_system_images - db_images
            orphaned_count = len(orphaned_files)
            
            if orphaned_count > 0:
                return {
                    'status': 'warning',
                    'message': f'发现 {orphaned_count} 个孤立的图片文件',
                    'details': {
                        'orphaned_count': orphaned_count,
                        'sample_files': list(orphaned_files)[:10]  # 只显示前10个
                    }
                }
            else:
                return {
                    'status': 'healthy',
                    'message': '没有发现孤立的图片文件',
                    'details': {}
                }
                
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'孤立文件检查失败: {str(e)}',
                'details': {}
            }
    
    @classmethod
    def run_full_health_check(cls):
        """运行完整的健康检查"""
        checks = {
            'database': cls.check_database(),
            'disk_space': cls.check_disk_space(),
            'memory': cls.check_memory_usage(),
            'upload_directory': cls.check_upload_directory(),
            'workflow': cls.check_workflow_health(),
            'share_links': cls.check_expired_share_links(),
            'orphaned_images': cls.check_orphaned_images()
        }
        
        # 计算总体健康状态
        overall_status = 'healthy'
        critical_count = 0
        warning_count = 0
        
        for check_name, result in checks.items():
            if result['status'] == 'critical' or result['status'] == 'unhealthy':
                critical_count += 1
                overall_status = 'unhealthy'
            elif result['status'] == 'warning':
                warning_count += 1
                if overall_status == 'healthy':
                    overall_status = 'warning'
        
        return {
            'overall_status': overall_status,
            'summary': {
                'total_checks': len(checks),
                'critical_issues': critical_count,
                'warnings': warning_count,
                'healthy': len(checks) - critical_count - warning_count
            },
            'checks': checks,
            'timestamp': datetime.now().isoformat()
        }
