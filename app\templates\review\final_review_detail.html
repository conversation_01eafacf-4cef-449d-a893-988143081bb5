<!-- 客户最终审核详情页面 -->
<style>
.upload-drop-zone {
    transition: all 0.3s ease;
}

.upload-drop-zone:hover {
    border-color: #0d6efd !important;
    background-color: #f8f9fa !important;
}

.upload-drop-zone.border-primary {
    border-color: #0d6efd !important;
    background-color: #e7f1ff !important;
}

/* 图片预览模态框样式 - 确保显示在最前面 */
#imageModal {
    z-index: 1070 !important;
}

#imageModal.show {
    z-index: 1070 !important;
}

/* 确保backdrop正确清理 */
.modal-backdrop {
    transition: opacity 0.15s linear;
}

.modal-backdrop.show {
    opacity: 0.5;
}

/* 防止backdrop残留 */
body.modal-open .modal-backdrop:not(.show) {
    display: none !important;
}

.content-preview-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
    margin: 5px 0;
}

/* 自定义驳回模态框样式 */
#customRejectModal .modal-header {
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

#customRejectModal .quick-reason-btn {
    transition: all 0.2s ease;
    border-radius: 20px;
    font-size: 12px;
    padding: 4px 12px;
}
</style>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <div>
                <h2><i class="bi bi-check-circle"></i> 最终审核 - {{ client.name }}</h2>
                <p class="text-muted">为该客户的文案进行最终审核</p>
            </div>
            <a href="{{ url_for('main_simple.final_review') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回客户列表
            </a>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card text-center status-card" data-status="" onclick="filterByStatus('')">
                <div class="card-body">
                    <h4 class="text-primary" id="totalCount">{{ content_data|length }}</h4>
                    <small class="text-muted">全部文案</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center status-card" data-status="image_uploaded" onclick="filterByStatus('image_uploaded')">
                <div class="card-body">
                    <h4 class="text-warning" id="imageUploadedCount">{{ status_counts.image_uploaded or 0 }}</h4>
                    <small class="text-muted">待最终审核</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center status-card" data-status="final_review" onclick="filterByStatus('final_review')">
                <div class="card-body">
                    <h4 class="text-info" id="finalReviewCount">{{ status_counts.final_review or 0 }}</h4>
                    <small class="text-muted">审核中</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-success" id="totalImageCount">
                        {% set total_images = 0 %}
                        {% for item in content_data %}
                            {% set total_images = total_images + item.image_count %}
                        {% endfor %}
                        {{ total_images }}
                    </h4>
                    <small class="text-muted">总图片数</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-secondary" id="completedCount">0</h4>
                    <small class="text-muted">已完成数</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="task_filter" class="form-label">任务</label>
                    <select class="form-select form-select-sm" id="task_filter" name="task_id">
                        <option value="">全部任务</option>
                        {% for task in tasks %}
                        <option value="{{ task.id }}"
                                {% if request.args.get('task_id') == task.id|string %}selected{% endif %}>
                            {{ task.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="status_filter" class="form-label">状态</label>
                    <select class="form-select form-select-sm" id="status_filter" name="status">
                        <option value="">全部状态</option>
                        <option value="image_uploaded" {% if request.args.get('status') == 'image_uploaded' %}selected{% endif %}>
                            待最终审核
                        </option>
                        <option value="final_review" {% if request.args.get('status') == 'final_review' %}selected{% endif %}>
                            审核中
                        </option>
                    </select>
                </div>

                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-primary btn-sm" id="searchBtn" onclick="performSearch(1, 20)">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetFilters()">
                            <i class="bi bi-arrow-clockwise"></i> 重置
                        </button>
                    </div>
                </div>

                <div class="col-md-3">
                    <label class="form-label">批量操作</label>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success btn-sm" onclick="batchApprove()">
                            <i class="bi bi-check-circle"></i> 批量通过
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="batchReject()">
                            <i class="bi bi-x-circle"></i> 批量驳回
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 文案列表 -->
    <div class="card">
        <div class="card-body">
            {% if content_data %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" class="form-check-input" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th width="80">ID</th>
                                <th>标题</th>
                                <th width="100">客户</th>
                                <th width="100">状态</th>
                                <th width="120">创建时间</th>
                                <th width="80">图片数量</th>
                                <th width="200">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in content_data %}
                            {% set status_class = '' %}
                            {% if item.content.workflow_status == 'final_review' %}
                                {% set status_class = 'final_review' %}
                            {% elif item.content.workflow_status == 'image_uploaded' %}
                                {% set status_class = 'image_uploaded' %}
                            {% endif %}
                            <tr data-content-id="{{ item.content.id }}" data-status="{{ status_class }}" class="status-filter-row">
                                <!-- 复选框 -->
                                <td>
                                    <input type="checkbox"
                                           class="form-check-input content-checkbox"
                                           data-content-id="{{ item.content.id }}"
                                           data-image-count="{{ item.image_count }}">
                                </td>
                                <!-- 文案ID -->
                                <td>{{ item.content.id }}</td>

                                <!-- 文案标题 -->
                                <td>
                                    <div class="fw-bold">{{ item.content.title }}</div>
                                </td>

                                <!-- 客户信息 -->
                                <td>
                                    <span class="badge bg-info">{{ item.content.client.name if item.content.client else '未知' }}</span>
                                </td>

                                <!-- 状态信息 -->
                                <td>
                                    {% if item.content.workflow_status == 'final_review' %}
                                        <span class="badge bg-info">审核中</span>
                                    {% elif item.content.workflow_status == 'image_uploaded' %}
                                        <span class="badge bg-warning">待最终审核</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ item.content.workflow_status }}</span>
                                    {% endif %}
                                </td>

                                <!-- 创建时间 -->
                                <td>
                                    <small class="text-muted">{{ item.content.created_at.strftime('%m-%d %H:%M') if item.content.created_at else '' }}</small>
                                </td>

                                <!-- 图片数量 -->
                                <td>
                                    <span class="badge bg-light text-dark" data-content-id="{{ item.content.id }}">
                                        {{ item.image_count }}张图片
                                    </span>
                                </td>

                                <!-- 操作按钮 -->
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                                onclick="viewContent({{ item.content.id }})" title="查看详情">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-success btn-sm" 
                                                onclick="approveContent({{ item.content.id }})" title="通过审核">
                                            <i class="bi bi-check-circle"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                                onclick="showRejectModal({{ item.content.id }})" title="驳回">
                                            <i class="bi bi-x-circle"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                {% if pagination.pages > 1 %}
                <nav aria-label="分页导航" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="javascript:void(0)" onclick="performSearch({{ pagination.prev_num }}, {{ per_page }})">上一页</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in pagination.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="javascript:void(0)" onclick="performSearch({{ page_num }}, {{ per_page }})">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="javascript:void(0)" onclick="performSearch({{ pagination.next_num }}, {{ per_page }})">下一页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-check-circle text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">暂无待最终审核的文案</h4>
                    <p class="text-muted">该客户暂时没有需要最终审核的文案</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 自定义驳回模态框 -->
<div class="modal fade" id="customRejectModal" tabindex="-1" aria-labelledby="customRejectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-danger text-white">
            <div class="modal-header">
                <h5 class="modal-title" id="customRejectModalLabel">
                    <i class="bi bi-x-circle me-2"></i>驳回文案
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="rejectForm">
                    <input type="hidden" id="rejectContentId" name="content_id">

                    <!-- 驳回类型选择 -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">驳回类型：</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="rejection_type" id="rejectText" value="content" checked>
                                    <label class="form-check-label" for="rejectText">
                                        <i class="bi bi-file-text me-1"></i>文案问题
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="rejection_type" id="rejectImage" value="image">
                                    <label class="form-check-label" for="rejectImage">
                                        <i class="bi bi-image me-1"></i>图片问题
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="rejection_type" id="rejectBoth" value="both">
                                    <label class="form-check-label" for="rejectBoth">
                                        <i class="bi bi-exclamation-triangle me-1"></i>两者都有问题
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速理由选择 -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">快速选择理由：</label>
                        <div class="d-flex flex-wrap gap-2 mb-2">
                            <button type="button" class="btn btn-outline-light btn-sm quick-reason-btn" onclick="addQuickReason('内容不符合要求')">内容不符合要求</button>
                            <button type="button" class="btn btn-outline-light btn-sm quick-reason-btn" onclick="addQuickReason('图片质量不佳')">图片质量不佳</button>
                            <button type="button" class="btn btn-outline-light btn-sm quick-reason-btn" onclick="addQuickReason('格式不正确')">格式不正确</button>
                            <button type="button" class="btn btn-outline-light btn-sm quick-reason-btn" onclick="addQuickReason('需要重新编辑')">需要重新编辑</button>
                            <button type="button" class="btn btn-outline-light btn-sm quick-reason-btn" onclick="addQuickReason('与品牌调性不符')">与品牌调性不符</button>
                        </div>
                    </div>

                    <!-- 详细理由输入 -->
                    <div class="mb-3">
                        <label for="rejectReason" class="form-label fw-bold">详细驳回理由：</label>
                        <textarea class="form-control" id="rejectReason" name="review_comment" rows="4"
                                  placeholder="请详细说明驳回理由，以便创作者了解需要改进的地方..." required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-light text-danger fw-bold" onclick="submitReject()">
                    <i class="bi bi-x-circle me-1"></i>确认驳回
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 全局变量
let currentContentId = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('=== 最终审核详情页面JavaScript开始执行 ===');

    // 绑定任务选择变更事件
    const taskSelect = document.getElementById('task_filter');
    if (taskSelect) {
        taskSelect.onchange = function() {
            console.log('=== 任务选择变更 ===');
            console.log('选择的任务ID:', this.value);
            performSearch(1, 20);
        };
    }

    // 绑定状态选择变更事件
    const statusSelect = document.getElementById('status_filter');
    if (statusSelect) {
        statusSelect.onchange = function() {
            console.log('=== 状态选择变更 ===');
            console.log('选择的状态:', this.value);
            performSearch(1, 20);
        };
    }

    console.log('✅ 最终审核详情页面初始化完成');
});

// 执行搜索的函数
function performSearch(page = 1, perPage = 20) {
    console.log('=== 执行搜索 ===');

    const taskSelect = document.getElementById('task_filter');
    const statusSelect = document.getElementById('status_filter');

    const taskId = taskSelect ? taskSelect.value : '';
    const status = statusSelect ? statusSelect.value : '';

    console.log('搜索参数:', { taskId, status, page, perPage });

    // 构建查询参数
    const params = new URLSearchParams();
    if (taskId) params.append('task_id', taskId);
    if (status) params.append('status', status);
    params.append('page', page);
    params.append('per_page', perPage);

    // 获取当前客户ID（从URL路径中获取）
    const pathParts = window.location.pathname.split('/');
    const clientId = pathParts[pathParts.length - 1];

    const url = `/simple/final-review/client/${clientId}?${params.toString()}`;
    console.log('请求URL:', url);

    // 显示加载状态
    const contentTable = document.querySelector('tbody');
    if (contentTable) {
        contentTable.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">正在搜索...</div>
                </td>
            </tr>
        `;
    }

    // 发送AJAX请求
    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('搜索响应状态:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.text();
    })
    .then(html => {
        console.log('搜索响应成功，更新页面内容');

        // 解析返回的HTML
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // 更新表格内容
        const newTableBody = doc.querySelector('tbody');
        if (newTableBody && contentTable) {
            contentTable.innerHTML = newTableBody.innerHTML;
            console.log('✅ 表格内容已更新');
        }

        // 更新统计信息
        updateStatistics(doc);

        // 更新分页
        updatePagination(doc);
    })
    .catch(error => {
        console.error('搜索失败:', error);
        if (contentTable) {
            contentTable.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <i class="bi bi-exclamation-triangle text-warning fs-1"></i>
                        <h5 class="text-muted mt-3">搜索失败</h5>
                        <p class="text-muted">请刷新页面重试</p>
                    </td>
                </tr>
            `;
        }
    });
}

// 更新统计信息
function updateStatistics(doc) {
    // 更新各种统计数字
    const totalCountElement = document.getElementById('totalCount');
    const imageUploadedCountElement = document.getElementById('imageUploadedCount');
    const finalReviewCountElement = document.getElementById('finalReviewCount');

    const newTotalCount = doc.getElementById('totalCount');
    const newImageUploadedCount = doc.getElementById('imageUploadedCount');
    const newFinalReviewCount = doc.getElementById('finalReviewCount');

    if (totalCountElement && newTotalCount) {
        totalCountElement.textContent = newTotalCount.textContent;
    }
    if (imageUploadedCountElement && newImageUploadedCount) {
        imageUploadedCountElement.textContent = newImageUploadedCount.textContent;
    }
    if (finalReviewCountElement && newFinalReviewCount) {
        finalReviewCountElement.textContent = newFinalReviewCount.textContent;
    }
}

// 更新分页
function updatePagination(doc) {
    const currentPagination = document.querySelector('nav[aria-label="分页导航"]');
    const newPagination = doc.querySelector('nav[aria-label="分页导航"]');

    if (currentPagination && newPagination) {
        currentPagination.innerHTML = newPagination.innerHTML;
    } else if (currentPagination && !newPagination) {
        currentPagination.remove();
    }
}

// 重置筛选条件的函数
function resetFilters() {
    console.log('=== 重置筛选条件 ===');

    const taskSelect = document.getElementById('task_filter');
    const statusSelect = document.getElementById('status_filter');

    // 重置任务选择为"全部任务"
    if (taskSelect) {
        taskSelect.value = '';
        console.log('重置任务选择为全部任务');
    }

    // 重置状态选择为"全部状态"
    if (statusSelect) {
        statusSelect.value = '';
        console.log('重置状态选择为全部状态');
    }

    // 执行搜索显示所有数据
    console.log('执行搜索显示所有数据');
    performSearch(1, 20);
}

// 按状态筛选
function filterByStatus(status) {
    const statusSelect = document.getElementById('status_filter');
    if (statusSelect) {
        statusSelect.value = status;
        performSearch(1, 20);
    }
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const contentCheckboxes = document.querySelectorAll('.content-checkbox');

    contentCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
}

// 查看文案详情
function viewContent(contentId) {
    console.log('查看文案详情，ID:', contentId);
    fetch(`/simple/api/contents/${contentId}/view`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        showContentModal('查看文案详情', html);
    })
    .catch(error => {
        console.error('加载文案详情失败:', error);
        alert('加载文案详情失败，请重试');
    });
}

// 审核通过
function approveContent(contentId) {
    // 直接执行通过，不再二次确认
    const formData = new FormData();
    formData.append('action', 'approve');
    formData.append('review_comment', '通过审核');

    fetch(`/simple/api/final-review/${contentId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('审核通过成功！', 'success');
            // 从列表中移除该行
            removeContentFromList(contentId);
        } else {
            showToast('操作失败：' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('审核失败:', error);
        showToast('审核失败，请重试', 'error');
    });
}

// 显示驳回模态框
function showRejectModal(contentId) {
    currentContentId = contentId;
    document.getElementById('rejectContentId').value = contentId;
    document.getElementById('rejectReason').value = '';

    const modal = new bootstrap.Modal(document.getElementById('customRejectModal'));
    modal.show();
}

// 添加快速理由
function addQuickReason(reason) {
    const textarea = document.getElementById('rejectReason');
    const currentValue = textarea.value.trim();

    if (currentValue) {
        textarea.value = currentValue + '\n' + reason;
    } else {
        textarea.value = reason;
    }
}

// 提交驳回
function submitReject() {
    const contentId = document.getElementById('rejectContentId').value;
    const rejectionType = document.querySelector('input[name="rejection_type"]:checked').value;
    const reviewComment = document.getElementById('rejectReason').value.trim();

    if (!reviewComment) {
        alert('请填写驳回理由');
        return;
    }

    const formData = new FormData();
    formData.append('action', 'reject');
    formData.append('rejection_type', rejectionType);
    formData.append('review_comment', reviewComment);

    fetch(`/simple/api/final-review/${contentId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('驳回成功！', 'success');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('customRejectModal'));
            modal.hide();
            // 从列表中移除该行
            removeContentFromList(contentId);
        } else {
            showToast('操作失败：' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('驳回失败:', error);
        showToast('驳回失败，请重试', 'error');
    });
}

// 从列表中移除内容行
function removeContentFromList(contentId) {
    const row = document.querySelector(`tr[data-content-id="${contentId}"]`);
    if (row) {
        // 获取该行的图片数量（用于更新统计）
        const imageCountBadge = row.querySelector('.badge.bg-success');
        let imageCount = 0;
        if (imageCountBadge) {
            const badgeText = imageCountBadge.textContent.trim();
            const match = badgeText.match(/(\d+)\s*张图片/);
            imageCount = match ? parseInt(match[1]) : 0;
        }

        row.remove();

        // 更新统计数字
        updateStatisticsAfterRemoval(imageCount);

        // 检查是否还有数据
        const remainingRows = document.querySelectorAll('tbody tr[data-content-id]');
        if (remainingRows.length === 0) {
            // 显示空数据提示
            const tbody = document.querySelector('tbody');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center py-5">
                            <i class="bi bi-check-circle text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">暂无待最终审核的文案</h4>
                            <p class="text-muted">该客户暂时没有需要最终审核的文案</p>
                        </td>
                    </tr>
                `;
            }
        }
    }
}

// 更新统计数字（移除内容后）
function updateStatisticsAfterRemoval(removedImageCount) {
    // 更新"全部文案"数量
    const totalCountElement = document.getElementById('totalCount');
    if (totalCountElement) {
        const currentCount = parseInt(totalCountElement.textContent) || 0;
        totalCountElement.textContent = Math.max(0, currentCount - 1);
    }

    // 更新"待最终审核"数量
    const imageUploadedCountElement = document.getElementById('imageUploadedCount');
    if (imageUploadedCountElement) {
        const currentCount = parseInt(imageUploadedCountElement.textContent) || 0;
        imageUploadedCountElement.textContent = Math.max(0, currentCount - 1);
    }

    // 更新"总图片数"
    const totalImageCountElement = document.getElementById('totalImageCount');
    if (totalImageCountElement) {
        const currentCount = parseInt(totalImageCountElement.textContent) || 0;
        totalImageCountElement.textContent = Math.max(0, currentCount - removedImageCount);
    }

    // 更新"已完成数"（增加1）
    const completedCountElement = document.getElementById('completedCount');
    if (completedCountElement) {
        const currentCount = parseInt(completedCountElement.textContent) || 0;
        completedCountElement.textContent = currentCount + 1;
    }
}

// 批量通过
function batchApprove() {
    const selectedCheckboxes = document.querySelectorAll('.content-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        alert('请先选择要操作的文案');
        return;
    }

    if (!confirm(`确定要批量通过 ${selectedCheckboxes.length} 篇文案吗？`)) {
        return;
    }

    const contentIds = Array.from(selectedCheckboxes).map(cb => cb.dataset.contentId);

    // 逐个处理
    let processedCount = 0;
    contentIds.forEach(contentId => {
        const formData = new FormData();
        formData.append('action', 'approve');
        formData.append('review_comment', '批量通过审核');

        fetch(`/simple/api/final-review/${contentId}`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            processedCount++;
            if (data.success) {
                removeContentFromList(contentId);
            }

            if (processedCount === contentIds.length) {
                showToast(`批量操作完成，成功处理 ${processedCount} 篇文案`, 'success');
            }
        })
        .catch(error => {
            console.error('批量审核失败:', error);
            processedCount++;
            if (processedCount === contentIds.length) {
                showToast('批量操作完成，部分可能失败', 'warning');
            }
        });
    });
}

// 批量驳回
function batchReject() {
    const selectedCheckboxes = document.querySelectorAll('.content-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        alert('请先选择要操作的文案');
        return;
    }

    alert('批量驳回功能需要逐个设置驳回理由，请使用单个驳回操作');
}

// 显示内容模态框
function showContentModal(title, content) {
    // 创建或获取模态框
    let modal = document.getElementById('contentViewModal');
    if (!modal) {
        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="contentViewModal" tabindex="-1" aria-labelledby="contentViewModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="contentViewModalLabel">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body" id="contentViewModalBody">
                            ${content}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        modal = document.getElementById('contentViewModal');
    } else {
        // 更新现有模态框内容
        document.getElementById('contentViewModalLabel').textContent = title;
        document.getElementById('contentViewModalBody').innerHTML = content;
    }

    // 显示模态框
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        const containerHtml = `
            <div class="toast-container position-fixed top-0 end-0 p-3" id="toastContainer" style="z-index: 1100;">
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', containerHtml);
        toastContainer = document.getElementById('toastContainer');
    }

    // 确定toast样式
    let bgClass = 'bg-info';
    let icon = 'bi-info-circle';

    switch(type) {
        case 'success':
            bgClass = 'bg-success';
            icon = 'bi-check-circle';
            break;
        case 'error':
        case 'danger':
            bgClass = 'bg-danger';
            icon = 'bi-exclamation-triangle';
            break;
        case 'warning':
            bgClass = 'bg-warning';
            icon = 'bi-exclamation-triangle';
            break;
    }

    // 创建toast HTML
    const toastId = 'toast_' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast ${bgClass} text-white" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header ${bgClass} text-white border-0">
                <i class="bi ${icon} me-2"></i>
                <strong class="me-auto">系统提示</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    // 添加toast到容器
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // 显示toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 3000
    });
    toast.show();

    // 在toast隐藏后移除元素
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

// 确保函数在全局作用域中可访问
window.viewContent = viewContent;
window.approveContent = approveContent;
window.showRejectModal = showRejectModal;
window.filterByStatus = filterByStatus;
window.toggleSelectAll = toggleSelectAll;
window.batchApprove = batchApprove;
window.batchReject = batchReject;
window.showContentModal = showContentModal;
window.showToast = showToast;
</script>
