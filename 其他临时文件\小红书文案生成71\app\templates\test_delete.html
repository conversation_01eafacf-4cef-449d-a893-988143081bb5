{% extends "base.html" %}

{% block title %}删除功能测试{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <h2>删除功能测试</h2>
    
    <div class="card">
        <div class="card-body">
            <p>测试删除功能</p>
            <button type="button" class="btn btn-danger" onclick="testDelete(123)">测试删除按钮</button>
            <button type="button" class="btn btn-warning" onclick="testBatchAction()">测试批量操作</button>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="deleteForm" method="post" action="">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    确定要删除这个文案吗？此操作不可恢复。
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger">删除</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 批量操作确认模态框 -->
<div class="modal fade" id="batchActionModal" tabindex="-1" aria-labelledby="batchActionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchActionModalLabel">确认批量操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="batchActionMessage">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmBatchBtn">确认</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function testDelete(contentId) {
        console.log('testDelete called with contentId:', contentId);
        const deleteForm = document.getElementById('deleteForm');
        if (deleteForm) {
            deleteForm.action = `/contents/${contentId}/delete`;
            console.log('Delete form action set to:', deleteForm.action);
            
            const deleteModal = document.getElementById('deleteModal');
            if (deleteModal) {
                console.log('Showing delete modal');
                const modal = new bootstrap.Modal(deleteModal);
                modal.show();
                console.log('Modal should be shown');
            } else {
                console.error('Delete modal not found');
            }
        } else {
            console.error('Delete form not found');
        }
    }
    
    function testBatchAction() {
        console.log('testBatchAction called');
        const batchActionModal = document.getElementById('batchActionModal');
        if (batchActionModal) {
            console.log('Showing batch action modal');
            const modal = new bootstrap.Modal(batchActionModal);
            modal.show();
        } else {
            console.error('Batch action modal not found');
        }
    }
</script>
{% endblock %}
