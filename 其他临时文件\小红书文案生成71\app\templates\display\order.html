{% extends "base.html" %}

{% block title %}设置展示顺序{% endblock %}

{% block page_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">设置展示顺序 - {{ client.name }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('display.rules') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回规则列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        
                        <div class="mb-3">
                            <label class="form-label">客户名称</label>
                            <input type="text" class="form-control" value="{{ client.name }}" readonly>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.order_type.label(class="form-label") }}
                            <div class="form-check">
                                {{ form.order_type(class="form-check-input") }}
                            </div>
                            {% if form.order_type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.order_type.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div id="customOrderSection" class="mb-3" style="display: none;">
                            {{ form.content_ids.label(class="form-label") }}
                            {{ form.content_ids(class="form-control", rows=10) }}
                            {% if form.content_ids.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.content_ids.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">每行输入一个文案ID，按顺序排列</small>
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存顺序设置
                            </button>
                            <a href="{{ url_for('display.rules') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                    
                    <!-- 可用文案参考 -->
                    <div class="mt-4">
                        <h5>可用文案参考（最近50条）</h5>
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>标题</th>
                                        <th>创建时间</th>
                                        <th>优先级</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for content in contents %}
                                    <tr>
                                        <td>{{ content.id }}</td>
                                        <td>
                                            <a href="{{ url_for('content.content_view', content_id=content.id) }}" target="_blank">
                                                {{ content.title|truncate(30) }}
                                            </a>
                                        </td>
                                        <td>{{ content.created_at.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            {% if content.publish_priority == 'high' %}
                                            <span class="badge bg-danger">高</span>
                                            {% elif content.publish_priority == 'normal' %}
                                            <span class="badge bg-primary">中</span>
                                            {% else %}
                                            <span class="badge bg-secondary">低</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center">没有可用文案</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 根据选择的排序方式显示/隐藏自定义顺序输入框
    document.addEventListener('DOMContentLoaded', function() {
        const orderTypeInputs = document.querySelectorAll('input[name="order_type"]');
        const customOrderSection = document.getElementById('customOrderSection');
        
        // 初始状态
        if (document.querySelector('input[name="order_type"]:checked').value === 'custom') {
            customOrderSection.style.display = 'block';
        }
        
        // 监听选择变化
        orderTypeInputs.forEach(input => {
            input.addEventListener('change', function() {
                if (this.value === 'custom') {
                    customOrderSection.style.display = 'block';
                } else {
                    customOrderSection.style.display = 'none';
                }
            });
        });
    });
</script>
{% endblock %} 