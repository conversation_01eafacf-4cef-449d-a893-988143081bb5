#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
客户审核超时检查服务
"""

from datetime import datetime, timedelta, time
from app.models import db
from app.models.content import Content
from app.models.client import Client
from app.models.content import ContentHistory
import logging

logger = logging.getLogger(__name__)

class ClientReviewTimeoutService:
    """客户审核超时检查服务"""
    
    @staticmethod
    def check_and_process_timeouts():
        """检查并处理客户审核超时的文案"""
        try:
            logger.info("开始检查客户审核超时文案...")
            
            # 获取所有启用自动通过的客户
            clients = Client.query.filter(
                Client.auto_approve_enabled == True,
                Client.status == True
            ).all()
            
            total_processed = 0
            
            for client in clients:
                processed_count = ClientReviewTimeoutService._process_client_timeouts(client)
                total_processed += processed_count
                
                if processed_count > 0:
                    logger.info(f"客户 {client.name} 处理了 {processed_count} 篇超时文案")
            
            logger.info(f"客户审核超时检查完成，共处理 {total_processed} 篇文案")
            return total_processed
            
        except Exception as e:
            logger.error(f"客户审核超时检查失败: {e}")
            return 0
    
    @staticmethod
    def _process_client_timeouts(client):
        """处理单个客户的超时文案"""
        processed_count = 0
        
        try:
            # 获取该客户所有待审核的文案
            pending_contents = Content.query.filter(
                Content.client_id == client.id,
                Content.workflow_status == 'pending_client_review',
                Content.client_review_status == 'pending',
                Content.is_deleted == False
            ).all()
            
            current_time = datetime.now()
            current_date = current_time.date()
            
            for content in pending_contents:
                should_auto_approve = False
                timeout_reason = ""
                
                # 检查时间间隔超时
                if ClientReviewTimeoutService._check_interval_timeout(content, client, current_time):
                    should_auto_approve = True
                    timeout_reason = f"超过{client.review_timeout_hours}小时未审核"
                
                # 检查截止时间超时
                elif ClientReviewTimeoutService._check_deadline_timeout(content, client, current_time):
                    should_auto_approve = True
                    timeout_reason = f"超过截止时间{client.review_deadline_time}未审核"
                
                # 执行自动通过
                if should_auto_approve:
                    ClientReviewTimeoutService._auto_approve_content(content, timeout_reason)
                    processed_count += 1
            
            return processed_count
            
        except Exception as e:
            logger.error(f"处理客户 {client.name} 超时文案失败: {e}")
            return 0
    
    @staticmethod
    def _check_interval_timeout(content, client, current_time):
        """检查时间间隔超时"""
        if not content.status_update_time:
            return False
        
        # 计算从进入客户审核状态到现在的时间差
        time_diff = current_time - content.status_update_time
        timeout_hours = client.review_timeout_hours or 24
        
        return time_diff.total_seconds() >= timeout_hours * 3600
    
    @staticmethod
    def _check_deadline_timeout(content, client, current_time):
        """检查截止时间超时"""
        if not client.review_deadline_time:
            return False
        
        # 获取今天的截止时间
        today = current_time.date()
        deadline_datetime = datetime.combine(today, client.review_deadline_time)
        
        # 如果当前时间已过截止时间，且文案是今天或之前进入审核状态的
        if current_time >= deadline_datetime:
            if content.status_update_time:
                content_date = content.status_update_time.date()
                return content_date <= today
        
        return False
    
    @staticmethod
    def _auto_approve_content(content, timeout_reason):
        """自动通过文案审核"""
        try:
            # 更新文案状态
            content.client_review_status = 'approved'
            content.client_review_time = datetime.now()
            content.status_update_time = datetime.now()

            # 检查是否需要手动发布（统一逻辑：关闭 = 自动化）
            from app.models.system_setting import SystemSetting
            auto_publish_enabled = SystemSetting.get_value('auto_publish_enabled', 'false')

            if auto_publish_enabled.lower() in ['false', '0']:
                # 关闭手动发布开关，自动发布，直接进入待发布状态
                content.workflow_status = 'pending_publish'
                content.publish_status = 'pending_publish'
            else:
                # 开启手动发布开关，进入准备发布状态，需要手动提交
                content.workflow_status = 'ready_to_publish'
            
            # 记录操作历史
            history = ContentHistory(
                content_id=content.id,
                action='client_review_auto_approved',
                comment=f'系统自动通过客户审核：{timeout_reason}',
                user_id=None,  # 系统操作
                created_at=datetime.now()
            )
            db.session.add(history)
            
            # 提交数据库更改
            db.session.commit()
            
            logger.info(f"文案 {content.id} 自动通过客户审核：{timeout_reason}")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"自动通过文案 {content.id} 失败: {e}")
            raise
    
    @staticmethod
    def get_timeout_status_for_client(client_id):
        """获取指定客户的超时状态统计"""
        try:
            client = Client.query.get(client_id)
            if not client:
                return None
            
            # 获取待审核文案
            pending_contents = Content.query.filter(
                Content.client_id == client_id,
                Content.workflow_status == 'pending_client_review',
                Content.client_review_status == 'pending',
                Content.is_deleted == False
            ).all()
            
            current_time = datetime.now()
            timeout_soon = []  # 即将超时的文案
            already_timeout = []  # 已经超时的文案
            
            for content in pending_contents:
                # 检查是否已经超时
                interval_timeout = ClientReviewTimeoutService._check_interval_timeout(content, client, current_time)
                deadline_timeout = ClientReviewTimeoutService._check_deadline_timeout(content, client, current_time)
                
                if interval_timeout or deadline_timeout:
                    already_timeout.append(content)
                else:
                    # 检查是否即将超时（1小时内）
                    if content.status_update_time:
                        time_diff = current_time - content.status_update_time
                        timeout_hours = client.review_timeout_hours or 24
                        hours_left = timeout_hours - (time_diff.total_seconds() / 3600)
                        
                        if hours_left <= 1:
                            timeout_soon.append(content)
            
            return {
                'client_name': client.name,
                'auto_approve_enabled': client.auto_approve_enabled,
                'timeout_hours': client.review_timeout_hours,
                'deadline_time': client.review_deadline_time.strftime('%H:%M') if client.review_deadline_time else None,
                'pending_count': len(pending_contents),
                'timeout_soon_count': len(timeout_soon),
                'already_timeout_count': len(already_timeout),
                'timeout_soon': [{'id': c.id, 'title': c.title} for c in timeout_soon],
                'already_timeout': [{'id': c.id, 'title': c.title} for c in already_timeout]
            }
            
        except Exception as e:
            logger.error(f"获取客户 {client_id} 超时状态失败: {e}")
            return None
