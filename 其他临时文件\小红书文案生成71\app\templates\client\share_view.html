{% extends "client/share_layout.html" %}

{% block title %}文案查看 - {{ client.name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">{{ client.name }} - 文案列表</h4>
                    <span class="badge bg-light text-dark">只读查看模式</span>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> 欢迎使用文案查看系统，您可以在此查看为您准备的文案内容。
                    </div>
                    
                    <!-- 筛选表单 -->
                    <form method="get" class="row g-3 mb-4">
                        <div class="col-md-4">
                            <div class="input-group">
                                <input type="text" class="form-control" name="search" value="{{ request.args.get('search', '') }}" placeholder="搜索文案内容">
                                <button class="btn btn-outline-secondary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" name="status">
                                <option value="">全部状态</option>
                                <option value="pending" {% if request.args.get('status') == 'pending' %}selected{% endif %}>待审核</option>
                                <option value="approved" {% if request.args.get('status') == 'approved' %}selected{% endif %}>已通过</option>
                                <option value="rejected" {% if request.args.get('status') == 'rejected' %}selected{% endif %}>已拒绝</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" name="date">
                                <option value="">全部日期</option>
                                <option value="today" {% if request.args.get('date') == 'today' %}selected{% endif %}>今天</option>
                                <option value="week" {% if request.args.get('date') == 'week' %}selected{% endif %}>本周</option>
                                <option value="month" {% if request.args.get('date') == 'month' %}selected{% endif %}>本月</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">筛选</button>
                        </div>
                    </form>
                    
                    <!-- 文案列表 -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>标题</th>
                                    <th>展示日期</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if contents %}
                                    {% for content in contents %}
                                    <tr>
                                        <td>{{ content.id }}</td>
                                        <td>{{ content.title }}</td>
                                        <td>{{ content.display_date.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            {% if content.client_review_status == 'approved' %}
                                            <span class="badge bg-success">已通过</span>
                                            {% elif content.client_review_status == 'rejected' %}
                                            <span class="badge bg-danger">已拒绝</span>
                                            {% else %}
                                            <span class="badge bg-warning text-dark">待审核</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('client.share_content_view', token=share.access_token, content_id=content.id) }}{% if request.args.get('key') %}?key={{ request.args.get('key') }}{% endif %}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> 查看
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="5" class="text-center py-4">暂无文案数据</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    {% if pagination and pagination.pages > 1 %}
                    <nav class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('client.share_access', token=share.access_token, page=pagination.prev_num) }}{% if request.args.get('key') %}?key={{ request.args.get('key') }}{% endif %}">
                                    <span>&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">&laquo;</span>
                            </li>
                            {% endif %}
                            
                            {% for page in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                {% if page %}
                                    {% if page == pagination.page %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page }}</span>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('client.share_access', token=share.access_token, page=page) }}{% if request.args.get('key') %}?key={{ request.args.get('key') }}{% endif %}">{{ page }}</a>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('client.share_access', token=share.access_token, page=pagination.next_num) }}{% if request.args.get('key') %}?key={{ request.args.get('key') }}{% endif %}">
                                    <span>&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">&raquo;</span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">文案统计信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-0">{{ stats.total|default(0) }}</h3>
                                    <p class="mb-0">总文案数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-0">{{ stats.approved|default(0) }}</h3>
                                    <p class="mb-0">已通过</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h3 class="mb-0">{{ stats.pending|default(0) }}</h3>
                                    <p class="mb-0">待审核</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-0">{{ stats.rejected|default(0) }}</h3>
                                    <p class="mb-0">已拒绝</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 