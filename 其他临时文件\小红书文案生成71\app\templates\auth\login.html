{% extends "base.html" %}

{% block title %}登录 - 小红书文案生成系统{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header text-center">
        <h4 class="mb-0">小红书文案生成系统</h4>
    </div>
    <div class="card-body">
        <h5 class="card-title text-center mb-4">用户登录</h5>
        <form method="post" action="{{ url_for('auth.login') }}">
            {{ form.csrf_token }}
            <div class="mb-3">
                {{ form.username.label(class="form-label") }}
                {{ form.username(class="form-control") }}
                {% if form.username.errors %}
                    {% for error in form.username.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
            <div class="mb-3">
                {{ form.password.label(class="form-label") }}
                {{ form.password(class="form-control") }}
                {% if form.password.errors %}
                    {% for error in form.password.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
            <div class="mb-3 form-check">
                {{ form.remember_me(class="form-check-input") }}
                {{ form.remember_me.label(class="form-check-label") }}
            </div>
            {{ form.submit(class="btn btn-primary w-100") }}
        </form>
    </div>
</div>
{% endblock %}