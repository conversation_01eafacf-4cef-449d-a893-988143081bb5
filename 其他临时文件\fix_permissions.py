#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
小红书文案生成系统 - 权限修复脚本
用于修复角色权限关联缺失问题
"""

import os
import sys
from datetime import datetime

# 将项目根目录添加到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import create_app
from app.models import db
from app.models.user import User, Role, Permission, UserRole

def fix_permissions():
    """修复权限和角色关联"""
    print("开始修复权限...")
    
    # 1. 清空现有角色权限关联数据
    db.session.execute(db.text("DELETE FROM role_permissions"))
    db.session.commit()
    print("已清空现有角色权限关联")
    
    # 2. 获取所有角色和权限
    roles = Role.query.all()
    permissions = Permission.query.all()
    
    # 创建权限名称到权限对象的映射
    perm_map = {p.name: p for p in permissions}
    
    # 检查 content_view 权限是否存在，如果不存在则创建
    content_view_perm = Permission.query.filter_by(name='content_view').first()
    if not content_view_perm:
        content_view_perm = Permission(name='content_view', description='查看文案权限')
        db.session.add(content_view_perm)
        db.session.commit()
        print("已创建 content_view 权限")
        # 更新权限映射
        permissions = Permission.query.all()
        perm_map = {p.name: p for p in permissions}
    
    # 3. 为超级管理员角色分配所有权限
    admin_role = Role.query.filter_by(name='超级管理员').first() or Role.query.filter_by(name='admin').first()
    if admin_role:
        for permission in permissions:
            # 检查是否已经有这个权限
            if permission not in admin_role.permissions:
                admin_role.permissions.append(permission)
        print(f"已为角色 '{admin_role.name}' 分配所有权限")
    
    # 4. 为内容编辑角色分配权限
    editor_role = Role.query.filter_by(name='内容编辑').first()
    if editor_role:
        editor_perms = [
            'template_manage', 'topic_manage', 'client_view', 
            'content_create', 'content_review', 'content_edit', 
            'content_manage', 'stats_view', 'content_view'  # 确保有 content_view 权限
        ]
        for perm_name in editor_perms:
            if perm_name in perm_map:
                if perm_map[perm_name] not in editor_role.permissions:
                    editor_role.permissions.append(perm_map[perm_name])
        print(f"已为角色 '{editor_role.name}' 分配权限")
    
    # 5. 为客户经理角色分配权限
    manager_role = Role.query.filter_by(name='客户经理').first()
    if manager_role:
        manager_perms = [
            'client_view', 'client_manage', 'task_manage',
            'content_view', 'stats_view', 'content_export'
        ]
        for perm_name in manager_perms:
            if perm_name in perm_map:
                if perm_map[perm_name] not in manager_role.permissions:
                    manager_role.permissions.append(perm_map[perm_name])
        print(f"已为角色 '{manager_role.name}' 分配权限")
    
    # 6. 为运营专员角色分配权限
    operator_role = Role.query.filter_by(name='运营专员').first()
    if operator_role:
        operator_perms = [
            'content_view', 'content_review', 'content_publish',
            'stats_view', 'display_manage'
        ]
        for perm_name in operator_perms:
            if perm_name in perm_map:
                if perm_map[perm_name] not in operator_role.permissions:
                    operator_role.permissions.append(perm_map[perm_name])
        print(f"已为角色 '{operator_role.name}' 分配权限")
    
    # 7. 为普通用户角色分配基础权限
    user_role = Role.query.filter_by(name='普通用户').first()
    if user_role:
        user_perms = ['content_view', 'stats_view']
        for perm_name in user_perms:
            if perm_name in perm_map:
                if perm_map[perm_name] not in user_role.permissions:
                    user_role.permissions.append(perm_map[perm_name])
        print(f"已为角色 '{user_role.name}' 分配权限")
    
    # 8. 确保admin用户关联到超级管理员角色
    admin_user = User.query.filter_by(username='admin').first()
    if admin_user and admin_role:
        # 检查是否已经有关联
        has_role = False
        for role in admin_user.roles:
            if role.id == admin_role.id:
                has_role = True
                break
        
        if not has_role:
            admin_user.roles.append(admin_role)
            print(f"已为用户 '{admin_user.username}' 分配角色 '{admin_role.name}'")
    
    # 9. 确保admin用户是激活状态
    if admin_user:
        admin_user.is_active = True
        print(f"已确保用户 '{admin_user.username}' 处于激活状态")
    
    # 提交所有更改
    db.session.commit()
    
    # 10. 验证权限分配是否成功
    admin_user = User.query.filter_by(username='admin').first()
    if admin_user:
        print(f"验证 admin 用户权限:")
        print(f"- 角色: {[role.name for role in admin_user.roles]}")
        all_perms = []
        for role in admin_user.roles:
            role_perms = [perm.name for perm in role.permissions]
            all_perms.extend(role_perms)
            print(f"- 角色 '{role.name}' 的权限: {role_perms}")
        print(f"- admin 用户是否有 content_view 权限: {'content_view' in all_perms}")
    
    print("权限修复完成！")

if __name__ == '__main__':
    app = create_app()
    with app.app_context():
        fix_permissions() 