{% extends "base_simple.html" %}

{% block title %}模板分类管理 - 小红书文案生成系统{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
<style>
    .category-actions {
        white-space: nowrap;
    }
    .subcategory {
        margin-left: 30px;
    }
    .subcategory:before {
        content: "└─ ";
    }
</style>
{% endblock %}

{% block content_auth %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>模板分类管理</h2>
        </div>
        <div class="col-md-4 text-end">
            <button type="button" class="btn btn-primary" onclick="showAddCategoryModal()">
                <i class="bi bi-plus-lg"></i> 添加分类
            </button>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>分类名称</th>
                            <th>排序</th>
                            <th>创建时间</th>
                            <th class="category-actions">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category in categories %}
                        <tr>
                            <td>{{ category.id }}</td>
                            <td {% if category.parent_id %}class="subcategory"{% endif %}>{{ category.name }}</td>
                            <td>{{ category.sort_order }}</td>
                            <td>{{ category.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td class="category-actions">
                                <button type="button" class="btn btn-sm btn-primary" title="编辑" onclick="showEditCategoryModal({{ category.id }})">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger" title="删除" onclick="deleteCategoryById({{ category.id }})">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="5" class="text-center">暂无分类数据</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页组件 -->
            {% if pagination and pagination.pages > 1 %}
                {% include 'components/pagination.html' %}
            {% elif pagination %}
                <!-- 即使只有一页也显示分页信息 -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="d-flex align-items-center">
                        <span class="me-2">每页显示：</span>
                        <select class="form-select form-select-sm" style="width: auto;" onchange="changePageSize(this.value, event)">
                            <option value="10" {% if per_page == 10 %}selected{% endif %}>10条</option>
                            <option value="20" {% if per_page == 20 %}selected{% endif %}>20条</option>
                            <option value="30" {% if per_page == 30 %}selected{% endif %}>30条</option>
                            <option value="50" {% if per_page == 50 %}selected{% endif %}>50条</option>
                            <option value="80" {% if per_page == 80 %}selected{% endif %}>80条</option>
                            <option value="100" {% if per_page == 100 %}selected{% endif %}>100条</option>
                        </select>
                        <span class="ms-3 text-muted">
                            共 {{ pagination.total }} 条记录，第 {{ pagination.page }} / {{ pagination.pages }} 页
                        </span>
                    </div>
                </div>


            {% endif %}
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个分类吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 删除分类
        let deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        let categoryId = null;
        
        // 点击删除按钮
        document.querySelectorAll('.delete-category').forEach(function(button) {
            button.addEventListener('click', function() {
                categoryId = this.getAttribute('data-id');
                deleteModal.show();
            });
        });
        
        // 确认删除
        document.getElementById('confirmDelete').addEventListener('click', function() {
            if (categoryId) {
                fetch(`/templates/category/delete/${categoryId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token() }}'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    deleteModal.hide();
                    if (data.success) {
                        // 删除成功，刷新页面
                        location.reload();
                    } else {
                        // 删除失败，显示错误信息
                        alert(data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除失败，请重试');
                });
            }
        });
    });


</script>



{% endblock %}