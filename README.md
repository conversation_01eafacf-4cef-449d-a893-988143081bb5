# 小红书文案生成系统

## 项目概述
小红书文案生成系统是一个通过模板和关键词替换的方式批量生成小红书文案的系统,并提供审核、编辑和发布功能。

## 系统架构
- 采用Python开发
- 单体架构（不采用前后端分离）
- Web框架：Flask + Flask-Admin（提供现成管理界面）
- 数据库：MySQL 5.7（直接连接）
- 数据库连接：PyMySQL
- 权限管理：Flask-Login（简单易用的用户认证系统）
- 前端：Bootstrap + jQuery

## 环境要求
- Python 3.8+
- MySQL 5.7+
- 其他依赖见requirements.txt

## 安装与配置

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置数据库
系统默认使用MySQL数据库，所有数据库配置都集中在 `app/config/config.py` 文件中的 `DB_CONFIG` 字典中：

```python
# 数据库配置集中管理
DB_CONFIG = {
    'ENGINE': 'mysql',
    'DRIVER': 'pymysql',
    'HOST': 'localhost',
    'USER': 'xhsrw666',
    'PASSWORD': 'xhsrw666',
    'NAME': 'xhsrw666',
    'CHARSET': 'utf8mb4'
}
```

如需修改数据库配置，只需修改此处即可，所有依赖数据库配置的功能都会自动使用更新后的配置。

### 3. 初始化数据库
```bash
# 创建数据库并导入表结构
flask init-mysql

# 创建管理员账号
flask create-admin
```

### 4. 运行应用
```bash
flask run
```

## 从SQLite迁移到MySQL

如果您之前使用的是SQLite数据库(dev.db)，现在想迁移到MySQL，请按照以下步骤操作:

1. 确保您的MySQL服务已启动，并在 `app/config/config.py` 中配置好数据库连接信息

2. 初始化MySQL数据库
   ```bash
   flask init-mysql
   ```

3. 运行迁移脚本，将SQLite数据迁移到MySQL
   ```bash
   python migrate_to_mysql.py
   ```
   
   此脚本会自动执行以下操作：
   - 检查必要的文件是否存在
   - 检查MySQL连接是否正常
   - 备份SQLite数据库
   - 创建MySQL数据库并导入表结构
   - 迁移数据从SQLite到MySQL

4. 删除SQLite数据库文件(可选)
   ```bash
   # 确认迁移成功后，可以删除SQLite数据库文件
   rm dev.db
   ```

## 系统使用
详细的使用说明请参考系统内的帮助文档或联系管理员。

## 开发团队
- 开发者: [您的名字]

## 许可证
本项目采用私有许可证，未经授权不得复制、分发或修改。 