"""
命令行工具
"""

import click
from flask.cli import with_appcontext
from app.models import db
from app.models.user import User, Role, Permission


@click.command('create-admin')
@click.option('--username', default='admin', help='管理员用户名')
@click.option('--password', default='admin123', help='管理员密码')
@click.option('--email', default='<EMAIL>', help='管理员邮箱')
@with_appcontext
def create_admin_command(username, password, email):
    """创建管理员账号"""
    # 创建权限
    permissions = {
        'admin_access': '管理后台访问',
        'template_manage': '模板管理',
        'topic_manage': '话题管理',
        'client_view': '客户查看',
        'client_create': '客户创建',
        'client_edit': '客户编辑',
        'client_delete': '客户删除',
        'task_view': '任务查看',
        'task_create': '任务创建',
        'task_edit': '任务编辑',
        'task_delete': '任务删除',
        'content_view': '文案查看',
        'content_create': '文案创建',
        'content_edit': '文案编辑',
        'content_delete': '文案删除',
        'content_review': '文案审核'
    }
    
    perm_objects = {}
    for code, name in permissions.items():
        perm = Permission.query.filter_by(code=code).first()
        if not perm:
            perm = Permission(name=name, code=code, description=f'允许{name}')
            db.session.add(perm)
        perm_objects[code] = perm
    
    # 创建管理员角色
    admin_role = Role.query.filter_by(name='超级管理员').first()
    if not admin_role:
        admin_role = Role(name='超级管理员', description='拥有所有权限')
        db.session.add(admin_role)
    
    # 添加所有权限到管理员角色
    for perm in perm_objects.values():
        if perm not in admin_role.permissions:
            admin_role.permissions.append(perm)
    
    # 创建管理员用户
    admin = User.query.filter_by(username=username).first()
    if not admin:
        admin = User(
            username=username,
            email=email,
            real_name='管理员',
            status=True
        )
        admin.password = password
        admin.roles.append(admin_role)
        db.session.add(admin)
    else:
        # 更新现有管理员
        admin.password = password
        admin.email = email
        if admin_role not in admin.roles:
            admin.roles.append(admin_role)
    
    db.session.commit()
    click.echo(f'管理员账号 {username} 创建/更新成功!') 