{% extends "base.html" %}

{% block title %}查看通知{% endblock %}

{% block content_auth %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">通知详情</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('notification.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回通知列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="notification-header mb-4">
                        <h4>
                            {{ notification.title }}
                            {% if notification.priority == 'high' %}
                            <span class="badge bg-danger">重要</span>
                            {% elif notification.priority == 'normal' %}
                            <span class="badge bg-primary">普通</span>
                            {% elif notification.priority == 'low' %}
                            <span class="badge bg-secondary">低优先级</span>
                            {% endif %}
                        </h4>
                        <div class="text-muted">
                            <small>
                                <i class="fas fa-clock"></i> {{ notification.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                                &nbsp;|&nbsp;
                                {% if notification.type == 'review_status' %}
                                <span class="badge bg-info">审核状态变更</span>
                                {% elif notification.type == 'client_operation' %}
                                <span class="badge bg-warning">客户操作</span>
                                {% elif notification.type == 'publish_notice' %}
                                <span class="badge bg-success">发布通知</span>
                                {% elif notification.type == 'system_change' %}
                                <span class="badge bg-dark">系统变更</span>
                                {% endif %}
                                &nbsp;|&nbsp;
                                {% if notification.is_read %}
                                <span class="text-muted"><i class="fas fa-check-double"></i> 已读</span>
                                {% else %}
                                <span class="text-primary"><i class="fas fa-envelope"></i> 未读</span>
                                {% endif %}
                            </small>
                        </div>
                    </div>
                    
                    <div class="notification-content mb-4">
                        <div class="card">
                            <div class="card-body">
                                {{ notification.content|safe }}
                            </div>
                        </div>
                    </div>
                    
                    {% if notification.related_content %}
                    <div class="notification-related mb-4">
                        <h5>相关文案</h5>
                        <div class="card">
                            <div class="card-body">
                                <h6>{{ notification.related_content.title }}</h6>
                                <p>{{ notification.related_content.content|truncate(200) }}</p>
                                <a href="{{ url_for('content.content_view', content_id=notification.related_content.id) }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                    <i class="fas fa-external-link-alt"></i> 查看文案详情
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="notification-actions">
                        <div class="btn-group">
                            {% if not notification.is_read %}
                            <a href="{{ url_for('notification.mark_read', notification_id=notification.id) }}" class="btn btn-outline-primary">
                                <i class="fas fa-check"></i> 标记为已读
                            </a>
                            {% endif %}
                            <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                <i class="fas fa-trash"></i> 删除通知
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这条通知吗？此操作不可撤销。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form action="{{ url_for('notification.delete', notification_id=notification.id) }}" method="post">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %} 