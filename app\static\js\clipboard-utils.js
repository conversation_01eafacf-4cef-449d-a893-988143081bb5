/**
 * 通用剪贴板工具 - 兼容HTTP和HTTPS环境
 * 解决在HTTP环境下无法使用现代剪贴板API的问题
 */

// 全局复制函数 - 兼容所有环境
window.copyToClipboard = function(text, options = {}) {
    // 如果传入的是元素ID，获取元素的值
    if (typeof text === 'string' && !text.startsWith('http') && text.length < 50 && !text.includes(' ')) {
        const element = document.getElementById(text);
        if (element) {
            text = element.value || element.textContent || element.innerText;
        }
    }
    
    // 默认选项
    const defaultOptions = {
        successMessage: '内容已复制到剪贴板',
        errorMessage: '复制失败，请手动复制',
        showToast: true,
        fallbackDialog: true
    };
    
    const config = { ...defaultOptions, ...options };
    
    // 判断复制的内容类型，自动设置消息
    if (!options.successMessage) {
        if (text.startsWith('http')) {
            config.successMessage = '链接已复制到剪贴板';
        } else if (text.length === 4 && /^[A-Z0-9]+$/.test(text)) {
            config.successMessage = '访问密钥已复制到剪贴板';
        }
    }
    
    console.log('尝试复制文本:', text);
    console.log('环境检查 - isSecureContext:', window.isSecureContext);
    console.log('环境检查 - protocol:', location.protocol);
    console.log('环境检查 - hostname:', location.hostname);
    
    // 检查是否支持现代剪贴板API
    const supportsModernAPI = navigator.clipboard && 
                             navigator.clipboard.writeText && 
                             (window.isSecureContext || 
                              location.protocol === 'https:' || 
                              location.hostname === 'localhost' || 
                              location.hostname === '127.0.0.1');
    
    if (supportsModernAPI) {
        console.log('使用现代剪贴板API');
        navigator.clipboard.writeText(text)
            .then(() => {
                console.log('现代API复制成功');
                if (config.showToast && typeof showToast === 'function') {
                    showToast(config.successMessage, 'success');
                } else {
                    console.log(config.successMessage);
                }
            })
            .catch(err => {
                console.warn('现代剪贴板API失败，使用降级方案:', err);
                fallbackCopy(text, config);
            });
    } else {
        console.log('使用降级复制方案 (HTTP环境或不支持现代API)');
        fallbackCopy(text, config);
    }
};

// 降级复制方案
function fallbackCopy(text, config) {
    console.log('执行降级复制方案');

    // 方法1: 使用 execCommand
    const success = execCommandCopy(text);

    // 在HTTP环境下，即使 execCommand 返回 true，也经常实际失败
    // 所以我们采用更保守的策略：直接显示手动复制对话框
    const isHttpEnvironment = location.protocol === 'http:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1';

    if (success && !isHttpEnvironment) {
        // 只有在HTTPS环境或localhost下才信任 execCommand 的结果
        if (config.showToast && typeof showToast === 'function') {
            showToast(config.successMessage, 'success');
        } else {
            console.log(config.successMessage);
        }
    } else {
        // HTTP环境下或复制失败时，直接显示手动复制对话框
        console.log('HTTP环境或复制可能失败，显示手动复制对话框');
        if (config.fallbackDialog) {
            showManualCopyDialog(text, config);
        } else {
            if (config.showToast && typeof showToast === 'function') {
                showToast(config.errorMessage, 'error');
            } else {
                console.error(config.errorMessage);
            }
        }
    }
}

// execCommand 复制方法 - 增强版本
function execCommandCopy(text) {
    console.log('尝试 execCommand 复制:', text);

    // 方法1: 使用 textarea
    let success = tryTextAreaCopy(text);

    if (!success) {
        console.log('textarea 方法失败，尝试 input 方法');
        // 方法2: 使用 input
        success = tryInputCopy(text);
    }

    if (!success) {
        console.log('input 方法失败，尝试选择现有元素');
        // 方法3: 尝试选择页面上现有的元素
        success = trySelectExistingElement(text);
    }

    console.log('最终 execCommand 复制结果:', success);

    // 即使返回 true，也要验证是否真的复制成功
    if (success) {
        // 延迟验证复制是否真的成功
        setTimeout(() => {
            verifyClipboard(text);
        }, 100);
    }

    return success;
}

// 使用 textarea 复制
function tryTextAreaCopy(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;

    // 设置样式确保元素可见且可操作
    textArea.style.position = 'fixed';
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.width = '2em';
    textArea.style.height = '2em';
    textArea.style.padding = '0';
    textArea.style.border = 'none';
    textArea.style.outline = 'none';
    textArea.style.boxShadow = 'none';
    textArea.style.background = 'transparent';
    textArea.style.fontSize = '16px';
    textArea.style.zIndex = '-1';
    textArea.style.opacity = '0';

    document.body.appendChild(textArea);

    try {
        textArea.focus();
        textArea.select();
        textArea.setSelectionRange(0, text.length);

        const successful = document.execCommand('copy');
        console.log('textarea execCommand 结果:', successful);
        return successful;
    } catch (err) {
        console.error('textarea execCommand 失败:', err);
        return false;
    } finally {
        document.body.removeChild(textArea);
    }
}

// 使用 input 复制
function tryInputCopy(text) {
    const input = document.createElement('input');
    input.value = text;
    input.type = 'text';

    input.style.position = 'fixed';
    input.style.top = '0';
    input.style.left = '0';
    input.style.opacity = '0';
    input.style.zIndex = '-1';

    document.body.appendChild(input);

    try {
        input.focus();
        input.select();
        input.setSelectionRange(0, text.length);

        const successful = document.execCommand('copy');
        console.log('input execCommand 结果:', successful);
        return successful;
    } catch (err) {
        console.error('input execCommand 失败:', err);
        return false;
    } finally {
        document.body.removeChild(input);
    }
}

// 尝试选择现有元素
function trySelectExistingElement(text) {
    // 查找页面上包含相同文本的输入框
    const inputs = document.querySelectorAll('input[type="text"], textarea');
    for (let input of inputs) {
        if (input.value === text) {
            try {
                input.focus();
                input.select();
                input.setSelectionRange(0, text.length);

                const successful = document.execCommand('copy');
                console.log('现有元素 execCommand 结果:', successful);
                if (successful) return true;
            } catch (err) {
                console.error('现有元素 execCommand 失败:', err);
            }
        }
    }
    return false;
}

// 验证剪贴板内容（如果支持读取）
function verifyClipboard(expectedText) {
    if (navigator.clipboard && navigator.clipboard.readText && window.isSecureContext) {
        navigator.clipboard.readText()
            .then(clipboardText => {
                if (clipboardText === expectedText) {
                    console.log('✓ 剪贴板验证成功');
                } else {
                    console.warn('✗ 剪贴板验证失败，期望:', expectedText, '实际:', clipboardText);
                }
            })
            .catch(err => {
                console.log('无法验证剪贴板内容 (权限限制):', err.message);
            });
    } else {
        console.log('无法验证剪贴板内容 (API不支持)');
    }
}

// 显示手动复制对话框
function showManualCopyDialog(text, config) {
    // 检查是否有Bootstrap模态框支持
    if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
        showBootstrapCopyDialog(text, config);
    } else {
        // 降级到简单的prompt
        showPromptCopyDialog(text, config);
    }
}

// Bootstrap 模态框版本
function showBootstrapCopyDialog(text, config) {
    const modalId = 'manualCopyModal_' + Date.now();
    const textareaId = 'copyTextarea_' + Date.now();

    const modalHtml = `
        <div class="modal fade" id="${modalId}" tabindex="-1" data-bs-backdrop="static">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title">
                            <i class="bi bi-exclamation-triangle"></i> 需要手动复制
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info mb-3">
                            <i class="bi bi-info-circle"></i>
                            <strong>由于浏览器安全限制</strong>，在HTTP环境下无法自动复制到剪贴板。<br>
                            请按照以下步骤手动复制：
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">要复制的内容：</label>
                            <div class="input-group">
                                <textarea id="${textareaId}" class="form-control" rows="3" readonly style="font-family: monospace; font-size: 14px;">${text}</textarea>
                                <button class="btn btn-outline-primary" type="button" onclick="selectAllText('${textareaId}')">
                                    <i class="bi bi-cursor-text"></i> 全选
                                </button>
                            </div>
                        </div>

                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">复制步骤：</h6>
                                <ol class="mb-0">
                                    <li>点击上方的 <span class="badge bg-primary">全选</span> 按钮，或者点击文本框</li>
                                    <li>按键盘快捷键：
                                        <ul>
                                            <li><kbd>Ctrl</kbd> + <kbd>C</kbd> (Windows/Linux)</li>
                                            <li><kbd>Cmd</kbd> + <kbd>C</kbd> (Mac)</li>
                                        </ul>
                                    </li>
                                    <li>在需要的地方按 <kbd>Ctrl</kbd> + <kbd>V</kbd> 粘贴</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-success" onclick="selectAllText('${textareaId}')">
                            <i class="bi bi-cursor-text"></i> 全选文本
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-check"></i> 已复制，关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加全选函数到全局
    window.selectAllText = function(textareaId) {
        const textarea = document.getElementById(textareaId);
        if (textarea) {
            textarea.focus();
            textarea.select();
            textarea.setSelectionRange(0, textarea.value.length);

            // 尝试再次复制
            try {
                const success = document.execCommand('copy');
                if (success) {
                    // 显示临时提示
                    const btn = event.target.closest('button');
                    const originalText = btn.innerHTML;
                    btn.innerHTML = '<i class="bi bi-check"></i> 已选中';
                    btn.classList.remove('btn-outline-primary', 'btn-success');
                    btn.classList.add('btn-success');

                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.classList.remove('btn-success');
                        btn.classList.add('btn-outline-primary');
                    }, 2000);
                }
            } catch (err) {
                console.log('手动复制尝试失败:', err);
            }
        }
    };

    // 添加模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById(modalId));
    modal.show();

    // 自动选中文本
    setTimeout(() => {
        const textarea = document.getElementById(textareaId);
        if (textarea) {
            textarea.focus();
            textarea.select();
            textarea.setSelectionRange(0, textarea.value.length);
        }
    }, 500);

    // 模态框关闭后清理DOM
    document.getElementById(modalId).addEventListener('hidden.bs.modal', function() {
        this.remove();
        // 清理全局函数
        if (window.selectAllText) {
            delete window.selectAllText;
        }
    });
}

// 简单 prompt 版本
function showPromptCopyDialog(text, config) {
    // 先尝试使用更友好的确认对话框
    const userWantsToCopy = confirm(
        '由于浏览器安全限制，无法自动复制到剪贴板。\n\n' +
        '点击"确定"显示复制内容，然后您可以手动选择和复制。\n\n' +
        '点击"取消"放弃复制操作。'
    );

    if (userWantsToCopy) {
        // 使用 prompt 让用户可以全选复制
        const userAction = window.prompt(
            '请选择以下内容并复制 (Ctrl+A 全选, Ctrl+C 复制):',
            text
        );

        if (userAction !== null && config.showToast && typeof showToast === 'function') {
            showToast('请确认已复制内容', 'info');
        }
    } else {
        if (config.showToast && typeof showToast === 'function') {
            showToast('已取消复制', 'info');
        }
    }
}

// 兼容旧版本的函数名
window.copyShareLink = function(shareUrl) {
    copyToClipboard(shareUrl, {
        successMessage: '分享链接已复制到剪贴板'
    });
};

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('剪贴板工具已加载');
    console.log('当前环境 - Protocol:', location.protocol);
    console.log('当前环境 - Hostname:', location.hostname);
    console.log('当前环境 - isSecureContext:', window.isSecureContext);
    console.log('现代剪贴板API支持:', !!(navigator.clipboard && navigator.clipboard.writeText));
});

// 导出给其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        copyToClipboard: window.copyToClipboard,
        copyShareLink: window.copyShareLink
    };
}
