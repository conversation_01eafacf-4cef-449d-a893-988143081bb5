"""
任务相关模型
"""
from datetime import datetime
from . import db


class Task(db.Model):
    """任务模型"""
    __tablename__ = 'tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>('clients.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    status = db.Column(db.String(20), default='processing')  # processing进行中，completed已完成
    target_count = db.Column(db.Integer, default=0)  # 目标文案数量
    actual_count = db.Column(db.Integer, default=0)  # 实际生成文案数量
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    created_by = db.Column(db.<PERSON>te<PERSON>, db.<PERSON><PERSON>('users.id'), nullable=False)
    
    # 关联
    creator = db.relationship('User', backref=db.backref('created_tasks', lazy='dynamic'))
    batches = db.relationship('Batch', backref='task', lazy='dynamic')
    contents = db.relationship('Content', backref='task', lazy='dynamic')
    
    def __repr__(self):
        return f'<Task {self.name}>'


class Batch(db.Model):
    """批次模型"""
    __tablename__ = 'batches'
    
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.Integer, db.ForeignKey('tasks.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    content_count = db.Column(db.Integer, default=0)  # 文案数量
    created_at = db.Column(db.DateTime, default=datetime.now)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # 关联
    creator = db.relationship('User', backref=db.backref('created_batches', lazy='dynamic'))
    contents = db.relationship('Content', backref='batch', lazy='dynamic')
    
    def __repr__(self):
        return f'<Batch {self.name}>' 