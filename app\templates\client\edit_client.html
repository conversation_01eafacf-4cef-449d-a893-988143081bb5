<!-- 编辑客户页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-person-gear"></i> 编辑客户</h2>
                <a href="{{ url_for('main_simple.clients') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回客户列表
                </a>
            </div>

            <div class="card">
                <div class="card-body">
                    <form method="post" action="{{ url_for('main_simple.edit_client', id=client.id) }}">
                        {{ form.csrf_token }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.name.label(class="form-label") }}
                                    {{ form.name(class="form-control", placeholder="请输入客户名称") }}
                                    {% if form.name.errors %}
                                        <div class="text-danger">
                                            {% for error in form.name.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.contact.label(class="form-label") }}
                                    {{ form.contact(class="form-control", placeholder="请输入联系人") }}
                                    {% if form.contact.errors %}
                                        <div class="text-danger">
                                            {% for error in form.contact.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.phone.label(class="form-label") }}
                                    {{ form.phone(class="form-control", placeholder="请输入电话号码") }}
                                    {% if form.phone.errors %}
                                        <div class="text-danger">
                                            {% for error in form.phone.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.email.label(class="form-label") }}
                                    {{ form.email(class="form-control", placeholder="请输入邮箱地址") }}
                                    {% if form.email.errors %}
                                        <div class="text-danger">
                                            {% for error in form.email.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    {{ form.address.label(class="form-label") }}
                                    {{ form.address(class="form-control", placeholder="请输入地址") }}
                                    {% if form.address.errors %}
                                        <div class="text-danger">
                                            {% for error in form.address.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    {{ form.daily_content_count.label(class="form-label") }}
                                    {{ form.daily_content_count(class="form-control", placeholder="每日内容数量") }}
                                    {% if form.daily_content_count.errors %}
                                        <div class="text-danger">
                                            {% for error in form.daily_content_count.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    {{ form.interval_min.label(class="form-label") }}
                                    {{ form.interval_min(class="form-control", placeholder="最小间隔(分钟)") }}
                                    {% if form.interval_min.errors %}
                                        <div class="text-danger">
                                            {% for error in form.interval_min.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    {{ form.interval_max.label(class="form-label") }}
                                    {{ form.interval_max(class="form-control", placeholder="最大间隔(分钟)") }}
                                    {% if form.interval_max.errors %}
                                        <div class="text-danger">
                                            {% for error in form.interval_max.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.display_start_time.label(class="form-label") }}
                                    {{ form.display_start_time(class="form-control") }}
                                    {% if form.display_start_time.errors %}
                                        <div class="text-danger">
                                            {% for error in form.display_start_time.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">状态设置</label>
                                    <div class="d-flex gap-4">
                                        <div class="form-check form-switch">
                                            {{ form.status(class="form-check-input", id="status_switch") }}
                                            <label class="form-check-label" for="status_switch">
                                                <span id="status_text">{{ '启用' if form.status.data else '禁用' }}</span>
                                            </label>
                                        </div>
                                        <div class="form-check form-switch">
                                            {{ form.need_review(class="form-check-input", id="need_review_switch") }}
                                            <label class="form-check-label" for="need_review_switch">
                                                <span id="need_review_text">{{ '需要审核' if form.need_review.data else '无需审核' }}</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    {{ form.remark.label(class="form-label") }}
                                    {{ form.remark(class="form-control", rows="3", placeholder="请输入备注信息") }}
                                    {% if form.remark.errors %}
                                        <div class="text-danger">
                                            {% for error in form.remark.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- 内容生成默认值设置 -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-gear"></i> 内容生成默认值设置</h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted small">设置该客户的默认话题、@用户和定位信息，选择客户后会自动填充到内容生成页面</p>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">默认必选话题</label>
                                            <input type="text"
                                                   class="form-control"
                                                   id="default-required-topics-input"
                                                   placeholder="输入话题后回车添加，支持多行粘贴">
                                            <div class="tag-display-container mt-2" id="default-required-topics-container">
                                                <!-- 标签将在这里显示 -->
                                            </div>
                                            <div class="form-text">每行一个话题，生成内容时会自动添加到必选话题中</div>
                                            <!-- 隐藏字段存储数据 -->
                                            {{ form.default_required_topics(style="display: none;") }}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">默认随机话题</label>
                                            <input type="text"
                                                   class="form-control"
                                                   id="default-random-topics-input"
                                                   placeholder="输入话题后回车添加，支持多行粘贴">
                                            <div class="tag-display-container mt-2" id="default-random-topics-container">
                                                <!-- 标签将在这里显示 -->
                                            </div>
                                            <div class="form-text">每行一个话题，生成内容时会随机选择添加</div>
                                            <!-- 隐藏字段存储数据 -->
                                            {{ form.default_random_topics(style="display: none;") }}
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">默认@用户</label>
                                            <input type="text"
                                                   class="form-control"
                                                   id="default-at-users-input"
                                                   placeholder="输入用户名后回车添加，支持多行粘贴">
                                            <div class="tag-display-container mt-2" id="default-at-users-container">
                                                <!-- 标签将在这里显示 -->
                                            </div>
                                            <div class="form-text">每行一个用户名，可以带@符号或不带，生成内容时会自动添加</div>
                                            <!-- 隐藏字段存储数据 -->
                                            {{ form.default_at_users(style="display: none;") }}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">默认定位信息</label>
                                            <input type="text"
                                                   class="form-control"
                                                   id="default-location-input"
                                                   placeholder="输入定位信息后回车添加，支持多行粘贴">
                                            <div class="tag-display-container mt-2" id="default-location-container">
                                                <!-- 标签将在这里显示 -->
                                            </div>
                                            <div class="form-text">每行一个定位信息，生成内容时会随机选择其中一个</div>
                                            <!-- 隐藏字段存储数据 -->
                                            {{ form.default_location(style="display: none;") }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-2 mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> 保存修改
                            </button>
                            <a href="{{ url_for('main_simple.clients') }}" class="btn btn-secondary">
                                <i class="bi bi-x-circle"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 标签样式 -->
<style>
.tag-display-container {
    min-height: 40px;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 8px;
    background-color: #f8f9fa;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    align-items: flex-start;
}

.tag-display-container.empty {
    color: #6c757d;
    font-style: italic;
}

.tag-display-container.empty::before {
    content: "暂无标签";
}

.tag {
    display: inline-flex;
    align-items: center;
    background-color: #0d6efd;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.875rem;
    margin: 2px;
}

.tag .remove {
    margin-left: 5px;
    cursor: pointer;
    font-weight: bold;
    opacity: 0.8;
}

.tag .remove:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}
</style>

<script>
console.log('编辑客户页面已加载');

// 标签管理功能
function addTag(value, containerId, hiddenFieldId) {
    console.log('📝 addTag 调用:', { value, containerId, hiddenFieldId });

    const container = document.getElementById(containerId);
    const hiddenField = document.getElementById(hiddenFieldId);

    if (!container) {
        console.error('❌ 找不到容器:', containerId);
        return false;
    }

    if (!hiddenField) {
        console.error('❌ 找不到隐藏字段:', hiddenFieldId);
        return false;
    }

    // 检查是否已存在相同标签
    const existingTags = container.querySelectorAll('.tag');
    for (let i = 0; i < existingTags.length; i++) {
        const existingText = existingTags[i].textContent.trim().replace('×', '').trim();
        if (existingText === value) {
            console.log('⚠️ 标签已存在，不重复添加:', value);
            return false;
        }
    }

    // 创建新标签
    const tag = document.createElement('span');
    tag.className = 'tag';
    tag.innerHTML = `${value} <span class="remove" onclick="removeTag(this, '${hiddenFieldId}')">×</span>`;

    // 添加到容器
    container.appendChild(tag);
    updateContainerEmptyState(container);

    // 更新隐藏字段
    updateHiddenField(containerId, hiddenFieldId);

    console.log('✅ 标签添加成功:', value);
    return true;
}

// 删除标签
function removeTag(removeBtn, hiddenFieldId) {
    console.log('🗑️ removeTag 调用:', hiddenFieldId);

    const tag = removeBtn.parentElement;
    const container = tag.parentElement;
    const containerId = container.id;

    console.log('🗑️ 删除标签详情:', {
        tagText: tag.textContent.replace('×', '').trim(),
        containerId: containerId,
        hiddenFieldId: hiddenFieldId
    });

    tag.remove();
    updateContainerEmptyState(container);
    updateHiddenField(containerId, hiddenFieldId);

    console.log('🗑️ 标签删除完成');
}

// 更新容器空状态
function updateContainerEmptyState(container) {
    const tags = container.querySelectorAll('.tag');
    if (tags.length === 0) {
        container.classList.add('empty');
    } else {
        container.classList.remove('empty');
    }
}

// 更新隐藏字段
function updateHiddenField(containerId, hiddenFieldId) {
    console.log('🔄 updateHiddenField:', { containerId, hiddenFieldId });

    const container = document.getElementById(containerId);
    const hiddenField = document.getElementById(hiddenFieldId);

    if (!container || !hiddenField) {
        console.error('❌ 找不到容器或隐藏字段:', { containerId, hiddenFieldId });
        return;
    }

    const tags = container.querySelectorAll('.tag');
    const values = [];

    tags.forEach(tag => {
        const text = tag.textContent.replace('×', '').trim();
        if (text) {
            values.push(text);
        }
    });

    // 所有字段都支持多个值，用换行符分隔
    hiddenField.value = values.join('\n');

    console.log('💾 隐藏字段已更新:', hiddenFieldId, '=', hiddenField.value);
}

// 状态开关事件
document.getElementById('status_switch').addEventListener('change', function() {
    document.getElementById('status_text').textContent = this.checked ? '启用' : '禁用';
});

document.getElementById('need_review_switch').addEventListener('change', function() {
    document.getElementById('need_review_text').textContent = this.checked ? '需要审核' : '无需审核';
});

// 表单提交成功后的处理
{% if request.method == 'POST' and not form.errors %}
    // 如果是POST请求且没有错误，显示成功消息并跳转
    if (window.showToast) {
        showToast('客户信息更新成功！', 'success');
    } else {
        alert('客户信息更新成功！');
    }
    
    setTimeout(() => {
        window.location.href = "{{ url_for('main_simple.clients') }}";
    }, 1500);
{% endif %}

// 初始化标签输入功能
document.addEventListener('DOMContentLoaded', function() {
    console.log('🏷️ 初始化标签输入功能...');

    // 输入框映射
    const inputMappings = [
        { inputId: 'default-required-topics-input', containerId: 'default-required-topics-container', hiddenFieldId: 'default_required_topics' },
        { inputId: 'default-random-topics-input', containerId: 'default-random-topics-container', hiddenFieldId: 'default_random_topics' },
        { inputId: 'default-at-users-input', containerId: 'default-at-users-container', hiddenFieldId: 'default_at_users' },
        { inputId: 'default-location-input', containerId: 'default-location-container', hiddenFieldId: 'default_location' }
    ];

    inputMappings.forEach(mapping => {
        const input = document.getElementById(mapping.inputId);
        if (input) {
            console.log('🔧 初始化输入框:', mapping.inputId);

            // 回车事件
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    console.log('⌨️ 回车事件:', mapping.inputId);
                    const value = this.value.trim();
                    if (value) {
                        // 特殊处理@用户字段
                        if (mapping.hiddenFieldId === 'default_at_users' && !value.startsWith('@')) {
                            addTag('@' + value, mapping.containerId, mapping.hiddenFieldId);
                        } else {
                            addTag(value, mapping.containerId, mapping.hiddenFieldId);
                        }
                        this.value = '';
                    }
                }
            });

            // 失焦事件
            input.addEventListener('blur', function() {
                const value = this.value.trim();
                if (value) {
                    // 特殊处理@用户字段
                    if (mapping.hiddenFieldId === 'default_at_users' && !value.startsWith('@')) {
                        addTag('@' + value, mapping.containerId, mapping.hiddenFieldId);
                    } else {
                        addTag(value, mapping.containerId, mapping.hiddenFieldId);
                    }
                    this.value = '';
                }
            });

            // 粘贴事件
            input.addEventListener('paste', function(e) {
                setTimeout(() => {
                    const pastedText = this.value;
                    if (pastedText.includes('\n')) {
                        const lines = pastedText.split('\n').map(line => line.trim()).filter(line => line);
                        lines.forEach(line => {
                            // 特殊处理@用户字段
                            if (mapping.hiddenFieldId === 'default_at_users' && !line.startsWith('@')) {
                                addTag('@' + line, mapping.containerId, mapping.hiddenFieldId);
                            } else {
                                addTag(line, mapping.containerId, mapping.hiddenFieldId);
                            }
                        });
                        this.value = '';
                    }
                }, 10);
            });
        }
    });

    // 从隐藏字段加载现有数据
    setTimeout(() => {
        console.log('📂 开始加载现有数据...');

        const mappings = [
            { hiddenId: 'default_required_topics', containerId: 'default-required-topics-container' },
            { hiddenId: 'default_random_topics', containerId: 'default-random-topics-container' },
            { hiddenId: 'default_at_users', containerId: 'default-at-users-container' },
            { hiddenId: 'default_location', containerId: 'default-location-container' }
        ];

        mappings.forEach(mapping => {
            const hiddenField = document.getElementById(mapping.hiddenId);
            const container = document.getElementById(mapping.containerId);

            if (hiddenField && container) {
                // 先清空容器中的所有标签，避免重复
                console.log('🧹 清空容器:', mapping.containerId);
                container.innerHTML = '';
                container.classList.add('empty');

                const value = hiddenField.value;
                console.log('📂 加载字段数据:', mapping.hiddenId, '=', value);

                if (value) {
                    const values = value.split('\n').map(v => v.trim()).filter(v => v);
                    console.log('📂 分割后的值:', values);

                    values.forEach(val => {
                        addTag(val, mapping.containerId, mapping.hiddenId);
                    });
                    updateContainerEmptyState(container);
                }
            } else {
                console.warn('⚠️ 找不到字段或容器:', mapping);
            }
        });

        console.log('📂 现有数据加载完成');
    }, 100);

    console.log('✅ 标签输入功能初始化完成');
});
</script>
