{% extends "base.html" %}

{% block title %}编辑展示计划{% endblock %}

{% block page_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">编辑展示计划</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('display.index', client_id=schedule.client_id) }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        
                        <div class="mb-3">
                            <label class="form-label">客户名称</label>
                            <input type="text" class="form-control" value="{{ client.name }}" readonly>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.content_id.label(class="form-label") }}
                            {{ form.content_id(class="form-select") }}
                            {% if form.content_id.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.content_id.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.display_date.label(class="form-label") }}
                            {{ form.display_date(class="form-control", type="date") }}
                            {% if form.display_date.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.display_date.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.display_time.label(class="form-label") }}
                            {{ form.display_time(class="form-control", type="time") }}
                            {% if form.display_time.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.display_time.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_fixed_time(class="form-check-input") }}
                                {{ form.is_fixed_time.label(class="form-check-label") }}
                            </div>
                            <small class="form-text text-muted">固定时间不会受到其他文案展示时间的影响</small>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.status.label(class="form-label") }}
                            {{ form.status(class="form-select") }}
                            {% if form.status.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.status.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存修改
                            </button>
                            <a href="{{ url_for('display.index', client_id=schedule.client_id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 