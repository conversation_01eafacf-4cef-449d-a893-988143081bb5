# -*- coding: utf-8 -*-
"""
用户相关模型
"""
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from . import db

# 用户角色关联表（多对多关系）
user_roles = db.<PERSON>(
    'user_roles',
    db.<PERSON>umn('id', db.Integer, primary_key=True),
    db.<PERSON>('user_id', db.Integer, db.<PERSON>('users.id'), nullable=False),
    db.<PERSON>umn('role_id', db.Integer, db.Foreign<PERSON>ey('roles.id'), nullable=False)
)

# 角色权限关联表（多对多关系）
role_permissions = db.<PERSON>(
    'role_permissions',
    db.Column('id', db.Integer, primary_key=True),
    db.Column('role_id', db.Integer, db.Foreign<PERSON>ey('roles.id'), nullable=False),
    db.<PERSON>umn('permission_id', db.<PERSON><PERSON><PERSON>, db.<PERSON>('permissions.id'), nullable=False)
)

# 用户权限关联表（多对多关系，新增）
user_permissions = db.Table(
    'user_permissions',
    db.Column('id', db.Integer, primary_key=True),
    db.Column('user_id', db.Integer, db.ForeignKey('users.id'), nullable=False),
    db.Column('permission_id', db.Integer, db.ForeignKey('permissions.id'), nullable=False)
)

# 用户菜单权限关联表（多对多关系，新增）
user_menu_permissions = db.Table(
    'user_menu_permissions',
    db.Column('id', db.Integer, primary_key=True),
    db.Column('user_id', db.Integer, db.ForeignKey('users.id'), nullable=False),
    db.Column('menu_id', db.Integer, db.ForeignKey('menu_items.id'), nullable=False)
)

class User(UserMixin, db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=True, index=True)
    real_name = db.Column(db.String(50))
    phone = db.Column(db.String(20))
    is_active = db.Column(db.Boolean, default=True, index=True)  # True为启用，False为禁用
    created_at = db.Column(db.DateTime, default=datetime.now)
    last_login = db.Column(db.DateTime)
    
    # 用户拥有的角色（多对多）
    roles = db.relationship('Role', secondary=user_roles, backref=db.backref('users', lazy='dynamic'))
    # 用户直接拥有的权限（多对多，新增）
    permissions = db.relationship('Permission', secondary=user_permissions, backref=db.backref('users_direct', lazy='dynamic'))
    # 用户菜单权限（多对多，新增）
    menu_permissions = db.relationship('MenuItem', secondary=user_menu_permissions, backref=db.backref('users', lazy='dynamic'))
    
    @property
    def password(self):
        """密码属性不可读"""
        raise AttributeError('密码不可读')
    
    @password.setter
    def password(self, password):
        """设置密码时进行哈希处理"""
        self.password_hash = generate_password_hash(password)
    
    def verify_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def has_permission(self, permission_name):
        """检查用户是否拥有指定权限（包括角色权限和直接权限）"""
        # 超级管理员拥有所有权限
        if self.has_role('超级管理员'):
            return True
            
        # 检查直接权限
        for perm in self.permissions:
            if perm.name == permission_name or perm.name.replace('.', '_') == permission_name or perm.name.replace('_', '.') == permission_name:
                return True
            
        # 通过角色获取权限
        for role in self.roles:
            for perm in role.permissions:
                # 支持点表示法，如'user.view'
                if perm.name == permission_name or perm.name.replace('.', '_') == permission_name or perm.name.replace('_', '.') == permission_name:
                    return True
        return False
    
    def has_role(self, role_name):
        """检查用户是否拥有指定角色"""
        for role in self.roles:
            if role.name == role_name:
                return True
        return False
    
    @property
    def is_admin(self):
        """检查用户是否为管理员"""
        return self.has_role('admin') or self.has_role('超级管理员')
    
    def get_menu_items(self):
        """获取用户可见的菜单项"""
        from app.models.menu import MenuItem

        # 超级管理员可以看到所有菜单
        if self.has_role('超级管理员'):
            return MenuItem.query.filter_by(is_active=True).order_by(MenuItem.order).all()

        # 使用菜单ID集合来避免重复
        menu_ids = set()

        # 获取用户直接分配的菜单权限
        for menu in self.menu_permissions:
            if menu.is_active:
                menu_ids.add(menu.id)

        # 获取通过角色权限获得的菜单项
        for role in self.roles:
            for perm in role.permissions:
                # 根据权限查找对应的菜单项
                menu_items = MenuItem.query.filter_by(permission=perm.name, is_active=True).all()
                for menu in menu_items:
                    menu_ids.add(menu.id)

        # 根据ID获取去重后的菜单项并按顺序排序
        if menu_ids:
            return MenuItem.query.filter(MenuItem.id.in_(menu_ids), MenuItem.is_active == True).order_by(MenuItem.order).all()
        else:
            return []
    
    def __repr__(self):
        return f'<User {self.username}>'


class Role(db.Model):
    """角色模型"""
    __tablename__ = 'roles'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    # 角色拥有的权限（多对多）
    permissions = db.relationship('Permission', secondary=role_permissions, 
                                 backref=db.backref('roles', lazy='dynamic'))
    
    def __repr__(self):
        return f'<Role {self.name}>'


class Permission(db.Model):
    """权限模型"""
    __tablename__ = 'permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    description = db.Column(db.String(200))
    
    def __repr__(self):
        return f'<Permission {self.name}>'


class UserRole(db.Model):
    """用户角色关联模型（用于直接操作）"""
    __tablename__ = 'user_role'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    # 关联关系
    user = db.relationship('User', backref=db.backref('user_roles', lazy='dynamic'))
    role = db.relationship('Role', backref=db.backref('user_roles', lazy='dynamic'))
    
    def __repr__(self):
        return f'<UserRole {self.user_id}:{self.role_id}>' 