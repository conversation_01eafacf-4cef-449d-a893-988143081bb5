"""
系统管理命令
"""
import click
import os
from flask.cli import with_appcontext
from app.commands import system_cli
from app.models import db
from app.models.user import User, Role, Permission, UserRole


@system_cli.command('create-admin')
@click.option('--username', prompt=True, help='管理员用户名')
@click.option('--password', prompt=True, hide_input=True, confirmation_prompt=True, help='管理员密码')
@click.option('--email', prompt=True, help='管理员邮箱')
@with_appcontext
def create_admin(username, password, email):
    """创建管理员用户"""
    # 检查用户是否已存在
    user = User.query.filter_by(username=username).first()
    if user:
        click.echo(f'用户 {username} 已存在')
        return
    
    # 创建用户
    user = User(
        username=username,
        email=email,
        password=password,
        is_active=True
    )
    db.session.add(user)
    db.session.flush()
    
    # 获取管理员角色
    admin_role = Role.query.filter_by(name='admin').first()
    if not admin_role:
        click.echo('管理员角色不存在，请先初始化角色')
        db.session.rollback()
        return
    
    # 分配角色
    user_role = UserRole(user_id=user.id, role_id=admin_role.id)
    db.session.add(user_role)
    
    db.session.commit()
    click.echo(f'管理员 {username} 创建成功')


@system_cli.command('init-db')
@with_appcontext
def init_db():
    """初始化数据库"""
    # 创建所有表
    db.create_all()
    
    # 检查是否已有数据
    if User.query.first():
        click.echo('数据库已初始化')
        return
    
    # 创建权限
    permissions = [
        # 基础权限
        Permission(name='admin_access', description='管理员访问权限'),
        Permission(name='content_create', description='创建文案权限'),
        Permission(name='content_review', description='审核文案权限'),
        Permission(name='content_manage', description='管理文案权限'),
        Permission(name='template_manage', description='管理模板权限'),
        Permission(name='topic_manage', description='管理话题权限'),
        Permission(name='client_manage', description='管理客户权限'),
        Permission(name='client_view', description='查看客户权限'),
        Permission(name='task_manage', description='管理任务权限'),
        
        # 额外权限
        Permission(name='content_publish', description='发布文案权限'),
        Permission(name='content_export', description='导出文案权限'),
        Permission(name='content_import', description='导入文案权限'),
        Permission(name='stats_view', description='查看统计数据权限'),
        Permission(name='system_settings', description='管理系统设置权限'),
        Permission(name='notification_manage', description='管理通知权限'),
        Permission(name='display_manage', description='管理展示计划权限'),
    ]
    
    for permission in permissions:
        db.session.add(permission)
    
    db.session.commit()
    
    # 创建角色
    admin_role = Role(name='admin', description='管理员')
    editor_role = Role(name='editor', description='编辑')
    reviewer_role = Role(name='reviewer', description='审核员')
    client_role = Role(name='client', description='客户')
    
    db.session.add(admin_role)
    db.session.add(editor_role)
    db.session.add(reviewer_role)
    db.session.add(client_role)
    
    db.session.commit()
    
    # 分配权限
    # 管理员拥有所有权限
    for permission in Permission.query.all():
        admin_role.permissions.append(permission)
    
    # 编辑拥有创建文案和管理模板权限
    editor_role.permissions.append(Permission.query.filter_by(name='content_create').first())
    editor_role.permissions.append(Permission.query.filter_by(name='template_manage').first())
    editor_role.permissions.append(Permission.query.filter_by(name='client_view').first())
    
    # 审核员拥有审核文案权限
    reviewer_role.permissions.append(Permission.query.filter_by(name='content_review').first())
    reviewer_role.permissions.append(Permission.query.filter_by(name='client_view').first())
    
    # 客户拥有查看客户权限
    client_role.permissions.append(Permission.query.filter_by(name='client_view').first())
    
    db.session.commit()
    
    # 创建默认管理员用户
    admin = User(
        username='admin',
        email='<EMAIL>',
        real_name='系统管理员',
        is_active=True
    )
    admin.password = 'admin123'  # 默认密码，生产环境应修改
    admin.roles.append(admin_role)
    db.session.add(admin)
    
    db.session.commit()
    click.echo('数据库初始化成功，已创建默认管理员账号 admin/admin123')


@system_cli.command('backup-db')
@click.option('--output', default='backup.sql', help='备份文件名')
@with_appcontext
def backup_db(output):
    """备份数据库"""
    from flask import current_app
    import subprocess
    
    # 获取数据库配置
    db_uri = current_app.config['SQLALCHEMY_DATABASE_URI']
    
    # 解析数据库URI
    if db_uri.startswith('mysql'):
        # 提取MySQL连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^/]+)/([^?]+)', db_uri)
        if match:
            user, password, host, dbname = match.groups()
            
            # 创建备份命令
            cmd = f'mysqldump -u {user} -p{password} -h {host} {dbname} > {output}'
            
            try:
                # 执行备份命令
                subprocess.run(cmd, shell=True, check=True)
                click.echo(f'数据库备份成功，文件名: {output}')
            except subprocess.CalledProcessError:
                click.echo('数据库备份失败')
        else:
            click.echo('无法解析MySQL连接信息')
    else:
        click.echo('仅支持MySQL数据库备份')


@system_cli.command('reset-permissions')
@with_appcontext
def reset_permissions():
    """重置所有权限和角色"""
    # 先删除角色权限关联
    db.session.execute(db.text("DELETE FROM role_permissions"))
    
    # 删除所有权限
    Permission.query.delete()
    
    # 创建新权限
    permissions = [
        # 基础权限
        Permission(name='admin_access', description='管理员访问权限'),
        Permission(name='content_create', description='创建文案权限'),
        Permission(name='content_review', description='审核文案权限'),
        Permission(name='content_manage', description='管理文案权限'),
        Permission(name='template_manage', description='管理模板权限'),
        Permission(name='topic_manage', description='管理话题权限'),
        Permission(name='client_manage', description='管理客户权限'),
        Permission(name='client_view', description='查看客户权限'),
        Permission(name='task_manage', description='管理任务权限'),
        
        # 额外权限
        Permission(name='content_publish', description='发布文案权限'),
        Permission(name='content_export', description='导出文案权限'),
        Permission(name='content_import', description='导入文案权限'),
        Permission(name='stats_view', description='查看统计数据权限'),
        Permission(name='system_settings', description='管理系统设置权限'),
        Permission(name='notification_manage', description='管理通知权限'),
        Permission(name='display_manage', description='管理展示计划权限'),
    ]
    
    for permission in permissions:
        db.session.add(permission)
    
    db.session.commit()
    
    # 为管理员角色分配所有权限
    admin_role = Role.query.filter_by(name='admin').first()
    if admin_role:
        for permission in Permission.query.all():
            admin_role.permissions.append(permission)
    
    db.session.commit()
    click.echo('权限重置成功') 