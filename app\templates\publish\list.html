{% extends "base_simple.html" %}

{% block title %}发布列表 - 小红书文案管理系统{% endblock %}

{% block content %}
<!-- 页面内容 -->
<div class="container mt-3">
    <h3>发布列表</h3>
    
    <!-- 状态筛选 -->
    <div class="status-filter">
        <a href="{{ url_for('main_simple.publish_list') }}" class="btn btn-outline-secondary {% if not current_status %}active{% endif %}">
            全部
        </a>
        <a href="{{ url_for('main_simple.publish_list', status='pending') }}" class="btn btn-outline-warning {% if current_status == 'pending' %}active{% endif %}">
            <i class="bi bi-clock"></i> 等待发布
        </a>
        <a href="{{ url_for('main_simple.publish_list', status='publishing') }}" class="btn btn-outline-info {% if current_status == 'publishing' %}active{% endif %}">
            <i class="bi bi-arrow-repeat"></i> 发布中
        </a>
        <a href="{{ url_for('main_simple.publish_list', status='published') }}" class="btn btn-outline-success {% if current_status == 'published' %}active{% endif %}">
            <i class="bi bi-check-circle"></i> 已发布
        </a>
        <a href="{{ url_for('main_simple.publish_list', status='failed') }}" class="btn btn-outline-danger {% if current_status == 'failed' %}active{% endif %}">
            <i class="bi bi-x-circle"></i> 发布失败
        </a>
    </div>

    <table class="table table-bordered table-hover">
        <thead>
            <tr>
                <th>标题</th>
                <th>图片</th>
                <th>客户</th>
                <th>发布状态</th>
                <th>发布时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for item in content_data %}
            <tr>
                <td>{{ item.content.title }}</td>
                <td>
                    {% if item.images %}
                        <span class="badge bg-primary">{{ item.image_count }} 张图片</span>
                    {% else %}
                        <span class="text-muted">无图片</span>
                    {% endif %}
                </td>
                <td>{{ item.content.client.name if item.content.client else '未知' }}</td>
                <td>
                    {% if item.content.workflow_status == 'published' %}
                        <span class="badge bg-success">
                            <i class="bi bi-check-circle"></i> 已发布
                        </span>
                    {% elif item.content.workflow_status == 'publishing' %}
                        <span class="badge bg-info">
                            <i class="bi bi-arrow-repeat"></i> 发布中
                        </span>
                    {% elif item.content.workflow_status == 'publish_failed' %}
                        <span class="badge bg-danger">
                            <i class="bi bi-x-circle"></i> 发布失败
                        </span>
                    {% elif item.content.workflow_status == 'pending_publish' %}
                        <span class="badge bg-warning">
                            <i class="bi bi-clock"></i> 等待发布
                        </span>
                    {% else %}
                        <span class="badge bg-secondary">{{ item.content.workflow_status }}</span>
                    {% endif %}
                </td>
                <td>
                    {% if item.content.published_at %}
                        {{ item.content.published_at.strftime('%Y-%m-%d %H:%M:%S') }}
                    {% elif item.content.created_at %}
                        {{ item.content.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                    {% else %}
                        <span class="text-muted">未发布</span>
                    {% endif %}
                </td>
                <td>
                    {% if item.content.workflow_status == 'publish_failed' %}
                        <button class="btn btn-warning btn-sm" onclick="retryPublish({{ item.content.id }})">重试</button>
                    {% elif item.content.workflow_status == 'pending_publish' %}
                        <button class="btn btn-info btn-sm" onclick="cancelPublish({{ item.content.id }})">取消</button>
                    {% endif %}
                    <button class="btn btn-secondary btn-sm" onclick="viewDetails({{ item.content.id }})">详情</button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    <!-- 分页 -->
    {% if pagination.pages > 1 %}
    <nav>
        <ul class="pagination justify-content-center">
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('main_simple.publish_list', page=pagination.prev_num, status=current_status) }}">上一页</a>
            </li>
            {% endif %}
            
            {% for page_num in pagination.iter_pages() %}
                {% if page_num %}
                    {% if page_num != pagination.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('main_simple.publish_list', page=page_num, status=current_status) }}">{{ page_num }}</a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('main_simple.publish_list', page=pagination.next_num, status=current_status) }}">下一页</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
// 重试发布
function retryPublish(contentId) {
    if (confirm('确定要重试发布这篇文案吗？')) {
        fetch(`/simple/api/publish/${contentId}/retry`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('重试发布已开始！');
                location.reload();
            } else {
                alert('重试失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('重试失败，请重试');
        });
    }
}

// 取消发布
function cancelPublish(contentId) {
    if (confirm('确定要取消发布这篇文案吗？')) {
        fetch(`/simple/api/publish/${contentId}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('发布已取消！');
                location.reload();
            } else {
                alert('取消失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('取消失败，请重试');
        });
    }
}

// 查看详情
function viewDetails(contentId) {
    // 可以打开一个模态框显示文案详情
    alert('查看详情功能待实现');
}
</script>
{% endblock %} 