"""
字符统计工具模块
提供统一的字符统计算法，确保前后端一致性
"""

import math
import re


def calculate_title_length(text):
    """
    计算标题字符长度
    规则：
    - 中文字符算1个
    - emoji算1个  
    - 英文字母、数字算0.5个
    - 其他字符算1个
    - 最终结果向上取整
    
    Args:
        text (str): 要计算的文本
        
    Returns:
        int: 字符长度
    """
    if not text:
        return 0
        
    length = 0
    for char in text:
        if ord(char) > 127:
            # 检查是否是中文字符
            if re.match(r'[\u4e00-\u9fff]', char):
                length += 1  # 中文字符算1个
            else:
                length += 1  # emoji算1个
        else:
            # 字母数字算0.5个
            length += 0.5
    
    return math.ceil(length)  # 向上取整


def calculate_content_length(text):
    """
    计算内容字符长度
    规则：按照小红书的统计方式
    - 所有字符（中文、英文、数字、emoji、标点符号、空格）都算1个

    Args:
        text (str): 要计算的文本

    Returns:
        int: 字符长度
    """
    if not text:
        return 0

    # 小红书的内容统计：每个字符都算1个
    return len(text)


def get_title_status(length, max_length=20):
    """
    获取标题状态
    
    Args:
        length (int): 标题长度
        max_length (int): 最大长度限制，默认20
        
    Returns:
        str: 'success' 或 'danger'
    """
    return 'success' if length <= max_length else 'danger'


def get_content_status(length, min_length=1, max_length=1000):
    """
    获取内容状态

    Args:
        length (int): 内容长度
        min_length (int): 最小长度限制，默认1（只要不为空）
        max_length (int): 最大长度限制，默认1000

    Returns:
        str: 'success' 或 'danger'
    """
    return 'success' if min_length <= length <= max_length else 'danger'


# 为了兼容性，提供简单的字符串长度计算函数
def calculate_simple_length(text):
    """
    简单字符串长度计算（用于兼容旧代码）
    
    Args:
        text (str): 要计算的文本
        
    Returns:
        int: 字符串长度
    """
    return len(text) if text else 0
