-- 添加客户分享链接的新字段
-- 执行时间：2025-07-22

-- 添加访问密钥字段
ALTER TABLE client_share_links ADD COLUMN access_key VARCHAR(10);

-- 添加任务ID字段
ALTER TABLE client_share_links ADD COLUMN task_id INT;

-- 添加外键约束
ALTER TABLE client_share_links ADD CONSTRAINT fk_client_share_links_task_id 
FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE SET NULL;

-- 添加索引
CREATE INDEX idx_client_share_links_task_id ON client_share_links(task_id);
CREATE INDEX idx_client_share_links_access_key ON client_share_links(access_key);

-- 更新现有记录的访问密钥（可选）
-- UPDATE client_share_links SET access_key = UPPER(SUBSTRING(MD5(RAND()), 1, 4)) WHERE access_key IS NULL;
