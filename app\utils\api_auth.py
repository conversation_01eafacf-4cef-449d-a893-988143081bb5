"""
API认证工具
"""
from functools import wraps
from flask import request, jsonify, current_app
from app.models import SystemSetting


def api_key_required(f):
    """
    API密钥验证装饰器
    用于验证API请求中的密钥是否有效
    
    使用方法：
    @api_bp.route('/some_endpoint')
    @api_key_required
    def some_endpoint():
        # 业务逻辑
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            print(f"API认证检查: {request.method} {request.path}")

            # 从请求头中获取API密钥
            api_key = request.headers.get('X-API-Key')
            print(f"收到API密钥: {api_key[:10] if api_key else 'None'}...")

            # 如果没有提供API密钥，返回401错误
            if not api_key:
                print("API密钥缺失")
                return jsonify({
                    'success': False,
                    'error': '缺少API密钥',
                    'error_code': 'missing_api_key'
                }), 401

            # 从数据库获取有效的API密钥
            api_key_setting = SystemSetting.query.filter_by(key='API_KEY').first()

            # 如果数据库中没有设置API密钥，或者提供的密钥不匹配，返回401错误
            if not api_key_setting or api_key_setting.value != api_key:
                print(f"API密钥验证失败: 数据库中的密钥={api_key_setting.value[:10] if api_key_setting else 'None'}...")
                return jsonify({
                    'success': False,
                    'error': 'API密钥无效',
                    'error_code': 'invalid_api_key'
                }), 401

            print("API密钥验证通过")
            # API密钥验证通过，继续执行被装饰的函数
            return f(*args, **kwargs)

        except Exception as e:
            print(f"API认证异常: {e}")
            return jsonify({
                'success': False,
                'error': 'API认证失败',
                'message': str(e)
            }), 500
    
    return decorated_function


def generate_api_key(length=32):
    """
    生成随机API密钥
    
    参数:
        length: 密钥长度，默认32位
    
    返回:
        生成的随机密钥
    """
    import secrets
    import string
    
    alphabet = string.ascii_letters + string.digits
    api_key = ''.join(secrets.choice(alphabet) for _ in range(length))
    
    return api_key 