import random
import re
import json
from datetime import datetime
from flask import current_app
from sqlalchemy import func
from app.models import db, Template, Content, Batch, Task

def generate_contents(client_id, task_id, batch_id, template_category_id, count, 
                     keywords, required_topics, random_topics, random_topics_count,
                     at_users, location, publish_priority, avoid_duplicates, 
                     allow_template_duplicate, current_user):
    """
    生成文案内容
    
    参数:
    - client_id: 客户ID
    - task_id: 任务ID
    - batch_id: 批次ID
    - template_category_id: 模板分类ID
    - count: 生成数量
    - keywords: 关键词字典 {标记名: [关键词列表]}
    - required_topics: 必选话题列表
    - random_topics: 随机话题列表
    - random_topics_count: 随机话题数量
    - at_users: @用户列表
    - location: 定位信息
    - publish_priority: 发布优先级
    - avoid_duplicates: 是否避免重复内容
    - allow_template_duplicate: 是否允许模板重复 (0:任务内不重复, 1:允许重复, 2:批次内不重复)
    - current_user: 当前用户
    
    返回:
    - 生成的文案列表
    """
    try:
        # 获取可用模板
        available_templates = get_available_templates(
            template_category_id, task_id, batch_id, 
            int(allow_template_duplicate)
        )
        
        if not available_templates:
            current_app.logger.warning(f"没有找到可用的模板，分类ID: {template_category_id}")
            return []
        
        # 如果不允许重复且可用模板数量小于请求数量，调整数量
        if int(allow_template_duplicate) == 0 and len(available_templates) < count:
            current_app.logger.warning(
                f"可用模板数量({len(available_templates)})小于请求数量({count})，"
                f"调整生成数量为{len(available_templates)}"
            )
            count = len(available_templates)
        
        # 随机选择模板
        selected_templates = random.sample(available_templates, min(count, len(available_templates)))
        
        # 如果允许重复且需要更多模板
        if int(allow_template_duplicate) > 0 and len(selected_templates) < count:
            # 重复选择模板直到达到请求数量
            additional_count = count - len(selected_templates)
            additional_templates = []
            
            for _ in range(additional_count):
                additional_templates.append(random.choice(available_templates))
            
            selected_templates.extend(additional_templates)
        
        # 解析关键词
        keywords_dict = parse_keywords(keywords)
        
        # 处理话题
        all_topics = process_topics(required_topics, random_topics, random_topics_count)
        
        # 处理@用户
        processed_at_users = process_at_users(at_users)
        
        # 生成文案
        contents = []
        for template in selected_templates:
            content = generate_single_content(
                client_id, task_id, batch_id, template,
                keywords_dict, all_topics, processed_at_users,
                location, publish_priority, current_user
            )
            contents.append(content)
        
        # 如果需要避免重复内容，进行检查
        if avoid_duplicates:
            contents = remove_duplicate_contents(contents)
        
        # 保存到数据库
        db.session.add_all(contents)
        db.session.commit()
        
        # 更新批次的文案数量
        update_batch_content_count(batch_id)
        
        return contents
    
    except Exception as e:
        current_app.logger.error(f"生成文案出错: {str(e)}")
        db.session.rollback()
        raise e

def get_available_templates(category_id, task_id, batch_id, allow_duplicate):
    """
    获取可用模板
    
    参数:
    - category_id: 模板分类ID
    - task_id: 任务ID
    - batch_id: 批次ID
    - allow_duplicate: 是否允许模板重复 (0:任务内不重复, 1:允许重复, 2:批次内不重复, 3:客户所有任务内不重复)
    
    返回:
    - 可用模板列表
    """
    # 获取分类下的所有模板
    templates = Template.query.filter_by(category_id=category_id, status=True).all()
    
    # 如果允许重复，直接返回所有模板
    if allow_duplicate == 1:
        return templates
    
    # 获取已使用的模板ID
    used_template_ids = []
    
    if allow_duplicate == 0:
        # 任务内不重复：获取该任务下所有已使用的模板ID（限制在当前分类）
        used_template_ids = db.session.query(Content.template_id).join(
            Template, Content.template_id == Template.id
        ).filter(
            Content.task_id == task_id,
            Template.category_id == category_id,
            Content.is_deleted != True
        ).distinct().all()
    elif allow_duplicate == 2:
        # 批次内不重复：获取该批次下所有已使用的模板ID（限制在当前分类）
        used_template_ids = db.session.query(Content.template_id).join(
            Template, Content.template_id == Template.id
        ).filter(
            Content.batch_id == batch_id,
            Template.category_id == category_id,
            Content.is_deleted != True
        ).distinct().all()
    elif allow_duplicate == 3:
        # 获取该任务所属的客户ID
        task = Task.query.get(task_id)
        if task:
            client_id = task.client_id
            # 客户所有任务内不重复：获取该客户所有任务下已使用的模板ID（限制在当前分类）
            used_template_ids = db.session.query(Content.template_id).join(
                Task, Content.task_id == Task.id
            ).join(
                Template, Content.template_id == Template.id
            ).filter(
                Task.client_id == client_id,
                Template.category_id == category_id,
                Content.is_deleted != True
            ).distinct().all()
    
    # 将查询结果转换为ID列表
    used_template_ids = [id[0] for id in used_template_ids]
    
    # 过滤掉已使用的模板
    available_templates = [t for t in templates if t.id not in used_template_ids]
    
    return available_templates

def parse_keywords(keywords_text):
    """解析关键词文本为字典"""
    keywords_dict = {}
    if not keywords_text:
        return keywords_dict
    
    for line in keywords_text.split('\n'):
        if ':' in line:
            mark, values = line.split(':', 1)
            keywords_dict[mark] = values.split(',')
    
    return keywords_dict

def process_topics(required_topics, random_topics, random_topics_count):
    """处理话题，组合必选话题和随机话题"""
    # 处理必选话题
    required_list = required_topics.strip().split('\n') if required_topics else []
    required_list = [t.strip() for t in required_list if t.strip()]
    
    # 处理随机话题
    random_list = random_topics.strip().split('\n') if random_topics else []
    random_list = [t.strip() for t in random_list if t.strip()]
    
    # 如果随机话题数量大于可用随机话题数量，调整数量
    random_topics_count = min(random_topics_count, len(random_list))
    
    # 随机选择指定数量的随机话题
    selected_random_topics = random.sample(random_list, random_topics_count) if random_list and random_topics_count > 0 else []
    
    # 组合所有话题
    all_topics = required_list + selected_random_topics
    
    return all_topics

def process_at_users(at_users_text):
    """处理@用户文本"""
    if not at_users_text:
        return []
    
    at_users_list = at_users_text.strip().split('\n')
    at_users_list = [u.strip() for u in at_users_list if u.strip()]
    
    return at_users_list

def generate_single_content(client_id, task_id, batch_id, template, keywords_dict, 
                           topics, at_users, location, publish_priority, current_user):
    """生成单篇文案"""
    # 替换模板中的标记
    title = template.title
    content = template.content
    
    # 替换关键词
    for mark, values in keywords_dict.items():
        if values:
            replacement = random.choice(values)
            pattern = r'\{' + re.escape(mark) + r'\}'
            title = re.sub(pattern, replacement, title)
            content = re.sub(pattern, replacement, content)
    
    # 创建文案对象
    content_obj = Content(
        client_id=client_id,
        task_id=task_id,
        batch_id=batch_id,
        template_id=template.id,
        title=title,
        content=content,
        topics=json.dumps(topics) if topics else None,
        location=location,
        publish_priority=publish_priority,
        created_by=current_user.id
    )
    
    return content_obj

def remove_duplicate_contents(contents):
    """移除重复内容"""
    unique_contents = []
    content_texts = set()
    
    for content in contents:
        # 使用标题和内容的组合作为唯一标识
        content_key = f"{content.title}|{content.content}"
        
        if content_key not in content_texts:
            content_texts.add(content_key)
            unique_contents.append(content)
    
    return unique_contents

def update_batch_content_count(batch_id):
    """更新批次的文案数量"""
    count = Content.query.filter_by(batch_id=batch_id).count()
    batch = Batch.query.get(batch_id)
    if batch:
        batch.content_count = count
        db.session.commit() 