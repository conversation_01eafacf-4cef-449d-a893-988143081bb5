{% extends 'base.html' %}

{% block title %}角色管理{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">角色管理</h1>
        <a href="{{ url_for('admin.admin_role') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 编辑角色
        </a>
    </div>

    <div class="card shadow">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>角色名称</th>
                            <th>描述</th>
                            <th>权限</th>
                            <th>用户数量</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for role in roles %}
                        <tr>
                            <td>{{ role.id }}</td>
                            <td>{{ role.name }}</td>
                            <td>{{ role.description or '-' }}</td>
                            <td>
                                {% for permission in role.permissions %}
                                <span class="badge bg-info">{{ permission.name }}</span>
                                {% else %}
                                <span class="badge bg-secondary">无权限</span>
                                {% endfor %}
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ role.users.count() }}</span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %} 