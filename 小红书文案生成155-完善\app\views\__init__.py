# -*- coding: utf-8 -*-
"""
视图模块
"""

from app.views.main import main_bp
from app.views.auth import auth_bp
from app.views.template import template_bp
from app.views.api import api_bp
from app.views.first_review import first_review_bp
from app.views.image_management import image_management_bp
from app.views.final_review import final_review_bp
from app.views.main_simple import main_simple_bp
from app.views.client_review import client_review_bp
from app.views.user_management import user_management_bp

def register_blueprints(app):
    """注册蓝图"""
    app.register_blueprint(main_bp)
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(template_bp, url_prefix='/templates')
    app.register_blueprint(first_review_bp)
    app.register_blueprint(image_management_bp)
    app.register_blueprint(final_review_bp)

    # 注册简化版主页面蓝图（新后台）
    app.register_blueprint(main_simple_bp, url_prefix='/simple')

    # 注册客户审核蓝图
    app.register_blueprint(client_review_bp)

    # 注册用户管理蓝图
    app.register_blueprint(user_management_bp)

    # 注册API蓝图（蓝图本身已经有/api/v1前缀）
    app.register_blueprint(api_bp)