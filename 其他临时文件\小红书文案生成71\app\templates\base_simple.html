<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}小红书文案生成系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    
    <!-- 自定义样式 -->
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
        }

        /* 标记美化样式 */
        .template-mark {
            display: inline-block;
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            color: #1565c0;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.85em;
            font-weight: 600;
            margin: 0 3px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.08);
            border: 1px solid #bbdefb;
            position: relative;
        }

        .template-mark::before {
            content: '🏷️';
            margin-right: 4px;
            font-size: 0.9em;
        }

        .template-mark:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.12);
            background: linear-gradient(135deg, #bbdefb 0%, #e1bee7 100%);
        }

        /* 模板标题行样式 */
        .template-title {
            line-height: 1.6;
            max-width: 300px;
            word-wrap: break-word;
        }

        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }

        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 0;
        }

        .sidebar .nav-link:hover {
            background-color: #e9ecef;
            color: #495057;
        }

        .sidebar .nav-link.active {
            background-color: #007bff;
            color: white;
        }

        .main-content {
            min-height: 100vh;
            padding: 0;
        }

        .page-content {
            display: none;
            padding: 20px;
        }

        .page-content.active {
            display: block;
        }

        .top-bar {
            background-color: white;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem;
            margin-bottom: 0;
        }

        .breadcrumb {
            margin-bottom: 0;
            background-color: transparent;
        }

    /* 右上角提示框样式 */
    .toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
    }

    .custom-toast {
        min-width: 300px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border-left: 4px solid #28a745;
        animation: slideInRight 0.3s ease-out;
    }

    .custom-toast.error {
        border-left-color: #dc3545;
    }

    .custom-toast.warning {
        border-left-color: #ffc107;
    }

    .custom-toast.info {
        border-left-color: #17a2b8;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .custom-toast.hiding {
        animation: slideOutRight 0.3s ease-in;
    }

    .toast-header {
        background: transparent;
        border-bottom: 1px solid #dee2e6;
        padding: 8px 12px;
    }

    .toast-body {
        padding: 12px;
        color: #495057;
    }

    .toast-close {
        background: none;
        border: none;
        font-size: 18px;
        color: #6c757d;
        cursor: pointer;
        padding: 0;
        margin-left: auto;
    }

    .toast-close:hover {
        color: #495057;
    }
    </style>

    {% block styles %}{% endblock %}
</head>
<body>
    <!-- 右上角提示框容器 -->
    <div class="toast-container" id="toastContainer"></div>

    <div class="container-fluid">
        <div class="row">
            <!-- 左侧菜单 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>功能菜单</span>
                    </h6>
                    <ul class="nav flex-column">
                        {% if current_user.has_permission('dashboard_access') %}
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('dashboard')" data-page="dashboard">
                                <i class="bi bi-speedometer2"></i> 控制台
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.has_permission('template_manage') %}
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('templates')" data-page="templates">
                                <i class="bi bi-file-text"></i> 模板管理
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.has_permission('client_manage') %}
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('clients')" data-page="clients">
                                <i class="bi bi-people"></i> 客户管理
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.has_permission('content_generate') %}
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('content')" data-page="content">
                                <i class="bi bi-pencil-square"></i> 内容生成
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.has_permission('content_manage') %}
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('review-content')" data-page="review-content">
                                <i class="bi bi-clipboard-check"></i> 初审文案
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.has_permission('image_manage') %}
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('image-upload')" data-page="image-upload">
                                <i class="bi bi-image"></i> 图片上传
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.has_permission('review.final') %}
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('final-review')" data-page="final-review">
                                <i class="bi bi-check2-square"></i> 最终审核
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.has_permission('client_manage') %}
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('client-review')" data-page="client-review">
                                <i class="bi bi-person-check"></i> 客户审核
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.has_permission('publish_manage') %}
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('publish-manage')" data-page="publish-manage">
                                <i class="bi bi-send"></i> 发布管理
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.has_permission('user_manage') %}
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('users')" data-page="users">
                                <i class="bi bi-person-gear"></i> 用户管理
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.has_permission('system_settings') %}
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('system')" data-page="system">
                                <i class="bi bi-gear"></i> 系统设置
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                    
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>系统设置</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.logout') }}">
                                <i class="bi bi-box-arrow-right"></i> 退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 main-content">
                <!-- 顶部面包屑 -->
                <div class="top-bar">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="#" onclick="showPage('dashboard')">首页</a></li>
                            <li class="breadcrumb-item active" aria-current="page" id="current-page-title">控制台</li>
                        </ol>
                    </nav>
                </div>

                <!-- 页面内容容器 -->
                <div id="page-container">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS - 使用简化版本 -->
    <script src="{{ url_for('static', filename='js/bootstrap-simple.min.js') }}"></script>
    
    <!-- 简化的页面切换系统 -->
    <script>
        // 页面配置 - 所有URL都指向简化后台路由
        const pageConfig = {
            'dashboard': { title: '控制台', url: '/simple/dashboard' },
            'templates': { title: '模板管理', url: '/simple/templates' },
            'clients': { title: '客户管理', url: '/simple/clients' },
            'content': { title: '内容生成', url: '/simple/content' },
            'review-content': { title: '初审文案', url: '/simple/review-content' },
            'image-upload': { title: '图片上传', url: '/simple/image-upload' },
            'final-review': { title: '最终审核', url: '/simple/final-review' },
            'client-review': { title: '客户审核', url: '/simple/client-review' },
            'publish-manage': { title: '发布管理', url: '/simple/publish-manage' },
            'users': { title: '用户管理', url: '/simple/users' },
            'system': { title: '系统设置', url: '/simple/system' }
        };

        // 当前活动页面
        let currentPage = 'dashboard';

        // 全局标记处理函数
        window.processTemplateMarks = function() {
            console.log('开始处理模板标记...');
            const titleElements = document.querySelectorAll('.template-title');
            console.log('找到标题元素数量:', titleElements.length);

            titleElements.forEach(function(titleElement) {
                let title = titleElement.innerHTML;
                console.log('处理标题:', title);

                // 如果已经包含 template-mark 标签，跳过处理
                if (title.includes('template-mark')) {
                    console.log('跳过已格式化的标题');
                    return;
                }

                // 保持 {标记名} 格式并添加美化样式
                title = title.replace(/\{([^}]+)\}/g, function(match, markName) {
                    console.log('找到标记:', markName);
                    // 使用统一的标记样式，保持{}格式
                    return `<span class="template-mark">{${markName}}</span>`;
                });

                titleElement.innerHTML = title;
            });
        };

        // 显示指定页面
        function showPage(pageId) {
            console.log('切换到页面:', pageId);
            
            // 隐藏所有页面内容
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });
            
            // 显示目标页面
            const targetPage = document.getElementById('page-' + pageId);
            if (targetPage) {
                targetPage.classList.add('active');

                // 检查页面内容是否为空或只有注释，如果是则加载内容
                const content = targetPage.innerHTML.trim();
                const isEmpty = !content ||
                               content.includes('这里将通过AJAX加载') ||
                               content.length < 100;

                if (isEmpty) {
                    console.log('页面内容为空，开始加载:', pageId);
                    loadPageContent(pageId, targetPage);
                }
            } else {
                // 如果页面不存在，通过AJAX加载
                loadPageContent(pageId);
            }
            
            // 更新菜单状态
            updateMenuState(pageId);
            
            // 更新面包屑
            updateBreadcrumb(pageId);
            
            // 更新URL - 使用pushState而不是hash
            const config = pageConfig[pageId];
            if (config) {
                const newUrl = `/simple/${pageId}`;
                window.history.pushState({ page: pageId }, config.title, newUrl);
                document.title = config.title + ' - 小红书文案生成系统';
            }
            
            currentPage = pageId;

            // 如果是模板页面，处理标记美化
            if (pageId === 'templates') {
                setTimeout(function() {
                    if (typeof window.processTemplateMarks === 'function') {
                        console.log('页面切换后处理模板标记美化...');
                        window.processTemplateMarks();
                    }
                }, 100);
            }
        }

        // 更新菜单激活状态
        function updateMenuState(pageId) {
            document.querySelectorAll('.sidebar .nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            const activeLink = document.querySelector(`[data-page="${pageId}"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }
        }

        // 更新面包屑
        function updateBreadcrumb(pageId) {
            const config = pageConfig[pageId];
            if (config) {
                document.getElementById('current-page-title').textContent = config.title;
            }
        }

        // 加载页面内容
        function loadPageContent(pageId, targetPage = null) {
            const config = pageConfig[pageId];
            if (!config) return;

            console.log('加载页面内容:', pageId, config.url);

            // 确定目标容器
            let container = targetPage;
            if (!container) {
                container = document.getElementById('page-container');
                container.innerHTML = `
                    <div class="page-content active" id="page-${pageId}">
                        <div class="text-center py-5">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载${config.title}...</p>
                        </div>
                    </div>
                `;
                container = document.getElementById('page-' + pageId);
            } else {
                // 显示加载指示器
                container.innerHTML = `
                    <div class="text-center py-5">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载${config.title}...</p>
                    </div>
                `;
            }

            // 加载页面内容
            fetch(config.url, {
                headers: { 'X-Requested-With': 'XMLHttpRequest' }
            })
            .then(response => {
                console.log('响应状态:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text();
            })
            .then(html => {
                console.log('收到HTML长度:', html.length);

                // 直接使用返回的HTML内容
                if (html && html.trim().length > 0) {
                    container.innerHTML = html;
                    console.log('页面内容加载成功:', pageId);

                    // 执行页面中的脚本
                    executePageScripts(container);

                    // 重新初始化AJAX表单处理
                    setTimeout(function() {
                        reinitializeAjaxForms();
                    }, 100);

                    // 如果是模板页面，处理标记格式
                    if (pageId === 'templates') {
                        setTimeout(function() {
                            if (typeof window.processTemplateMarks === 'function') {
                                console.log('AJAX加载后处理模板标记美化...');
                                window.processTemplateMarks();
                            }
                        }, 200);
                    }
                } else {
                    container.innerHTML = '<div class="alert alert-warning">页面内容为空</div>';
                }
            })
            .catch(error => {
                console.error('页面加载失败:', error);
                container.innerHTML = `<div class="alert alert-danger">页面加载失败: ${error.message}</div>`;
            });
        }

        // 执行页面脚本
        function executePageScripts(container) {
            // 如果传入的是document，查找所有脚本
            // 如果传入的是容器元素，只查找容器内的脚本
            const scripts = container.querySelectorAll ?
                           container.querySelectorAll('script') :
                           container.querySelectorAll('script');

            scripts.forEach(script => {
                if (script.src) {
                    // 外部脚本
                    const newScript = document.createElement('script');
                    newScript.src = script.src;
                    document.head.appendChild(newScript);
                } else {
                    // 内联脚本
                    try {
                        eval(script.textContent);
                    } catch (e) {
                        console.error('脚本执行失败:', e);
                    }
                }
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 根据URL路径确定初始页面
            const path = window.location.pathname;
            let initialPage = 'dashboard';

            // 从路径中提取页面名称
            const pathParts = path.split('/');
            if (pathParts.length >= 3 && pathParts[1] === 'simple') {
                const pageName = pathParts[2];
                if (pageConfig[pageName]) {
                    initialPage = pageName;
                }
            }

            console.log('初始页面:', initialPage, '路径:', path);
            showPage(initialPage);

            // 如果是模板页面，处理标记美化
            if (initialPage === 'templates') {
                console.log('模板页面，延迟处理标记美化...');
                setTimeout(function() {
                    if (typeof window.processTemplateMarks === 'function') {
                        window.processTemplateMarks();
                    }
                }, 200);
            }
        });

        // 监听浏览器前进后退
        window.addEventListener('popstate', function(event) {
            if (event.state && event.state.page) {
                const pageName = event.state.page;
                if (pageConfig[pageName] && pageName !== currentPage) {
                    console.log('浏览器导航到:', pageName);
                    showPage(pageName);
                }
            } else {
                // 如果没有状态，根据URL路径确定页面
                const path = window.location.pathname;
                const pathParts = path.split('/');
                if (pathParts.length >= 3 && pathParts[1] === 'simple') {
                    const pageName = pathParts[2] || 'dashboard';
                    if (pageConfig[pageName] && pageName !== currentPage) {
                        console.log('URL导航到:', pageName);
                        showPage(pageName);
                    }
                }
            }
        });

    // 显示右上角提示框
    function showToast(message, type = 'success', duration = 3000) {
        const container = document.getElementById('toastContainer');
        if (!container) return;

        // 创建提示框元素
        const toast = document.createElement('div');
        toast.className = `custom-toast ${type}`;

        // 根据类型设置图标
        let icon = '';
        switch(type) {
            case 'success':
                icon = '<i class="bi bi-check-circle-fill text-success"></i>';
                break;
            case 'error':
                icon = '<i class="bi bi-x-circle-fill text-danger"></i>';
                break;
            case 'warning':
                icon = '<i class="bi bi-exclamation-triangle-fill text-warning"></i>';
                break;
            case 'info':
                icon = '<i class="bi bi-info-circle-fill text-info"></i>';
                break;
            default:
                icon = '<i class="bi bi-check-circle-fill text-success"></i>';
        }

        toast.innerHTML = `
            <div class="toast-header">
                ${icon}
                <strong class="ms-2">${type === 'success' ? '成功' : type === 'error' ? '错误' : type === 'warning' ? '警告' : '提示'}</strong>
                <button type="button" class="toast-close" onclick="hideToast(this)">×</button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;

        // 添加到容器
        container.appendChild(toast);

        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => {
                hideToast(toast);
            }, duration);
        }

        return toast;
    }

    // 隐藏提示框
    function hideToast(element) {
        const toast = element.nodeType === 1 ? element : element.closest('.custom-toast');
        if (!toast) return;

        toast.classList.add('hiding');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    // AJAX表单提交处理
    function handleAjaxForm() {
        const ajaxForms = document.querySelectorAll('form[data-ajax-form="true"]');

        ajaxForms.forEach(form => {
            // 移除之前的事件监听器，防止重复绑定
            form.removeEventListener('submit', handleFormSubmit);
            form.addEventListener('submit', handleFormSubmit);
        });
    }

    // 表单提交处理函数
    function handleFormSubmit(e) {
        e.preventDefault();

        const form = e.target;
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn ? submitBtn.innerHTML : '';

        // 防止重复提交
        if (submitBtn && submitBtn.disabled) {
            return;
        }

        // 显示加载状态
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 处理中...';
        }

        // 在提交前更新关键词字段（如果存在updateKeywordsField函数）
        if (typeof updateKeywordsField === 'function') {
            updateKeywordsField();
            console.log('已调用updateKeywordsField函数');

            // 检查keywords字段的值
            const keywordsField = document.getElementById('keywords');
            if (keywordsField) {
                console.log('keywords字段值:', keywordsField.value);
            }
        }

        // 准备表单数据
        const formData = new FormData(form);

        // 发送AJAX请求
        fetch(form.action || window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // 显示成功提示
                showToast(data.message || '操作成功', 'success');

                // 如果需要清空表单
                if (data.clear_form) {
                    setTimeout(() => {
                        clearFormTags();
                    }, 500);
                }

                // 如果有重定向，延迟跳转
                if (data.redirect) {
                    setTimeout(() => {
                        if (data.redirect.startsWith('/simple/')) {
                            // 简化版页面，使用AJAX加载
                            const pageName = data.redirect.replace('/simple/', '');
                            showPage(pageName);
                        } else {
                            // 其他页面，直接跳转
                            window.location.href = data.redirect;
                        }
                    }, 1500);
                }
            } else {
                // 显示错误提示
                showToast(data.message || '操作失败', 'error');
            }
        })
        .catch(error => {
            console.error('AJAX请求失败:', error);
            showToast('请求失败，请重试', 'error');
        })
        .finally(() => {
            // 恢复按钮状态
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        });
    }

    // 页面加载完成后初始化AJAX表单处理
    document.addEventListener('DOMContentLoaded', function() {
        handleAjaxForm();
    });

    // 当通过AJAX加载新页面内容时，重新初始化表单处理
    function reinitializeAjaxForms() {
        handleAjaxForm();
    }

    // 清空表单中的所有标签
    function clearFormTags() {
        try {
            // 清空标记替换区域的标签
            const markTagsContainers = document.querySelectorAll('.tags-container[data-mark]');
            markTagsContainers.forEach(container => {
                container.innerHTML = '';
            });

            // 清空必选话题
            const requiredTopicsContainer = document.getElementById('required-topics-container');
            if (requiredTopicsContainer) {
                requiredTopicsContainer.innerHTML = '';
                const requiredTopicsField = document.getElementById('required_topics');
                if (requiredTopicsField) requiredTopicsField.value = '';
            }

            // 清空随机话题
            const randomTopicsContainer = document.getElementById('random-topics-container');
            if (randomTopicsContainer) {
                randomTopicsContainer.innerHTML = '';
                const randomTopicsField = document.getElementById('random_topics');
                if (randomTopicsField) randomTopicsField.value = '';
            }

            // 清空@用户
            const atUsersContainer = document.getElementById('at-users-container');
            if (atUsersContainer) {
                atUsersContainer.innerHTML = '';
                const atUsersField = document.getElementById('at_users');
                if (atUsersField) atUsersField.value = '';
            }

            // 清空定位信息
            const locationsContainer = document.getElementById('locations-container');
            if (locationsContainer) {
                locationsContainer.innerHTML = '';
                const locationField = document.getElementById('location');
                if (locationField) locationField.value = '';
            }

            // 清空关键词字段
            const keywordsField = document.getElementById('keywords');
            if (keywordsField) keywordsField.value = '';

            // 更新底部预览状态
            if (typeof updateBottomPreviewFromCurrentState === 'function') {
                setTimeout(updateBottomPreviewFromCurrentState, 100);
            }

            console.log('表单标签已清空');

        } catch (error) {
            console.error('清空表单标签失败:', error);
        }
    }

    // === 全局模态框函数 ===

    // 显示内容模态框
    function showContentModal(title, content) {
        // 移除已存在的模态框
        const existingModal = document.getElementById('contentModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="contentModal" tabindex="-1" aria-labelledby="contentModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="contentModalLabel">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            ${content}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('contentModal'));
        modal.show();

        // 模态框关闭时移除DOM元素
        document.getElementById('contentModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    // 关闭内容模态框
    function closeContentModal() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('contentModal'));
        if (modal) {
            modal.hide();
        }
    }

    // 刷新审核页面
    function refreshReviewPage() {
        if (typeof loadContents === 'function') {
            loadContents();
        } else {
            location.reload();
        }
    }

    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    {% block scripts %}{% endblock %}
</body>
</html>
