{% extends "base_simple.html" %}

{% block title %}修改密码 - 小红书文案管理系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h4 class="mb-0">修改密码</h4>
            </div>
            <div class="card-body">
                <form method="post" action="{{ url_for('auth.change_password') }}">
                    {{ form.csrf_token }}
                    <div class="mb-3">
                        {{ form.old_password.label(class="form-label") }}
                        {{ form.old_password(class="form-control") }}
                        {% if form.old_password.errors %}
                            {% for error in form.old_password.errors %}
                            <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        {{ form.new_password.label(class="form-label") }}
                        {{ form.new_password(class="form-control") }}
                        {% if form.new_password.errors %}
                            {% for error in form.new_password.errors %}
                            <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        {{ form.confirm_password.label(class="form-label") }}
                        {{ form.confirm_password(class="form-control") }}
                        {% if form.confirm_password.errors %}
                            {% for error in form.confirm_password.errors %}
                            <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>
                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                        <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary">取消</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %} 