{% extends "base.html" %}

{% block title %}任务统计 - 小红书文案生成系统{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
<style>
    .chart-container {
        height: 300px;
    }
    .progress {
        height: 20px;
    }
</style>
{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">任务统计</h2>
        <div class="btn-group">
            <a href="{{ url_for('stats.index') }}" class="btn btn-outline-primary">
                <i class="bi bi-bar-chart"></i> 统计概览
            </a>
            <a href="{{ url_for('stats.content_stats') }}" class="btn btn-outline-primary">
                <i class="bi bi-file-text"></i> 文案统计
            </a>
            <a href="{{ url_for('stats.user_stats') }}" class="btn btn-outline-primary">
                <i class="bi bi-people"></i> 用户统计
            </a>
        </div>
    </div>

    <!-- 时间范围选择 -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">时间范围选择</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">开始日期</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">结束日期</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary w-100">查询</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 图表 -->
    <div class="row">
        <!-- 任务状态分布 -->
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">任务状态分布</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="taskStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 客户任务分布 -->
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">客户任务分布</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="clientTaskChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务完成率统计 -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">任务完成率排行</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>任务名称</th>
                                    <th>目标数量</th>
                                    <th>实际数量</th>
                                    <th>完成率</th>
                                    <th>进度</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for task in task_completion_stats %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ task.name }}</td>
                                    <td>{{ task.target_count }}</td>
                                    <td>{{ task.actual_count }}</td>
                                    <td>{{ task.completion_rate | round(1) }}%</td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar bg-success" role="progressbar" 
                                                 style="width: {{ task.completion_rate }}%;" 
                                                 aria-valuenow="{{ task.completion_rate }}" 
                                                 aria-valuemin="0" 
                                                 aria-valuemax="100">
                                                {{ task.completion_rate | round(1) }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="6" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 客户任务统计 -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">客户任务数量排行</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>客户名称</th>
                                    <th>任务数量</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for client in client_task_stats %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ client.name }}</td>
                                    <td>{{ client.count }}</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="3" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务状态统计 -->
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">任务状态统计</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>状态</th>
                                    <th>数量</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% set status_map = {
                                    'processing': '进行中',
                                    'completed': '已完成'
                                } %}
                                {% for status, count in task_status_stats %}
                                <tr>
                                    <td>{{ status_map.get(status, status) }}</td>
                                    <td>{{ count }}</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="2" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 任务状态分布饼图
    const taskStatusCtx = document.getElementById('taskStatusChart').getContext('2d');
    const taskStatusLabels = [];
    const taskStatusData = [];
    const taskStatusColors = ['#4e73df', '#1cc88a'];
    
    {% set status_map = {
        'processing': '进行中',
        'completed': '已完成'
    } %}
    
    {% for status, count in task_status_stats %}
        taskStatusLabels.push('{{ status_map.get(status, status) }}');
        taskStatusData.push({{ count }});
    {% endfor %}
    
    new Chart(taskStatusCtx, {
        type: 'doughnut',
        data: {
            labels: taskStatusLabels,
            datasets: [{
                data: taskStatusData,
                backgroundColor: taskStatusColors,
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });
    
    // 客户任务分布条形图
    const clientTaskCtx = document.getElementById('clientTaskChart').getContext('2d');
    const clientTaskLabels = [];
    const clientTaskData = [];
    
    {% for client in client_task_stats %}
        clientTaskLabels.push('{{ client.name }}');
        clientTaskData.push({{ client.count }});
    {% endfor %}
    
    new Chart(clientTaskCtx, {
        type: 'bar',
        data: {
            labels: clientTaskLabels,
            datasets: [{
                label: '任务数量',
                data: clientTaskData,
                backgroundColor: '#4e73df',
                borderColor: '#4e73df',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
});
</script>
{% endblock %} 