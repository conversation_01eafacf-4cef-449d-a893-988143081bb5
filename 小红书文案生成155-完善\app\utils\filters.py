"""
自定义Jinja2过滤器
"""
from markupsafe import Markup

def init_app(app):
    """初始化自定义过滤器"""
    
    @app.template_filter()
    def nl2br(value):
        """将换行符转换为HTML的<br>标签，精确保留空行"""
        if value is None:
            return ""
        
        # 确保字符串末尾有一个换行符，这样最后一行的换行也会被处理
        if value and not value.endswith('\n'):
            value = value + '\n'
        
        # 将所有换行符直接替换为<br>标签
        # 这种方法比之前的循环处理更简单且能确保每个换行符都被正确转换
        result = value.replace('\n', '<br>')
        
        # 返回一个安全的HTML标记
        return Markup(result) 