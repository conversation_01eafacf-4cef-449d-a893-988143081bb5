<!-- 模板分类管理模态框内容 -->
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-8">
            <h5>模板分类管理</h5>
        </div>
        <div class="col-md-4 text-end">
            <button type="button" class="btn btn-primary btn-sm" onclick="showAddCategoryModal()">
                <i class="bi bi-plus-lg"></i> 添加分类
            </button>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>ID</th>
                            <th>分类名称</th>
                            <th>父分类</th>
                            <th>模板数量</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category in categories %}
                        <tr {% if category.parent_id %}class="subcategory"{% endif %}>
                            <td>{{ category.id }}</td>
                            <td>
                                {% if category.parent_id %}
                                    <span class="text-muted">└─</span>
                                {% endif %}
                                {{ category.name }}
                            </td>
                            <td>
                                {% if category.parent %}
                                    {{ category.parent.name }}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ category.templates.count() }}</span>
                            </td>
                            <td class="category-actions">
                                <button type="button" class="btn btn-outline-primary btn-sm" 
                                        onclick="showEditCategoryModal({{ category.id }})" 
                                        title="编辑分类">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                        onclick="deleteCategoryById({{ category.id }})" 
                                        title="删除分类">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if categories|length == 0 %}
            <div class="text-center py-4">
                <i class="bi bi-folder-x" style="font-size: 3rem; color: #ccc;"></i>
                <p class="text-muted mt-2">暂无分类数据</p>
                <button type="button" class="btn btn-primary" onclick="showAddCategoryModal()">
                    <i class="bi bi-plus-lg"></i> 添加第一个分类
                </button>
            </div>
            {% endif %}

            <!-- 分页 -->
            {% if pagination.pages > 1 %}
            <nav aria-label="分页导航">
                <ul class="pagination justify-content-center">
                    {% if pagination.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="changePage({{ pagination.prev_num }}); return false;">上一页</a>
                    </li>
                    {% endif %}

                    {% for page_num in pagination.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != pagination.page %}
                            <li class="page-item">
                                <a class="page-link" href="#" onclick="changePage({{ page_num }}); return false;">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if pagination.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="changePage({{ pagination.next_num }}); return false;">下一页</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>

            <!-- 每页显示数量选择 -->
            <div class="d-flex justify-content-center align-items-center mt-3">
                <label for="per-page-select" class="form-label me-2">每页显示：</label>
                <select class="form-select" id="per-page-select" style="width: auto;" onchange="changePageSize(this.value)">
                    <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                    <option value="20" {% if per_page == 20 %}selected{% endif %}>20</option>
                    <option value="30" {% if per_page == 30 %}selected{% endif %}>30</option>
                    <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                    <option value="80" {% if per_page == 80 %}selected{% endif %}>80</option>
                    <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                </select>
                <span class="text-muted ms-2">条记录</span>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 添加分类模态框 -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加分类</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="addCategoryContent">
                <!-- 内容将通过AJAX加载 -->
            </div>
        </div>
    </div>
</div>

<!-- 编辑分类模态框 -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑分类</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="editCategoryContent">
                <!-- 内容将通过AJAX加载 -->
            </div>
        </div>
    </div>
</div>

<style>
    .category-actions {
        white-space: nowrap;
    }
    .subcategory {
        margin-left: 30px;
    }
    .subcategory:before {
        content: "└─ ";
    }
</style>

<script>
console.log('🚀 分类管理模态框JavaScript加载');

// 分页函数
function changePage(pageNum) {
    console.log('📄 分类管理分页:', pageNum);
    if (window.currentModalAjaxHandler) {
        const perPage = document.querySelector('#per-page-select')?.value || 20;
        window.currentModalAjaxHandler(pageNum, perPage);
    }
}

// 每页显示数量变更
function changePageSize(newSize) {
    console.log('📊 分类管理每页显示数量变更:', newSize);
    if (window.currentModalAjaxHandler) {
        window.currentModalAjaxHandler(1, newSize);
    }
}
</script>
