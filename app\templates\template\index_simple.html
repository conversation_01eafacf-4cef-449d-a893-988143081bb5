<!-- 简化版模板管理页面内容 -->
<div class="container-fluid">
    <!-- 页面标题和操作区 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="d-flex align-items-center">
                <i class="bi bi-file-text fs-3 text-primary me-3"></i>
                <div>
                    <h2 class="mb-1">模板管理</h2>
                    <p class="text-muted mb-0">管理您的文案模板、分类和标记</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTemplateModal">
                    <i class="bi bi-plus-lg"></i> 添加模板
                </button>
                <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#categoriesModal">
                    <i class="bi bi-folder2-open"></i> 分类管理
                </button>
                <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#marksModal">
                    <i class="bi bi-tags"></i> 标记管理
                </button>

            </div>
        </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">
                    <i class="bi bi-funnel me-2"></i>筛选条件
                </h6>
            </div>
            <div class="card-body">
                <form id="filterForm" method="GET">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="category" class="form-label">
                                    <i class="bi bi-folder me-1"></i>分类筛选
                                </label>
                                <select class="form-select" id="category" name="category_id" onchange="filterTemplates()">
                                    <option value="">全部分类</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}" {% if request.args.get('category_id')|int == category.id %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="status" class="form-label">
                                    <i class="bi bi-toggle-on me-1"></i>状态筛选
                                </label>
                                <select class="form-select" id="status" name="status" onchange="filterTemplates()">
                                    <option value="">全部状态</option>
                                    <option value="1" {% if request.args.get('status') == '1' %}selected{% endif %}>启用</option>
                                    <option value="0" {% if request.args.get('status') == '0' %}selected{% endif %}>禁用</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <div class="mb-3 w-100">
                                <button type="button" class="btn btn-primary me-2" onclick="filterTemplates()">
                                    <i class="bi bi-search"></i> 筛选
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="resetFilter()">
                                    <i class="bi bi-x-circle"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 模板列表 -->
    <div class="card shadow-sm" id="template-list-container">
        <div class="card-header bg-white">
            <h6 class="card-title mb-0">
                <i class="bi bi-list-ul me-2"></i>模板列表
                <span class="badge bg-primary ms-2">{{ pagination.total if pagination else 0 }} 个模板</span>
            </h6>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center" style="width: 80px;">ID</th>
                            <th>标题</th>
                            <th style="width: 150px;">分类</th>
                            <th style="width: 100px;" class="text-center">状态</th>
                            <th style="width: 160px;">创建时间</th>
                            <th style="width: 200px;" class="text-center">操作</th>
                        </tr>
                    </thead>
                    <tbody id="template-list">
                        {% for template in templates %}
                        <tr>
                            <td class="text-center">
                                <span class="badge bg-light text-dark">#{{ template.id }}</span>
                            </td>
                            <td class="template-title">
                                <div class="fw-bold">{{ template.title | beautify_marks }}</div>
                            </td>
                            <td>
                                {% if template.category %}
                                <span class="badge bg-info">{{ template.category.name }}</span>
                                {% else %}
                                <span class="text-muted">未分类</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <span class="badge bg-{{ 'success' if template.status else 'secondary' }} status-badge"
                                      style="cursor: pointer;"
                                      onclick="toggleTemplateStatus({{ template.id }})"
                                      title="点击切换状态">
                                    <i class="bi bi-{{ 'check-circle' if template.status else 'x-circle' }} me-1"></i>
                                    {{ '启用' if template.status else '禁用' }}
                                </span>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ template.created_at.strftime('%Y-%m-%d %H:%M') if template.created_at else '-' }}
                                </small>
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" title="编辑" onclick="editTemplate({{ template.id }})">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-sm" title="删除" onclick="deleteTemplate({{ template.id }}, '{{ template.title }}')">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页组件 -->
            {% if pagination %}
                {% include 'components/pagination.html' %}
            {% endif %}
        </div>
    </div>
</div>

<!-- 添加模板模态框 -->
<div class="modal fade" id="addTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle me-2"></i>添加模板
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="addTemplateContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分类管理模态框 -->
<div class="modal fade" id="categoriesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="bi bi-folder2-open me-2"></i>分类管理
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="categoriesContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 标记管理模态框 -->
<div class="modal fade" id="marksModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="bi bi-tags me-2"></i>标记管理
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="marksContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 子模态框 -->
<!-- 添加分类模态框 -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="bi bi-folder-plus me-2"></i>添加分类
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="addCategoryContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑分类模态框 -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-secondary text-white">
                <h5 class="modal-title">
                    <i class="bi bi-pencil-square me-2"></i>编辑分类
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="editCategoryContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加标记模态框 -->
<div class="modal fade" id="addMarkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="bi bi-tag-fill me-2"></i>添加标记
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="addMarkContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑标记模态框 -->
<div class="modal fade" id="editMarkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-secondary text-white">
                <h5 class="modal-title">
                    <i class="bi bi-pencil-square me-2"></i>编辑标记
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="editMarkContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑模板模态框 -->
<div class="modal fade" id="editTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="bi bi-pencil-square me-2"></i>编辑模板
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="editTemplateContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.filter-section {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.template-mark {
    background-color: #e9f5ff;
    border: 1px solid #b3d7ff;
    border-radius: 3px;
    padding: 2px 4px;
    font-weight: bold;
    color: #0066cc;
    display: inline-block;
    margin: 0 2px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
</style>

<script>
// 简化版模板管理JavaScript
console.log('模板管理页面已加载');

// 页面加载完成后清理重复的标记格式（可选）
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded 事件触发');
    // formatExistingTemplateTitles(); // 暂时注释掉，让全局函数处理
});

// 延迟执行清理函数（如果需要）
// setTimeout(function() {
//     console.log('延迟执行格式化函数');
//     formatExistingTemplateTitles();
// }, 100);

// 格式化现有模板标题中的标记（简化版，让全局函数处理样式）
function formatExistingTemplateTitles() {
    console.log('🔧 开始清理模板标题中的重复标记...');

    const titleCells = document.querySelectorAll('.template-title');
    console.log(`🔍 找到 ${titleCells.length} 个模板标题元素`);

    let cleanedCount = 0;

    titleCells.forEach(function(cell, index) {
        const originalHTML = cell.innerHTML;

        // 如果已经包含 template-mark 标签，清除并重新处理
        if (originalHTML.includes('template-mark')) {
            console.log(`🔄 清除第 ${index + 1} 个标题的格式化标签`);
            // 提取纯文本内容
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = originalHTML;
            const cleanText = tempDiv.textContent || tempDiv.innerText;
            cell.textContent = cleanText;
            cleanedCount++;
            console.log(`🧹 清理后的文本: "${cleanText}"`);
        }
    });

    console.log(`🎉 已清理 ${cleanedCount} 个模板标题，等待全局函数重新格式化`);
}

// 定义简化版本的分页函数
function simpleChangePageSize(newSize) {
    // 获取当前的筛选参数
    const categoryId = document.getElementById('category')?.value || '';
    const status = document.getElementById('status')?.value || '';

    // 构建查询参数
    const params = new URLSearchParams();
    params.set('per_page', newSize);
    params.set('page', '1'); // 重置到第一页
    if (categoryId) params.set('category_id', categoryId);
    if (status) params.set('status', status);

    // 使用AJAX重新加载
    fetch(`/simple/templates?${params.toString()}`, {
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => response.text())
    .then(html => {
        // 只更新模板列表容器，保持页面布局
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newListContainer = doc.querySelector('#template-list-container');
        if (newListContainer) {
            document.querySelector('#template-list-container').innerHTML = newListContainer.innerHTML;
            // 重新设置分页函数，确保新的分页组件使用我们的函数
            window.changePageSize = simpleChangePageSize;
            window.changePage = simpleChangePage;
        }
    })
    .catch(error => {
        console.error('更新失败:', error);
        alert('更新失败，请重试');
    });
}

function simpleChangePage(pageNum) {
    // 获取当前的筛选参数和每页显示数量
    const categoryId = document.getElementById('category')?.value || '';
    const status = document.getElementById('status')?.value || '';
    // 更精确地获取分页组件中的每页显示数量选择器
    const paginationContainer = document.querySelector('#template-list-container');
    const perPageSelect = paginationContainer?.querySelector('.form-select');
    const perPage = perPageSelect?.value || 20;

    // 构建查询参数
    const params = new URLSearchParams();
    params.set('page', pageNum);
    params.set('per_page', perPage);
    if (categoryId) params.set('category_id', categoryId);
    if (status) params.set('status', status);

    // 使用AJAX重新加载
    fetch(`/simple/templates?${params.toString()}`, {
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => response.text())
    .then(html => {
        // 只更新模板列表容器，保持页面布局
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newListContainer = doc.querySelector('#template-list-container');
        if (newListContainer) {
            document.querySelector('#template-list-container').innerHTML = newListContainer.innerHTML;
            // 重新设置分页函数，确保新的分页组件使用我们的函数
            window.changePageSize = simpleChangePageSize;
            window.changePage = simpleChangePage;
        }
    })
    .catch(error => {
        console.error('翻页失败:', error);
        alert('翻页失败，请重试');
    });
}

// 设置全局函数供分页组件使用
window.changePageForSimple = simpleChangePage;

// 保存原来的分页函数（如果存在）
window.originalChangePageSize = window.changePageSize;
window.originalChangePage = window.changePage;

// 设置当前页面的分页函数
window.changePageSize = simpleChangePageSize;
window.changePage = simpleChangePage;

// 筛选模板 - 全局函数，确保下拉框和按钮可以调用
window.filterTemplates = function() {
    console.log('🔍 开始筛选模板...');

    const categoryElement = document.getElementById('category');
    const statusElement = document.getElementById('status');

    if (!categoryElement || !statusElement) {
        console.error('❌ 筛选元素未找到');
        return;
    }

    const categoryId = categoryElement.value;
    const status = statusElement.value;

    console.log('📊 筛选参数:', { categoryId, status });

    // 获取当前的每页显示数量，保持用户设置
    const paginationContainer = document.querySelector('#template-list-container');
    const perPageSelect = paginationContainer?.querySelector('.form-select');
    const currentPerPage = perPageSelect?.value || 20;

    // 构建查询参数，重置到第一页
    const params = new URLSearchParams();
    params.append('page', '1'); // 重置到第一页
    params.append('per_page', currentPerPage); // 保持当前每页显示数量
    if (categoryId && categoryId !== '') {
        params.append('category_id', categoryId);
    }
    if (status !== '') {
        params.append('status', status);
    }

    const url = `/simple/templates?${params.toString()}`;
    console.log('🌐 请求URL:', url);

    // 显示加载状态
    const container = document.querySelector('#template-list-container');
    if (container) {
        container.innerHTML = '<div class="text-center p-4"><div class="spinner-border" role="status"><span class="visually-hidden">加载中...</span></div></div>';
    }

    // 重新加载模板列表
    fetch(url, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'text/html'
        }
    })
    .then(response => {
        console.log('📨 筛选响应状态:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.text();
    })
    .then(html => {
        console.log('📄 收到HTML响应，长度:', html.length);

        // 解析返回的HTML
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // 查找模板列表容器
        const newListContainer = doc.querySelector('#template-list-container') ||
                                doc.querySelector('.table-responsive') ||
                                doc.querySelector('table');

        if (newListContainer) {
            const currentContainer = document.querySelector('#template-list-container');
            if (currentContainer) {
                currentContainer.innerHTML = newListContainer.innerHTML;
                console.log('✅ 模板列表已更新');

                // 重新绑定事件
                bindTemplateEvents();

                showToast('筛选完成', 'success');
            } else {
                console.error('❌ 找不到当前模板列表容器');
                // 如果找不到容器，尝试重新加载整个页面内容
                location.reload();
            }
        } else {
            console.error('❌ 新HTML中找不到模板列表容器');
            console.log('📄 HTML内容预览:', html.substring(0, 500));
            showToast('筛选结果解析失败', 'warning');
        }
    })
    .catch(error => {
        console.error('❌ 筛选失败:', error);
        showToast('筛选失败：' + error.message, 'danger');

        // 恢复原始内容
        loadTemplates();
    });
};

// 重新绑定模板事件
function bindTemplateEvents() {
    console.log('🔗 重新绑定模板事件');

    // 重新设置分页函数
    window.changePageSize = simpleChangePageSize;
    window.changePage = simpleChangePage;

    // 重新处理模板标记美化
    if (typeof window.processTemplateMarks === 'function') {
        console.log('🎨 重新处理模板标记美化');
        window.processTemplateMarks();
    } else {
        console.warn('⚠️ 全局标记处理函数不存在');
    }

    // 重新绑定所有按钮事件（如果需要的话）
    // 这里可以添加其他需要重新绑定的事件
}

// 重置筛选 - 全局函数
window.resetFilter = function() {
    console.log('🔄 重置筛选条件');

    const categoryElement = document.getElementById('category');
    const statusElement = document.getElementById('status');

    if (categoryElement) categoryElement.value = '';
    if (statusElement) statusElement.value = '';

    console.log('✅ 筛选条件已重置');
    window.filterTemplates();
};



// 通用模态框遮罩层清理函数
function cleanupModalBackdrop() {
    console.log('🧹 开始清理模态框遮罩层');

    // 移除所有可能的遮罩层
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => {
        console.log('移除遮罩层:', backdrop);
        backdrop.remove();
    });

    // 移除body上的modal相关类和样式
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';

    console.log('✅ 模态框遮罩层清理完成');
}

// 子模态框专用的遮罩层处理函数
function handleChildModalBackdrop() {
    console.log('🎭 处理子模态框遮罩层');

    // 获取所有遮罩层
    const backdrops = document.querySelectorAll('.modal-backdrop');
    console.log('当前遮罩层数量:', backdrops.length);

    // 如果有多个遮罩层，只保留最新的一个用于子模态框
    if (backdrops.length > 1) {
        // 移除多余的遮罩层，只保留最后一个
        for (let i = 0; i < backdrops.length - 1; i++) {
            backdrops[i].remove();
            console.log('移除多余遮罩层:', i);
        }
    }

    // 设置剩余遮罩层的层级
    const remainingBackdrop = document.querySelector('.modal-backdrop');
    if (remainingBackdrop) {
        remainingBackdrop.style.zIndex = '1069';
        console.log('✅ 子模态框遮罩层设置完成');
    }
}

// 这些函数将被全局函数替换，暂时保留以避免错误

// 模态框事件处理
document.getElementById('addTemplateModal').addEventListener('show.bs.modal', function() {
    console.log('显示添加模板模态框');

    // 设置模态框层级
    const modal = document.getElementById('addTemplateModal');
    modal.style.zIndex = '1055';

    // 加载添加模板表单
    loadModalContent('/simple/templates/add', 'addTemplateContent').then(function() {
        console.log('添加模板模态框内容加载完成');

        // 备用方案：直接在这里设置事件监听器
        setTimeout(function() {
            console.log('🔧 备用方案：设置添加模板事件监听器');

            const addTitleField = document.getElementById('add_title_field');
            const addContentField = document.getElementById('add_content');
            const addSubmitBtn = document.getElementById('add-submit-btn');

            console.log('🔍 备用方案查找元素:', {
                addTitleField: addTitleField,
                addContentField: addContentField,
                addSubmitBtn: addSubmitBtn
            });

            if (addTitleField && addContentField && addSubmitBtn) {
                console.log('✅ 备用方案找到元素，设置事件监听器');

                // 设置全局焦点变量
                window.addTemplateCurrentFocus = addContentField;

                // 焦点更新函数
                function updateAddFocusIndicator() {
                    console.log('🔄 备用方案更新焦点指示器');

                    addTitleField.classList.remove('current-focus');
                    addContentField.classList.remove('current-focus');

                    const titleIndicator = document.getElementById('add-title-focus-indicator');
                    const contentIndicator = document.getElementById('add-content-focus-indicator');

                    if (window.addTemplateCurrentFocus === addTitleField) {
                        console.log('📍 备用方案设置标题为当前焦点');
                        addTitleField.classList.add('current-focus');
                        if (titleIndicator) titleIndicator.textContent = '当前活动 ✓';
                        if (contentIndicator) contentIndicator.textContent = '点击此处输入内容';
                    } else if (window.addTemplateCurrentFocus === addContentField) {
                        console.log('📍 备用方案设置内容为当前焦点');
                        addContentField.classList.add('current-focus');
                        if (titleIndicator) titleIndicator.textContent = '点击此处输入标题';
                        if (contentIndicator) contentIndicator.textContent = '当前活动 ✓';
                    }
                }

                // 添加事件监听器
                addTitleField.addEventListener('focus', function(e) {
                    console.log('🎯 备用方案-标题字段获得焦点');
                    window.addTemplateCurrentFocus = this;
                    updateAddFocusIndicator();
                });

                addTitleField.addEventListener('click', function(e) {
                    console.log('🖱️ 备用方案-标题字段被点击');
                    window.addTemplateCurrentFocus = this;
                    updateAddFocusIndicator();
                });

                addContentField.addEventListener('focus', function(e) {
                    console.log('🎯 备用方案-内容字段获得焦点');
                    window.addTemplateCurrentFocus = this;
                    updateAddFocusIndicator();
                });

                addContentField.addEventListener('click', function(e) {
                    console.log('🖱️ 备用方案-内容字段被点击');
                    window.addTemplateCurrentFocus = this;
                    updateAddFocusIndicator();
                });

                // 初始化焦点指示器
                updateAddFocusIndicator();

                // 添加标记插入函数
                window.insertAddTemplateMark = function(markName) {
                    console.log('🏷️ 备用方案插入标记:', markName);

                    const targetField = window.addTemplateCurrentFocus;
                    if (!targetField) {
                        console.error('❌ 备用方案没有焦点字段');
                        return;
                    }

                    const markText = '{' + markName + '}';
                    const cursorPos = targetField.selectionStart;
                    const textBefore = targetField.value.substring(0, cursorPos);
                    const textAfter = targetField.value.substring(targetField.selectionEnd);

                    targetField.value = textBefore + markText + textAfter;
                    targetField.selectionStart = targetField.selectionEnd = cursorPos + markText.length;
                    targetField.focus();

                    console.log('✅ 备用方案标记插入完成');
                };

                // 添加提交按钮事件监听器（防重复）
                if (!addSubmitBtn.hasAttribute('data-backup-listener')) {
                    addSubmitBtn.setAttribute('data-backup-listener', 'true');
                    addSubmitBtn.addEventListener('click', function() {
                        console.log('🔄 备用方案-添加表单提交按钮被点击');
                        submitAddTemplateForm();
                    });
                    console.log('✅ 备用方案-提交按钮事件监听器已设置');
                } else {
                    console.log('ℹ️ 备用方案-提交按钮事件监听器已存在，跳过设置');
                }

                // 添加表单提交函数
                window.submitAddTemplateForm = function() {
                    console.log('🔄 备用方案-开始提交添加表单');

                    // 防止重复提交
                    if (window.isSubmittingAddTemplate) {
                        console.log('⚠️ 备用方案-表单正在提交中，忽略重复请求');
                        return;
                    }

                    const form = document.getElementById('add-template-form');
                    if (!form) {
                        console.error('❌ 备用方案-找不到表单');
                        return;
                    }

                    // 设置提交状态
                    window.isSubmittingAddTemplate = true;

                    // 禁用提交按钮
                    const submitBtn = document.getElementById('add-submit-btn');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 提交中...';
                    }

                    // 获取表单数据
                    const formData = new FormData(form);

                    // 添加状态开关的值
                    const statusSwitch = document.getElementById('add_status_switch');
                    if (statusSwitch) {
                        formData.set('status', statusSwitch.checked ? 'on' : '');
                    }

                    console.log('📤 备用方案-提交表单数据');

                    // 提交表单
                    fetch('/simple/templates/add', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('✅ 备用方案-表单提交响应:', data);
                        if (data.success) {
                            // 显示成功消息
                            if (window.showToast) {
                                window.showToast('模板添加成功！', 'success');
                            } else {
                                alert('模板添加成功！');
                            }

                            // 关闭模态框
                            const modal = bootstrap.Modal.getInstance(document.getElementById('addTemplateModal'));
                            if (modal) {
                                modal.hide();
                            }

                            // 刷新页面
                            setTimeout(() => {
                                location.reload();
                            }, 1000);
                        } else {
                            console.error('❌ 备用方案-表单提交失败:', data.message);
                            if (window.showToast) {
                                window.showToast('添加失败：' + data.message, 'danger');
                            } else {
                                alert('添加失败：' + data.message);
                            }

                            // 重置提交状态
                            window.isSubmittingAddTemplate = false;
                            const submitBtn = document.getElementById('add-submit-btn');
                            if (submitBtn) {
                                submitBtn.disabled = false;
                                submitBtn.innerHTML = '<i class="bi bi-check-circle"></i> 添加模板';
                            }
                        }
                    })
                    .catch(error => {
                        console.error('❌ 备用方案-表单提交错误:', error);
                        if (window.showToast) {
                            window.showToast('提交失败，请重试', 'danger');
                        } else {
                            alert('提交失败，请重试');
                        }

                        // 重置提交状态
                        window.isSubmittingAddTemplate = false;
                        const submitBtn = document.getElementById('add-submit-btn');
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class="bi bi-check-circle"></i> 添加模板';
                        }
                    });
                };

                // 状态开关事件处理
                const addStatusSwitch = document.getElementById('add_status_switch');
                const addStatusText = document.getElementById('add_status_text');

                if (addStatusSwitch && addStatusText) {
                    addStatusSwitch.addEventListener('change', function() {
                        addStatusText.textContent = this.checked ? '启用' : '禁用';
                        console.log('🔄 备用方案-状态开关切换:', this.checked ? '启用' : '禁用');
                    });
                    console.log('✅ 备用方案-状态开关事件监听器设置完成');
                }

                console.log('✅ 备用方案事件监听器设置完成');
            } else {
                console.error('❌ 备用方案找不到表单元素');
            }
        }, 500);
    });
});

// 添加模态框关闭事件监听器
document.getElementById('addTemplateModal').addEventListener('hidden.bs.modal', function() {
    console.log('添加模板模态框已关闭');
    // 重置提交状态
    window.isSubmittingAddTemplate = false;
    console.log('✅ 已重置添加模板提交状态');
});

// 添加模板模态框显示后的处理
document.getElementById('addTemplateModal').addEventListener('shown.bs.modal', function() {
    console.log('添加模板模态框已完全显示');
    fixModalLayers('addTemplateModal');
});

// 通用模态框层级修复函数
function fixModalLayers(modalId) {
    const modal = document.getElementById(modalId);
    const backdrop = document.querySelector('.modal-backdrop:last-child');

    if (modal) {
        modal.style.zIndex = '1055';
        modal.style.pointerEvents = 'auto';

        const dialog = modal.querySelector('.modal-dialog');
        if (dialog) {
            dialog.style.zIndex = '1056';
            dialog.style.position = 'relative';
        }
    }

    if (backdrop) {
        backdrop.style.zIndex = '1050';
        backdrop.style.pointerEvents = 'auto';
    }

    console.log(`✅ ${modalId} 层级修复完成`);
}

// 为所有模态框添加关闭事件监听器
const modalIds = ['editTemplateModal', 'addTemplateModal', 'categoriesModal', 'marksModal', 'addCategoryModal', 'editCategoryModal', 'addMarkModal', 'editMarkModal'];

modalIds.forEach(modalId => {
    const modalElement = document.getElementById(modalId);
    if (modalElement) {
        modalElement.addEventListener('hidden.bs.modal', function() {
            console.log(`${modalId} 已关闭，清理遮罩层`);
            setTimeout(cleanupModalBackdrop, 100);
        });
    }
});

document.getElementById('categoriesModal').addEventListener('show.bs.modal', function() {
    console.log('显示分类管理模态框');

    // 如果正在显示子模态框，则跳过重新加载
    if (window.isShowingChildModal) {
        console.log('⚠️ 子模态框正在显示，跳过分类管理模态框重新加载');
        return;
    }

    // 确保分类管理模态框的层级正确
    const categoriesModal = document.getElementById('categoriesModal');
    categoriesModal.style.zIndex = '1055';

    // 延迟设置背景遮罩层级
    setTimeout(() => {
        const backdrop = document.querySelector('.modal-backdrop:last-child');
        if (backdrop) {
            backdrop.style.zIndex = '1054';
        }
        console.log('✅ 分类管理模态框层级设置完成');
    }, 100);

    // 设置AJAX分页处理器
    window.currentModalAjaxHandler = function(page, perPage) {
        console.log('分类管理模态框AJAX处理器被调用:', page, perPage);
        // 如果没有传递perPage，从模态框中获取当前值
        if (!perPage || perPage === 20) {
            const modalContent = document.querySelector('.modal.show .modal-body');
            const currentPerPage = modalContent?.querySelector('.form-select')?.value;
            if (currentPerPage) {
                perPage = parseInt(currentPerPage);
                console.log('从模态框获取每页显示数量:', perPage);
            }
        }
        loadModalContent('/simple/templates/categories', 'categoriesContent', page, perPage);
    };

    // 覆盖全局分页函数，专门用于模态框
    window.changePageSize = function(newSize, evt) {
        console.log('模态框changePageSize被调用:', newSize);
        if (window.currentModalAjaxHandler) {
            window.currentModalAjaxHandler(1, newSize);
        } else {
            console.error('没有找到模态框AJAX处理器');
        }
    };

    window.changePage = function(pageNum, evt) {
        console.log('模态框changePage被调用:', pageNum);
        if (window.currentModalAjaxHandler) {
            // 从模态框内部获取当前的每页显示数量
            const modalContent = document.querySelector('.modal.show .modal-body');
            const currentPerPage = modalContent?.querySelector('.form-select')?.value || 20;
            console.log('当前每页显示数量:', currentPerPage);
            window.currentModalAjaxHandler(pageNum, currentPerPage);
        } else {
            console.error('没有找到模态框AJAX处理器');
        }
    };

    console.log('分类管理模态框AJAX处理器已设置，全局分页函数已覆盖');

    // 加载分类管理内容
    loadModalContent('/simple/templates/categories', 'categoriesContent');
});

// 添加分类管理模态框显示后的处理
document.getElementById('categoriesModal').addEventListener('shown.bs.modal', function() {
    console.log('分类管理模态框已完全显示');
    fixModalLayers('categoriesModal');
});

document.getElementById('marksModal').addEventListener('show.bs.modal', function() {
    console.log('显示标记管理模态框');

    // 如果正在显示子模态框，则跳过重新加载
    if (window.isShowingChildModal) {
        console.log('⚠️ 子模态框正在显示，跳过标记管理模态框重新加载');
        return;
    }

    // 设置模态框层级
    const modal = document.getElementById('marksModal');
    modal.style.zIndex = '1055';

    // 设置AJAX分页处理器
    window.currentModalAjaxHandler = function(page, perPage) {
        console.log('标记管理模态框AJAX处理器被调用:', page, perPage);
        // 如果没有传递perPage，从模态框中获取当前值
        if (!perPage || perPage === 20) {
            const modalContent = document.querySelector('.modal.show .modal-body');
            const currentPerPage = modalContent?.querySelector('.form-select')?.value;
            if (currentPerPage) {
                perPage = parseInt(currentPerPage);
                console.log('从模态框获取每页显示数量:', perPage);
            }
        }
        loadModalContent('/simple/templates/marks', 'marksContent', page, perPage);
    };

    // 覆盖全局分页函数，专门用于模态框
    window.changePageSize = function(newSize, evt) {
        console.log('模态框changePageSize被调用:', newSize);
        if (window.currentModalAjaxHandler) {
            window.currentModalAjaxHandler(1, newSize);
        } else {
            console.error('没有找到模态框AJAX处理器');
        }
    };

    window.changePage = function(pageNum, evt) {
        console.log('模态框changePage被调用:', pageNum);
        if (window.currentModalAjaxHandler) {
            // 从模态框内部获取当前的每页显示数量
            const modalContent = document.querySelector('.modal.show .modal-body');
            const currentPerPage = modalContent?.querySelector('.form-select')?.value || 20;
            console.log('当前每页显示数量:', currentPerPage);
            window.currentModalAjaxHandler(pageNum, currentPerPage);
        } else {
            console.error('没有找到模态框AJAX处理器');
        }
    };

    console.log('标记管理模态框AJAX处理器已设置，全局分页函数已覆盖');

    // 加载标记管理内容（如果没有子模态框在显示）
    if (!window.isShowingChildModal) {
        loadModalContent('/simple/templates/marks', 'marksContent');
    } else {
        console.log('⚠️ 子模态框正在显示，跳过加载标记管理内容');
    }
});

// 添加标记管理模态框显示后的处理
document.getElementById('marksModal').addEventListener('shown.bs.modal', function() {
    console.log('标记管理模态框已完全显示');
    fixModalLayers('marksModal');
});

// 清理模态框AJAX处理器
document.getElementById('categoriesModal').addEventListener('hidden.bs.modal', function () {
    window.currentModalAjaxHandler = null;

    // 恢复原来的分页函数
    window.changePageSize = window.originalChangePageSize;
    window.changePage = window.originalChangePage;

    console.log('分类管理模态框已关闭，清理AJAX处理器，恢复原分页函数');
});

document.getElementById('marksModal').addEventListener('hidden.bs.modal', function () {
    window.currentModalAjaxHandler = null;

    // 恢复原来的分页函数
    window.changePageSize = window.originalChangePageSize;
    window.changePage = window.originalChangePage;

    console.log('标记管理模态框已关闭，清理AJAX处理器，恢复原分页函数');
});

// 加载模态框内容
function loadModalContent(url, containerId, page = 1, perPage = 20) {
    const container = document.getElementById(containerId);

    // 如果没有指定perPage，尝试从当前模态框中获取
    if (perPage === 20) {
        const modalContent = document.querySelector('.modal.show .modal-body');
        const currentPerPage = modalContent?.querySelector('.form-select')?.value;
        if (currentPerPage) {
            perPage = parseInt(currentPerPage);
            console.log('从模态框获取当前每页显示数量:', perPage);
        }
    }

    // 构建完整的URL，包含分页参数
    const urlObj = new URL(url, window.location.origin);
    urlObj.searchParams.set('page', page);
    urlObj.searchParams.set('per_page', perPage);

    console.log(`加载模态框内容: ${urlObj.toString()}`);

    return fetch(urlObj.toString(), {
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => response.text())
    .then(html => {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // 提取主要内容
        let content = doc.querySelector('.container') ||
                     doc.querySelector('.card-body') ||
                     doc.querySelector('body');

        if (content) {
            container.innerHTML = content.innerHTML;

            // 执行新加载内容中的JavaScript代码
            const scripts = container.querySelectorAll('script');
            console.log('🔍 找到的script标签数量:', scripts.length);

            scripts.forEach((script, index) => {
                console.log(`🔄 执行第${index + 1}个script标签`);
                try {
                    if (script.src) {
                        console.log('📄 外部脚本:', script.src);
                        const newScript = document.createElement('script');
                        newScript.src = script.src;
                        newScript.onload = () => console.log('✅ 外部脚本加载完成:', script.src);
                        newScript.onerror = () => console.error('❌ 外部脚本加载失败:', script.src);
                        document.head.appendChild(newScript);
                    } else {
                        console.log('📝 内联脚本长度:', script.textContent.length);
                        console.log('📝 内联脚本前100字符:', script.textContent.substring(0, 100));

                        // 使用eval执行内联脚本
                        eval(script.textContent);
                        console.log('✅ 内联脚本执行完成');
                    }
                } catch (error) {
                    console.error('❌ 脚本执行错误:', error);
                }
            });

            // 为表单添加AJAX提交处理
            const forms = container.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // 根据容器ID确定正确的提交URL
                    let submitUrl = form.action;
                    if (!submitUrl || submitUrl.includes('/simple/')) {
                        // 如果没有action或者action指向错误的URL，根据容器ID确定正确的URL
                        if (containerId.includes('addCategory')) {
                            submitUrl = '/simple/templates/category/add';
                        } else if (containerId.includes('editCategory')) {
                            // 从URL或其他地方获取分类ID
                            const categoryId = getCategoryIdFromContext();
                            submitUrl = `/simple/templates/category/edit/${categoryId}`;
                        } else if (containerId.includes('addMark')) {
                            submitUrl = '/simple/templates/mark/add';
                        } else if (containerId.includes('editMark')) {
                            const markId = getMarkIdFromContext();
                            submitUrl = `/simple/templates/mark/edit/${markId}`;
                        } else if (containerId.includes('addTemplate')) {
                            submitUrl = '/simple/templates/add';
                        } else if (containerId.includes('editTemplate')) {
                            const templateId = getTemplateIdFromContext();
                            submitUrl = `/simple/templates/edit/${templateId}`;
                        }
                    }

                    handleFormSubmitWithUrl(this, containerId.replace('Content', 'Modal'), submitUrl);
                });
            });

            // 确保AJAX处理器在内容更新后仍然有效，并手动绑定分页事件
            setTimeout(() => {
                if (containerId === 'categoriesContent') {
                    if (!window.currentModalAjaxHandler) {
                        window.currentModalAjaxHandler = function(page, perPage) {
                            console.log('重新设置分类管理模态框AJAX处理器:', page, perPage);
                            loadModalContent('/simple/templates/categories', 'categoriesContent', page, perPage);
                        };
                        console.log('分类管理AJAX处理器已重新设置');
                    }
                    // 手动绑定分页事件
                    bindModalPaginationEvents(containerId);
                } else if (containerId === 'marksContent') {
                    if (!window.currentModalAjaxHandler) {
                        window.currentModalAjaxHandler = function(page, perPage) {
                            console.log('重新设置标记管理模态框AJAX处理器:', page, perPage);
                            loadModalContent('/simple/templates/marks', 'marksContent', page, perPage);
                        };
                        console.log('标记管理AJAX处理器已重新设置');
                    }
                    // 手动绑定分页事件
                    bindModalPaginationEvents(containerId);
                }
            }, 100);

            // 返回成功状态
            return Promise.resolve();
        } else {
            container.innerHTML = '<div class="alert alert-danger">内容加载失败</div>';
            return Promise.reject('内容加载失败');
        }
    })
    .catch(error => {
        console.error('加载失败:', error);
        container.innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';
        return Promise.reject(error);
    });
}

// 手动绑定模态框中的分页事件
function bindModalPaginationEvents(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    console.log('绑定模态框分页事件:', containerId);

    // 绑定每页显示数量选择器
    const pageSizeSelect = container.querySelector('.form-select');
    if (pageSizeSelect) {
        // 移除旧的事件监听器
        pageSizeSelect.removeEventListener('change', handlePageSizeChange);
        // 添加新的事件监听器
        pageSizeSelect.addEventListener('change', handlePageSizeChange);
        console.log('已绑定每页显示数量选择器');
    }

    // 绑定分页链接
    const paginationLinks = container.querySelectorAll('.pagination a');
    paginationLinks.forEach((link, index) => {
        link.removeEventListener('click', handlePageChange);
        link.addEventListener('click', handlePageChange);
        console.log(`绑定分页链接 ${index}:`, link.href, link.getAttribute('onclick'));
    });
    console.log('已绑定分页链接:', paginationLinks.length);

    // 创建全局的模态框分页函数，覆盖原有的函数
    window.changePageSize = function(newSize, evt) {
        console.log('模态框changePageSize被调用:', newSize);
        if (window.currentModalAjaxHandler) {
            window.currentModalAjaxHandler(1, newSize);
        } else {
            console.error('没有找到模态框AJAX处理器');
        }
    };

    window.changePage = function(pageNum, evt) {
        console.log('模态框changePage被调用:', pageNum);
        if (window.currentModalAjaxHandler) {
            // 从模态框内部获取当前的每页显示数量
            const modalContent = document.querySelector('.modal.show .modal-body');
            const currentPerPage = modalContent?.querySelector('.form-select')?.value || container.querySelector('.form-select')?.value || 20;
            console.log('当前每页显示数量:', currentPerPage);
            window.currentModalAjaxHandler(pageNum, currentPerPage);
        } else {
            console.error('没有找到模态框AJAX处理器');
        }
    };

    console.log('已覆盖全局分页函数');
}

// 处理每页显示数量改变
function handlePageSizeChange(event) {
    event.preventDefault();
    const newSize = event.target.value;
    console.log('模态框每页显示数量改变:', newSize);

    if (window.currentModalAjaxHandler) {
        window.currentModalAjaxHandler(1, newSize);
    } else {
        console.error('没有找到模态框AJAX处理器');
    }
}

// 处理分页点击
function handlePageChange(event) {
    event.preventDefault();

    // 从href属性中提取页码
    const href = event.target.getAttribute('href');
    if (href && href !== '#') {
        const url = new URL(href, window.location.origin);
        const pageNum = parseInt(url.searchParams.get('page')) || 1;
        console.log('模态框分页点击:', pageNum);

        if (window.currentModalAjaxHandler) {
            const currentPerPage = document.querySelector('.form-select')?.value || 20;
            window.currentModalAjaxHandler(pageNum, currentPerPage);
        } else {
            console.error('没有找到模态框AJAX处理器');
        }
    } else {
        // 如果href是#，尝试从onclick属性中提取页码
        const onclickAttr = event.target.getAttribute('onclick');
        if (onclickAttr) {
            const pageMatch = onclickAttr.match(/changePage\((\d+)/);
            if (pageMatch) {
                const pageNum = parseInt(pageMatch[1]);
                console.log('模态框分页点击(从onclick):', pageNum);

                if (window.currentModalAjaxHandler) {
                    const currentPerPage = document.querySelector('.form-select')?.value || 20;
                    window.currentModalAjaxHandler(pageNum, currentPerPage);
                } else {
                    console.error('没有找到模态框AJAX处理器');
                }
            }
        }
    }
}

// 重新编写的添加分类功能
window.showAddCategoryModal = function() {
    console.log('显示添加分类模态框');

    // 设置全局标志，阻止父模态框重新加载
    window.isShowingChildModal = true;

    // 阻止事件冒泡
    if (event) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();
    }

    const modalElement = document.getElementById('addCategoryModal');
    const contentContainer = document.getElementById('addCategoryContent');

    // 直接创建表单HTML内容
    const formHTML = `
        <div class="container-fluid">
            <form id="addCategoryForm">
                <div class="mb-3">
                    <label for="addCategoryName" class="form-label">分类名称</label>
                    <input type="text" class="form-control" id="addCategoryName" name="name" required>
                </div>
                <div class="mb-3">
                    <label for="addCategoryParent" class="form-label">父分类</label>
                    <select class="form-select" id="addCategoryParent" name="parent_id">
                        <option value="0">无（顶级分类）</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="addCategorySortOrder" class="form-label">排序</label>
                    <input type="number" class="form-control" id="addCategorySortOrder" name="sort_order" value="0">
                    <div class="form-text">数字越小排序越靠前</div>
                </div>
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">添加分类</button>
                </div>
            </form>
        </div>
    `;

    // 设置表单内容
    contentContainer.innerHTML = formHTML;

    // 加载父分类选项
    loadParentCategories('addCategoryParent');

    // 绑定表单提交事件
    const form = document.getElementById('addCategoryForm');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        submitAddCategoryForm();
    });

    // 设置子模态框的层级
    modalElement.style.zIndex = '1070';
    modalElement.style.position = 'fixed';

    // 添加强制显示类
    modalElement.classList.add('child-modal-force-show');

    // 显示模态框
    const modal = new bootstrap.Modal(modalElement);
    modal.show();

    // 设置子模态框显示后的层级处理
    modalElement.addEventListener('shown.bs.modal', function() {
        // 确保子模态框在最上层
        modalElement.style.zIndex = '1070';
        const dialog = modalElement.querySelector('.modal-dialog');
        if (dialog) {
            dialog.style.zIndex = '1071';
            dialog.style.position = 'relative';
        }

        // 处理遮罩层
        setTimeout(() => {
            handleChildModalBackdrop();
        }, 100);

        console.log('✅ 添加分类模态框层级设置完成');
    }, { once: true });

    // 监听模态框关闭事件
    modalElement.addEventListener('hidden.bs.modal', function() {
        window.isShowingChildModal = false;
        modalElement.classList.remove('child-modal-force-show');
        console.log('✅ 添加分类模态框已关闭');
    }, { once: true });

    console.log('✅ 添加分类模态框已显示');
};



// 重新编写的编辑分类功能
window.showEditCategoryModal = function(categoryId) {
    console.log('显示编辑分类模态框:', categoryId);

    // 设置全局标志，阻止父模态框重新加载
    window.isShowingChildModal = true;

    // 阻止事件冒泡
    if (event) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();
    }

    const modalElement = document.getElementById('editCategoryModal');
    const contentContainer = document.getElementById('editCategoryContent');
    modalElement.dataset.categoryId = categoryId;

    // 直接创建表单HTML内容
    const formHTML = `
        <div class="container-fluid">
            <div id="editCategoryLoading" class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>
            <form id="editCategoryForm" style="display: none;">
                <div class="mb-3">
                    <label for="editCategoryName" class="form-label">分类名称</label>
                    <input type="text" class="form-control" id="editCategoryName" name="name" required>
                </div>
                <div class="mb-3">
                    <label for="editCategoryParent" class="form-label">父分类</label>
                    <select class="form-select" id="editCategoryParent" name="parent_id">
                        <option value="0">无（顶级分类）</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="editCategorySortOrder" class="form-label">排序</label>
                    <input type="number" class="form-control" id="editCategorySortOrder" name="sort_order" value="0">
                    <div class="form-text">数字越小排序越靠前</div>
                </div>
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">保存修改</button>
                </div>
            </form>
        </div>
    `;

    // 设置表单内容
    contentContainer.innerHTML = formHTML;

    // 设置子模态框的层级
    modalElement.style.zIndex = '1070';
    modalElement.style.position = 'fixed';

    // 添加强制显示类
    modalElement.classList.add('child-modal-force-show');

    // 显示模态框
    const modal = new bootstrap.Modal(modalElement);
    modal.show();

    // 设置子模态框显示后的层级处理
    modalElement.addEventListener('shown.bs.modal', function() {
        // 确保子模态框在最上层
        modalElement.style.zIndex = '1070';
        const dialog = modalElement.querySelector('.modal-dialog');
        if (dialog) {
            dialog.style.zIndex = '1071';
            dialog.style.position = 'relative';
        }

        // 处理遮罩层
        setTimeout(() => {
            handleChildModalBackdrop();
        }, 100);

        console.log('✅ 编辑分类模态框层级设置完成');
    }, { once: true });

    // 加载分类数据
    loadCategoryData(categoryId);

    // 监听模态框关闭事件
    modalElement.addEventListener('hidden.bs.modal', function() {
        window.isShowingChildModal = false;
        modalElement.classList.remove('child-modal-force-show');
        console.log('✅ 编辑分类模态框已关闭');
    }, { once: true });

    console.log('✅ 编辑分类模态框已显示');
};

// 加载父分类选项
function loadParentCategories(selectId, excludeId = null) {
    fetch('/simple/templates/categories/data')
    .then(response => response.json())
    .then(data => {
        const select = document.getElementById(selectId);
        if (!select) return;

        // 清空现有选项（保留"无"选项）
        select.innerHTML = '<option value="0">无（顶级分类）</option>';

        if (data.success && data.categories) {
            data.categories.forEach(category => {
                // 排除自己（编辑时）
                if (excludeId && category.id == excludeId) return;

                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                select.appendChild(option);
            });
        }
    })
    .catch(error => {
        console.error('加载父分类失败:', error);
    });
}

// 加载分类数据（编辑时使用）
function loadCategoryData(categoryId) {
    fetch(`/simple/templates/category/${categoryId}/data`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.category) {
            const category = data.category;

            // 填充表单数据
            document.getElementById('editCategoryName').value = category.name || '';
            document.getElementById('editCategorySortOrder').value = category.sort_order || 0;

            // 加载父分类选项（排除自己）
            loadParentCategories('editCategoryParent', categoryId);

            // 延迟设置父分类选择（等待选项加载完成）
            setTimeout(() => {
                const parentSelect = document.getElementById('editCategoryParent');
                if (parentSelect && category.parent_id) {
                    parentSelect.value = category.parent_id;
                }
            }, 500);

            // 绑定表单提交事件
            const form = document.getElementById('editCategoryForm');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                submitEditCategoryForm(categoryId);
            });

            // 隐藏加载动画，显示表单
            document.getElementById('editCategoryLoading').style.display = 'none';
            document.getElementById('editCategoryForm').style.display = 'block';
        } else {
            throw new Error(data.message || '加载分类数据失败');
        }
    })
    .catch(error => {
        console.error('加载分类数据失败:', error);
        document.getElementById('editCategoryLoading').innerHTML =
            '<div class="alert alert-danger">加载失败：' + error.message + '</div>';
    });
}

// 提交添加分类表单
function submitAddCategoryForm() {
    const form = document.getElementById('addCategoryForm');
    const formData = new FormData(form);

    // 禁用提交按钮
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.disabled = true;
    submitBtn.textContent = '提交中...';

    fetch('/simple/templates/category/add', {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || ''
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('addCategoryModal'));

            // 监听模态框关闭完成事件，然后刷新父模态框
            const modalElement = document.getElementById('addCategoryModal');
            modalElement.addEventListener('hidden.bs.modal', function() {
                console.log('🔄 添加分类模态框已关闭，开始刷新分类列表');

                // 重置子模态框标志
                window.isShowingChildModal = false;

                // 延迟刷新，确保父模态框状态正确
                setTimeout(() => {
                    if (window.currentModalAjaxHandler) {
                        console.log('🔄 执行分类列表刷新');
                        window.currentModalAjaxHandler(1, 20);
                    } else {
                        console.log('⚠️ 没有找到分类管理AJAX处理器，尝试重新加载');
                        // 如果没有AJAX处理器，直接重新加载内容
                        loadModalContent('/simple/templates/categories', 'categoriesContent');
                    }
                }, 100);
            }, { once: true });

            modal.hide();

            // 显示成功消息
            if (window.showToast) {
                window.showToast('分类添加成功', 'success');
            } else {
                alert('分类添加成功');
            }
        } else {
            throw new Error(data.message || '添加失败');
        }
    })
    .catch(error => {
        console.error('添加分类失败:', error);
        if (window.showToast) {
            window.showToast('添加失败：' + error.message, 'danger');
        } else {
            alert('添加失败：' + error.message);
        }
    })
    .finally(() => {
        // 恢复提交按钮
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    });
}

// 提交编辑分类表单
function submitEditCategoryForm(categoryId) {
    const form = document.getElementById('editCategoryForm');
    const formData = new FormData(form);

    // 禁用提交按钮
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.disabled = true;
    submitBtn.textContent = '保存中...';

    fetch(`/simple/templates/category/edit/${categoryId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || ''
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('editCategoryModal'));

            // 监听模态框关闭完成事件，然后刷新父模态框
            const modalElement = document.getElementById('editCategoryModal');
            modalElement.addEventListener('hidden.bs.modal', function() {
                console.log('🔄 编辑分类模态框已关闭，开始刷新分类列表');

                // 重置子模态框标志
                window.isShowingChildModal = false;

                // 延迟刷新，确保父模态框状态正确
                setTimeout(() => {
                    if (window.currentModalAjaxHandler) {
                        console.log('🔄 执行分类列表刷新');
                        window.currentModalAjaxHandler(1, 20);
                    } else {
                        console.log('⚠️ 没有找到分类管理AJAX处理器，尝试重新加载');
                        // 如果没有AJAX处理器，直接重新加载内容
                        loadModalContent('/simple/templates/categories', 'categoriesContent');
                    }
                }, 100);
            }, { once: true });

            modal.hide();

            // 显示成功消息
            if (window.showToast) {
                window.showToast('分类修改成功', 'success');
            } else {
                alert('分类修改成功');
            }
        } else {
            throw new Error(data.message || '修改失败');
        }
    })
    .catch(error => {
        console.error('修改分类失败:', error);
        if (window.showToast) {
            window.showToast('修改失败：' + error.message, 'danger');
        } else {
            alert('修改失败：' + error.message);
        }
    })
    .finally(() => {
        // 恢复提交按钮
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    });
}

// 重新编写的添加标记功能
window.showAddMarkModal = function() {
    console.log('显示添加标记模态框');

    // 设置全局标志，阻止父模态框重新加载
    window.isShowingChildModal = true;

    // 阻止事件冒泡
    if (event) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();
    }

    const modalElement = document.getElementById('addMarkModal');
    const contentContainer = document.getElementById('addMarkContent');

    // 直接创建表单HTML内容
    const formHTML = `
        <div class="container-fluid">
            <form id="addMarkForm">
                <div class="mb-3">
                    <label for="addMarkName" class="form-label">标记名称</label>
                    <input type="text" class="form-control" id="addMarkName" name="name" required>
                </div>
                <div class="mb-3">
                    <label for="addMarkDescription" class="form-label">描述</label>
                    <textarea class="form-control" id="addMarkDescription" name="description" rows="3"></textarea>
                </div>
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">添加标记</button>
                </div>
            </form>
        </div>
    `;

    // 设置表单内容
    contentContainer.innerHTML = formHTML;

    // 绑定表单提交事件
    const form = document.getElementById('addMarkForm');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        submitAddMarkForm();
    });

    // 设置子模态框的层级
    modalElement.style.zIndex = '1070';
    modalElement.style.position = 'fixed';

    // 调试：检查模态框元素
    console.log('🔍 添加标记模态框元素:', modalElement);
    console.log('🔍 模态框当前样式:', {
        display: modalElement.style.display,
        zIndex: modalElement.style.zIndex,
        position: modalElement.style.position
    });

    // 显示模态框
    const modal = new bootstrap.Modal(modalElement);
    console.log('🔍 Bootstrap模态框实例:', modal);

    // 添加强制显示类
    modalElement.classList.add('child-modal-force-show');

    modal.show();

    // 调试：检查模态框显示后的状态
    setTimeout(() => {
        console.log('🔍 模态框显示后状态:', {
            classList: modalElement.classList.toString(),
            display: getComputedStyle(modalElement).display,
            zIndex: getComputedStyle(modalElement).zIndex,
            visibility: getComputedStyle(modalElement).visibility,
            opacity: getComputedStyle(modalElement).opacity
        });
    }, 200);

    // 设置子模态框显示后的层级处理
    modalElement.addEventListener('shown.bs.modal', function() {
        console.log('🎯 添加标记模态框shown事件触发');

        // 确保子模态框在最上层
        modalElement.style.zIndex = '1070';
        modalElement.style.display = 'block';
        modalElement.style.opacity = '1';
        modalElement.style.visibility = 'visible';

        const dialog = modalElement.querySelector('.modal-dialog');
        if (dialog) {
            dialog.style.zIndex = '1071';
            dialog.style.position = 'relative';
        }

        // 处理遮罩层
        setTimeout(() => {
            handleChildModalBackdrop();
        }, 100);

        console.log('✅ 添加标记模态框层级设置完成');
    }, { once: true });

    // 监听模态框隐藏事件，查看是什么导致了隐藏
    modalElement.addEventListener('hide.bs.modal', function(e) {
        console.log('⚠️ 添加标记模态框即将隐藏，原因:', e);
        console.log('⚠️ 事件目标:', e.target);
        console.log('⚠️ 当前元素:', modalElement);
    });

    // 监听模态框关闭事件
    modalElement.addEventListener('hidden.bs.modal', function() {
        window.isShowingChildModal = false;
        modalElement.classList.remove('child-modal-force-show');
        console.log('✅ 添加标记模态框已关闭');
    }, { once: true });

    console.log('✅ 添加标记模态框已显示');
};

// 重新编写的编辑标记功能
window.showEditMarkModal = function(markId) {
    console.log('显示编辑标记模态框:', markId);

    // 设置全局标志，阻止父模态框重新加载
    window.isShowingChildModal = true;

    // 阻止事件冒泡
    if (event) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();
    }

    const modalElement = document.getElementById('editMarkModal');
    const contentContainer = document.getElementById('editMarkContent');
    modalElement.dataset.markId = markId;

    // 直接创建表单HTML内容
    const formHTML = `
        <div class="container-fluid">
            <div id="editMarkLoading" class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>
            <form id="editMarkForm" style="display: none;">
                <div class="mb-3">
                    <label for="editMarkName" class="form-label">标记名称</label>
                    <input type="text" class="form-control" id="editMarkName" name="name" required>
                </div>
                <div class="mb-3">
                    <label for="editMarkDescription" class="form-label">描述</label>
                    <textarea class="form-control" id="editMarkDescription" name="description" rows="3"></textarea>
                </div>
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">保存修改</button>
                </div>
            </form>
        </div>
    `;

    // 设置表单内容
    contentContainer.innerHTML = formHTML;

    // 设置子模态框的层级
    modalElement.style.zIndex = '1070';
    modalElement.style.position = 'fixed';

    // 调试：检查模态框元素
    console.log('🔍 编辑标记模态框元素:', modalElement);
    console.log('🔍 模态框当前样式:', {
        display: modalElement.style.display,
        zIndex: modalElement.style.zIndex,
        position: modalElement.style.position
    });

    // 显示模态框
    const modal = new bootstrap.Modal(modalElement);
    console.log('🔍 Bootstrap模态框实例:', modal);

    // 添加强制显示类
    modalElement.classList.add('child-modal-force-show');

    modal.show();

    // 调试：检查模态框显示后的状态
    setTimeout(() => {
        console.log('🔍 编辑模态框显示后状态:', {
            classList: modalElement.classList.toString(),
            display: getComputedStyle(modalElement).display,
            zIndex: getComputedStyle(modalElement).zIndex,
            visibility: getComputedStyle(modalElement).visibility,
            opacity: getComputedStyle(modalElement).opacity
        });
    }, 200);

    // 设置子模态框显示后的层级处理
    modalElement.addEventListener('shown.bs.modal', function() {
        console.log('🎯 编辑标记模态框shown事件触发');

        // 确保子模态框在最上层
        modalElement.style.zIndex = '1070';
        modalElement.style.display = 'block';
        modalElement.style.opacity = '1';
        modalElement.style.visibility = 'visible';

        const dialog = modalElement.querySelector('.modal-dialog');
        if (dialog) {
            dialog.style.zIndex = '1071';
            dialog.style.position = 'relative';
        }

        // 处理遮罩层
        setTimeout(() => {
            handleChildModalBackdrop();
        }, 100);

        console.log('✅ 编辑标记模态框层级设置完成');
    }, { once: true });

    // 监听模态框隐藏事件，查看是什么导致了隐藏
    modalElement.addEventListener('hide.bs.modal', function(e) {
        console.log('⚠️ 编辑标记模态框即将隐藏，原因:', e);
        console.log('⚠️ 事件目标:', e.target);
        console.log('⚠️ 当前元素:', modalElement);
    });

    // 加载标记数据
    loadMarkData(markId);

    // 监听模态框关闭事件
    modalElement.addEventListener('hidden.bs.modal', function() {
        window.isShowingChildModal = false;
        modalElement.classList.remove('child-modal-force-show');
        console.log('✅ 编辑标记模态框已关闭');
    }, { once: true });

    console.log('✅ 编辑标记模态框已显示');
};

// 加载标记数据（编辑时使用）
function loadMarkData(markId) {
    fetch(`/simple/templates/mark/${markId}/data`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.mark) {
            const mark = data.mark;

            // 填充表单数据
            document.getElementById('editMarkName').value = mark.name || '';
            document.getElementById('editMarkDescription').value = mark.description || '';

            // 绑定表单提交事件
            const form = document.getElementById('editMarkForm');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                submitEditMarkForm(markId);
            });

            // 隐藏加载动画，显示表单
            document.getElementById('editMarkLoading').style.display = 'none';
            document.getElementById('editMarkForm').style.display = 'block';
        } else {
            throw new Error(data.message || '加载标记数据失败');
        }
    })
    .catch(error => {
        console.error('加载标记数据失败:', error);
        document.getElementById('editMarkLoading').innerHTML =
            '<div class="alert alert-danger">加载失败：' + error.message + '</div>';
    });
}

// 提交添加标记表单
function submitAddMarkForm() {
    const form = document.getElementById('addMarkForm');
    const formData = new FormData(form);

    // 禁用提交按钮
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.disabled = true;
    submitBtn.textContent = '提交中...';

    fetch('/simple/templates/mark/add', {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || ''
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('addMarkModal'));

            // 监听模态框关闭完成事件，然后刷新父模态框
            const modalElement = document.getElementById('addMarkModal');
            modalElement.addEventListener('hidden.bs.modal', function() {
                console.log('🔄 添加标记模态框已关闭，开始刷新标记列表');

                // 重置子模态框标志
                window.isShowingChildModal = false;

                // 延迟刷新，确保父模态框状态正确
                setTimeout(() => {
                    if (window.currentModalAjaxHandler) {
                        console.log('🔄 执行标记列表刷新');
                        window.currentModalAjaxHandler(1, 20);
                    } else {
                        console.log('⚠️ 没有找到标记管理AJAX处理器，尝试重新加载');
                        // 如果没有AJAX处理器，直接重新加载内容
                        loadModalContent('/simple/templates/marks', 'marksContent');
                    }
                }, 100);
            }, { once: true });

            modal.hide();

            // 显示成功消息
            if (window.showToast) {
                window.showToast('标记添加成功', 'success');
            } else {
                alert('标记添加成功');
            }
        } else {
            throw new Error(data.message || '添加失败');
        }
    })
    .catch(error => {
        console.error('添加标记失败:', error);
        if (window.showToast) {
            window.showToast('添加失败：' + error.message, 'danger');
        } else {
            alert('添加失败：' + error.message);
        }
    })
    .finally(() => {
        // 恢复提交按钮
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    });
}

// 提交编辑标记表单
function submitEditMarkForm(markId) {
    const form = document.getElementById('editMarkForm');
    const formData = new FormData(form);

    // 禁用提交按钮
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.disabled = true;
    submitBtn.textContent = '保存中...';

    fetch(`/simple/templates/mark/edit/${markId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || ''
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('editMarkModal'));

            // 监听模态框关闭完成事件，然后刷新父模态框
            const modalElement = document.getElementById('editMarkModal');
            modalElement.addEventListener('hidden.bs.modal', function() {
                console.log('🔄 编辑标记模态框已关闭，开始刷新标记列表');

                // 重置子模态框标志
                window.isShowingChildModal = false;

                // 延迟刷新，确保父模态框状态正确
                setTimeout(() => {
                    if (window.currentModalAjaxHandler) {
                        console.log('🔄 执行标记列表刷新');
                        window.currentModalAjaxHandler(1, 20);
                    } else {
                        console.log('⚠️ 没有找到标记管理AJAX处理器，尝试重新加载');
                        // 如果没有AJAX处理器，直接重新加载内容
                        loadModalContent('/simple/templates/marks', 'marksContent');
                    }
                }, 100);
            }, { once: true });

            modal.hide();

            // 显示成功消息
            if (window.showToast) {
                window.showToast('标记修改成功', 'success');
            } else {
                alert('标记修改成功');
            }
        } else {
            throw new Error(data.message || '修改失败');
        }
    })
    .catch(error => {
        console.error('修改标记失败:', error);
        if (window.showToast) {
            window.showToast('修改失败：' + error.message, 'danger');
        } else {
            alert('修改失败：' + error.message);
        }
    })
    .finally(() => {
        // 恢复提交按钮
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    });
};

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 编辑模板函数
window.editTemplate = function(templateId) {
    console.log('🔧 编辑模板:', templateId);

    // 防止重复点击
    if (window.editingTemplate) {
        console.log('⚠️ 正在编辑中，忽略重复点击');
        return;
    }

    window.editingTemplate = true;

    const modalElement = document.getElementById('editTemplateModal');
    const contentContainer = document.getElementById('editTemplateContent');

    // 显示加载状态
    contentContainer.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载模板数据...</p>
        </div>
    `;

    modalElement.dataset.templateId = templateId;
    const modal = new bootstrap.Modal(modalElement);
    modal.show();

    // 获取模板数据
    fetch(`/simple/templates/edit/${templateId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.text();
    })
    .then(html => {
        console.log('✅ 模板数据加载成功');
        contentContainer.innerHTML = html;

        // 重置编辑状态
        window.editingTemplate = false;

        // 初始化编辑表单
        setTimeout(() => {
            initializeEditTemplateForm(templateId);
        }, 100);
    })
    .catch(error => {
        console.error('❌ 加载模板数据失败:', error);
        contentContainer.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i>
                加载失败：${error.message}
            </div>
        `;
        window.editingTemplate = false;
    });
};

// 初始化编辑模板表单
function initializeEditTemplateForm(templateId) {
    console.log('🔧 初始化编辑模板表单:', templateId);

    // 获取表单元素
    const editTitleField = document.getElementById('edit_title_field');
    const editContentField = document.getElementById('edit_content');
    const editSubmitBtn = document.getElementById('edit-submit-btn');
    const statusSwitch = document.getElementById('edit_status_switch');
    const statusText = document.getElementById('edit_status_text');

    console.log('🔍 查找编辑表单元素:', {
        editTitleField: !!editTitleField,
        editContentField: !!editContentField,
        editSubmitBtn: !!editSubmitBtn,
        statusSwitch: !!statusSwitch,
        statusText: !!statusText
    });

    if (!editTitleField || !editContentField || !editSubmitBtn) {
        console.error('❌ 找不到编辑表单的关键元素');
        return;
    }

    // 设置全局焦点变量
    window.editTemplateCurrentFocus = editContentField; // 默认焦点在内容区域

    // 更新焦点指示器函数
    function updateEditFocusIndicator() {
        console.log('🔄 更新编辑表单焦点指示器');

        // 移除所有焦点指示
        editTitleField.classList.remove('current-focus');
        editContentField.classList.remove('current-focus');

        // 更新焦点指示文本
        const titleIndicator = document.getElementById('edit-title-focus-indicator');
        const contentIndicator = document.getElementById('edit-content-focus-indicator');

        if (window.editTemplateCurrentFocus === editTitleField) {
            console.log('📍 设置标题为当前焦点');
            editTitleField.classList.add('current-focus');
            if (titleIndicator) titleIndicator.textContent = '当前活动 ✓';
            if (contentIndicator) contentIndicator.textContent = '点击此处输入内容';
        } else if (window.editTemplateCurrentFocus === editContentField) {
            console.log('📍 设置内容为当前焦点');
            editContentField.classList.add('current-focus');
            if (titleIndicator) titleIndicator.textContent = '点击此处输入标题';
            if (contentIndicator) contentIndicator.textContent = '当前活动 ✓';
        }
    }

    // 绑定焦点事件
    editTitleField.addEventListener('focus', function() {
        console.log('🎯 编辑-标题字段获得焦点');
        window.editTemplateCurrentFocus = this;
        updateEditFocusIndicator();
    });

    editTitleField.addEventListener('click', function() {
        console.log('🖱️ 编辑-标题字段被点击');
        window.editTemplateCurrentFocus = this;
        updateEditFocusIndicator();
    });

    editContentField.addEventListener('focus', function() {
        console.log('🎯 编辑-内容字段获得焦点');
        window.editTemplateCurrentFocus = this;
        updateEditFocusIndicator();
    });

    editContentField.addEventListener('click', function() {
        console.log('🖱️ 编辑-内容字段被点击');
        window.editTemplateCurrentFocus = this;
        updateEditFocusIndicator();
    });

    // 状态开关事件
    if (statusSwitch && statusText) {
        statusSwitch.addEventListener('change', function() {
            statusText.textContent = this.checked ? '启用' : '禁用';
            console.log('📊 状态开关变更:', this.checked ? '启用' : '禁用');
        });
    }

    // 提交按钮事件
    editSubmitBtn.addEventListener('click', function() {
        console.log('🔄 点击保存修改按钮');
        submitEditTemplateForm(templateId);
    });

    // 初始化焦点指示器
    updateEditFocusIndicator();

    console.log('✅ 编辑模板表单初始化完成');
}

// 编辑模板专用的插入标记函数
window.editInsertMark = function(markName) {
    console.log('🏷️ 编辑模板插入标记:', markName);

    const markText = '{' + markName + '}';
    const targetField = window.editTemplateCurrentFocus;

    if (!targetField) {
        console.error('❌ 没有焦点字段');
        return;
    }

    console.log('📍 插入到字段:', targetField.id);

    // 获取当前光标位置
    const startPos = targetField.selectionStart || 0;
    const endPos = targetField.selectionEnd || 0;

    // 在光标位置插入标记
    targetField.value =
        targetField.value.substring(0, startPos) +
        markText +
        targetField.value.substring(endPos);

    // 将光标位置设置到插入的标记之后
    const newPos = startPos + markText.length;
    targetField.selectionStart = targetField.selectionEnd = newPos;

    // 保持焦点在目标输入框
    targetField.focus();

    console.log('✅ 标记插入成功');
};

// 提交编辑表单
function submitEditTemplateForm(templateId) {
    console.log('🔄 开始提交编辑表单, 模板ID:', templateId);

    const form = document.getElementById('edit-template-form');
    const editTitleField = document.getElementById('edit_title_field');
    const editContentField = document.getElementById('edit_content');
    const editSubmitBtn = document.getElementById('edit-submit-btn');

    if (!form || !editTitleField || !editContentField) {
        console.error('❌ 找不到表单元素');
        return;
    }

    // 禁用提交按钮，防止重复提交
    editSubmitBtn.disabled = true;
    editSubmitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 保存中...';

    // 创建FormData
    const formData = new FormData(form);

    const submitUrl = `/simple/templates/edit/${templateId}`;
    console.log('🎯 提交URL:', submitUrl);

    // 提交表单
    fetch(submitUrl, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('📡 收到响应，状态:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('📨 服务器响应:', data);

        if (data.success) {
            console.log('✅ 编辑保存成功');

            if (window.showToast) {
                showToast('保存成功！', 'success');
            } else {
                alert('保存成功！');
            }

            // 关闭模态框
            const modalElement = document.getElementById('editTemplateModal');
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }

            // 刷新页面以显示更新后的数据
            setTimeout(() => {
                window.location.reload();
            }, 1000);

        } else {
            console.log('❌ 编辑保存失败:', data.message);
            if (window.showToast) {
                showToast('保存失败：' + (data.message || '未知错误'), 'danger');
            } else {
                alert('保存失败：' + (data.message || '未知错误'));
            }
        }
    })
    .catch(error => {
        console.error('❌ 提交失败:', error);
        if (window.showToast) {
            showToast('提交失败：' + error.message, 'danger');
        } else {
            alert('提交失败：' + error.message);
        }
    })
    .finally(() => {
        // 恢复提交按钮
        editSubmitBtn.disabled = false;
        editSubmitBtn.innerHTML = '<i class="bi bi-check-circle"></i> 保存修改';
    });
}

// 切换模板状态函数
window.toggleTemplateStatus = function(templateId) {
    console.log('切换模板状态:', templateId);

    // 防止重复点击
    if (window.togglingStatus) {
        console.log('⚠️ 正在切换状态中，忽略重复点击');
        return;
    }

    // 设置切换状态标志
    window.togglingStatus = true;

    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');
    console.log('CSRF令牌:', csrfToken);

    // 直接切换，不需要确认对话框
    const headers = {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    };

    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    fetch(`/simple/templates/toggle_status/${templateId}`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({})
    })
    .then(response => {
        console.log('状态切换响应状态:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('🎯 状态切换响应数据:', data);
        console.log('🎯 响应中的status值:', data.status, '类型:', typeof data.status);

        if (data.success) {
            console.log('✅ 服务器返回成功，开始更新页面显示');

            // 尝试更新页面上的状态显示
            const updateSuccess = updateTemplateStatusDisplay(templateId, data.status);

            if (updateSuccess === false) {
                console.log('⚠️ 状态标签更新失败，将在2秒后刷新页面');
                if (window.showToast) {
                    showToast(data.message + '（页面将自动刷新）', 'success');
                }
                // 延迟刷新页面，让用户看到成功消息
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                if (window.showToast) {
                    showToast(data.message, 'success');
                } else {
                    console.log('✅ 状态切换成功:', data.message);
                }
            }
        } else {
            console.error('❌ 服务器返回失败:', data.message);
            if (window.showToast) {
                showToast(data.message || '操作失败', 'danger');
            } else {
                alert(data.message || '操作失败');
            }
        }
    })
    .catch(error => {
        console.error('切换状态失败:', error);
        showToast('操作失败，请重试', 'danger');
    })
    .finally(() => {
        // 重置切换状态标志
        window.togglingStatus = false;
        console.log('✅ 状态切换操作完成，重置标志');
    });
};

// 更新模板状态显示函数
function updateTemplateStatusDisplay(templateId, newStatus) {
    console.log('🔄 更新模板状态显示:', templateId, newStatus);

    // 尝试多种选择器来找到状态标签
    let statusBadge = null;

    // 方法1: 使用onclick属性选择器
    statusBadge = document.querySelector(`span[onclick="toggleTemplateStatus(${templateId})"]`);

    // 方法2: 如果方法1失败，尝试通过父元素查找
    if (!statusBadge) {
        console.log('🔍 方法1失败，尝试方法2');
        const allStatusBadges = document.querySelectorAll('.status-badge');
        console.log('🔍 找到的状态标签数量:', allStatusBadges.length);

        // 遍历所有状态标签，找到对应的模板ID
        allStatusBadges.forEach((badge, index) => {
            const onclickAttr = badge.getAttribute('onclick');
            console.log(`🔍 检查第${index + 1}个标签的onclick:`, onclickAttr);
            if (onclickAttr && onclickAttr.includes(`toggleTemplateStatus(${templateId})`)) {
                statusBadge = badge;
                console.log('✅ 通过方法2找到了状态标签');
            }
        });
    }

    // 方法3: 如果还是找不到，尝试通过表格行查找
    if (!statusBadge) {
        console.log('🔍 方法2失败，尝试方法3');
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach((row, index) => {
            const firstCell = row.querySelector('td:first-child .badge');
            if (firstCell && firstCell.textContent.includes(`#${templateId}`)) {
                statusBadge = row.querySelector('.status-badge');
                if (statusBadge) {
                    console.log('✅ 通过方法3找到了状态标签');
                }
            }
        });
    }

    if (statusBadge) {
        console.log('✅ 找到状态标签，开始更新');

        // 更新图标和文本
        const iconClass = newStatus ? 'check-circle' : 'x-circle';
        const statusText = newStatus ? '启用' : '禁用';
        const badgeClass = newStatus ? 'bg-success' : 'bg-secondary';

        // 更新完整的HTML内容
        statusBadge.innerHTML = `<i class="bi bi-${iconClass} me-1"></i>${statusText}`;

        // 更新样式类 - 先移除旧的样式类
        statusBadge.classList.remove('bg-success', 'bg-secondary');
        statusBadge.classList.add(badgeClass);

        // 确保基础类存在
        if (!statusBadge.classList.contains('badge')) {
            statusBadge.classList.add('badge');
        }
        if (!statusBadge.classList.contains('status-badge')) {
            statusBadge.classList.add('status-badge');
        }

        // 确保样式属性保持
        statusBadge.style.cursor = 'pointer';
        statusBadge.title = '点击切换状态';

        console.log('✅ 状态显示已更新:', statusText, '样式类:', statusBadge.className);

        // 强制重绘
        statusBadge.offsetHeight;

        return true; // 返回成功

    } else {
        console.error('❌ 所有方法都无法找到状态标签，模板ID:', templateId);
        console.log('❌ 页面HTML结构可能有问题');
        return false; // 返回失败
    }
}

// 删除模板函数
window.deleteTemplate = function(templateId, templateTitle) {
    // 防止重复点击
    if (window.deletingTemplate) {
        console.log('⚠️ 正在删除中，忽略重复点击');
        return;
    }

    if (confirm(`确定要删除模板 "${templateTitle}" 吗？此操作不可恢复！`)) {
        console.log('删除模板:', templateId);

        // 设置删除状态，防止重复点击
        window.deletingTemplate = true;

        fetch(`/simple/templates/delete/${templateId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => {
            console.log('📨 删除响应状态:', response.status);

            if (!response.ok) {
                return response.text().then(text => {
                    console.error('❌ 删除服务器错误响应:', text);
                    throw new Error(`HTTP ${response.status}: ${text}`);
                });
            }

            // 尝试解析JSON
            return response.text().then(text => {
                console.log('📨 删除原始响应文本:', text);
                try {
                    const data = JSON.parse(text);
                    console.log('📨 删除解析后的JSON:', data);
                    return data;
                } catch (e) {
                    console.error('❌ JSON解析失败:', e);
                    throw new Error('服务器返回的不是有效的JSON格式');
                }
            });
        })
        .then(data => {
            console.log('📨 删除响应数据:', data);

            if (data && data.success) {
                console.log('✅ 删除成功，开始更新界面');
                const message = data.message || '模板删除成功！';

                // 显示成功提示
                showToast(message, 'success');

                // 从表格中移除对应的行
                console.log('🗑️ 开始移除模板行:', templateId);
                removeTemplateRow(templateId);
            } else {
                console.log('❌ 删除失败，服务器返回错误');
                const errorMessage = '删除失败：' + (data && data.message ? data.message : '未知错误');
                showToast(errorMessage, 'danger');
            }
        })
        .catch(error => {
            console.error('❌ 删除操作失败:', error);
            const errorMessage = '删除失败，请重试: ' + error.message;
            showToast(errorMessage, 'danger');
        })
        .finally(() => {
            // 重置删除状态
            window.deletingTemplate = false;
        });
    }
};

// 获取上下文中的ID的辅助函数
window.getCategoryIdFromContext = function() {
    // 从当前打开的模态框中获取分类ID
    const modal = document.querySelector('#editCategoryModal');
    if (modal && modal.dataset.categoryId) {
        return modal.dataset.categoryId;
    }
    // 如果没有找到，返回默认值或从其他地方获取
    return null;
};

window.getMarkIdFromContext = function() {
    const modal = document.querySelector('#editMarkModal');
    if (modal && modal.dataset.markId) {
        return modal.dataset.markId;
    }
    return null;
};

window.getTemplateIdFromContext = function() {
    const modal = document.querySelector('#editTemplateModal');
    if (modal && modal.dataset.templateId) {
        return modal.dataset.templateId;
    }
    return null;
};

// 显示提示消息
window.showToast = function(message, type = 'success') {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    `;
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 添加到页面
    document.body.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
};

// 表单提交处理（带URL参数）
window.handleFormSubmitWithUrl = function(form, modalId, submitUrl) {
    const formData = new FormData(form);

    console.log('提交表单到:', submitUrl);

    fetch(submitUrl, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // 关闭当前编辑模态框（子模态框）
            const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
            if (modal) {
                modal.hide();

                // 确保遮罩层被移除
                setTimeout(() => {
                    const backdrops = document.querySelectorAll('.modal-backdrop');
                    backdrops.forEach(backdrop => backdrop.remove());

                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';

                    console.log('🧹 已清理子模态框遮罩层');
                }, 300);
            }

            // 显示成功提示
            showToast(data.message || '操作成功！', 'success');

            // 判断是否需要刷新分类管理或标记管理的内容
            if (modalId.includes('Category')) {
                // 刷新分类管理内容
                setTimeout(() => {
                    loadModalContent('/simple/templates/categories', 'categoriesContent');
                }, 300);
            } else if (modalId.includes('Mark')) {
                // 刷新标记管理内容
                setTimeout(() => {
                    loadModalContent('/simple/templates/marks', 'marksContent');
                }, 300);
            } else {
                // 其他情况刷新整个页面
                setTimeout(() => {
                    location.reload();
                }, 500);
            }
        } else {
            showToast('操作失败：' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('提交失败:', error);
        showToast('提交失败，请重试', 'danger');
    });
};

// 兼容旧的函数名
window.handleFormSubmit = window.handleFormSubmitWithUrl;

// 初始化模板表单的JavaScript（用于弹窗中的动态内容）
function initializeTemplateForm() {
    console.log('初始化模板表单JavaScript');

    // 获取DOM元素
    const titleField = document.getElementById('title_field');
    const contentField = document.getElementById('content');

    if (!titleField || !contentField) {
        console.error('找不到模板表单元素');
        return;
    }

    console.log('找到模板表单元素:', { titleField, contentField });

    // 全局变量（用于模板表单）
    window.templateFormCurrentFocus = contentField; // 默认焦点在内容区域

    // 保持{标记名}格式不变
    // 如果有其他格式，转换为{标记名}格式
    if (titleField.value) {
        titleField.value = titleField.value.replace(/《([^》]+)》/g, '{$1}');
    }
    if (contentField.value) {
        contentField.value = contentField.value.replace(/《([^》]+)》/g, '{$1}');
    }

    // 获取预览元素
    const titlePreview = document.getElementById('title-preview');
    const contentPreview = document.getElementById('content-preview');

    // 标记美化函数
    function beautifyMarks(text) {
        if (!text || !text.includes('{') || !text.includes('}')) {
            return text;
        }

        return text.replace(/\{([^}]+)\}/g, function(match, markName) {
            const markType = getMarkType(markName);
            return `<span class="inline-mark-tag mark-type-${markType}">${markName}</span>`;
        });
    }

    // 获取标记类型
    function getMarkType(markName) {
        if (markName.includes('用户') || markName.includes('@')) {
            return 'user';
        } else if (markName.includes('话题') || markName.includes('#')) {
            return 'topic';
        } else if (markName.includes('位置') || markName.includes('地点')) {
            return 'location';
        } else {
            return 'text';
        }
    }

    // 更新预览 - 修改为不显示预览区域
    function updatePreview(field, preview) {
        // 隐藏预览区域，不再显示标记内容
        preview.style.display = 'none';
    }

    // 更新标记高亮显示
    function updateMarkHighlight(element) {
        if (!element) return;
        // 使用CSS来高亮显示标记
        var content = element.value;
        if (content.includes('{') && content.includes('}')) {
            element.classList.add('has-marks');
        } else {
            element.classList.remove('has-marks');
        }
    }

    // 初始化预览 - 隐藏所有预览区域
    titlePreview.style.display = 'none';
    contentPreview.style.display = 'none';



    // 更新焦点指示器
    function updateFocusIndicator() {
        // 移除所有焦点指示
        titleField.classList.remove('current-focus');
        contentField.classList.remove('current-focus');

        // 更新焦点指示文本
        const titleIndicator = document.getElementById('title-focus-indicator');
        const contentIndicator = document.getElementById('content-focus-indicator');

        if (window.templateFormCurrentFocus === titleField) {
            titleField.classList.add('current-focus');
            if (titleIndicator) titleIndicator.textContent = '当前活动 ✓';
            if (contentIndicator) contentIndicator.textContent = '点击此处输入内容';
        } else if (window.templateFormCurrentFocus === contentField) {
            contentField.classList.add('current-focus');
            if (titleIndicator) titleIndicator.textContent = '点击此处输入标题';
            if (contentIndicator) contentIndicator.textContent = '当前活动 ✓';
        }

        // 更新标记按钮的提示文本
        const focusText = window.templateFormCurrentFocus === titleField ? '标题' : '内容';
        document.querySelectorAll('.mark-button').forEach(function(btn) {
            const markName = btn.textContent.trim().replace('🏷️ ', '');
            btn.title = `插入标记 "${markName}" 到${focusText}区域`;
        });
    }



    // 全局插入标记函数（覆盖模板表单中的函数）
    window.insertMark = function(markName) {
        console.log('insertMark被调用，标记名:', markName);
        console.log('当前焦点元素:', window.templateFormCurrentFocus);

        const markText = '{' + markName + '}';
        const targetField = window.templateFormCurrentFocus || contentField;

        console.log('插入标记:', markName, '到:', targetField === titleField ? '标题' : '内容');

        // 获取当前光标位置
        const startPos = targetField.selectionStart || 0;
        const endPos = targetField.selectionEnd || 0;

        // 在光标位置插入标记
        targetField.value =
            targetField.value.substring(0, startPos) +
            markText +
            targetField.value.substring(endPos);

        // 将光标位置设置到插入的标记之后
        const newPos = startPos + markText.length;
        targetField.selectionStart = targetField.selectionEnd = newPos;

        // 保持焦点在目标输入框
        targetField.focus();

        // 不再显示预览，直接隐藏预览区域
        if (targetField === titleField) {
            titlePreview.style.display = 'none';
        } else {
            contentPreview.style.display = 'none';
        }

        console.log('标记插入成功:', markName);
    };

    // 设置事件监听器
    titleField.addEventListener('focus', function() {
        window.templateFormCurrentFocus = titleField;
        updateFocusIndicator();
        console.log('焦点在标题输入框');
    });

    titleField.addEventListener('click', function() {
        window.templateFormCurrentFocus = titleField;
        updateFocusIndicator();
    });

    titleField.addEventListener('input', function() {
        // 不显示预览
        titlePreview.style.display = 'none';
    });

    titleField.addEventListener('keyup', function() {
        // 不显示预览
        titlePreview.style.display = 'none';
    });

    contentField.addEventListener('focus', function() {
        window.templateFormCurrentFocus = contentField;
        updateFocusIndicator();
        console.log('焦点在内容输入框');
    });

    contentField.addEventListener('click', function() {
        window.templateFormCurrentFocus = contentField;
        updateFocusIndicator();
    });

    contentField.addEventListener('input', function() {
        // 不显示预览
        contentPreview.style.display = 'none';
    });

    contentField.addEventListener('keyup', function() {
        // 不显示预览
        contentPreview.style.display = 'none';
    });

    // 初始化焦点指示器
    updateFocusIndicator();

    console.log('模板表单JavaScript初始化完成');

    // 保持{标记名}格式不变
    // 如果有其他格式，转换为{标记名}格式
    if (titleField.value) {
        titleField.value = titleField.value.replace(/《([^》]+)》/g, '{$1}');
    }
    if (contentField.value) {
        contentField.value = contentField.value.replace(/《([^》]+)》/g, '{$1}');
    }

    // 初始化高亮显示
    updateMarkHighlight(titleField);
    updateMarkHighlight(contentField);
    updateFocusIndicator();

    // 设置提交按钮事件监听器
    const submitBtn = document.getElementById('submit-btn');
    if (submitBtn) {
        submitBtn.addEventListener('click', function() {
            submitTemplateForm();
        });
    }

    console.log('模板表单JavaScript初始化完成');

    // 提交模板表单函数
    function submitTemplateForm() {
        console.log('提交模板表单');

        // 处理标题中的括号标记，转换为后端格式
        const originalTitleValue = titleField.value;
        const originalContentValue = contentField.value;

        // 标记格式已经是{标记名}，不需要转换
        // 如果有其他格式，统一转换为{标记名}
        titleField.value = titleField.value.replace(/《([^》]+)》/g, '{$1}');
        contentField.value = contentField.value.replace(/《([^》]+)》/g, '{$1}');

        // 获取表单
        const form = titleField.closest('form');
        if (!form) {
            console.error('找不到表单元素');
            alert('找不到表单元素，请刷新页面重试');
            return;
        }

        // 确定提交URL
        let submitUrl = '/simple/templates/add';
        const modalElement = document.getElementById('editTemplateModal');
        if (modalElement && modalElement.dataset.templateId) {
            const templateId = modalElement.dataset.templateId;
            submitUrl = `/simple/templates/edit/${templateId}`;
        }

        console.log('提交到URL:', submitUrl);

        // 创建FormData
        const formData = new FormData(form);

        // 提交表单
        fetch(submitUrl, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(function(response) {
            if (!response.ok) {
                throw new Error('网络响应不正常');
            }
            return response.json();
        })
        .then(function(data) {
            if (data.success) {
                showToast('保存成功！', 'success');
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('addTemplateModal')) ||
                             bootstrap.Modal.getInstance(document.getElementById('editTemplateModal'));
                if (modal) {
                    modal.hide();

                    // 确保遮罩层被移除
                    setTimeout(() => {
                        const backdrops = document.querySelectorAll('.modal-backdrop');
                        backdrops.forEach(backdrop => backdrop.remove());

                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';

                        console.log('🧹 已清理模板保存后的遮罩层');
                    }, 300);
                }

                // 判断是新增还是编辑
                const modalElement = document.getElementById('editTemplateModal');
                const isEdit = modalElement && modalElement.dataset.templateId;

                if (isEdit) {
                    // 编辑模式：更新现有行
                    updateTemplateRow(data.template);
                } else {
                    // 新增模式：添加新行到表格
                    addTemplateRow(data.template);
                }
            } else {
                showToast('保存失败：' + (data.message || '未知错误'), 'danger');
                // 恢复原始值
                titleField.value = originalTitleValue;
                contentField.value = originalContentValue;
            }
        })
        .catch(function(error) {
            console.error('提交表单出错:', error);
            showToast('保存失败，请重试', 'danger');
            // 恢复原始值
            titleField.value = originalTitleValue;
            contentField.value = originalContentValue;
        });
    }



    // 更新现有模板行
    function updateTemplateRow(template) {
        const tbody = document.getElementById('template-list');
        if (!tbody) {
            console.error('找不到模板列表表格');
            return;
        }

        // 找到对应的行
        const rows = tbody.querySelectorAll('tr');
        for (let row of rows) {
            const idCell = row.querySelector('td:first-child');
            if (idCell && idCell.textContent == template.id) {
                // 更新行内容
                row.innerHTML = `
                    <td>${template.id}</td>
                    <td class="template-title">${template.title}</td>
                    <td>${template.category_name || '-'}</td>
                    <td>
                        <span class="badge bg-${template.status ? 'success' : 'secondary'}">
                            ${template.status ? '启用' : '禁用'}
                        </span>
                    </td>
                    <td>${formatDateTime(template.created_at)}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm" title="编辑" onclick="editTemplate(${template.id})">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm" title="删除" onclick="deleteTemplate(${template.id}, '${template.title}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                `;

                // 处理新更新行中的标记
                const titleCell = row.querySelector('.template-title');
                if (titleCell) {
                    let title = titleCell.innerHTML;
                    // 只处理 {标记名} 格式
                    title = title.replace(/\{([^}]+)\}/g, function(match, markName) {
                        return `<span class="template-mark">{${markName}}</span>`;
                    });
                    titleCell.innerHTML = title;
                }

                // 添加更新高亮效果
                row.classList.add('table-warning');
                setTimeout(() => {
                    row.classList.remove('table-warning');
                }, 3000);
                break;
            }
        }
    }



    // 格式化日期时间
    function formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '-';
        try {
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            }).replace(/\//g, '-');
        } catch (e) {
            return dateTimeStr;
        }
    }
}

// 格式化日期时间 - 全局函数
window.formatDateTime = function(dateTimeStr) {
    if (!dateTimeStr) return '-';
    try {
        const date = new Date(dateTimeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        }).replace(/\//g, '-');
    } catch (e) {
        return dateTimeStr;
    }
};

// 从表格中移除模板行
window.removeTemplateRow = function(templateId) {
    console.log('🗑️ 移除模板行:', templateId);

    const tbody = document.getElementById('template-list');
    if (!tbody) {
        console.error('❌ 找不到模板列表表格');
        return;
    }

    // 找到对应的行
    const rows = tbody.querySelectorAll('tr');
    console.log('🔍 查找模板行，总行数:', rows.length);

    let found = false;
    for (let row of rows) {
        const idCell = row.querySelector('td:first-child');
        if (idCell) {
            const cellText = idCell.textContent.trim();
            console.log('🔍 检查行ID:', cellText, '目标ID:', templateId);

            // 提取数字部分进行比较（去掉#符号）
            const cellId = cellText.replace('#', '');
            const targetId = String(templateId);

            console.log('🔍 提取后的ID比较:', cellId, '===', targetId);

            if (cellId === targetId) {
                console.log('✅ 找到目标行，开始删除动画');
                found = true;

                // 添加删除动画效果
                row.style.transition = 'opacity 0.5s ease-out';
                row.style.opacity = '0';

                // 延迟移除元素
                setTimeout(() => {
                    if (row.parentNode) {
                        row.parentNode.removeChild(row);
                        console.log('✅ 模板行已移除');
                    } else {
                        console.error('❌ 行已经被移除或没有父节点');
                    }
                }, 500);
                break;
            }
        }
    }

    if (!found) {
        console.log('⚠️ 第一种方法未找到，尝试备用方法');

        // 备用方法：通过data属性或其他方式查找
        const allRows = tbody.querySelectorAll('tr');
        for (let row of allRows) {
            // 方法2：检查行中是否有包含模板ID的删除按钮
            const deleteBtn = row.querySelector(`[onclick*="deleteTemplate(${templateId})"]`);
            if (deleteBtn) {
                console.log('✅ 通过删除按钮找到目标行');
                found = true;

                // 添加删除动画效果
                row.style.transition = 'opacity 0.5s ease-out';
                row.style.opacity = '0';

                // 延迟移除元素
                setTimeout(() => {
                    if (row.parentNode) {
                        row.parentNode.removeChild(row);
                        console.log('✅ 模板行已移除（备用方法）');
                    }
                }, 500);
                break;
            }
        }
    }

    if (!found) {
        console.error('❌ 所有方法都找不到要删除的模板行:', templateId);
        console.log('⚠️ 将在2秒后刷新页面以同步状态');

        // 如果找不到行，延迟刷新页面
        setTimeout(() => {
            window.location.reload();
        }, 2000);
    }
};

// 删除分类函数
window.deleteCategoryById = function(categoryId) {
    if (confirm('确定要删除这个分类吗？此操作不可恢复！')) {
        console.log('删除分类:', categoryId);

        fetch(`/simple/templates/category/delete/${categoryId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || ''
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('分类删除成功！', 'success');
                // 刷新分类管理内容
                setTimeout(() => {
                    loadModalContent('/simple/templates/categories', 'categoriesContent');
                }, 500);
            } else {
                showToast('删除失败：' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('删除失败:', error);
            showToast('删除失败，请重试', 'danger');
        });
    }
};

// 删除标记函数
window.deleteMarkById = function(markId) {
    if (confirm('确定要删除这个标记吗？此操作不可恢复！')) {
        console.log('删除标记:', markId);

        fetch(`/simple/templates/mark/delete/${markId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || ''
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('标记删除成功！', 'success');
                // 刷新标记管理内容
                setTimeout(() => {
                    loadModalContent('/simple/templates/marks', 'marksContent');
                }, 500);
            } else {
                showToast('删除失败：' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('删除失败:', error);
            showToast('删除失败，请重试', 'danger');
        });
    }
};

// 标记处理函数已在基础模板中定义为全局函数

// 在添加新模板行时也要处理标记 - 全局函数
window.addTemplateRow = function(template) {
    const tbody = document.getElementById('template-list');
    if (!tbody) {
        console.error('找不到模板列表表格');
        return;
    }

    // 创建新行
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${template.id}</td>
        <td class="template-title">${template.title}</td>
        <td>${template.category_name || '-'}</td>
        <td>
            <span class="badge bg-${template.status ? 'success' : 'secondary'}">
                ${template.status ? '启用' : '禁用'}
            </span>
        </td>
        <td>${formatDateTime(template.created_at)}</td>
        <td>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary btn-sm" title="编辑" onclick="editTemplate(${template.id})">
                    <i class="bi bi-pencil"></i>
                </button>
                <button type="button" class="btn btn-outline-danger btn-sm" title="删除" onclick="deleteTemplate(${template.id}, '${escapeHtml(template.title)}')">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </td>
    `;

    // 添加到表格顶部（最新的在前面）
    tbody.insertBefore(row, tbody.firstChild);

    console.log('新增模板行已添加，模板ID:', template.id);
    console.log('编辑按钮HTML:', row.querySelector('button[title="编辑"]').outerHTML);

    // 处理新行中的标记
    const titleCell = row.querySelector('.template-title');
    if (titleCell) {
        let title = titleCell.innerHTML;
        // 只处理 {标记名} 格式
        title = title.replace(/\{([^}]+)\}/g, function(match, markName) {
            return `<span class="template-mark">{${markName}}</span>`;
        });
        titleCell.innerHTML = title;
    }

    // 添加高亮效果
    row.classList.add('table-success');
    setTimeout(() => {
        row.classList.remove('table-success');
    }, 3000);
};

// 添加事件委托，确保动态添加的按钮也能正常工作
document.addEventListener('click', function(e) {
    // 处理编辑按钮点击
    if (e.target.closest('button[title="编辑"]')) {
        const button = e.target.closest('button[title="编辑"]');
        const onclickAttr = button.getAttribute('onclick');
        if (onclickAttr) {
            const match = onclickAttr.match(/editTemplate\((\d+)\)/);
            if (match) {
                const templateId = match[1];
                console.log('通过事件委托触发编辑模板:', templateId);
                window.editTemplate(parseInt(templateId));
                e.preventDefault();
                e.stopPropagation();
            }
        }
    }

    // 处理删除按钮点击
    if (e.target.closest('button[title="删除"]')) {
        // 防止重复点击
        if (window.deletingTemplate) {
            console.log('⚠️ 正在删除中，忽略事件委托的重复点击');
            e.preventDefault();
            e.stopPropagation();
            return;
        }

        const button = e.target.closest('button[title="删除"]');
        const onclickAttr = button.getAttribute('onclick');
        if (onclickAttr) {
            const match = onclickAttr.match(/deleteTemplate\((\d+),\s*'([^']*)'\)/);
            if (match) {
                const templateId = match[1];
                const templateTitle = match[2];
                console.log('通过事件委托触发删除模板:', templateId, templateTitle);
                window.deleteTemplate(parseInt(templateId), templateTitle);
                e.preventDefault();
                e.stopPropagation();
            }
        }
    }
});

console.log('模板管理页面JavaScript已加载，事件委托已设置');

// 立即执行标记美化处理（调试用）
console.log('🚀 开始立即处理标记美化...');
setTimeout(function() {
    console.log('🔍 检查页面中的标记...');
    const titleElements = document.querySelectorAll('.template-title');
    console.log('找到标题元素数量:', titleElements.length);

    if (titleElements.length > 0) {
        titleElements.forEach(function(titleElement, index) {
            let title = titleElement.innerHTML;
            console.log(`标题 ${index + 1}:`, title);

            // 如果包含未处理的标记，进行处理
            if (title.includes('{') && title.includes('}') && !title.includes('template-mark')) {
                title = title.replace(/\{([^}]+)\}/g, function(match, markName) {
                    console.log('处理标记:', markName);
                    return `<span class="template-mark">{${markName}}</span>`;
                });
                titleElement.innerHTML = title;
                console.log(`✅ 标题 ${index + 1} 已美化`);
            } else if (title.includes('template-mark')) {
                console.log(`✅ 标题 ${index + 1} 已经美化过了`);
            } else {
                console.log(`ℹ️ 标题 ${index + 1} 没有标记`);
            }
        });
    } else {
        console.log('⚠️ 没有找到标题元素，可能页面还未加载完成');
    }
}, 1000);

// 全局遮罩层清理函数
function clearAllBackdrops() {
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => backdrop.remove());

    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';

    console.log('🧹 已清理所有遮罩层');
}

// 页面加载完成后，监听模态框事件
document.addEventListener('DOMContentLoaded', function() {
    // 清理可能残留的遮罩层
    clearAllBackdrops();

    // 监听所有模态框的隐藏事件，确保遮罩层被清理
    document.addEventListener('hidden.bs.modal', function() {
        setTimeout(clearAllBackdrops, 100);
    });

    console.log('🔧 已设置全局遮罩层清理监听器');
});

</script>

<style>
/* 修复所有模态框层级问题 */

/* 主模态框层级 */
#addTemplateModal,
#editTemplateModal,
#categoriesModal,
#marksModal {
    z-index: 1055 !important;
}

/* 主模态框对话框 */
#addTemplateModal .modal-dialog,
#editTemplateModal .modal-dialog,
#categoriesModal .modal-dialog,
#marksModal .modal-dialog {
    z-index: 1056 !important;
    position: relative !important;
}

/* 子模态框基础样式 */
#addCategoryModal,
#editCategoryModal,
#addMarkModal,
#editMarkModal {
    z-index: 1070 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
}

/* 确保子模态框默认隐藏 */
#addCategoryModal:not(.show),
#editCategoryModal:not(.show),
#addMarkModal:not(.show),
#editMarkModal:not(.show) {
    display: none !important;
}

/* 子模态框对话框 */
#addCategoryModal .modal-dialog,
#editCategoryModal .modal-dialog,
#addMarkModal .modal-dialog,
#editMarkModal .modal-dialog {
    z-index: 1071 !important;
    position: relative !important;
}

/* 子模态框显示状态 */
#addCategoryModal.show,
#editCategoryModal.show,
#addMarkModal.show,
#editMarkModal.show {
    display: block !important;
    z-index: 1070 !important;
}

/* 背景遮罩层级控制 */
.modal-backdrop {
    z-index: 1050 !important;
}

/* 确保模态框内容可以交互 */
.modal.show {
    z-index: 1055 !important;
}

.modal.show .modal-dialog {
    z-index: 1056 !important;
    position: relative !important;
}

/* 子模态框遮罩层级控制 */
#addCategoryModal + .modal-backdrop,
#editCategoryModal + .modal-backdrop,
#addMarkModal + .modal-backdrop,
#editMarkModal + .modal-backdrop {
    z-index: 1069 !important;
}

/* 强制显示子模态框 - 只在show状态下生效 */
.modal.child-modal-force-show.show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 1070 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    pointer-events: auto !important;
}

.modal.child-modal-force-show.show .modal-dialog {
    z-index: 1071 !important;
    position: relative !important;
    pointer-events: auto !important;
}


</style>

<script>
// 修复数据库字符集
function fixEmojiCharset() {
    if (!confirm('此操作将修改数据库字符集为utf8mb4以支持emoji，确定继续吗？')) {
        return;
    }

    // 显示加载状态
    const loadingToast = showToast('正在修复数据库字符集...', 'info', 0);

    fetch('/simple/fix-emoji-charset', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        // 隐藏加载提示
        if (loadingToast) {
            loadingToast.hide();
        }

        if (data.success) {
            showToast(data.message, 'success');
            console.log('修复详情:', data.details);

            // 显示详细信息
            let details = data.details.join('\n');
            alert('修复完成！\n\n详细信息：\n' + details);
        } else {
            showToast('修复失败: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        // 隐藏加载提示
        if (loadingToast) {
            loadingToast.hide();
        }
        console.error('修复字符集失败:', error);
        showToast('修复字符集失败，请重试', 'danger');
    });
}

// 恢复emoji内容
function restoreEmojiContent() {
    if (!confirm('此操作将尝试恢复模板中被损坏的emoji字符，确定继续吗？')) {
        return;
    }

    // 显示加载状态
    const loadingToast = showToast('正在恢复emoji字符...', 'info', 0);

    fetch('/simple/restore-emoji-content', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        // 隐藏加载提示
        if (loadingToast) {
            loadingToast.hide();
        }

        if (data.success) {
            showToast(data.message, 'success');
            console.log('恢复详情:', data.details);

            // 刷新页面显示最新数据
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showToast('恢复失败: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        // 隐藏加载提示
        if (loadingToast) {
            loadingToast.hide();
        }
        console.error('恢复emoji失败:', error);
        showToast('恢复emoji失败，请重试', 'danger');
    });
}

// 调试模板内容
function debugTemplateContent() {
    fetch('/simple/contents/debug-template-content', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('模板调试信息:', data.templates);

            let debugInfo = '模板调试信息：\n\n';
            data.templates.forEach(template => {
                debugInfo += `ID: ${template.id}\n`;
                debugInfo += `标题: ${template.title}\n`;
                debugInfo += `内容: ${template.content}\n`;
                debugInfo += `标记: ${JSON.stringify(template.marks)}\n`;
                debugInfo += '---\n';
            });

            // 创建一个新窗口显示调试信息
            const debugWindow = window.open('', '_blank', 'width=800,height=600');
            debugWindow.document.write(`
                <html>
                <head><title>模板调试信息</title></head>
                <body>
                    <h2>模板调试信息</h2>
                    <pre style="white-space: pre-wrap; font-family: monospace;">${debugInfo}</pre>
                </body>
                </html>
            `);
        } else {
            showToast('获取调试信息失败: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('调试失败:', error);
        showToast('获取调试信息失败，请重试', 'danger');
    });
}

// 通用Toast提示函数
function showToast(message, type = 'info', duration = 3000) {
    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // 创建toast元素
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'danger' ? 'danger' : type === 'success' ? 'success' : 'info'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: duration > 0,
        delay: duration
    });

    toast.show();

    // 返回toast对象以便外部控制
    return toast;
}
</script>
