{% extends "base.html" %}

{% block title %}发布超时设置{% endblock %}

{% block page_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">发布超时设置</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('publish.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('publish.timeout_settings') }}">
                        {{ form.csrf_token }}
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <p><i class="fas fa-info-circle"></i> 发布超时设置说明：</p>
                                    <ul>
                                        <li>发布超时时间：文案状态为"发布中"超过此时间后，系统将根据超时处理策略进行处理</li>
                                        <li>超时处理策略：
                                            <ul>
                                                <li><strong>保持超时状态</strong>：将状态标记为"发布超时"，需要手动处理</li>
                                                <li><strong>自动重置为待发布</strong>：将状态重置为"未发布"，可再次被API获取</li>
                                                <li><strong>自动标记为发布失败</strong>：将状态标记为"发布失败"</li>
                                            </ul>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.timeout.label(class="form-label") }}
                                    {{ form.timeout(class="form-control") }}
                                    {% if form.timeout.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.timeout.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">设置范围：1-72小时</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.action.label(class="form-label") }}
                                    {{ form.action(class="form-select") }}
                                    {% if form.action.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.action.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> 保存设置
                                </button>
                                <a href="{{ url_for('publish.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> 取消
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 