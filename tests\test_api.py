"""
API接口测试
"""
import json
import pytest
from flask import url_for

from app.models import SystemSetting, db
from app.utils.api_auth import generate_api_key


@pytest.fixture
def api_key(app):
    """创建测试API密钥"""
    with app.app_context():
        # 生成API密钥
        key = generate_api_key()
        
        # 保存到数据库
        setting = SystemSetting(
            key='API_KEY',
            value=key,
            description='API密钥'
        )
        db.session.add(setting)
        db.session.commit()
        
        return key


def test_api_without_key(client):
    """测试不带API密钥访问API"""
    response = client.get('/api/v1/contents')
    assert response.status_code == 401
    data = json.loads(response.data)
    assert data['success'] == False
    assert 'error' in data
    assert data['error_code'] == 'missing_api_key'


def test_api_with_invalid_key(client):
    """测试使用无效API密钥访问API"""
    response = client.get('/api/v1/contents', headers={'X-API-Key': 'invalid_key'})
    assert response.status_code == 401
    data = json.loads(response.data)
    assert data['success'] == False
    assert 'error' in data
    assert data['error_code'] == 'invalid_api_key'


def test_get_contents(client, api_key):
    """测试获取文案列表API"""
    response = client.get('/api/v1/contents', headers={'X-API-Key': api_key})
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data['success'] == True
    assert 'contents' in data
    assert 'total' in data
    assert 'page' in data
    assert 'pages' in data


def test_get_content(client, api_key):
    """测试获取单篇文案API"""
    response = client.get('/api/v1/content', headers={'X-API-Key': api_key})
    # 可能没有可用文案，所以状态码可能是404
    if response.status_code == 200:
        data = json.loads(response.data)
        assert data['success'] == True
        assert 'content' in data
        assert 'id' in data['content']
        assert 'title' in data['content']
        assert 'content' in data['content']
    else:
        assert response.status_code == 404
        data = json.loads(response.data)
        assert 'error' in data


def test_get_content_by_id(client, api_key, app):
    """测试根据ID获取文案API"""
    with app.app_context():
        from app.models.content import Content
        # 获取一个存在的文案ID
        content = Content.query.first()
        if content:
            response = client.get(f'/api/v1/content/{content.id}', headers={'X-API-Key': api_key})
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['success'] == True
            assert 'content' in data
            assert data['content']['id'] == content.id
        else:
            # 如果没有文案，测试使用不存在的ID
            response = client.get('/api/v1/content/9999', headers={'X-API-Key': api_key})
            assert response.status_code == 404
            data = json.loads(response.data)
            assert 'error' in data


def test_update_content_status(client, api_key, app):
    """测试更新文案状态API"""
    with app.app_context():
        from app.models.content import Content
        # 获取一个存在的文案ID
        content = Content.query.first()
        if content:
            # 更新状态为成功
            response = client.post(
                f'/api/v1/content/{content.id}/status',
                headers={
                    'X-API-Key': api_key,
                    'Content-Type': 'application/json'
                },
                data=json.dumps({
                    'status': 'success',
                    'publish_url': 'https://example.com/test',
                    'platform': '小红书',
                    'account': 'test_account'
                })
            )
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['success'] == True
            assert 'message' in data
            
            # 检查数据库中的状态是否已更新
            updated_content = Content.query.get(content.id)
            assert updated_content.publish_status == 'published'
            assert updated_content.workflow_status == 'published'
        else:
            # 如果没有文案，测试使用不存在的ID
            response = client.post(
                '/api/v1/content/9999/status',
                headers={
                    'X-API-Key': api_key,
                    'Content-Type': 'application/json'
                },
                data=json.dumps({
                    'status': 'success'
                })
            )
            assert response.status_code == 404
            data = json.loads(response.data)
            assert 'error' in data


def test_batch_update(client, api_key, app):
    """测试批量更新API"""
    with app.app_context():
        from app.models.content import Content
        # 获取存在的文案ID列表
        contents = Content.query.limit(2).all()
        content_ids = [content.id for content in contents]
        
        if content_ids:
            # 批量更新优先级
            response = client.post(
                '/api/v1/contents/batch',
                headers={
                    'X-API-Key': api_key,
                    'Content-Type': 'application/json'
                },
                data=json.dumps({
                    'content_ids': content_ids,
                    'action': 'priority',
                    'priority': 'high'
                })
            )
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['success'] == True
            assert data['count'] == len(content_ids)
            
            # 检查数据库中的优先级是否已更新
            for content_id in content_ids:
                content = Content.query.get(content_id)
                assert content.publish_priority == 'high'


def test_get_stats(client, api_key):
    """测试获取统计数据API"""
    response = client.get('/api/v1/stats', headers={'X-API-Key': api_key})
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data['success'] == True
    assert 'stats' in data
    assert 'status' in data['stats']
    assert 'priority' in data['stats']
    assert 'client' in data['stats']
    assert 'total' in data['stats']


def test_get_settings(client, api_key):
    """测试获取系统设置API"""
    response = client.get('/api/v1/settings', headers={'X-API-Key': api_key})
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data['success'] == True
    assert 'settings' in data
    assert 'publish_timeout' in data['settings']
    assert 'timeout_action' in data['settings'] 