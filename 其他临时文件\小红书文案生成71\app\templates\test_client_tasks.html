<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试客户任务功能</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>测试客户任务功能</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>客户和任务选择</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="client_id" class="form-label">选择客户:</label>
                            <select id="client_id" class="form-select">
                                <option value="">-- 请选择客户 --</option>
                                <option value="1">客户1</option>
                                <option value="2">客户2</option>
                                <option value="3">客户3</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="task_id" class="form-label">选择任务:</label>
                            <select id="task_id" class="form-select">
                                <option value="0">-- 创建新任务 --</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="new_task_name" class="form-label">任务名称:</label>
                            <input type="text" id="new_task_name" class="form-control" placeholder="任务名称">
                            <small id="task-name-hint" class="text-muted">请输入新任务的名称</small>
                        </div>
                        
                        <button type="button" class="btn btn-primary" onclick="testAPI()">测试API</button>
                        <button type="button" class="btn btn-secondary" onclick="clearLog()">清空日志</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>调试信息</h5>
                    </div>
                    <div class="card-body">
                        <pre id="debug-info" style="height: 400px; overflow-y: auto; background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; font-size: 12px;">等待操作...</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const debugInfo = document.getElementById('debug-info');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'color: red;' : (type === 'success' ? 'color: green;' : '');
            debugInfo.innerHTML += `<span style="${color}">[${timestamp}] ${message}</span>\n`;
            debugInfo.scrollTop = debugInfo.scrollHeight;
        }
        
        function clearLog() {
            debugInfo.innerHTML = '';
        }
        
        // 客户选择变化事件
        document.getElementById('client_id').addEventListener('change', function() {
            const clientId = this.value;
            log(`客户选择变化: ${clientId}`);
            
            if (clientId) {
                log(`开始获取客户 ${clientId} 的任务列表...`);
                
                // 调用新的API
                fetch(`/simple/contents/get-all-tasks/${clientId}`)
                    .then(response => {
                        log(`API响应状态: ${response.status}`);
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        log(`API响应数据: ${JSON.stringify(data, null, 2)}`, 'success');
                        
                        const taskSelect = document.getElementById('task_id');
                        if (data.success) {
                            // 清空现有选项
                            taskSelect.innerHTML = '';
                            
                            // 添加"创建新任务"选项
                            const createNewOption = document.createElement('option');
                            createNewOption.value = '0';
                            createNewOption.textContent = '-- 创建新任务 --';
                            taskSelect.appendChild(createNewOption);
                            
                            // 添加现有任务选项
                            if (data.tasks && data.tasks.length > 0) {
                                data.tasks.forEach(task => {
                                    const option = document.createElement('option');
                                    option.value = task.id;
                                    option.textContent = task.name;
                                    taskSelect.appendChild(option);
                                });
                                log(`成功加载 ${data.tasks.length} 个任务`, 'success');
                            } else {
                                log('该客户没有任务');
                            }
                            
                            // 默认选择"创建新任务"
                            taskSelect.value = '0';
                            updateTaskNameDisplay();
                        } else {
                            log(`API返回失败: ${data.message || '未知错误'}`, 'error');
                        }
                    })
                    .catch(error => {
                        log(`获取任务列表失败: ${error.message}`, 'error');
                    });
            } else {
                log('清空任务选择');
                const taskSelect = document.getElementById('task_id');
                taskSelect.innerHTML = '';
                const createNewOption = document.createElement('option');
                createNewOption.value = '0';
                createNewOption.textContent = '-- 创建新任务 --';
                taskSelect.appendChild(createNewOption);
                taskSelect.value = '0';
                updateTaskNameDisplay();
            }
        });
        
        // 任务选择变化事件
        document.getElementById('task_id').addEventListener('change', function() {
            log(`任务选择变化: ${this.value} (${this.options[this.selectedIndex].text})`);
            updateTaskNameDisplay();
        });
        
        // 更新任务名称显示
        function updateTaskNameDisplay() {
            const taskSelect = document.getElementById('task_id');
            const newTaskNameInput = document.getElementById('new_task_name');
            const hintElement = document.getElementById('task-name-hint');
            
            if (taskSelect.value === '0') {
                // 创建新任务
                newTaskNameInput.readOnly = false;
                newTaskNameInput.style.backgroundColor = '';
                newTaskNameInput.style.cursor = 'text';
                
                // 设置默认任务名称
                const now = new Date();
                const defaultTaskName = `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月${String(now.getDate()).padStart(2, '0')}日任务`;
                newTaskNameInput.value = defaultTaskName;
                
                hintElement.textContent = '请输入新任务的名称';
                hintElement.className = 'text-muted';
                
                log(`设置为创建新任务模式，默认名称: ${defaultTaskName}`);
            } else {
                // 选择已有任务
                newTaskNameInput.readOnly = true;
                newTaskNameInput.style.backgroundColor = '#e9ecef';
                newTaskNameInput.style.cursor = 'not-allowed';
                newTaskNameInput.value = taskSelect.options[taskSelect.selectedIndex].text;
                
                hintElement.textContent = '已选择现有任务，任务名称不可修改';
                hintElement.className = 'text-info';
                
                log(`设置为已有任务模式: ${newTaskNameInput.value}`);
            }
        }
        
        // 测试API函数
        function testAPI() {
            const clientId = document.getElementById('client_id').value;
            if (!clientId) {
                log('请先选择客户', 'error');
                return;
            }
            
            log(`手动测试API: /simple/contents/get-all-tasks/${clientId}`);
            
            fetch(`/simple/contents/get-all-tasks/${clientId}`)
                .then(response => {
                    log(`手动测试 - API响应状态: ${response.status}`);
                    return response.text();
                })
                .then(text => {
                    log(`手动测试 - API响应内容: ${text}`, 'success');
                    try {
                        const data = JSON.parse(text);
                        log(`手动测试 - 解析后的JSON: ${JSON.stringify(data, null, 2)}`, 'success');
                    } catch (e) {
                        log(`手动测试 - JSON解析失败: ${e.message}`, 'error');
                    }
                })
                .catch(error => {
                    log(`手动测试 - 请求失败: ${error.message}`, 'error');
                });
        }
        
        // 初始化
        log('页面初始化完成');
        updateTaskNameDisplay();
    </script>
</body>
</html>
