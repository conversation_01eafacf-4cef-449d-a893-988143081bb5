"""
日志工具模块
"""

import os
import logging
from logging.handlers import RotatingFileHandler


def setup_logger(app):
    """配置日志"""
    log_level = app.config.get('LOG_LEVEL', 'INFO')
    log_dir = os.path.join(app.root_path, 'logs')
    
    # 确保日志目录存在
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 应用日志
    app_log_file = os.path.join(log_dir, 'app.log')
    app_handler = RotatingFileHandler(app_log_file, maxBytes=10 * 1024 * 1024, backupCount=10)
    app_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    app_handler.setLevel(getattr(logging, log_level))
    
    # 错误日志
    error_log_file = os.path.join(log_dir, 'error.log')
    error_handler = RotatingFileHandler(error_log_file, maxBytes=10 * 1024 * 1024, backupCount=10)
    error_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    error_handler.setLevel(logging.ERROR)
    
    # 访问日志
    access_log_file = os.path.join(log_dir, 'access.log')
    access_handler = RotatingFileHandler(access_log_file, maxBytes=10 * 1024 * 1024, backupCount=10)
    access_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(message)s'
    ))
    
    # 配置根日志
    app.logger.addHandler(app_handler)
    app.logger.addHandler(error_handler)
    app.logger.setLevel(getattr(logging, log_level))
    
    # 创建访问日志记录器
    access_logger = logging.getLogger('access')
    access_logger.addHandler(access_handler)
    access_logger.setLevel(logging.INFO)
    access_logger.propagate = False
    
    return access_logger 