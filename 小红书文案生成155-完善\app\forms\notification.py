"""
通知系统表单
"""
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, BooleanField, RadioField, TextAreaField, DateField
from wtforms.validators import DataRequired, Optional, Length


class NotificationFilterForm(FlaskForm):
    """通知筛选表单"""
    type = SelectField('通知类型', choices=[
        ('', '全部类型'),
        ('review_status', '审核状态变更'),
        ('client_operation', '客户操作'),
        ('publish_notice', '发布通知'),
        ('system_change', '系统变更')
    ], default='')
    
    priority = SelectField('优先级', choices=[
        ('', '全部优先级'),
        ('high', '高'),
        ('normal', '中'),
        ('low', '低')
    ], default='')
    
    is_read = SelectField('读取状态', choices=[
        ('', '全部状态'),
        ('0', '未读'),
        ('1', '已读')
    ], default='')
    
    date_from = StringField('开始日期')
    date_to = StringField('结束日期')
    search = StringField('搜索')


class NotificationSettingForm(FlaskForm):
    """通知设置表单"""
    enable_all = BooleanField('启用所有通知', default=True)
    
    # 通知类型设置
    enable_review_status = BooleanField('审核状态变更通知', default=True)
    enable_client_operation = BooleanField('客户操作通知', default=True)
    enable_publish_notice = BooleanField('发布通知', default=True)
    enable_system_change = BooleanField('系统变更通知', default=True)
    
    # 通知接收角色设置
    recipient_roles = SelectField('通知接收角色', choices=[
        ('all', '所有角色'),
        ('admin', '仅管理员'),
        ('reviewer', '审核员及以上'),
        ('editor', '编辑及以上')
    ], default='all')
    
    # 通知显示设置
    display_count = SelectField('显示数量', choices=[
        ('10', '10条'),
        ('20', '20条'),
        ('50', '50条'),
        ('100', '100条')
    ], default='20')
    
    auto_mark_read = BooleanField('查看后自动标记为已读', default=True)


class NotificationCreateForm(FlaskForm):
    """创建通知表单"""
    title = StringField('通知标题', validators=[
        DataRequired('请输入通知标题'),
        Length(max=100, message='标题不能超过100个字符')
    ])
    
    content = TextAreaField('通知内容', validators=[
        DataRequired('请输入通知内容')
    ])
    
    type = SelectField('通知类型', choices=[
        ('review_status', '审核状态变更'),
        ('client_operation', '客户操作'),
        ('publish_notice', '发布通知'),
        ('system_change', '系统变更')
    ], default='system_change')
    
    priority = RadioField('优先级', choices=[
        ('high', '高'),
        ('normal', '中'),
        ('low', '低')
    ], default='normal')
    
    recipient_type = RadioField('接收者类型', choices=[
        ('all', '所有用户'),
        ('role', '按角色选择'),
        ('user', '指定用户')
    ], default='all')
    
    recipient_role = SelectField('接收角色', default=0, coerce=int)
    recipient_user = SelectField('接收用户', default=0, coerce=int) 