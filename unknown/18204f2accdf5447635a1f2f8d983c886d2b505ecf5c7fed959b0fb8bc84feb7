# -*- coding: utf-8 -*-
"""
发布管理视图
"""
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from sqlalchemy import func, desc, or_

from app import db
from app.models import Content, PublishRecord, SystemSetting
from app.utils.permission import permission_required
from app.forms.publish import PublishFilterForm, PublishBatchForm, PublishTimeoutForm

# 创建蓝图
publish_bp = Blueprint('publish', __name__)

@publish_bp.route('/')
@login_required
@permission_required('content_publish')
def index():
    """发布管理首页"""
    form = PublishFilterForm(request.args)
    
    # 查询条件
    query = Content.query
    
    # 筛选条件
    if form.client_id.data and form.client_id.data is not None:
        query = query.filter(Content.client_id == form.client_id.data)
    
    if form.status.data:
        query = query.filter(Content.publish_status == form.status.data)
    
    if form.priority.data:
        query = query.filter(Content.publish_priority == form.priority.data)
    
    if form.start_date.data:
        query = query.filter(Content.created_at >= form.start_date.data)
    
    if form.end_date.data:
        # 添加一天，使得结束日期包含当天
        end_date = form.end_date.data + timedelta(days=1)
        query = query.filter(Content.created_at < end_date)
    
    # 只显示已通过客户审核的文案
    query = query.filter(Content.workflow_status.in_(['client_approved', 'pending_publish', 'published']))
    
    # 排序
    if form.sort_by.data == 'priority':
        # 按优先级排序：高 > 中 > 低
        priority_order = {
            'high': 0,
            'normal': 1,
            'low': 2
        }
        query = query.order_by(
            # 使用case语句进行自定义排序
            db.case(
                {value: key for key, value in priority_order.items()},
                value=Content.publish_priority
            ),
            desc(Content.created_at)
        )
    elif form.sort_by.data == 'status':
        # 按状态排序
        status_order = {
            'unpublished': 0,
            'publishing': 1,
            'publish_timeout': 2,
            'failed': 3,
            'partial_published': 4,
            'published': 5
        }
        query = query.order_by(
            db.case(
                {value: key for key, value in status_order.items()},
                value=Content.publish_status
            ),
            desc(Content.created_at)
        )
    else:
        # 默认按创建时间排序
        query = query.order_by(desc(Content.created_at))
    
    # 分页
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    contents = pagination.items
    
    # 获取发布超时时间设置
    publish_timeout_setting = SystemSetting.query.filter_by(key='PUBLISH_TIMEOUT').first()
    publish_timeout = int(publish_timeout_setting.value) if publish_timeout_setting else 86400  # 默认24小时
    
    # 统计数据
    status_counts = db.session.query(
        Content.publish_status,
        func.count(Content.id).label('count')
    ).filter(
        Content.workflow_status.in_(['client_approved', 'pending_publish', 'published'])
    ).group_by(Content.publish_status).all()
    
    status_stats = {status: count for status, count in status_counts}
    
    # 批量操作表单
    batch_form = PublishBatchForm()
    
    return render_template(
        'publish/index.html',
        contents=contents,
        pagination=pagination,
        form=form,
        batch_form=batch_form,
        status_stats=status_stats,
        publish_timeout=publish_timeout
    )

@publish_bp.route('/batch_update', methods=['POST'])
@login_required
@permission_required('content_publish')
def batch_update():
    """批量更新发布状态"""
    form = PublishBatchForm()
    
    if form.validate_on_submit():
        content_ids = request.form.getlist('content_ids')
        action = form.action.data
        status = form.status.data
        priority = form.priority.data
        
        if not content_ids:
            flash('请至少选择一篇文案', 'warning')
            return redirect(url_for('publish.index'))
        
        count = 0
        now = datetime.now()
        
        for content_id in content_ids:
            content = Content.query.get(content_id)
            if not content:
                continue
            
            # 检查权限
            if not current_user.has_permission('content_publish'):
                continue
            
            # 根据操作类型执行不同的操作
            if action == 'status':
                # 更新发布状态
                old_status = content.publish_status
                content.publish_status = status
                
                # 如果标记为发布成功，同时更新工作流状态为已发布
                if status == 'published':
                    content.workflow_status = 'published'
                    content.publish_time = now
                    
                    # 创建发布记录
                    publish_record = PublishRecord(
                        content_id=content.id,
                        status='success',
                        platform='手动标记',
                        account='系统管理员',
                        publish_time=now,
                        ext_info=f'{{"operator": "{current_user.username}"}}'
                    )
                    db.session.add(publish_record)
                
                # 如果从发布中状态重置为待发布，清除发布时间
                if old_status == 'publishing' and status == 'unpublished':
                    content.publish_time = None
                
                count += 1
                
            elif action == 'priority':
                # 更新发布优先级
                content.publish_priority = priority
                count += 1
        
        db.session.commit()
        
        if action == 'status':
            status_text = {
                'unpublished': '未发布',
                'publishing': '发布中',
                'published': '已发布',
                'failed': '发布失败',
                'partial_published': '部分发布',
                'publish_timeout': '发布超时'
            }.get(status, status)
            flash(f'已将 {count} 篇文案状态更新为 {status_text}', 'success')
        else:
            priority_text = {
                'high': '高',
                'normal': '中',
                'low': '低'
            }.get(priority, priority)
            flash(f'已将 {count} 篇文案优先级更新为 {priority_text}', 'success')
    
    return redirect(url_for('publish.index'))

@publish_bp.route('/timeout_settings', methods=['GET', 'POST'])
@login_required
@permission_required('system_settings')
def timeout_settings():
    """发布超时设置"""
    form = PublishTimeoutForm()
    
    # 获取当前设置
    publish_timeout_setting = SystemSetting.query.filter_by(key='PUBLISH_TIMEOUT').first()
    publish_timeout = int(publish_timeout_setting.value) if publish_timeout_setting else 86400  # 默认24小时
    
    # 获取超时处理策略
    timeout_action_setting = SystemSetting.query.filter_by(key='PUBLISH_TIMEOUT_ACTION').first()
    timeout_action = timeout_action_setting.value if timeout_action_setting else 'keep_timeout'  # 默认保持超时状态
    
    if request.method == 'GET':
        form.timeout.data = publish_timeout // 3600  # 转换为小时
        form.action.data = timeout_action
    
    if form.validate_on_submit():
        # 更新超时时间设置
        timeout_value = form.timeout.data * 3600  # 转换为秒
        
        if publish_timeout_setting:
            publish_timeout_setting.value = str(timeout_value)
            publish_timeout_setting.updated_at = datetime.now()
            publish_timeout_setting.updated_by = current_user.id
        else:
            new_setting = SystemSetting(
                key='PUBLISH_TIMEOUT',
                value=str(timeout_value),
                description='发布超时时间（秒）',
                updated_at=datetime.now(),
                updated_by=current_user.id
            )
            db.session.add(new_setting)
        
        # 更新超时处理策略
        if timeout_action_setting:
            timeout_action_setting.value = form.action.data
            timeout_action_setting.updated_at = datetime.now()
            timeout_action_setting.updated_by = current_user.id
        else:
            new_action_setting = SystemSetting(
                key='PUBLISH_TIMEOUT_ACTION',
                value=form.action.data,
                description='发布超时处理策略',
                updated_at=datetime.now(),
                updated_by=current_user.id
            )
            db.session.add(new_action_setting)
        
        db.session.commit()
        flash('发布超时设置已更新', 'success')
        return redirect(url_for('publish.index'))
    
    return render_template('publish/timeout_settings.html', form=form)

@publish_bp.route('/check_timeouts')
@login_required
@permission_required('content_publish')
def check_timeouts():
    """检查并处理超时发布"""
    # 获取超时设置
    publish_timeout_setting = SystemSetting.query.filter_by(key='PUBLISH_TIMEOUT').first()
    publish_timeout = int(publish_timeout_setting.value) if publish_timeout_setting else 86400  # 默认24小时
    
    # 获取超时处理策略
    timeout_action_setting = SystemSetting.query.filter_by(key='PUBLISH_TIMEOUT_ACTION').first()
    timeout_action = timeout_action_setting.value if timeout_action_setting else 'keep_timeout'  # 默认保持超时状态
    
    # 计算超时时间点
    timeout_point = datetime.now() - timedelta(seconds=publish_timeout)
    
    # 查找所有发布中且已超时的文案
    timeout_contents = Content.query.filter(
        Content.publish_status == 'publishing',
        Content.publish_time < timeout_point
    ).all()
    
    count = 0
    for content in timeout_contents:
        # 根据策略处理超时
        if timeout_action == 'auto_reset':
            # 自动重置为待发布
            content.publish_status = 'unpublished'
            content.publish_time = None
        elif timeout_action == 'auto_fail':
            # 自动标记为发布失败
            content.publish_status = 'failed'
        else:
            # 保持超时状态
            content.publish_status = 'publish_timeout'
        
        count += 1
    
    db.session.commit()
    
    flash(f'已处理 {count} 篇超时文案', 'success')
    return redirect(url_for('publish.index'))

@publish_bp.route('/detail/<int:content_id>')
@login_required
@permission_required('content_publish')
def detail(content_id):
    """文案发布详情"""
    content = Content.query.get_or_404(content_id)
    
    # 获取发布记录
    publish_records = PublishRecord.query.filter_by(content_id=content_id).order_by(desc(PublishRecord.publish_time)).all()
    
    return render_template(
        'publish/detail.html',
        content=content,
        publish_records=publish_records
    )

@publish_bp.route('/update/<int:content_id>', methods=['POST'])
@login_required
@permission_required('content_publish')
def update(content_id):
    """更新单个文案的发布状态"""
    content = Content.query.get_or_404(content_id)
    
    status = request.form.get('status')
    priority = request.form.get('priority')
    
    if status:
        content.publish_status = status
        
        # 如果标记为发布成功，同时更新工作流状态为已发布
        if status == 'published':
            content.workflow_status = 'published'
            content.publish_time = datetime.now()
            
            # 创建发布记录
            publish_record = PublishRecord(
                content_id=content.id,
                status='success',
                platform='手动标记',
                account='系统管理员',
                publish_time=datetime.now(),
                ext_info=f'{{"operator": "{current_user.username}"}}'
            )
            db.session.add(publish_record)
        
        # 如果从发布中状态重置为待发布，清除发布时间
        if content.publish_status == 'publishing' and status == 'unpublished':
            content.publish_time = None
    
    if priority:
        content.publish_priority = priority
    
    db.session.commit()
    
    return redirect(url_for('publish.detail', content_id=content_id))

@publish_bp.route('/api/get_content', methods=['POST'])
def api_get_content():
    """API接口：获取待发布文案"""
    # 验证API密钥
    api_key = request.headers.get('X-API-Key')
    if not api_key:
        return jsonify({'error': '缺少API密钥'}), 401
    
    # 验证API密钥是否有效
    api_key_setting = SystemSetting.query.filter_by(key='API_KEY').first()
    if not api_key_setting or api_key_setting.value != api_key:
        return jsonify({'error': 'API密钥无效'}), 401
    
    # 获取请求参数
    data = request.get_json() or {}
    client_id = data.get('client_id')
    priority = data.get('priority')
    
    # 构建查询
    query = Content.query.filter(
        Content.workflow_status == 'pending_publish',
        Content.publish_status == 'unpublished'
    )
    
    # 根据客户ID筛选
    if client_id:
        query = query.filter(Content.client_id == client_id)
    
    # 根据优先级筛选
    if priority:
        query = query.filter(Content.publish_priority == priority)
    else:
        # 默认按优先级排序：高 > 中 > 低
        priority_order = {
            'high': 0,
            'normal': 1,
            'low': 2
        }
        query = query.order_by(
            # 使用case语句进行自定义排序
            db.case(
                {value: key for key, value in priority_order.items()},
                value=Content.publish_priority
            ),
            Content.created_at
        )
    
    # 获取一篇文案
    content = query.first()
    
    if not content:
        return jsonify({'error': '没有可用的待发布文案'}), 404
    
    # 更新文案状态为发布中
    content.publish_status = 'publishing'
    content.publish_time = datetime.now()
    db.session.commit()
    
    # 构建响应数据
    response_data = {
        'id': content.id,
        'title': content.title,
        'content': content.content,
        'topics': content.topics,
        'location': content.location,
        'images': content.image_urls,
        'client_id': content.client_id,
        'created_at': content.created_at.isoformat() if content.created_at else None,
        'priority': content.publish_priority
    }
    
    return jsonify(response_data)

@publish_bp.route('/api/update_status', methods=['POST'])
def api_update_status():
    """API接口：更新文案发布状态"""
    # 验证API密钥
    api_key = request.headers.get('X-API-Key')
    if not api_key:
        return jsonify({'error': '缺少API密钥'}), 401
    
    # 验证API密钥是否有效
    api_key_setting = SystemSetting.query.filter_by(key='API_KEY').first()
    if not api_key_setting or api_key_setting.value != api_key:
        return jsonify({'error': 'API密钥无效'}), 401
    
    # 获取请求参数
    data = request.get_json() or {}
    content_id = data.get('content_id')
    status = data.get('status')
    publish_url = data.get('publish_url')
    platform = data.get('platform')
    account = data.get('account')
    error_message = data.get('error_message')
    ext_info = data.get('ext_info')
    
    # 验证必填参数
    if not content_id or not status:
        return jsonify({'error': '缺少必填参数：content_id 或 status'}), 400
    
    # 获取文案
    content = Content.query.get(content_id)
    if not content:
        return jsonify({'error': f'文案不存在：{content_id}'}), 404
    
    # 更新文案状态
    if status == 'success':
        content.publish_status = 'published'
        content.workflow_status = 'published'
    elif status == 'failed':
        content.publish_status = 'failed'
    else:
        return jsonify({'error': f'无效的状态值：{status}'}), 400
    
    # 创建发布记录
    publish_record = PublishRecord(
        content_id=content_id,
        status=status,
        platform=platform or '',
        account=account or '',
        publish_url=publish_url or '',
        publish_time=datetime.now(),
        error_message=error_message or '',
        ext_info=ext_info or '{}'
    )
    
    db.session.add(publish_record)
    db.session.commit()
    
    return jsonify({'success': True, 'message': '状态已更新'}) 