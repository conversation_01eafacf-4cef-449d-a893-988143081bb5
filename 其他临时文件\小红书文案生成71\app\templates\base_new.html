<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}小红书文案生成系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/sidebar.css') }}">
    {% block styles %}{% endblock %}
</head>
<body>
    {% if current_user.is_authenticated %}
    <!-- 左侧菜单布局 -->
    <div class="app-container">
        <!-- 左侧菜单 -->
        <aside class="sidebar" id="sidebar">
            <!-- 品牌标识 -->
            <div class="sidebar-header">
                <div class="brand">
                    <i class="fas fa-edit"></i>
                    <span class="brand-text">文案生成系统</span>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            
            <!-- 用户信息 -->
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-details">
                    <div class="user-name">{{ current_user.username }}</div>
                    <div class="user-role">{{ current_user.real_name or '用户' }}</div>
                </div>
                <div class="user-menu dropdown">
                    <button class="btn btn-link dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('navigation.my_permissions') }}" data-ajax-link>
                            <i class="fas fa-key me-2"></i>我的权限
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}" data-ajax-link>
                            <i class="fas fa-lock me-2"></i>修改密码
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                            <i class="fas fa-sign-out-alt me-2"></i>退出登录
                        </a></li>
                    </ul>
                </div>
            </div>
            
            <!-- 菜单导航 -->
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    {% set menu_items = get_user_menu_items() %}
                    {% for item in menu_items %}
                        {% if not item.parent_id %}
                            <li class="nav-item">
                                <a href="{{ item.url }}" class="nav-link" 
                                   {% if not item.url.startswith('http') and not item.url.startswith('/admin/') %}data-ajax-link{% endif %}>
                                    <i class="{{ item.icon or 'fas fa-circle' }}"></i>
                                    <span class="nav-text">{{ item.name }}</span>
                                    {% if item.name == '通知中心' %}
                                        <span class="badge bg-danger notification-badge" id="sidebarNotificationBadge" style="display: none;">0</span>
                                    {% endif %}
                                </a>
                            </li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部工具栏 -->
            <header class="content-header">
                <div class="header-left">
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="breadcrumb-container">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}" data-ajax-link>首页</a></li>
                                {% block breadcrumb %}{% endblock %}
                            </ol>
                        </nav>
                    </div>
                </div>
                <div class="header-right">
                    <div class="notification-icon">
                        <a href="{{ url_for('notification.index') }}" data-ajax-link>
                            <i class="fas fa-bell"></i>
                            <span class="badge bg-danger notification-badge" id="headerNotificationBadge" style="display: none;">0</span>
                        </a>
                    </div>
                </div>
            </header>
            
            <!-- 页面内容 -->
            <div class="content-wrapper" id="main-content">
                {% block content_auth %}{% endblock %}
            </div>
        </main>
    </div>
    {% else %}
    <!-- 未登录用户的内容 -->
    <div class="auth-container">
        {% block content %}{% endblock %}
    </div>
    {% endif %}

    <!-- Flash 消息 -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- 自定义脚本 -->
    <script src="{{ url_for('static', filename='js/sidebar.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ajax-nav.js') }}"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
