"""
静态资源优化工具模块
用于优化静态资源加载性能
"""
import os
import hashlib
from flask import url_for, current_app


def get_asset_url(filename, folder=''):
    """
    获取静态资源URL，添加版本号以优化缓存
    
    参数:
        filename: 文件名
        folder: 文件夹，默认为空
    
    返回:
        带版本号的URL
    """
    # 构建文件路径
    if folder:
        path = os.path.join(current_app.static_folder, folder, filename)
        url = url_for('static', filename=f'{folder}/{filename}')
    else:
        path = os.path.join(current_app.static_folder, filename)
        url = url_for('static', filename=filename)
    
    # 获取文件修改时间作为版本号
    try:
        mtime = int(os.path.getmtime(path))
        return f"{url}?v={mtime}"
    except OSError:
        # 文件不存在时，返回原始URL
        return url


def get_asset_hash(filename, folder=''):
    """
    获取静态资源的哈希值，用于内容寻址
    
    参数:
        filename: 文件名
        folder: 文件夹，默认为空
    
    返回:
        文件内容的哈希值
    """
    # 构建文件路径
    if folder:
        path = os.path.join(current_app.static_folder, folder, filename)
    else:
        path = os.path.join(current_app.static_folder, filename)
    
    # 计算文件哈希值
    try:
        with open(path, 'rb') as f:
            file_hash = hashlib.md5(f.read()).hexdigest()
        return file_hash[:8]  # 取前8位即可
    except OSError:
        # 文件不存在时，返回空字符串
        return ''


def get_preload_assets():
    """
    获取需要预加载的静态资源列表
    
    返回:
        预加载资源列表，每个元素是(URL, 类型)元组
    """
    preload_assets = [
        ('css/bootstrap.min.css', 'style'),
        ('css/adminlte.min.css', 'style'),
        ('js/jquery.min.js', 'script'),
        ('js/bootstrap.bundle.min.js', 'script'),
        ('js/adminlte.min.js', 'script')
    ]
    
    result = []
    for asset, asset_type in preload_assets:
        # 获取带版本号的URL
        url = get_asset_url(os.path.basename(asset), os.path.dirname(asset))
        result.append((url, asset_type))
    
    return result


def generate_asset_manifest():
    """
    生成静态资源清单，用于服务端推送
    
    返回:
        资源清单字典，键为资源路径，值为哈希值
    """
    manifest = {}
    
    # CSS文件
    css_dir = os.path.join(current_app.static_folder, 'css')
    if os.path.exists(css_dir):
        for filename in os.listdir(css_dir):
            if filename.endswith('.css'):
                file_hash = get_asset_hash(filename, 'css')
                manifest[f'/static/css/{filename}'] = file_hash
    
    # JS文件
    js_dir = os.path.join(current_app.static_folder, 'js')
    if os.path.exists(js_dir):
        for filename in os.listdir(js_dir):
            if filename.endswith('.js'):
                file_hash = get_asset_hash(filename, 'js')
                manifest[f'/static/js/{filename}'] = file_hash
    
    return manifest


def get_critical_css():
    """
    获取关键CSS，用于内联到HTML中
    
    返回:
        关键CSS内容
    """
    critical_css_path = os.path.join(current_app.static_folder, 'css', 'critical.css')
    
    try:
        with open(critical_css_path, 'r', encoding='utf-8') as f:
            return f.read()
    except OSError:
        # 文件不存在时，返回空字符串
        return '' 