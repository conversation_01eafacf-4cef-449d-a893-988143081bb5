/* 小红书文案生成系统自定义样式 */

body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

.footer {
    margin-top: auto;
}

/* 自定义卡片阴影效果 */
.card.shadow {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important;
    transition: box-shadow 0.3s ease;
}

.card.shadow:hover {
    box-shadow: 0 1rem 2rem rgba(0,0,0,.15) !important;
}

/* 表单样式 */
.invalid-feedback {
    display: block;
}

/* 自定义确认对话框样式 */
.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.custom-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.custom-modal {
    background: white;
    border-radius: 12px;
    padding: 0;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.7);
    transition: transform 0.3s ease;
}

.custom-modal-overlay.show .custom-modal {
    transform: scale(1);
}

.custom-modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e9ecef;
}

.custom-modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #dc3545;
    display: flex;
    align-items: center;
}

.custom-modal-title i {
    margin-right: 8px;
    font-size: 20px;
}

.custom-modal-body {
    padding: 20px 24px;
    color: #6c757d;
    line-height: 1.5;
}

.custom-modal-footer {
    padding: 16px 24px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.custom-modal-footer .btn {
    min-width: 80px;
    border-radius: 6px;
    font-weight: 500;
}

/* 自定义提示框样式 */
.custom-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    z-index: 10001;
    display: flex;
    align-items: center;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 350px;
}

.custom-toast.show {
    transform: translateX(0);
}

.custom-toast.error {
    background: #dc3545;
}

.custom-toast.warning {
    background: #ffc107;
    color: #212529;
}

.custom-toast.info {
    background: #17a2b8;
}

.custom-toast i {
    margin-right: 10px;
    font-size: 18px;
}

.custom-toast-message {
    flex: 1;
    font-weight: 500;
}

/* 导航栏样式 */
.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85);
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #ffffff;
}

/* AJAX导航样式 */
#loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    background: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    display: none;
}

.ajax-fade {
    opacity: 0.6;
    transition: opacity 0.3s;
}

/* 活动菜单项样式 */
.navbar-nav .nav-link.active {
    color: #ffffff;
    font-weight: bold;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
} 