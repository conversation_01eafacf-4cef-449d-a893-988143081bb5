{% extends 'base.html' %}

{% block title %}用户管理{% endblock %}

{% block head %}
<style>
.role-badges {
    line-height: 1.8;
    padding: 4px 0;
}

.role-badge {
    display: inline-block !important;
    margin: 2px 3px 2px 0 !important;
    padding: 4px 8px !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    border-radius: 4px !important;
    white-space: nowrap !important;
    border: 1px solid !important;
    text-decoration: none !important;
}

/* 管理类权限 - 红色 */
.role-badge-admin {
    background-color: #dc3545 !important;
    color: white !important;
    border-color: #dc3545 !important;
}

/* 生成类权限 - 橙色 */
.role-badge-manager {
    background-color: #fd7e14 !important;
    color: white !important;
    border-color: #fd7e14 !important;
}

/* 编辑类权限 - 绿色 */
.role-badge-editor {
    background-color: #198754 !important;
    color: white !important;
    border-color: #198754 !important;
}

/* 查看类权限 - 紫色 */
.role-badge-viewer {
    background-color: #6f42c1 !important;
    color: white !important;
    border-color: #6f42c1 !important;
}

/* 默认权限 - 蓝色 */
.role-badge-default {
    background-color: #0d6efd !important;
    color: white !important;
    border-color: #0d6efd !important;
}

/* 特殊权限 - 青色 */
.role-badge-special {
    background-color: #20c997 !important;
    color: white !important;
    border-color: #20c997 !important;
}

/* 无权限 - 灰色 */
.role-badge-none {
    background-color: #6c757d !important;
    color: white !important;
    border-color: #6c757d !important;
}

.user-table td {
    vertical-align: middle;
    padding: 12px 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .role-badge {
        font-size: 0.7rem;
        padding: 3px 6px;
        margin: 1px 2px 1px 0;
    }
}
</style>
{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">用户管理</h1>
        <a href="{{ url_for('user_management.user_create') }}" class="btn btn-primary" data-ajax-link>
            <i class="fas fa-plus"></i> 创建用户
        </a>
    </div>

    <div class="card shadow">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover user-table">
                    <thead>
                        <tr>
                            <th style="width: 50px;">ID</th>
                            <th style="width: 100px;">用户名</th>
                            <th style="width: 100px;">真实姓名</th>
                            <th style="width: 150px;">邮箱</th>
                            <th style="width: 400px;">权限</th>
                            <th style="width: 70px;">状态</th>
                            <th style="width: 130px;">最后登录</th>
                            <th style="width: 180px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ user.id }}</td>
                            <td>{{ user.username }}</td>
                            <td>{{ user.real_name or '-' }}</td>
                            <td>{{ user.email }}</td>
                            <td style="min-width: 200px;">
                                <div class="role-badges" data-user-id="{{ user.id }}">
                                    {% set user_permissions = [] %}

                                    {# 收集用户的所有权限（直接权限 + 角色权限） #}
                                    {% for perm in user.permissions %}
                                        {% if perm not in user_permissions %}
                                            {% set _ = user_permissions.append(perm) %}
                                        {% endif %}
                                    {% endfor %}

                                    {% for role in user.roles %}
                                        {% for perm in role.permissions %}
                                            {% if perm not in user_permissions %}
                                                {% set _ = user_permissions.append(perm) %}
                                            {% endif %}
                                        {% endfor %}
                                    {% endfor %}

                                    {# 权限名称映射 #}
                                    {% set permission_names = {
                                        'dashboard_access': '控制面板',
                                        'user_manage': '用户管理',
                                        'client_manage': '客户管理',
                                        'template_manage': '模板管理',
                                        'content_manage': '文案管理',
                                        'content_generate': '生成文案',
                                        'topic_manage': '话题管理',
                                        'task_manage': '任务管理',
                                        'publish_manage': '发布管理',
                                        'supplement_manage': '文案补充',
                                        'display_manage': '文案展示',
                                        'notification_manage': '通知中心',
                                        'stats_view': '数据统计',
                                        'export_manage': '导入导出',
                                        'system_settings': '系统设置'
                                    } %}

                                    {# 显示权限标签 #}
                                    {% for perm in user_permissions %}
                                    {% set perm_display = permission_names.get(perm.name, perm.description or perm.name) %}
                                    {% set perm_class = 'role-badge-default' %}

                                    {# 根据权限类型设置颜色 #}
                                    {% if perm.name in ['user_manage', 'client_manage', 'template_manage', 'content_manage', 'topic_manage', 'task_manage', 'publish_manage', 'supplement_manage', 'display_manage', 'notification_manage', 'export_manage', 'system_settings'] %}
                                        {% set perm_class = 'role-badge-admin' %}
                                    {% elif perm.name in ['content_generate'] %}
                                        {% set perm_class = 'role-badge-manager' %}
                                    {% elif 'edit' in perm.name or 'update' in perm.name %}
                                        {% set perm_class = 'role-badge-editor' %}
                                    {% elif perm.name in ['dashboard_access', 'stats_view'] %}
                                        {% set perm_class = 'role-badge-viewer' %}
                                    {% elif perm.name.startswith('admin') or perm.name.endswith('admin') %}
                                        {% set perm_class = 'role-badge-special' %}
                                    {% endif %}

                                    <span class="role-badge {{ perm_class }}" title="{{ perm.name }}: {{ perm_display }}"
                                          style="display: inline-block; margin: 2px 3px 2px 0; padding: 4px 8px; font-size: 0.75rem; font-weight: 500; border-radius: 4px; white-space: nowrap; border: 1px solid; text-decoration: none;
                                          {% if perm_class == 'role-badge-admin' %}background-color: #dc3545; color: white; border-color: #dc3545;
                                          {% elif perm_class == 'role-badge-manager' %}background-color: #fd7e14; color: white; border-color: #fd7e14;
                                          {% elif perm_class == 'role-badge-editor' %}background-color: #198754; color: white; border-color: #198754;
                                          {% elif perm_class == 'role-badge-viewer' %}background-color: #6f42c1; color: white; border-color: #6f42c1;
                                          {% elif perm_class == 'role-badge-special' %}background-color: #20c997; color: white; border-color: #20c997;
                                          {% else %}background-color: #0d6efd; color: white; border-color: #0d6efd;{% endif %}">
                                        {{ perm_display }}
                                    </span>
                                    {% else %}
                                    <span class="role-badge role-badge-none" title="该用户暂无任何权限"
                                          style="display: inline-block; margin: 2px 3px 2px 0; padding: 4px 8px; font-size: 0.75rem; font-weight: 500; border-radius: 4px; white-space: nowrap; border: 1px solid; text-decoration: none; background-color: #6c757d; color: white; border-color: #6c757d;">
                                        无权限
                                    </span>
                                    {% endfor %}

                                    {# 权限统计信息 #}
                                    {% if user_permissions %}
                                    <div class="mt-1">
                                        <small class="text-muted">
                                            共 {{ user_permissions|length }} 项权限
                                        </small>
                                    </div>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if user.is_active %}
                                <span class="badge bg-success">启用</span>
                                {% else %}
                                <span class="badge bg-danger">禁用</span>
                                {% endif %}
                            </td>
                            <td>{{ user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else '从未登录' }}</td>
                            <td>
                                <div class="d-flex flex-wrap gap-1">
                                    <a href="{{ url_for('user_management.user_edit', user_id=user.id) }}" class="btn btn-sm btn-outline-primary mb-1" data-ajax-link>
                                        <i class="fas fa-edit"></i> 编辑
                                    </a>
                                    <a href="{{ url_for('user_management.user_permissions', user_id=user.id) }}" class="btn btn-sm btn-outline-success mb-1" data-ajax-link>
                                        <i class="fas fa-key"></i> 权限
                                    </a>
                                    <a href="{{ url_for('user_management.user_password', user_id=user.id) }}" class="btn btn-sm btn-outline-warning mb-1" data-ajax-link>
                                        <i class="fas fa-lock"></i> 密码
                                    </a>
                                    {% if user.id != current_user.id %}
                                    <button type="button" class="btn btn-sm btn-outline-danger mb-1"
                                            data-bs-toggle="modal"
                                            data-bs-target="#deleteModal"
                                            data-user-id="{{ user.id }}"
                                            data-user-name="{{ user.username }}">
                                        <i class="fas fa-trash"></i> 禁用
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if pagination.pages > 1 %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if pagination.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('user_management.user_list', page=pagination.prev_num) }}" data-ajax-link>&laquo;</a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">&laquo;</span>
                    </li>
                    {% endif %}

                    {% for page_num in pagination.iter_pages() %}
                        {% if page_num %}
                            {% if page_num == pagination.page %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('user_management.user_list', page=page_num) }}" data-ajax-link>{{ page_num }}</a>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if pagination.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('user_management.user_list', page=pagination.next_num) }}" data-ajax-link>&raquo;</a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">&raquo;</span>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认禁用用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要禁用用户 <strong id="delete-user-name"></strong> 吗？</p>
                <p class="text-danger">禁用后该用户将无法登录系统！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="delete-form" action="" method="post">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">确认禁用</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 设置删除模态框数据
        const deleteModal = document.getElementById('deleteModal');
        if (deleteModal) {
            deleteModal.addEventListener('show.bs.modal', function (event) {
                const button = event.relatedTarget;
                const userId = button.getAttribute('data-user-id');
                const userName = button.getAttribute('data-user-name');
                
                document.getElementById('delete-user-name').textContent = userName;
                document.getElementById('delete-form').action = "{{ url_for('user_management.user_delete', user_id=0) }}".replace('0', userId);
            });
        }
    });
</script>
{% endblock %} 