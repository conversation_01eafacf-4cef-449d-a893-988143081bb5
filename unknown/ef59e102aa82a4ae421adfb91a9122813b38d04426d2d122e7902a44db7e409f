/* 全局样式 */
:root {
    --sidebar-width: 260px;
    --sidebar-collapsed-width: 70px;
    --sidebar-bg: #2c3e50;
    --sidebar-color: #ecf0f1;
    --sidebar-hover: #34495e;
    --sidebar-active: #3498db;
    --header-height: 60px;
    --content-bg: #f5f7fa;
    --border-color: #dfe6e9;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--content-bg);
    color: #333;
}

/* 应用容器 */
.app-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* 侧边栏样式 */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--sidebar-bg);
    color: var(--sidebar-color);
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

/* 侧边栏头部 */
.sidebar-header {
    height: var(--header-height);
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.brand {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 18px;
    white-space: nowrap;
    overflow: hidden;
}

.brand i {
    font-size: 20px;
    margin-right: 10px;
}

.sidebar-toggle {
    background: transparent;
    border: none;
    color: var(--sidebar-color);
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s;
}

.sidebar-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar.collapsed .brand-text {
    display: none;
}

.sidebar.collapsed .sidebar-toggle i {
    transform: rotate(180deg);
}

/* 用户信息 */
.user-info {
    padding: 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.user-avatar i {
    font-size: 20px;
}

.user-details {
    flex-grow: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.user-name {
    font-weight: 600;
    font-size: 14px;
}

.user-role {
    font-size: 12px;
    opacity: 0.8;
}

.user-menu {
    margin-left: auto;
}

.user-menu .btn-link {
    color: var(--sidebar-color);
    padding: 5px;
}

.sidebar.collapsed .user-details,
.sidebar.collapsed .user-menu {
    display: none;
}

.sidebar.collapsed .user-info {
    justify-content: center;
    padding: 15px 0;
}

.sidebar.collapsed .user-avatar {
    margin-right: 0;
}

/* 导航菜单 */
.sidebar-nav {
    flex-grow: 1;
    overflow-y: auto;
    padding: 15px 0;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--sidebar-color);
    text-decoration: none;
    transition: all 0.2s;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background-color: var(--sidebar-hover);
    color: white;
}

.nav-link.active {
    background-color: var(--sidebar-active);
    border-left-color: white;
    color: white;
}

.nav-link i {
    font-size: 18px;
    width: 24px;
    text-align: center;
    margin-right: 10px;
}

.nav-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar.collapsed .nav-text {
    display: none;
}

.sidebar.collapsed .nav-link {
    padding: 15px 0;
    justify-content: center;
}

.sidebar.collapsed .nav-link i {
    margin-right: 0;
}

/* 主内容区域 */
.main-content {
    flex-grow: 1;
    margin-left: var(--sidebar-width);
    transition: margin-left 0.3s ease;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.sidebar.collapsed ~ .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* 内容头部 */
.content-header {
    height: var(--header-height);
    background-color: white;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    position: sticky;
    top: 0;
    z-index: 900;
}

.header-left {
    display: flex;
    align-items: center;
}

.mobile-menu-toggle {
    display: none;
    background: transparent;
    border: none;
    font-size: 20px;
    margin-right: 15px;
    cursor: pointer;
}

.current-page-title {
    color: #2c3e50;
    font-weight: 500;
    margin-left: 10px;
}

.tab-actions {
    display: flex;
    align-items: center;
}

.close-tab {
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    transition: all 0.2s;
}

.close-tab:hover {
    background-color: #f8f9fa;
    color: #dc3545;
}

.notification-icon {
    margin-left: 10px;
}

.breadcrumb-container {
    margin-left: 10px;
}

.breadcrumb {
    margin-bottom: 0;
    background-color: transparent;
}

.notification-icon {
    position: relative;
    cursor: pointer;
}

.notification-icon i {
    font-size: 20px;
    color: #555;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 10px;
    padding: 2px 5px;
}

/* 内容区域 */
.content-wrapper {
    padding: 20px;
    flex-grow: 1;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
        box-shadow: none;
    }
    
    .sidebar.mobile-visible {
        transform: translateX(0);
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
        display: none;
    }
    
    .sidebar-overlay.visible {
        display: block;
    }
}

/* 认证页面样式 */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
}

/* 重置认证页面的样式，避免被侧边栏样式影响 */
.auth-container * {
    box-sizing: border-box;
}

.auth-container .card {
    max-width: 400px;
    width: 100%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.auth-container .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 30px 20px;
    border: none;
}

.auth-container .card-body {
    padding: 30px;
    background: white;
}

.auth-container .form-control {
    border-radius: 8px;
    border: 2px solid #e1e5e9;
    padding: 12px 15px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.auth-container .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.auth-container .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    padding: 12px 30px;
    font-weight: 600;
    width: 100%;
    transition: all 0.3s ease;
}

.auth-container .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Flash 消息 */
.flash-messages {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 350px;
}

/* 隐藏右侧顶部标题条和所有顶部布局 */
.main-content .top-header,
.main-content .page-header,
.main-content .content-header,
.main-content > .container-fluid:first-child,
.main-content > .row:first-child,
.main-content > .d-flex:first-child,
.main-content > .py-4:first-child,
.main-content > div:first-child:not(.tab-navigation):not(.content-area) {
    display: none !important;
}

/* 标签页导航样式 - 紧贴顶部 */
.tab-navigation {
    background: var(--content-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 40px;
    overflow: hidden;
    position: relative;
    top: 0;
    margin: 0;
    order: -1; /* 确保标签页在最前面 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 确保主内容区域的标签页在最顶部 */
.main-content {
    padding-top: 0 !important;
}

.main-content .tab-navigation {
    margin-top: 0 !important;
    border-top: none !important;
}

/* 标签页激活状态的动画效果 */
@keyframes tabActivate {
    0% {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
    }
    100% {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }
}

.tab-item.active {
    animation: tabActivate 0.3s ease-out;
}

.tab-list {
    display: flex;
    align-items: center;
    overflow-x: auto;
    flex: 1;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.tab-list::-webkit-scrollbar {
    display: none;
}

.tab-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-bottom: none;
    margin-right: 2px;
    cursor: pointer;
    white-space: nowrap;
    min-width: 120px;
    max-width: 200px;
    position: relative;
    transition: all 0.3s ease;
    color: #6c757d;
    font-weight: 400;
    border-radius: 6px 6px 0 0;
}

.tab-item:hover {
    background: #e9ecef;
    color: #495057;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 激活状态的标签 - 明显的蓝色主题 */
.tab-item.active {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: 1px solid #007bff;
    border-bottom: none;
    color: #ffffff;
    font-weight: 600;
    z-index: 1;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    transform: translateY(-2px);
}

/* 激活标签的底部连接线 */
.tab-item.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: #007bff;
    z-index: 2;
}

.tab-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 13px;
    color: inherit;
    transition: all 0.3s ease;
}

/* 激活标签的标题样式 */
.tab-item.active .tab-title {
    color: #ffffff;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.tab-close {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 12px;
    margin-left: 8px;
    padding: 2px 4px;
    border-radius: 2px;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
}

.tab-item:hover .tab-close {
    opacity: 1;
}

/* 激活标签的关闭按钮 */
.tab-item.active .tab-close {
    color: #ffffff;
    opacity: 0.8;
}

.tab-item.active .tab-close:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    opacity: 1;
}

.tab-close:hover {
    background: #dc3545;
    color: white;
}

.tab-controls {
    padding: 0 10px;
    border-left: 1px solid var(--border-color);
}

/* 内容区域优化 */
.content-wrapper {
    background: var(--content-bg);
    min-height: calc(100vh - 60px);
    overflow-y: auto;
}

.content-area {
    padding: 15px 0 0 0;
    margin: 0;
}

/* 避免重复的容器样式 */
.content-area .container-fluid {
    padding: 20px;
}

.content-area .card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.content-area .card-header {
    background: var(--content-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 15px 20px;
}

.content-area .card-body {
    padding: 20px;
}

/* 选择性隐藏顶部标题布局，但保留按钮 */
.content-area .d-flex.justify-content-between h1,
.content-area .d-flex.justify-content-between h2,
.content-area .d-flex.justify-content-between h3,
.content-area .d-flex.justify-content-between .h1,
.content-area .d-flex.justify-content-between .h2,
.content-area .d-flex.justify-content-between .h3 {
    display: none !important;
}

/* 保留按钮区域，但调整样式 */
.content-area .d-flex.justify-content-between {
    margin-bottom: 1rem !important;
    padding-top: 0 !important;
}

/* 如果整个布局只包含标题，则隐藏 */
.content-area .d-flex.justify-content-between:not(:has(.btn)):not(:has(a)):not(:has(button)) {
    display: none !important;
}

/* 移除页面标题，但不影响卡片内的标题 */
.content-area > .container-fluid > h1,
.content-area > .container-fluid > h2,
.content-area > .container-fluid > h3,
.content-area > .container-fluid > .h1,
.content-area > .container-fluid > .h2,
.content-area > .container-fluid > .h3,
.content-area > .container-fluid > .page-title,
.content-area > h1,
.content-area > h2,
.content-area > h3,
.content-area > .h1,
.content-area > .h2,
.content-area > .h3,
.content-area > .page-title {
    display: none !important;
}

/* 调整内容区域样式 */
.content-area .container-fluid {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

/* 调整卡片样式 */
.content-area .card {
    margin-top: 0 !important;
}

/* 确保按钮正确显示 */
.content-area .btn {
    display: inline-block !important;
    margin-bottom: 0.5rem;
}

/* 确保按钮容器正确显示 */
.content-area .d-flex:has(.btn),
.content-area .text-end:has(.btn),
.content-area .col-md-4:has(.btn) {
    display: flex !important;
}

/* 调整按钮容器的布局 */
.content-area .d-flex.justify-content-between:has(.btn) {
    display: flex !important;
    justify-content: flex-end !important;
    margin-bottom: 1rem !important;
    padding: 0 20px !important;
}

/* 强制显示重要的按钮区域 */
.content-area .text-end,
.content-area .col-md-4.text-end,
.content-area .d-flex:has(a[href*="create"]),
.content-area .d-flex:has(a[href*="add"]) {
    display: block !important;
    text-align: right !important;
    margin-bottom: 1rem !important;
    padding: 0 20px !important;
}
