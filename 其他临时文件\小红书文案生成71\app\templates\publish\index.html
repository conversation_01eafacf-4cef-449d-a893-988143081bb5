{% extends "base.html" %}

{% block title %}发布管理{% endblock %}

{% block content_auth %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">发布管理</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('publish.timeout_settings') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-cog"></i> 超时设置
                        </a>
                        <a href="{{ url_for('publish.check_timeouts') }}" class="btn btn-sm btn-outline-warning">
                            <i class="fas fa-clock"></i> 检查超时
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 筛选表单 -->
                    <form method="get" action="{{ url_for('publish.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-2">
                                {{ form.client_id.label(class="form-label") }}
                                {{ form.client_id(class="form-select") }}
                            </div>
                            <div class="col-md-2">
                                {{ form.status.label(class="form-label") }}
                                {{ form.status(class="form-select") }}
                            </div>
                            <div class="col-md-2">
                                {{ form.priority.label(class="form-label") }}
                                {{ form.priority(class="form-select") }}
                            </div>
                            <div class="col-md-2">
                                {{ form.start_date.label(class="form-label") }}
                                {{ form.start_date(class="form-control", type="date") }}
                            </div>
                            <div class="col-md-2">
                                {{ form.end_date.label(class="form-label") }}
                                {{ form.end_date(class="form-control", type="date") }}
                            </div>
                            <div class="col-md-2">
                                {{ form.sort_by.label(class="form-label") }}
                                {{ form.sort_by(class="form-select") }}
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> 筛选
                                </button>
                                <a href="{{ url_for('publish.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> 重置
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- 统计信息 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">发布状态统计</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-2 text-center">
                                            <div class="info-box bg-light">
                                                <div class="info-box-content">
                                                    <span class="info-box-text">未发布</span>
                                                    <span class="info-box-number text-primary">{{ status_stats.get('unpublished', 0) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-2 text-center">
                                            <div class="info-box bg-light">
                                                <div class="info-box-content">
                                                    <span class="info-box-text">发布中</span>
                                                    <span class="info-box-number text-info">{{ status_stats.get('publishing', 0) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-2 text-center">
                                            <div class="info-box bg-light">
                                                <div class="info-box-content">
                                                    <span class="info-box-text">已发布</span>
                                                    <span class="info-box-number text-success">{{ status_stats.get('published', 0) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-2 text-center">
                                            <div class="info-box bg-light">
                                                <div class="info-box-content">
                                                    <span class="info-box-text">发布失败</span>
                                                    <span class="info-box-number text-danger">{{ status_stats.get('failed', 0) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-2 text-center">
                                            <div class="info-box bg-light">
                                                <div class="info-box-content">
                                                    <span class="info-box-text">部分发布</span>
                                                    <span class="info-box-number text-warning">{{ status_stats.get('partial_published', 0) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-2 text-center">
                                            <div class="info-box bg-light">
                                                <div class="info-box-content">
                                                    <span class="info-box-text">发布超时</span>
                                                    <span class="info-box-number text-secondary">{{ status_stats.get('publish_timeout', 0) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 批量操作表单 -->
                    <form method="post" action="{{ url_for('publish.batch_update') }}" id="batch-form">
                        {{ batch_form.csrf_token }}
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="btn-group">
                                    <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        批量操作
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <h6 class="dropdown-header">更新状态</h6>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="setBatchAction('status', 'unpublished')">
                                                标记为未发布
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="setBatchAction('status', 'publishing')">
                                                标记为发布中
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="setBatchAction('status', 'published')">
                                                标记为已发布
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="setBatchAction('status', 'failed')">
                                                标记为发布失败
                                            </a>
                                        </li>
                                        <li>
                                            <div class="dropdown-divider"></div>
                                        </li>
                                        <li>
                                            <h6 class="dropdown-header">更新优先级</h6>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="setBatchAction('priority', 'high')">
                                                设为高优先级
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="setBatchAction('priority', 'normal')">
                                                设为中优先级
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="setBatchAction('priority', 'low')">
                                                设为低优先级
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                <button type="button" class="btn btn-secondary" onclick="selectAll()">全选</button>
                                <button type="button" class="btn btn-secondary" onclick="deselectAll()">取消全选</button>
                            </div>
                        </div>

                        <!-- 隐藏字段 -->
                        {{ batch_form.action }}
                        {{ batch_form.status }}
                        {{ batch_form.priority }}

                        <!-- 文案列表 -->
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead>
                                    <tr>
                                        <th width="40px">选择</th>
                                        <th width="60px">ID</th>
                                        <th>标题</th>
                                        <th>客户</th>
                                        <th>发布状态</th>
                                        <th>优先级</th>
                                        <th>创建时间</th>
                                        <th>发布时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for content in contents %}
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="content_ids" value="{{ content.id }}" class="form-check-input content-checkbox">
                                        </td>
                                        <td>{{ content.id }}</td>
                                        <td>
                                            <a href="{{ url_for('content.detail', content_id=content.id) }}" target="_blank">
                                                {{ content.title|truncate(30) }}
                                            </a>
                                        </td>
                                        <td>{{ content.client.name if content.client else '无' }}</td>
                                        <td>
                                            {% if content.publish_status == 'unpublished' %}
                                            <span class="badge bg-primary">未发布</span>
                                            {% elif content.publish_status == 'publishing' %}
                                            <span class="badge bg-info">发布中</span>
                                            {% elif content.publish_status == 'published' %}
                                            <span class="badge bg-success">已发布</span>
                                            {% elif content.publish_status == 'failed' %}
                                            <span class="badge bg-danger">发布失败</span>
                                            {% elif content.publish_status == 'partial_published' %}
                                            <span class="badge bg-warning">部分发布</span>
                                            {% elif content.publish_status == 'publish_timeout' %}
                                            <span class="badge bg-secondary">发布超时</span>
                                            {% else %}
                                            <span class="badge bg-secondary">{{ content.publish_status }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if content.publish_priority == 'high' %}
                                            <span class="badge bg-danger">高</span>
                                            {% elif content.publish_priority == 'normal' %}
                                            <span class="badge bg-warning">中</span>
                                            {% elif content.publish_priority == 'low' %}
                                            <span class="badge bg-info">低</span>
                                            {% else %}
                                            <span class="badge bg-secondary">{{ content.publish_priority }}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ content.created_at.strftime('%Y-%m-%d %H:%M') if content.created_at else '无' }}</td>
                                        <td>{{ content.publish_time.strftime('%Y-%m-%d %H:%M') if content.publish_time else '无' }}</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ url_for('publish.detail', content_id=content.id) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> 详情
                                                </a>
                                                <button type="button" class="btn btn-sm btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <span class="visually-hidden">Toggle Dropdown</span>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <h6 class="dropdown-header">状态操作</h6>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="updateSingleStatus({{ content.id }}, 'unpublished')">
                                                            标记为未发布
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="updateSingleStatus({{ content.id }}, 'publishing')">
                                                            标记为发布中
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="updateSingleStatus({{ content.id }}, 'published')">
                                                            标记为已发布
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="updateSingleStatus({{ content.id }}, 'failed')">
                                                            标记为发布失败
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <div class="dropdown-divider"></div>
                                                    </li>
                                                    <li>
                                                        <h6 class="dropdown-header">优先级操作</h6>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="updateSinglePriority({{ content.id }}, 'high')">
                                                            设为高优先级
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="updateSinglePriority({{ content.id }}, 'normal')">
                                                            设为中优先级
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="updateSinglePriority({{ content.id }}, 'low')">
                                                            设为低优先级
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td colspan="9" class="text-center">没有找到符合条件的文案</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </form>

                    <!-- 分页 -->
                    {% if pagination and pagination.pages > 1 %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('publish.index', page=pagination.prev_num, **request.args) }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% for p in pagination.iter_pages() %}
                            {% if p %}
                            {% if p == pagination.page %}
                            <li class="page-item active">
                                <a class="page-link" href="{{ url_for('publish.index', page=p, **request.args) }}">{{ p }}</a>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('publish.index', page=p, **request.args) }}">{{ p }}</a>
                            </li>
                            {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#">&hellip;</a>
                            </li>
                            {% endif %}
                            {% endfor %}

                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('publish.index', page=pagination.next_num, **request.args) }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 单个文案状态更新表单 -->
<form id="single-update-form" method="post" style="display: none;">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    <input type="hidden" name="status" id="single-status">
    <input type="hidden" name="priority" id="single-priority">
</form>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 批量操作设置
    function setBatchAction(action, value) {
        document.getElementById('action').value = action;
        
        if (action === 'status') {
            document.getElementById('status').value = value;
        } else if (action === 'priority') {
            document.getElementById('priority').value = value;
        }
        
        // 检查是否有选中的文案
        let checkboxes = document.querySelectorAll('.content-checkbox:checked');
        if (checkboxes.length === 0) {
            alert('请至少选择一篇文案');
            return;
        }
        
        // 提交表单
        document.getElementById('batch-form').submit();
    }
    
    // 全选
    function selectAll() {
        let checkboxes = document.querySelectorAll('.content-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
    }
    
    // 取消全选
    function deselectAll() {
        let checkboxes = document.querySelectorAll('.content-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
    }
    
    // 更新单个文案状态
    function updateSingleStatus(contentId, status) {
        let form = document.getElementById('single-update-form');
        form.action = `/publish/update/${contentId}`;
        document.getElementById('single-status').value = status;
        document.getElementById('single-priority').value = '';
        form.submit();
    }
    
    // 更新单个文案优先级
    function updateSinglePriority(contentId, priority) {
        let form = document.getElementById('single-update-form');
        form.action = `/publish/update/${contentId}`;
        document.getElementById('single-status').value = '';
        document.getElementById('single-priority').value = priority;
        form.submit();
    }
</script>
{% endblock %} 