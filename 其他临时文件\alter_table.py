"""
修改users表的password_hash字段长度
"""
import pymysql

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'xhsrw666',
    'password': 'xhsrw666',
    'db': 'xhsrw666',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def alter_password_hash_field():
    """修改password_hash字段长度"""
    try:
        # 连接数据库
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 执行修改表结构的SQL
        sql = "ALTER TABLE users MODIFY COLUMN password_hash VARCHAR(255) NOT NULL;"
        cursor.execute(sql)
        
        # 提交更改
        conn.commit()
        print("成功修改password_hash字段长度为255")
        
        # 关闭连接
        cursor.close()
        conn.close()
    except Exception as e:
        print(f"修改失败: {e}")

if __name__ == "__main__":
    alter_password_hash_field() 