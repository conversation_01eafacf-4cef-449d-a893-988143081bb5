-- 小红书文案生成系统 - 权限设置SQL
-- MySQL 5.7版本
-- 执行前请确保已备份数据库

-- 1. 清空现有权限数据（保留admin_access）
DELETE FROM role_permissions WHERE permission_id != 2;
DELETE FROM permissions WHERE id != 2;

-- 2. 插入所有必要的权限
INSERT INTO permissions (id, name, code, description) VALUES
(3, '模板管理', 'template_manage', '允许管理文案模板'),
(4, '话题管理', 'topic_manage', '允许管理话题'),
(5, '客户查看', 'client_view', '允许查看客户信息'),
(6, '客户创建', 'client_create', '允许创建客户'),
(7, '客户编辑', 'client_edit', '允许编辑客户信息'),
(8, '客户删除', 'client_delete', '允许删除客户'),
(9, '任务查看', 'task_view', '允许查看任务'),
(10, '任务创建', 'task_create', '允许创建任务'),
(11, '任务编辑', 'task_edit', '允许编辑任务'),
(12, '任务删除', 'task_delete', '允许删除任务'),
(13, '文案查看', 'content_view', '允许查看文案'),
(14, '文案创建', 'content_create', '允许创建文案'),
(15, '文案编辑', 'content_edit', '允许编辑文案'),
(16, '文案删除', 'content_delete', '允许删除文案'),
(17, '文案审核', 'content_review', '允许审核文案'),
(18, '文案发布', 'content_publish', '允许发布文案'),
(19, '系统设置', 'system_settings', '允许修改系统设置'),
(20, '用户管理', 'user_manage', '允许管理用户'),
(21, '角色管理', 'role_manage', '允许管理角色'),
(22, '数据统计', 'data_statistics', '允许查看数据统计'),
(23, '批量操作', 'batch_operations', '允许执行批量操作'),
(24, '导出数据', 'export_data', '允许导出数据'),
(25, '导入数据', 'import_data', '允许导入数据');

-- 3. 创建更多角色（可选）
INSERT INTO roles (id, name, description, created_at) VALUES
(3, '内容编辑', '负责文案编辑和审核', NOW()),
(4, '客户经理', '负责客户管理和任务分配', NOW()),
(5, '运营专员', '负责文案发布和运营', NOW()),
(6, '普通用户', '基础功能访问权限', NOW());

-- 4. 为超级管理员分配所有权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(2, 3), (2, 4), (2, 5), (2, 6), (2, 7), (2, 8), (2, 9), (2, 10), (2, 11), (2, 12),
(2, 13), (2, 14), (2, 15), (2, 16), (2, 17), (2, 18), (2, 19), (2, 20), (2, 21), (2, 22),
(2, 23), (2, 24), (2, 25);

-- 5. 为内容编辑角色分配权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(3, 3), (3, 4), (3, 13), (3, 14), (3, 15), (3, 16), (3, 17), (3, 22), (3, 23);

-- 6. 为客户经理角色分配权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(4, 5), (4, 6), (4, 7), (4, 8), (4, 9), (4, 10), (4, 11), (4, 12), (4, 13), (4, 22), (4, 23), (4, 24);

-- 7. 为运营专员角色分配权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(5, 13), (5, 17), (5, 18), (5, 22), (5, 23), (5, 24);

-- 8. 为普通用户角色分配基础权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(6, 13), (6, 22);

-- 9. 添加一些默认的模板分类
INSERT INTO template_categories (id, name, parent_id, sort_order, created_at, updated_at) VALUES
(1, '美妆护肤', NULL, 1, NOW(), NOW()),
(2, '时尚穿搭', NULL, 2, NOW(), NOW()),
(3, '美食探店', NULL, 3, NOW(), NOW()),
(4, '旅游攻略', NULL, 4, NOW(), NOW()),
(5, '生活分享', NULL, 5, NOW(), NOW()),
(6, '护肤心得', 1, 1, NOW(), NOW()),
(7, '彩妆教程', 1, 2, NOW(), NOW()),
(8, '穿搭搭配', 2, 1, NOW(), NOW()),
(9, '潮流趋势', 2, 2, NOW(), NOW()),
(10, '餐厅推荐', 3, 1, NOW(), NOW()),
(11, '美食制作', 3, 2, NOW(), NOW());

-- 10. 添加一些默认话题
INSERT INTO topics (id, name, type, priority, created_at, updated_at) VALUES
(1, '#美妆分享', 'random', 1, NOW(), NOW()),
(2, '#护肤心得', 'random', 1, NOW(), NOW()),
(3, '#穿搭搭配', 'random', 1, NOW(), NOW()),
(4, '#美食探店', 'random', 1, NOW(), NOW()),
(5, '#旅游攻略', 'random', 1, NOW(), NOW()),
(6, '#生活分享', 'random', 1, NOW(), NOW()),
(7, '#好物推荐', 'random', 2, NOW(), NOW()),
(8, '#购物分享', 'random', 2, NOW(), NOW()),
(9, '#职场穿搭', 'random', 2, NOW(), NOW()),
(10, '#约会穿搭', 'random', 2, NOW(), NOW());

-- 11. 添加一些快捷理由
INSERT INTO quick_reasons (id, content, sort_order, created_at) VALUES
(1, '内容质量不符合要求', 1, NOW()),
(2, '标题不够吸引人', 2, NOW()),
(3, '图片质量需要提升', 3, NOW()),
(4, '文案长度需要调整', 4, NOW()),
(5, '话题标签需要优化', 5, NOW()),
(6, '发布时间需要调整', 6, NOW()),
(7, '内容重复度过高', 7, NOW()),
(8, '品牌露出过多', 8, NOW()),
(9, '文案风格需要调整', 9, NOW()),
(10, '其他原因', 10, NOW());

-- 12. 添加一些系统设置
INSERT INTO system_settings (id, `key`, value, description, updated_at) VALUES
(1, 'default_content_count', '5', '默认每日文案数量', NOW()),
(2, 'review_timeout_hours', '24', '审核超时时间（小时）', NOW()),
(3, 'auto_publish_enabled', 'false', '是否启用自动发布', NOW()),
(4, 'client_share_enabled', 'true', '是否启用客户分享功能', NOW()),
(5, 'notification_enabled', 'true', '是否启用通知功能', NOW()),
(6, 'content_backup_enabled', 'true', '是否启用文案备份', NOW()),
(7, 'max_upload_size', '10485760', '最大上传文件大小（字节）', NOW()),
(8, 'allowed_image_types', 'jpg,jpeg,png,gif', '允许的图片类型', NOW()),
(9, 'default_publish_interval_min', '30', '默认发布间隔最小值（分钟）', NOW()),
(10, 'default_publish_interval_max', '120', '默认发布间隔最大值（分钟）', NOW());

-- 13. 更新权限表的自增ID
ALTER TABLE permissions AUTO_INCREMENT = 26;

-- 14. 更新角色表的自增ID
ALTER TABLE roles AUTO_INCREMENT = 7;

-- 15. 更新模板分类表的自增ID
ALTER TABLE template_categories AUTO_INCREMENT = 12;

-- 16. 更新话题表的自增ID
ALTER TABLE topics AUTO_INCREMENT = 11;

-- 17. 更新快捷理由表的自增ID
ALTER TABLE quick_reasons AUTO_INCREMENT = 11;

-- 18. 更新系统设置表的自增ID
ALTER TABLE system_settings AUTO_INCREMENT = 11;

-- 执行完成提示
SELECT '权限设置完成！' AS message;
SELECT '超级管理员已拥有所有权限' AS admin_info;
SELECT '已创建5个角色：超级管理员、内容编辑、客户经理、运营专员、普通用户' AS roles_info;
SELECT '已添加默认的模板分类、话题、快捷理由和系统设置' AS defaults_info; 