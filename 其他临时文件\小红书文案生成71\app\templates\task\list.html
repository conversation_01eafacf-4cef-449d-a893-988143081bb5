{% extends "base.html" %}

{% block title %}任务管理{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">任务管理</h2>
        {% if current_user.has_permission('task_create') %}
        <a href="{{ url_for('task.task_create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 创建任务
        </a>
        {% endif %}
    </div>

    <div class="card mb-4">
        <div class="card-header bg-light">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <div class="form-group">
                        {{ form.client_id.label(class="form-label") }}
                        {{ form.client_id(class="form-select") }}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        {{ form.status.label(class="form-label") }}
                        {{ form.status(class="form-select") }}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        {{ form.search.label(class="form-label") }}
                        {{ form.search(class="form-control", value=search, placeholder="搜索任务名称或描述") }}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        {{ form.date_from.label(class="form-label") }}
                        {{ form.date_from(class="form-control", type="date", value=date_from) }}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        {{ form.date_to.label(class="form-label") }}
                        {{ form.date_to(class="form-control", type="date", value=date_to) }}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        {{ form.sort_by.label(class="form-label") }}
                        {{ form.sort_by(class="form-select") }}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        {{ form.sort_order.label(class="form-label") }}
                        {{ form.sort_order(class="form-select") }}
                    </div>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> 筛选
                    </button>
                </div>
            </form>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40px">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th>ID</th>
                            <th>任务名称</th>
                            <th>客户</th>
                            <th>目标数量</th>
                            <th>实际数量</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for task in tasks %}
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input task-checkbox" type="checkbox" value="{{ task.id }}">
                                </div>
                            </td>
                            <td>{{ task.id }}</td>
                            <td>
                                <a href="{{ url_for('task.task_view', task_id=task.id) }}" class="text-decoration-none">
                                    {{ task.name }}
                                </a>
                            </td>
                            <td>{{ task.client.name }}</td>
                            <td>{{ task.target_count or '未设置' }}</td>
                            <td>{{ task.actual_count }}</td>
                            <td>
                                {% if task.status == 'processing' %}
                                <span class="badge bg-primary">进行中</span>
                                {% elif task.status == 'completed' %}
                                <span class="badge bg-success">已完成</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ task.status }}</span>
                                {% endif %}
                            </td>
                            <td>{{ task.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('task.task_view', task_id=task.id) }}" class="btn btn-outline-info" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    {% if current_user.has_permission('task_edit') %}
                                    <a href="{{ url_for('task.task_edit', task_id=task.id) }}" class="btn btn-outline-primary" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    
                                    {% if current_user.has_permission('task_delete') %}
                                    <button type="button" class="btn btn-outline-danger" onclick="confirmDelete({{ task.id }})" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="9" class="text-center py-4">暂无任务数据</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        {% if tasks %}
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="batch-actions">
                    <form id="batchActionForm" method="post" action="{{ url_for('task.task_batch_action') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" name="task_ids" id="selectedTaskIds">
                        <div class="input-group">
                            {{ batch_form.action(class="form-select", style="max-width: 200px;") }}
                            <button type="button" class="btn btn-outline-primary" onclick="confirmBatchAction()">
                                执行操作
                            </button>
                        </div>
                    </form>
                </div>
                
                {% if pagination.pages > 1 %}
                <nav>
                    <ul class="pagination justify-content-center mb-0">
                        {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('task.task_list', page=pagination.prev_num, client_id=client_id, status=status, date_from=date_from, date_to=date_to, search=search, sort_by=sort_by, sort_order=sort_order) }}">
                                <span>&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">&laquo;</span>
                        </li>
                        {% endif %}
                        
                        {% for page in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                            {% if page %}
                                {% if page == pagination.page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page }}</span>
                                </li>
                                {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('task.task_list', page=page, client_id=client_id, status=status, date_from=date_from, date_to=date_to, search=search, sort_by=sort_by, sort_order=sort_order) }}">{{ page }}</a>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('task.task_list', page=pagination.next_num, client_id=client_id, status=status, date_from=date_from, date_to=date_to, search=search, sort_by=sort_by, sort_order=sort_order) }}">
                                <span>&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">&raquo;</span>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个任务吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" action="">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 批量操作确认模态框 -->
<div class="modal fade" id="batchActionModal" tabindex="-1" aria-labelledby="batchActionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchActionModalLabel">确认批量操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="batchActionMessage">确定要执行此操作吗？</p>
                <div class="form-check mb-3">
                    {{ batch_form.confirm(class="form-check-input") }}
                    {{ batch_form.confirm.label(class="form-check-label") }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmBatchBtn">确认</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 全选/取消全选
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.task-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    });
    
    // 确认删除
    function confirmDelete(taskId) {
        document.getElementById('deleteForm').action = `/tasks/${taskId}/delete`;
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }
    
    // 确认批量操作
    function confirmBatchAction() {
        const checkboxes = document.querySelectorAll('.task-checkbox:checked');
        if (checkboxes.length === 0) {
            alert('请至少选择一个任务');
            return;
        }
        
        const taskIds = Array.from(checkboxes).map(cb => cb.value).join(',');
        document.getElementById('selectedTaskIds').value = taskIds;
        
        const action = document.getElementById('action').value;
        let actionText = '';
        switch (action) {
            case 'complete':
                actionText = '标记完成';
                break;
            case 'delete':
                actionText = '批量删除';
                break;
            default:
                actionText = '执行操作';
                break;
        }
        
        document.getElementById('batchActionMessage').textContent = `确定要${actionText}选中的 ${checkboxes.length} 个任务吗？此操作可能无法撤销。`;
        
        const modal = new bootstrap.Modal(document.getElementById('batchActionModal'));
        modal.show();
        
        document.getElementById('confirmBatchBtn').onclick = function() {
            if (document.getElementById('confirm').checked) {
                document.getElementById('batchActionForm').submit();
            } else {
                alert('请确认操作');
            }
        };
    }
</script>
{% endblock %} 