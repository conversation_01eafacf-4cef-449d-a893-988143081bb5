"""
客户管理视图
"""
from datetime import datetime, timedelta
import uuid
import secrets
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort
from flask_login import login_required, current_user
from sqlalchemy import desc, func

from app.models import db
from app.models.client import Client, ClientShare
from app.models.content import Content
from app.models.user import Permission
from app.forms.client import ClientForm, ClientShareForm
from app.utils.decorators import permission_required, ajax_aware

# 创建蓝图
client_bp = Blueprint('client', __name__)


@client_bp.route('/')
@login_required
@permission_required('client_manage')
@ajax_aware
def client_list():
    """客户列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    # 限制每页显示数量的范围
    if per_page not in [10, 20, 30, 50, 80, 100]:
        per_page = 20

    query = Client.query
    
    # 搜索条件
    search = request.args.get('search', '')
    if search:
        query = query.filter(
            (Client.name.like(f'%{search}%')) |
            (Client.contact.like(f'%{search}%')) |
            (Client.phone.like(f'%{search}%')) |
            (Client.email.like(f'%{search}%'))
        )
    
    # 状态过滤
    status = request.args.get('status')
    if status:
        query = query.filter(Client.status == (status == 'active'))
    
    # 排序
    sort_by = request.args.get('sort_by', 'created_at')
    sort_order = request.args.get('sort_order', 'desc')
    
    if sort_order == 'desc':
        query = query.order_by(desc(getattr(Client, sort_by)))
    else:
        query = query.order_by(getattr(Client, sort_by))
    
    pagination = query.paginate(page=page, per_page=per_page)
    clients = pagination.items
    
    # 检查是否是AJAX请求
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # 返回部分HTML，只包含表格内容和分页
        html = render_template(
            'client/list.html',
            clients=clients,
            pagination=pagination,
            search=search,
            status=status,
            sort_by=sort_by,
            sort_order=sort_order,
            partial=True  # 标记这是部分渲染
        )
        return html
    
    # 常规请求返回完整页面
    return render_template(
        'client/list.html',
        clients=clients,
        pagination=pagination,
        per_page=per_page,
        search=search,
        status=status,
        sort_by=sort_by,
        sort_order=sort_order
    )


@client_bp.route('/list-json')
@login_required
def client_list_json():
    """获取客户列表（JSON API）"""
    clients = Client.query.filter_by(status=True).all()
    result = []
    
    for client in clients:
        result.append({
            'id': client.id,
            'name': client.name,
            'contact': client.contact,
            'need_review': client.need_review
        })
    
    return jsonify(result)


@client_bp.route('/create', methods=['GET', 'POST'])
@login_required
def client_create():
    """创建客户"""
    form = ClientForm()

    if request.method == 'GET':
        # GET请求，返回表单
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            # AJAX请求，返回模态框表单
            return render_template('client/client_form_modal.html', form=form)
        else:
            # 普通请求，返回完整页面
            return render_template('client/create.html', form=form)

    if form.validate_on_submit():
        try:
            client = Client(
                name=form.name.data,
                contact=form.contact.data,
                phone=form.phone.data,
                email=form.email.data,
                need_review=form.need_review.data,
                daily_content_count=form.daily_content_count.data,
                display_start_time=form.display_start_time.data,
                interval_min=form.interval_min.data,
                interval_max=form.interval_max.data,
                status=form.status.data
            )

            # 处理扩展字段
            ext_data = {}
            if form.address.data:
                ext_data['address'] = form.address.data
            if form.remark.data:
                ext_data['remark'] = form.remark.data

            if ext_data:
                client.ext_data = ext_data

            db.session.add(client)
            db.session.commit()
            flash('客户创建成功', 'success')

            # 检查是否是AJAX请求
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'success': True,
                    'message': '客户创建成功',
                    'client_id': client.id,
                    'redirect': url_for('client.client_list')
                })

            return redirect(url_for('client.client_list'))

        except Exception as e:
            db.session.rollback()
            error_msg = f'创建失败：{str(e)}'

            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'success': False,
                    'message': error_msg
                }), 400
            else:
                flash(error_msg, 'danger')
    else:
        # 表单验证失败
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({
                'success': False,
                'message': '表单验证失败',
                'errors': form.errors
            }), 400
    
    return render_template('client/create.html', form=form)


@client_bp.route('/<int:client_id>/edit', methods=['GET', 'POST'])
@login_required
def client_edit(client_id):
    """编辑客户"""
    client = Client.query.get_or_404(client_id)
    form = ClientForm(obj=client)

    # 填充扩展字段
    ext_data = client.ext_data
    if ext_data:
        if 'address' in ext_data:
            form.address.data = ext_data.get('address')
        if 'remark' in ext_data:
            form.remark.data = ext_data.get('remark')

    if request.method == 'GET':
        # GET请求，返回表单
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            # AJAX请求，返回模态框表单
            return render_template('client/client_form_modal.html', form=form, client=client, edit_mode=True)
        else:
            # 普通请求，返回完整页面
            return render_template('client/edit.html', form=form, client=client)

    if form.validate_on_submit():
        client.name = form.name.data
        client.contact = form.contact.data
        client.phone = form.phone.data
        client.email = form.email.data
        client.need_review = form.need_review.data
        client.daily_content_count = form.daily_content_count.data
        client.display_start_time = form.display_start_time.data
        client.interval_min = form.interval_min.data
        client.interval_max = form.interval_max.data
        client.status = form.status.data
        
        # 处理扩展字段
        ext_data = client.ext_data or {}
        if form.address.data:
            ext_data['address'] = form.address.data
        elif 'address' in ext_data:
            del ext_data['address']
            
        if form.remark.data:
            ext_data['remark'] = form.remark.data
        elif 'remark' in ext_data:
            del ext_data['remark']
        
        client.ext_data = ext_data
        
        db.session.commit()
        flash('客户信息更新成功', 'success')
        
        # 检查是否是AJAX请求
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({
                'success': True,
                'message': '客户信息更新成功',
                'redirect': url_for('client.client_list')
            })
        
        return redirect(url_for('client.client_list'))
    
    # 表单验证失败
    if form.errors and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({
            'success': False,
            'message': '表单验证失败',
            'errors': form.errors
        }), 400
    
    return render_template('client/edit.html', form=form, client=client)


@client_bp.route('/<int:client_id>/toggle_status', methods=['POST'])
@login_required
def toggle_status(client_id):
    """切换客户状态"""
    try:
        print(f"DEBUG - 切换客户状态: client_id={client_id}")
        print(f"DEBUG - 当前用户: {current_user.username}")

        client = Client.query.get_or_404(client_id)
        print(f"DEBUG - 找到客户: {client.name}, 当前状态: {client.status}")

        client.status = not client.status
        db.session.commit()

        status_text = '启用' if client.status else '禁用'
        print(f"DEBUG - 状态切换成功: {status_text}")

        return jsonify({
            'success': True,
            'status': client.status,
            'message': f'客户已{status_text}'
        })
    except Exception as e:
        print(f"ERROR - 切换状态失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'切换状态失败: {str(e)}'
        }), 500


@client_bp.route('/<int:client_id>/toggle_review', methods=['POST'])
@login_required
def toggle_review(client_id):
    """切换客户审核状态"""
    try:
        print(f"DEBUG - 切换客户审核状态: client_id={client_id}")
        print(f"DEBUG - 当前用户: {current_user.username}")

        client = Client.query.get_or_404(client_id)
        print(f"DEBUG - 找到客户: {client.name}, 当前审核状态: {client.need_review}")

        client.need_review = not client.need_review
        db.session.commit()

        status_text = '需要审核' if client.need_review else '无需审核'
        print(f"DEBUG - 审核状态切换成功: {status_text}")

        return jsonify({
            'success': True,
            'need_review': client.need_review,
            'message': f'客户已设置为{status_text}'
        })
    except Exception as e:
        print(f"ERROR - 切换审核状态失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'切换审核状态失败: {str(e)}'
        }), 500


@client_bp.route('/<int:client_id>/data', methods=['GET'])
@login_required
def client_data(client_id):
    """获取单个客户数据"""
    try:
        client = Client.query.get_or_404(client_id)

        return jsonify({
            'id': client.id,
            'name': client.name,
            'contact': client.contact,
            'phone': client.phone,
            'email': client.email,
            'need_review': client.need_review,
            'status': client.status,
            'created_at': client.created_at.strftime('%Y-%m-%d %H:%M') if client.created_at else None
        })
    except Exception as e:
        print(f"ERROR - 获取客户数据失败: {str(e)}")
        return jsonify({
            'error': f'获取客户数据失败: {str(e)}'
        }), 500


@client_bp.route('/<int:client_id>/delete', methods=['POST'])
@login_required
def client_delete(client_id):
    """删除客户"""
    try:
        print(f"DEBUG - 删除客户请求: client_id={client_id}")
        print(f"DEBUG - 当前用户: {current_user.username}")

        client = Client.query.get_or_404(client_id)
        print(f"DEBUG - 找到客户: {client.name}")

        # 检查是否有关联的内容
        task_count = client.tasks.count() if hasattr(client, 'tasks') else 0
        content_count = client.contents.count() if hasattr(client, 'contents') else 0

        print(f"DEBUG - 关联检查: 任务数={task_count}, 内容数={content_count}")

        if task_count > 0 or content_count > 0:
            print(f"DEBUG - 删除被阻止: 存在关联数据")
            return jsonify({
                'success': False,
                'message': f'该客户有关联的任务({task_count})或文案({content_count})，无法删除'
            })

        # 删除客户分享链接
        share_count = ClientShare.query.filter_by(client_id=client_id).count()
        ClientShare.query.filter_by(client_id=client_id).delete()
        print(f"DEBUG - 删除了 {share_count} 个分享链接")

        # 删除客户
        client_name = client.name
        db.session.delete(client)
        db.session.commit()

        print(f"DEBUG - 客户删除成功: {client_name}")

        return jsonify({
            'success': True,
            'message': f'客户"{client_name}"已成功删除'
        })

    except Exception as e:
        print(f"ERROR - 删除客户失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        }), 500


@client_bp.route('/<int:client_id>/shares')
@login_required
@permission_required('client_manage')
def share_list(client_id):
    """客户分享链接列表"""
    client = Client.query.get_or_404(client_id)
    shares = ClientShare.query.filter_by(client_id=client_id).order_by(ClientShare.created_at.desc()).all()
    
    # 检查是否是AJAX请求
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return render_template(
            'client/shares.html',
            client=client,
            shares=shares,
            is_ajax=True
        )
    
    return render_template(
        'client/shares.html',
        client=client,
        shares=shares
    )


@client_bp.route('/<int:client_id>/shares/create', methods=['GET', 'POST'])
@login_required
def share_create(client_id):
    """创建客户分享链接"""
    client = Client.query.get_or_404(client_id)
    form = ClientShareForm()
    
    if form.validate_on_submit():
        # 生成访问令牌
        access_token = str(uuid.uuid4()).replace('-', '')
        
        # 处理密码
        password = None
        if form.has_password.data:
            password = secrets.token_hex(3)  # 生成6位随机密码
        
        # 处理过期时间
        expires_at = None
        if form.expires_days.data:
            expires_at = datetime.now() + timedelta(days=form.expires_days.data)
        
        share = ClientShare(
            client_id=client.id,
            access_token=access_token,
            password=password,
            expires_at=expires_at,
            view_permission=form.view_permission.data,
            edit_permission=form.edit_permission.data,
            review_permission=form.review_permission.data,
            created_by=current_user.id
        )
        
        db.session.add(share)
        db.session.commit()
        
        # 生成完整的分享链接，如果有密码则包含密码参数
        base_url = url_for('client.share_access', token=access_token, _external=True)
        full_url = base_url
        if password:
            full_url = f"{base_url}?key={password}"
        
        flash('分享链接创建成功', 'success')
        
        # 检查是否是AJAX请求
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({
                'success': True,
                'message': '分享链接创建成功',
                'redirect': url_for('client.share_list', client_id=client.id, new_share_id=share.id, 
                                   new_share_url=full_url, new_share_password=password)
            })
        
        # 返回到分享链接列表页面，并传递新创建的链接信息
        return redirect(url_for('client.share_list', client_id=client.id, new_share_id=share.id, 
                               new_share_url=full_url, new_share_password=password))
    
    return render_template('client/share_create.html', form=form, client=client)


@client_bp.route('/shares/<int:share_id>/delete', methods=['POST'])
@login_required
def share_delete(share_id):
    """删除分享链接"""
    share = ClientShare.query.get_or_404(share_id)
    
    # 记录客户ID用于重定向
    client_id = share.client_id
    
    # 删除分享链接
    db.session.delete(share)
    db.session.commit()
    
    # 返回JSON响应
    return jsonify({
        'success': True,
        'message': '分享链接已删除'
    })


@client_bp.route('/share/<string:token>')
def share_access(token):
    """访问分享链接"""
    share = ClientShare.query.filter_by(access_token=token).first_or_404()
    
    # 检查链接是否有效
    if not share.is_valid():
        abort(404)
    
    # 检查是否需要密码
    if share.password:
        # 检查URL中是否包含密码参数
        url_key = request.args.get('key')
        if not url_key or url_key != share.password:
            # 没有验证过，显示密码输入页面
            return render_template('client/share_password.html', share=share)
    
    client = Client.query.get_or_404(share.client_id)
    
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    # 构建查询
    query = Content.query.filter_by(client_id=client.id, is_deleted=False)
    
    # 应用筛选条件
    search = request.args.get('search', '')
    if search:
        query = query.filter(Content.content.like(f'%{search}%') | Content.title.like(f'%{search}%'))
    
    status = request.args.get('status')
    if status == 'pending':
        query = query.filter(Content.client_review_status == 'pending')
    elif status == 'approved':
        query = query.filter(Content.client_review_status == 'approved')
    elif status == 'rejected':
        query = query.filter(Content.client_review_status == 'rejected')
    
    # 日期筛选
    date_filter = request.args.get('date')
    if date_filter == 'today':
        today = datetime.now().date()
        query = query.filter(func.date(Content.display_date) == today)
    elif date_filter == 'week':
        today = datetime.now().date()
        start_of_week = today - timedelta(days=today.weekday())
        query = query.filter(func.date(Content.display_date) >= start_of_week)
    elif date_filter == 'month':
        today = datetime.now().date()
        start_of_month = today.replace(day=1)
        query = query.filter(func.date(Content.display_date) >= start_of_month)
    
    # 排序
    query = query.order_by(Content.display_date.asc(), Content.id.asc())
    
    # 分页
    pagination = query.paginate(page=page, per_page=per_page)
    contents = pagination.items
    
    # 获取统计信息
    stats = {
        'total': Content.query.filter_by(client_id=client.id, is_deleted=False).count(),
        'pending': Content.query.filter_by(client_id=client.id, is_deleted=False, client_review_status='pending').count(),
        'approved': Content.query.filter_by(client_id=client.id, is_deleted=False, client_review_status='approved').count(),
        'rejected': Content.query.filter_by(client_id=client.id, is_deleted=False, client_review_status='rejected').count(),
    }
    
    # 根据权限展示不同的页面
    if share.review_permission:
        # 展示审核界面
        return render_template('client/share_review.html', client=client, share=share, 
                              contents=contents, pagination=pagination, stats=stats)
    elif share.edit_permission:
        # 展示编辑界面
        return render_template('client/share_edit.html', client=client, share=share,
                              contents=contents, pagination=pagination, stats=stats)
    else:
        # 展示查看界面
        return render_template('client/share_view.html', client=client, share=share,
                              contents=contents, pagination=pagination, stats=stats)


@client_bp.route('/share/<string:token>/verify', methods=['POST'])
def share_verify(token):
    """验证分享链接密码"""
    share = ClientShare.query.filter_by(access_token=token).first_or_404()
    
    # 检查链接是否有效
    if not share.is_valid():
        abort(404)
    
    password = request.form.get('password')
    if not password or password != share.password:
        flash('密码错误', 'danger')
        return redirect(url_for('client.share_access', token=token))
    
    return redirect(url_for('client.share_access', token=token, verified=1))


@client_bp.route('/<int:client_id>/usage')
@login_required
@permission_required('client_manage')
def client_usage(client_id):
    """客户使用记录"""
    client = Client.query.get_or_404(client_id)
    
    # 获取使用记录
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # 获取文案数量
    content_count = Content.query.filter_by(client_id=client_id, is_deleted=False).count()
    
    # 获取文案状态统计
    content_stats = {
        'pending': Content.query.filter_by(client_id=client_id, is_deleted=False, client_review_status='pending').count(),
        'approved': Content.query.filter_by(client_id=client_id, is_deleted=False, client_review_status='approved').count(),
        'rejected': Content.query.filter_by(client_id=client_id, is_deleted=False, client_review_status='rejected').count(),
    }
    
    # 获取最近的文案
    recent_contents = Content.query.filter_by(client_id=client_id, is_deleted=False).order_by(Content.created_at.desc()).limit(10).all()
    
    # 获取分享链接
    shares = ClientShare.query.filter_by(client_id=client_id).order_by(ClientShare.created_at.desc()).all()
    
    # 检查是否是AJAX请求
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return render_template(
            'client/usage.html',
            client=client,
            content_count=content_count,
            content_stats=content_stats,
            recent_contents=recent_contents,
            shares=shares,
            is_ajax=True
        )
    
    return render_template(
        'client/usage.html',
        client=client,
        content_count=content_count,
        content_stats=content_stats,
        recent_contents=recent_contents,
        shares=shares
    )


@client_bp.route('/share/<string:token>/content/<int:content_id>')
def share_content_view(token, content_id):
    """查看分享链接中的文案内容"""
    share = ClientShare.query.filter_by(access_token=token).first_or_404()
    
    # 检查链接是否有效
    if not share.is_valid():
        abort(404)
    
    # 检查是否需要密码
    if share.password:
        # 检查URL中是否包含密码参数
        url_key = request.args.get('key')
        if not url_key or url_key != share.password:
            # 没有验证过，显示密码输入页面
            return render_template('client/share_password.html', share=share)
    
    client = Client.query.get_or_404(share.client_id)
    content = Content.query.filter_by(id=content_id, client_id=client.id).first_or_404()
    
    return render_template('client/share_content_view.html', client=client, share=share, content=content) 