"""
数据模型测试
"""
import pytest
from datetime import datetime, timedelta

from app import db
from app.models.user import User, Role, Permission, UserRole
from app.models.client import Client
from app.models.template import Template, TemplateCategory
from app.models.topic import Topic, TopicRelation
from app.models.task import Task, Batch
from app.models.content import Content
from app.models.reason import RejectionReason


def test_user_model(app):
    """测试用户模型"""
    with app.app_context():
        # 创建测试用户
        user = User(
            username='testuser',
            email='<EMAIL>',
            password='password123'
        )
        db.session.add(user)
        db.session.commit()
        
        # 检查用户是否成功创建
        assert user.id is not None
        assert user.username == 'testuser'
        assert user.email == '<EMAIL>'
        assert user.verify_password('password123')
        assert not user.verify_password('wrongpassword')
        
        # 检查默认值
        assert user.is_active == True
        assert user.created_at is not None


def test_role_permission_model(app):
    """测试角色和权限模型"""
    with app.app_context():
        # 创建权限
        permission = Permission(name='test_permission', description='Test Permission')
        db.session.add(permission)
        db.session.commit()
        
        # 创建角色
        role = Role(name='test_role', description='Test Role')
        db.session.add(role)
        db.session.commit()
        
        # 分配权限给角色
        role.permissions.append(permission)
        db.session.commit()
        
        # 检查权限分配
        assert permission in role.permissions
        assert role in permission.roles


def test_client_model(app):
    """测试客户模型"""
    with app.app_context():
        # 创建测试客户
        client = Client(
            name='测试客户',
            contact='联系人',
            phone='13800138000',
            email='<EMAIL>',
            need_review=True,
            daily_content_count=5,
            interval_min=30,
            interval_max=120,
            status=True
        )
        db.session.add(client)
        db.session.commit()
        
        # 检查客户是否成功创建
        assert client.id is not None
        assert client.name == '测试客户'
        assert client.contact == '联系人'
        assert client.need_review == True
        assert client.daily_content_count == 5


def test_template_model(app):
    """测试模板模型"""
    with app.app_context():
        # 创建模板分类
        category = TemplateCategory(name='测试分类')
        db.session.add(category)
        db.session.commit()
        
        # 创建模板
        template = Template(
            title='测试模板',
            content='这是一个测试模板，包含{{关键词1}}和{{关键词2}}',
            category_id=category.id,
            status=True
        )
        db.session.add(template)
        db.session.commit()
        
        # 检查模板是否成功创建
        assert template.id is not None
        assert template.title == '测试模板'
        assert '{{关键词1}}' in template.content
        assert template.category_id == category.id
        assert template.status == True
        
        # 检查关联关系
        assert template.category == category


def test_topic_model(app):
    """测试话题模型"""
    with app.app_context():
        # 创建话题
        topic1 = Topic(name='话题1', type='required', priority=1)
        topic2 = Topic(name='话题2', type='random', priority=2)
        db.session.add(topic1)
        db.session.add(topic2)
        db.session.commit()
        
        # 创建话题关联
        relation = TopicRelation(
            topic_id=topic1.id,
            related_topic_id=topic2.id,
            weight=3
        )
        db.session.add(relation)
        db.session.commit()
        
        # 检查话题是否成功创建
        assert topic1.id is not None
        assert topic1.name == '话题1'
        assert topic1.type == 'required'
        assert topic1.priority == 1
        
        # 检查关联关系
        assert relation.topic_id == topic1.id
        assert relation.related_topic_id == topic2.id
        assert relation.weight == 3


def test_task_model(app):
    """测试任务模型"""
    with app.app_context():
        # 创建客户
        client = Client(name='测试客户')
        db.session.add(client)
        
        # 创建模板分类和模板
        category = TemplateCategory(name='测试分类')
        db.session.add(category)
        db.session.commit()
        
        template = Template(
            title='测试模板',
            content='测试内容',
            category_id=category.id
        )
        db.session.add(template)
        db.session.commit()
        
        # 创建任务
        task = Task(
            name='测试任务',
            description='这是一个测试任务',
            client_id=client.id,
            template_id=template.id,
            target_count=10,
            actual_count=0,
            status='pending'
        )
        db.session.add(task)
        db.session.commit()
        
        # 创建批次
        batch = Batch(
            task_id=task.id,
            name='批次1',
            count=5,
            status='pending'
        )
        db.session.add(batch)
        db.session.commit()
        
        # 检查任务是否成功创建
        assert task.id is not None
        assert task.name == '测试任务'
        assert task.client_id == client.id
        assert task.template_id == template.id
        assert task.target_count == 10
        assert task.status == 'pending'
        
        # 检查批次是否成功创建
        assert batch.id is not None
        assert batch.task_id == task.id
        assert batch.name == '批次1'
        assert batch.count == 5
        assert batch.status == 'pending'
        
        # 检查关联关系
        assert batch.task == task
        assert batch in task.batches


def test_content_model(app):
    """测试文案模型"""
    with app.app_context():
        # 创建客户
        client = Client(name='测试客户')
        db.session.add(client)
        
        # 创建任务
        task = Task(
            name='测试任务',
            description='这是一个测试任务',
            client_id=client.id,
            target_count=10,
            actual_count=0,
            status='pending'
        )
        db.session.add(task)
        db.session.commit()
        
        # 创建文案
        content = Content(
            title='测试文案',
            content='这是一个测试文案内容',
            topics='话题1,话题2',
            client_id=client.id,
            task_id=task.id,
            workflow_status='draft'
        )
        db.session.add(content)
        db.session.commit()
        
        # 检查文案是否成功创建
        assert content.id is not None
        assert content.title == '测试文案'
        assert content.content == '这是一个测试文案内容'
        assert content.client_id == client.id
        assert content.task_id == task.id
        assert content.workflow_status == 'draft'
        
        # 检查关联关系
        assert content.client == client
        assert content.task == task
        
        # 检查话题列表转换
        assert content.topics_list == ['话题1', '话题2']


def test_rejection_reason_model(app):
    """测试拒绝理由模型"""
    with app.app_context():
        # 创建拒绝理由
        reason = RejectionReason(
            content='格式不符合要求',
            type='first_review',
            is_quick_reason=True
        )
        db.session.add(reason)
        db.session.commit()
        
        # 检查拒绝理由是否成功创建
        assert reason.id is not None
        assert reason.content == '格式不符合要求'
        assert reason.type == 'first_review'
        assert reason.is_quick_reason == True
        assert reason.created_at is not None 