# -*- coding: utf-8 -*-
"""
主页视图
"""

from datetime import datetime
from flask import Blueprint, render_template, redirect, url_for
from flask_login import login_required, current_user

from app.utils.decorators import ajax_aware

# 创建主页蓝图
main_bp = Blueprint('main', __name__)


@main_bp.route('/')
@ajax_aware
def index():
    """首页"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    return redirect(url_for('auth.login'))


@main_bp.route('/dashboard')
@login_required
@ajax_aware
def dashboard():
    """仪表盘"""
    return render_template('main/dashboard.html')


@main_bp.route('/test-ajax')
@login_required
@ajax_aware
def test_ajax():
    """AJAX测试页面"""
    context = {
        'title': 'AJAX测试页面 - 小红书文案生成系统',
        'now': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    return 'test_ajax.html', context


@main_bp.route('/test-fixed')
@login_required
@ajax_aware
def test_fixed():
    """AJAX修复测试页面"""
    context = {
        'title': 'AJAX修复测试页面 - 小红书文案生成系统',
        'now': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    return 'main/test_ajax_fixed.html', context


@main_bp.route('/clients')
@login_required
def clients_redirect():
    """客户管理重定向"""
    return redirect(url_for('client.client_list'))


@main_bp.route('/tasks')
@login_required
def tasks_redirect():
    """任务管理重定向"""
    return redirect(url_for('task.task_list')) 