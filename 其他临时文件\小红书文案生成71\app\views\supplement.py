# -*- coding: utf-8 -*-
"""
文案补充机制视图
"""
from datetime import datetime
from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from sqlalchemy import func, desc, or_

from app import db
from app.models import Content, Task, Batch, Client, SystemSetting
from app.utils.permission import permission_required
from app.forms.supplement import SupplementForm, SupplementSettingForm

# 创建蓝图
supplement_bp = Blueprint('supplement', __name__)

@supplement_bp.route('/')
@login_required
@permission_required('content_manage')
def index():
    """文案补充机制首页"""
    # 获取待补充的文案任务列表
    tasks = Task.query.filter(Task.status == 'in_progress').order_by(desc(Task.created_at)).all()
    
    # 获取补充设置
    supplement_setting = SystemSetting.query.filter_by(key='SUPPLEMENT_SOURCE').first()
    supplement_source = supplement_setting.value if supplement_setting else 'unassigned'  # 默认从未分配文案池补充
    
    return render_template(
        'supplement/index.html',
        tasks=tasks,
        supplement_source=supplement_source
    )

@supplement_bp.route('/settings', methods=['GET', 'POST'])
@login_required
@permission_required('system_settings')
def settings():
    """补充设置页面"""
    form = SupplementSettingForm()
    
    # 获取当前设置
    supplement_source_setting = SystemSetting.query.filter_by(key='SUPPLEMENT_SOURCE').first()
    supplement_source = supplement_source_setting.value if supplement_source_setting else 'unassigned'
    
    if request.method == 'GET':
        form.source.data = supplement_source
    
    if form.validate_on_submit():
        # 更新补充来源设置
        if supplement_source_setting:
            supplement_source_setting.value = form.source.data
            supplement_source_setting.updated_at = datetime.now()
            supplement_source_setting.updated_by = current_user.id
        else:
            new_setting = SystemSetting(
                key='SUPPLEMENT_SOURCE',
                value=form.source.data,
                description='文案补充来源',
                updated_at=datetime.now(),
                updated_by=current_user.id
            )
            db.session.add(new_setting)
        
        db.session.commit()
        flash('补充设置已更新', 'success')
        return redirect(url_for('supplement.index'))
    
    return render_template('supplement/settings.html', form=form)

@supplement_bp.route('/task/<int:task_id>')
@login_required
@permission_required('content_manage')
def task_detail(task_id):
    """任务补充详情页面"""
    task = Task.query.get_or_404(task_id)
    
    # 获取已删除的文案数量
    deleted_count = Content.query.filter(
        Content.task_id == task_id,
        Content.is_deleted == True
    ).count()
    
    # 获取可用于补充的文案数量
    supplement_setting = SystemSetting.query.filter_by(key='SUPPLEMENT_SOURCE').first()
    supplement_source = supplement_setting.value if supplement_setting else 'unassigned'
    
    if supplement_source == 'unassigned':
        # 从未分配文案池补充
        available_count = Content.query.filter(
            Content.task_id == None,
            Content.is_deleted == False
        ).count()
    else:
        # 新生成文案
        available_count = 0  # 需要生成新文案时，可用数量为0
    
    # 获取批次列表
    batches = Batch.query.filter_by(task_id=task_id).order_by(Batch.created_at).all()
    
    # 每个批次的删除文案数量
    batch_deleted_counts = {}
    for batch in batches:
        count = Content.query.filter(
            Content.batch_id == batch.id,
            Content.is_deleted == True
        ).count()
        batch_deleted_counts[batch.id] = count
    
    return render_template(
        'supplement/task_detail.html',
        task=task,
        deleted_count=deleted_count,
        available_count=available_count,
        batches=batches,
        batch_deleted_counts=batch_deleted_counts,
        supplement_source=supplement_source
    )

@supplement_bp.route('/supplement/<int:task_id>', methods=['GET', 'POST'])
@login_required
@permission_required('content_manage')
def supplement_task(task_id):
    """补充任务文案"""
    task = Task.query.get_or_404(task_id)
    form = SupplementForm()
    
    # 获取批次列表，用于表单选择
    batches = Batch.query.filter_by(task_id=task_id).order_by(Batch.created_at).all()
    form.batch_id.choices = [(batch.id, f"{batch.name} ({batch.content_count}篇)") for batch in batches]
    
    # 获取补充来源设置
    supplement_setting = SystemSetting.query.filter_by(key='SUPPLEMENT_SOURCE').first()
    supplement_source = supplement_setting.value if supplement_setting else 'unassigned'
    
    if form.validate_on_submit():
        batch_id = form.batch_id.data
        count = form.count.data
        
        # 检查选择的批次是否存在
        batch = Batch.query.get(batch_id)
        if not batch:
            flash('所选批次不存在', 'danger')
            return redirect(url_for('supplement.task_detail', task_id=task_id))
        
        # 获取该批次已删除的文案数量
        deleted_count = Content.query.filter(
            Content.batch_id == batch_id,
            Content.is_deleted == True
        ).count()
        
        # 如果补充数量大于已删除数量，提示错误
        if count > deleted_count:
            flash(f'补充数量不能大于已删除数量 ({deleted_count})', 'danger')
            return redirect(url_for('supplement.task_detail', task_id=task_id))
        
        # 根据补充来源执行不同的补充逻辑
        if supplement_source == 'unassigned':
            # 从未分配文案池补充
            available_contents = Content.query.filter(
                Content.task_id == None,
                Content.is_deleted == False
            ).limit(count).all()
            
            if len(available_contents) < count:
                flash(f'未分配文案池中只有 {len(available_contents)} 篇可用文案，无法补充 {count} 篇', 'warning')
                return redirect(url_for('supplement.task_detail', task_id=task_id))
            
            # 更新文案的任务和批次信息
            for content in available_contents:
                content.task_id = task_id
                content.batch_id = batch_id
                content.client_id = task.client_id
            
            db.session.commit()
            flash(f'成功从未分配文案池补充了 {len(available_contents)} 篇文案', 'success')
        else:
            # 新生成文案的逻辑需要调用文案生成功能
            flash('新生成文案功能尚未实现，请从未分配文案池补充', 'warning')
            return redirect(url_for('supplement.task_detail', task_id=task_id))
        
        return redirect(url_for('supplement.task_detail', task_id=task_id))
    
    return render_template(
        'supplement/supplement_form.html',
        form=form,
        task=task,
        supplement_source=supplement_source
    )

@supplement_bp.route('/batch_supplement', methods=['POST'])
@login_required
@permission_required('content_manage')
def batch_supplement():
    """批量补充文案"""
    task_id = request.form.get('task_id', type=int)
    batch_id = request.form.get('batch_id', type=int)
    count = request.form.get('count', type=int)
    
    if not task_id or not batch_id or not count:
        return jsonify({'success': False, 'message': '参数错误'})
    
    task = Task.query.get_or_404(task_id)
    batch = Batch.query.get_or_404(batch_id)
    
    # 获取该批次已删除的文案数量
    deleted_count = Content.query.filter(
        Content.batch_id == batch_id,
        Content.is_deleted == True
    ).count()
    
    # 如果补充数量大于已删除数量，返回错误
    if count > deleted_count:
        return jsonify({'success': False, 'message': f'补充数量不能大于已删除数量 ({deleted_count})'})
    
    # 获取补充来源设置
    supplement_setting = SystemSetting.query.filter_by(key='SUPPLEMENT_SOURCE').first()
    supplement_source = supplement_setting.value if supplement_setting else 'unassigned'
    
    # 根据补充来源执行不同的补充逻辑
    if supplement_source == 'unassigned':
        # 从未分配文案池补充
        available_contents = Content.query.filter(
            Content.task_id == None,
            Content.is_deleted == False
        ).limit(count).all()
        
        if len(available_contents) < count:
            return jsonify({
                'success': False, 
                'message': f'未分配文案池中只有 {len(available_contents)} 篇可用文案，无法补充 {count} 篇'
            })
        
        # 更新文案的任务和批次信息
        for content in available_contents:
            content.task_id = task_id
            content.batch_id = batch_id
            content.client_id = task.client_id
        
        db.session.commit()
        return jsonify({
            'success': True, 
            'message': f'成功从未分配文案池补充了 {len(available_contents)} 篇文案'
        })
    else:
        # 新生成文案的逻辑需要调用文案生成功能
        return jsonify({
            'success': False, 
            'message': '新生成文案功能尚未实现，请从未分配文案池补充'
        })

@supplement_bp.route('/auto_supplement/<int:content_id>', methods=['POST'])
@login_required
@permission_required('content_manage')
def auto_supplement(content_id):
    """文案删除后自动补充"""
    content = Content.query.get_or_404(content_id)
    task_id = content.task_id
    batch_id = content.batch_id
    client_id = content.client_id
    
    if not task_id or not batch_id:
        return jsonify({'success': False, 'message': '该文案没有关联任务或批次'})
    
    # 获取补充来源设置
    supplement_setting = SystemSetting.query.filter_by(key='SUPPLEMENT_SOURCE').first()
    supplement_source = supplement_setting.value if supplement_setting else 'unassigned'
    
    # 根据补充来源执行不同的补充逻辑
    if supplement_source == 'unassigned':
        # 从未分配文案池补充一篇
        new_content = Content.query.filter(
            Content.task_id == None,
            Content.is_deleted == False
        ).first()
        
        if not new_content:
            return jsonify({'success': False, 'message': '未分配文案池中没有可用文案'})
        
        # 更新文案的任务和批次信息
        new_content.task_id = task_id
        new_content.batch_id = batch_id
        new_content.client_id = client_id
        
        db.session.commit()
        return jsonify({
            'success': True, 
            'message': f'成功补充了1篇文案（ID: {new_content.id}）',
            'content_id': new_content.id
        })
    else:
        # 新生成文案的逻辑需要调用文案生成功能
        return jsonify({
            'success': False, 
            'message': '新生成文案功能尚未实现，请从未分配文案池补充'
        })

@supplement_bp.route('/delete_and_supplement/<int:content_id>', methods=['POST'])
@login_required
@permission_required('content_manage')
def delete_and_supplement(content_id):
    """删除并补充文案"""
    content = Content.query.get_or_404(content_id)
    task_id = content.task_id
    batch_id = content.batch_id
    client_id = content.client_id
    
    # 标记文案为已删除
    content.is_deleted = True
    content.deleted_at = datetime.now()
    content.deleted_by = current_user.id
    
    db.session.commit()
    
    # 根据请求参数决定是否自动补充
    auto_supplement_flag = request.form.get('auto_supplement', 'false')
    
    if auto_supplement_flag == 'true' and task_id and batch_id:
        # 获取补充来源设置
        supplement_setting = SystemSetting.query.filter_by(key='SUPPLEMENT_SOURCE').first()
        supplement_source = supplement_setting.value if supplement_setting else 'unassigned'
        
        # 根据补充来源执行不同的补充逻辑
        if supplement_source == 'unassigned':
            # 从未分配文案池补充一篇
            new_content = Content.query.filter(
                Content.task_id == None,
                Content.is_deleted == False
            ).first()
            
            if new_content:
                # 更新文案的任务和批次信息
                new_content.task_id = task_id
                new_content.batch_id = batch_id
                new_content.client_id = client_id
                
                db.session.commit()
                return jsonify({
                    'success': True, 
                    'message': f'已删除文案并成功补充了1篇新文案（ID: {new_content.id}）',
                    'content_id': new_content.id
                })
            else:
                return jsonify({
                    'success': True, 
                    'message': '已删除文案，但未分配文案池中没有可用文案进行补充'
                })
        else:
            # 新生成文案的逻辑需要调用文案生成功能
            return jsonify({
                'success': True, 
                'message': '已删除文案，但新生成文案功能尚未实现，无法自动补充'
            })
    
    return jsonify({
        'success': True, 
        'message': '已成功删除文案'
    }) 