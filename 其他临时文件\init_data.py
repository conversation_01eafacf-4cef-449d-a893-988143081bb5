#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
初始化基础数据脚本
"""

from flask import Flask
from app import db
from app.models.user import User, Role, Permission, UserRole
from app.models.template import TemplateCategory, TemplateMark
from app.models.topic import Topic
from app.models.content import QuickReason, RejectionReason
from datetime import datetime
from app.config import config

def create_minimal_app():
    """创建最小化应用实例，仅用于数据库操作"""
    app = Flask(__name__)
    app.config.from_object(config['default'])
    db.init_app(app)
    return app

def init_roles_and_permissions():
    """初始化角色和权限"""
    app = create_minimal_app()
    with app.app_context():
        # 检查是否已存在角色
        if Role.query.count() > 0:
            print('角色数据已存在，跳过初始化')
            return
        
        # 创建权限
        permissions = [
            Permission(name='admin_access', description='管理员访问权限'),
            Permission(name='content_create', description='创建文案权限'),
            Permission(name='content_review', description='审核文案权限'),
            Permission(name='content_manage', description='管理文案权限'),
            Permission(name='template_manage', description='管理模板权限'),
            Permission(name='topic_manage', description='管理话题权限'),
            Permission(name='client_manage', description='管理客户权限'),
            Permission(name='client_view', description='查看客户权限'),
            Permission(name='task_manage', description='管理任务权限'),
            Permission(name='content_publish', description='发布文案权限'),
            Permission(name='content_export', description='导出文案权限'),
            Permission(name='content_import', description='导入文案权限'),
            Permission(name='stats_view', description='查看统计数据权限'),
            Permission(name='system_settings', description='管理系统设置权限'),
            Permission(name='notification_manage', description='管理通知权限'),
            Permission(name='display_manage', description='管理展示计划权限')
        ]
        db.session.add_all(permissions)
        db.session.commit()
        
        # 创建角色
        roles = [
            Role(name='超级管理员', description='拥有所有权限'),
            Role(name='内容编辑', description='负责文案编辑和审核'),
            Role(name='客户经理', description='负责客户管理和任务分配'),
            Role(name='运营专员', description='负责文案发布和运营'),
            Role(name='普通用户', description='基础功能访问权限')
        ]
        db.session.add_all(roles)
        db.session.commit()
        
        # 为超级管理员角色分配所有权限
        admin_role = Role.query.filter_by(name='超级管理员').first()
        admin_role.permissions = Permission.query.all()
        db.session.commit()
        
        # 为管理员用户分配超级管理员角色
        admin_user = User.query.filter_by(username='admin').first()
        if admin_user:
            admin_user.roles = [admin_role]
            db.session.commit()
        
        print('角色和权限初始化成功')

def init_template_categories():
    """初始化模板分类"""
    app = create_minimal_app()
    with app.app_context():
        # 检查是否已存在模板分类
        if TemplateCategory.query.count() > 0:
            print('模板分类数据已存在，跳过初始化')
            return
        
        # 创建一级分类
        categories = [
            TemplateCategory(name='美妆护肤', sort_order=1),
            TemplateCategory(name='时尚穿搭', sort_order=2),
            TemplateCategory(name='美食探店', sort_order=3),
            TemplateCategory(name='旅游攻略', sort_order=4),
            TemplateCategory(name='生活分享', sort_order=5)
        ]
        db.session.add_all(categories)
        db.session.commit()
        
        # 创建二级分类
        sub_categories = [
            TemplateCategory(name='护肤心得', parent_id=1, sort_order=1),
            TemplateCategory(name='彩妆教程', parent_id=1, sort_order=2),
            TemplateCategory(name='穿搭搭配', parent_id=2, sort_order=1),
            TemplateCategory(name='潮流趋势', parent_id=2, sort_order=2),
            TemplateCategory(name='餐厅推荐', parent_id=3, sort_order=1),
            TemplateCategory(name='美食制作', parent_id=3, sort_order=2)
        ]
        db.session.add_all(sub_categories)
        db.session.commit()
        
        print('模板分类初始化成功')

def init_topics():
    """初始化话题"""
    app = create_minimal_app()
    with app.app_context():
        # 检查是否已存在话题
        if Topic.query.count() > 0:
            print('话题数据已存在，跳过初始化')
            return
        
        # 创建话题
        topics = [
            Topic(name='#美妆分享', type='random', priority=1),
            Topic(name='#护肤心得', type='random', priority=1),
            Topic(name='#穿搭搭配', type='random', priority=1),
            Topic(name='#美食探店', type='random', priority=1),
            Topic(name='#旅游攻略', type='random', priority=1),
            Topic(name='#生活分享', type='random', priority=1),
            Topic(name='#好物推荐', type='random', priority=2),
            Topic(name='#购物分享', type='random', priority=2),
            Topic(name='#职场穿搭', type='random', priority=2),
            Topic(name='#约会穿搭', type='random', priority=2)
        ]
        db.session.add_all(topics)
        db.session.commit()
        
        print('话题初始化成功')

def init_quick_reasons():
    """初始化快捷理由"""
    app = create_minimal_app()
    with app.app_context():
        # 检查是否已存在快捷理由
        if QuickReason.query.count() > 0:
            print('快捷理由数据已存在，跳过初始化')
            return
        
        # 创建快捷理由
        reasons = [
            QuickReason(content='内容质量不符合要求', sort_order=1),
            QuickReason(content='标题不够吸引人', sort_order=2),
            QuickReason(content='图片质量需要提升', sort_order=3),
            QuickReason(content='文案长度需要调整', sort_order=4),
            QuickReason(content='话题标签需要优化', sort_order=5),
            QuickReason(content='发布时间需要调整', sort_order=6),
            QuickReason(content='内容重复度过高', sort_order=7),
            QuickReason(content='品牌露出过多', sort_order=8),
            QuickReason(content='文案风格需要调整', sort_order=9),
            QuickReason(content='其他原因', sort_order=10)
        ]
        db.session.add_all(reasons)
        db.session.commit()
        
        print('快捷理由初始化成功')

if __name__ == '__main__':
    init_roles_and_permissions()
    init_template_categories()
    init_topics()
    init_quick_reasons()
    print('所有基础数据初始化完成') 