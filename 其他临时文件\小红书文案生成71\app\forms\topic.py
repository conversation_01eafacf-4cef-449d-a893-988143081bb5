"""
话题管理相关表单
"""

from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, IntegerField, SubmitField, HiddenField
from wtforms.validators import DataRequired, Length, Optional, NumberRange


class TopicForm(FlaskForm):
    """话题表单"""
    name = StringField('话题名称', validators=[
        DataRequired(message='话题名称不能为空'),
        Length(1, 100, message='话题名称长度不正确')
    ])
    type = SelectField('话题类型', choices=[
        ('required', '必选话题'),
        ('random', '随机话题')
    ], default='random')
    priority = IntegerField('优先级', default=0, validators=[
        NumberRange(min=0, max=100, message='优先级范围为0-100')
    ])
    submit = SubmitField('保存')


class TopicRelationForm(FlaskForm):
    """话题关联表单"""
    topic_id = HiddenField('话题ID', validators=[
        DataRequired(message='话题ID不能为空')
    ])
    related_topic_id = SelectField('关联话题', coerce=int, validators=[
        DataRequired(message='请选择关联话题')
    ])
    weight = IntegerField('关联权重', default=1, validators=[
        NumberRange(min=1, max=10, message='权重范围为1-10')
    ])
    submit = SubmitField('保存') 