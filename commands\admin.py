"""
管理员相关命令
"""
import click
from flask.cli import with_appcontext
from app.models import db
from app.models.user import User, Role

@click.command('create-admin')
@click.option('--username', prompt=True, help='管理员用户名')
@click.option('--password', prompt=True, hide_input=True, confirmation_prompt=True, help='管理员密码')
@click.option('--email', prompt=True, help='管理员邮箱')
@with_appcontext
def create_admin_command(username, password, email):
    """创建管理员用户"""
    # 检查用户是否已存在
    user = User.query.filter_by(username=username).first()
    if user:
        click.echo(f'用户 {username} 已存在')
        return
    
    # 获取超级管理员角色
    admin_role = Role.query.filter_by(name='超级管理员').first()
    if not admin_role:
        click.echo('超级管理员角色不存在，请先初始化角色')
        return
    
    # 创建用户
    user = User(
        username=username,
        email=email,
        is_admin=True
    )
    user.set_password(password)
    user.roles = [admin_role]
    
    db.session.add(user)
    db.session.commit()
    
    click.echo(f'成功创建管理员用户 {username}') 