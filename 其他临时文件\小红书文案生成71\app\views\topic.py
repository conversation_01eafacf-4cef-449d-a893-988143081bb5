# -*- coding: utf-8 -*-
"""
话题管理视图
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.models import db
from app.models.topic import Topic, TopicRelation
from app.forms.topic import TopicForm, TopicRelationForm

# 创建话题管理蓝图
topic_bp = Blueprint('topic', __name__, url_prefix='/topic')


@topic_bp.route('/')
@login_required
def index():
    """话题管理首页"""
    if not current_user.has_permission('topic_manage'):
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('main.dashboard'))
    
    topics = Topic.query.order_by(Topic.priority.desc(), Topic.id.asc()).all()
    return render_template('topic/index.html', topics=topics)


@topic_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add_topic():
    """添加话题"""
    if not current_user.has_permission('topic_manage'):
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('main.dashboard'))
    
    form = TopicForm()
    
    if form.validate_on_submit():
        topic = Topic(
            name=form.name.data,
            type=form.type.data,
            priority=form.priority.data
        )
        db.session.add(topic)
        db.session.commit()
        flash('话题添加成功', 'success')
        return redirect(url_for('topic.index'))
    
    return render_template('topic/topic_form.html', form=form, title='添加话题')


@topic_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_topic(id):
    """编辑话题"""
    if not current_user.has_permission('topic_manage'):
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('main.dashboard'))
    
    topic = Topic.query.get_or_404(id)
    form = TopicForm(obj=topic)
    
    if form.validate_on_submit():
        topic.name = form.name.data
        topic.type = form.type.data
        topic.priority = form.priority.data
        db.session.commit()
        flash('话题更新成功', 'success')
        return redirect(url_for('topic.index'))
    
    return render_template('topic/topic_form.html', form=form, title='编辑话题')


@topic_bp.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete_topic(id):
    """删除话题"""
    if not current_user.has_permission('topic_manage'):
        return jsonify({'success': False, 'message': '您没有权限执行此操作'})
    
    topic = Topic.query.get_or_404(id)
    
    # 检查是否有关联的话题关系
    if TopicRelation.query.filter((TopicRelation.topic_id == id) | 
                                 (TopicRelation.related_topic_id == id)).count() > 0:
        # 删除关联关系
        TopicRelation.query.filter((TopicRelation.topic_id == id) | 
                                  (TopicRelation.related_topic_id == id)).delete()
    
    # 检查是否有文案使用此话题（这里简单实现，实际需要检查文案中的话题JSON字段）
    # 如果有更复杂的关联检查，应该在这里添加
    
    db.session.delete(topic)
    db.session.commit()
    return jsonify({'success': True, 'message': '话题删除成功'})


@topic_bp.route('/relations/<int:id>')
@login_required
def relations(id):
    """话题关联管理"""
    if not current_user.has_permission('topic_manage'):
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('main.dashboard'))
    
    topic = Topic.query.get_or_404(id)
    relations = TopicRelation.query.filter_by(topic_id=id).all()
    
    # 创建关联表单
    form = TopicRelationForm()
    form.topic_id.data = id
    
    # 获取可选的关联话题（排除自己和已经关联的话题）
    related_ids = [r.related_topic_id for r in relations]
    related_ids.append(id)  # 排除自己
    available_topics = Topic.query.filter(~Topic.id.in_(related_ids)).all()
    
    form.related_topic_id.choices = [(t.id, t.name) for t in available_topics]
    
    return render_template('topic/relations.html', 
                          topic=topic, 
                          relations=relations, 
                          form=form)


@topic_bp.route('/relation/add', methods=['POST'])
@login_required
def add_relation():
    """添加话题关联"""
    if not current_user.has_permission('topic_manage'):
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('main.dashboard'))
    
    form = TopicRelationForm()
    
    # 动态设置选项
    topic_id = request.form.get('topic_id', type=int)
    if topic_id:
        relations = TopicRelation.query.filter_by(topic_id=topic_id).all()
        related_ids = [r.related_topic_id for r in relations]
        related_ids.append(topic_id)  # 排除自己
        available_topics = Topic.query.filter(~Topic.id.in_(related_ids)).all()
        form.related_topic_id.choices = [(t.id, t.name) for t in available_topics]
    
    if form.validate_on_submit():
        relation = TopicRelation(
            topic_id=form.topic_id.data,
            related_topic_id=form.related_topic_id.data,
            weight=form.weight.data
        )
        db.session.add(relation)
        db.session.commit()
        flash('话题关联添加成功', 'success')
        return redirect(url_for('topic.relations', id=form.topic_id.data))
    
    for field, errors in form.errors.items():
        for error in errors:
            flash(f"{getattr(form, field).label.text}: {error}", 'danger')
    
    return redirect(url_for('topic.relations', id=form.topic_id.data))


@topic_bp.route('/relation/delete/<int:id>', methods=['POST'])
@login_required
def delete_relation(id):
    """删除话题关联"""
    if not current_user.has_permission('topic_manage'):
        return jsonify({'success': False, 'message': '您没有权限执行此操作'})
    
    relation = TopicRelation.query.get_or_404(id)
    topic_id = relation.topic_id
    
    db.session.delete(relation)
    db.session.commit()
    return jsonify({'success': True, 'message': '话题关联删除成功', 'topic_id': topic_id}) 