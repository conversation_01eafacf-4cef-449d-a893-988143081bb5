<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{{ content.title or '文案详情' }} - {{ client.name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        /* 小红书风格的顶部导航 */
        .mobile-header {
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(255, 36, 66, 0.3);
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background-color 0.2s;
        }
        
        .back-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .header-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            flex: 1;
        }
        
        /* 内容容器 */
        .content-container {
            background: white;
            margin: 0.5rem;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        /* 文章头部 */
        .article-header {
            padding: 1.5rem;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .article-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
            line-height: 1.4;
            margin: 0 0 1rem 0;
        }
        
        .article-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85rem;
            color: #999;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-approved {
            background: #d1edff;
            color: #0c5460;
        }

        .status-published {
            background: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }
        
        /* 文章内容 */
        .article-content {
            padding: 1.5rem;
        }
        
        .content-text {
            font-size: 1rem;
            line-height: 1.6;
            color: #333;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        /* 图片展示 */
        .images-section {
            padding: 0 1.5rem 1.5rem;
        }
        
        .images-title {
            font-size: 1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 0.75rem;
        }
        
        .image-item {
            aspect-ratio: 1;
            border-radius: 12px;
            overflow: hidden;
            background: #f8f9fa;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .image-item:active {
            transform: scale(0.95);
        }
        
        .image-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        /* 详细信息 */
        .details-section {
            padding: 1.5rem;
            background: #f8f9fa;
            margin: 0.5rem;
            border-radius: 16px;
        }
        
        .details-title {
            font-size: 1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .detail-item:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-size: 0.9rem;
            color: #666;
        }
        
        .detail-value {
            font-size: 0.9rem;
            color: #333;
            font-weight: 500;
        }
        
        /* 操作按钮 */
        .action-section {
            padding: 1rem;
            background: white;
            position: sticky;
            bottom: 0;
            border-top: 1px solid #f0f0f0;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
        }
        
        .action-buttons {
            display: flex;
            gap: 0.75rem;
        }
        
        .action-btn {
            flex: 1;
            padding: 0.875rem;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.2s;
        }
        
        .btn-approve {
            background: #ff2442;
            color: white;
        }
        
        .btn-approve:hover {
            background: #e01e3a;
            color: white;
        }
        
        .btn-reject {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dee2e6;
        }
        
        .btn-reject:hover {
            background: #e9ecef;
            color: #495057;
        }
        
        .btn-disabled {
            background: #e9ecef;
            color: #6c757d;
            cursor: not-allowed;
        }
        
        /* 图片预览模态框 */
        .image-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .image-modal img {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }
        
        .image-modal-close {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: none;
            border: none;
            color: white;
            font-size: 2rem;
            cursor: pointer;
        }
        
        /* 底部安全区域 */
        .safe-area-bottom {
            height: env(safe-area-inset-bottom);
            background: white;
        }
        
        /* 响应式调整 */
        @media (max-width: 375px) {
            .images-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .article-header, .article-content, .images-section, .details-section {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 移动端头部 -->
    <div class="mobile-header">
        <button class="back-btn" onclick="goBack()">
            <i class="bi bi-arrow-left"></i>
        </button>
        <h1 class="header-title">文案详情</h1>
    </div>

    <!-- 内容容器 -->
    <div class="content-container">
        <!-- 文章头部 -->
        <div class="article-header">
            <h2 class="article-title">{{ content.title or '无标题' }}</h2>
            <div class="article-meta">
                <span>{{ content.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                {% if content.publish_status == 'published' %}
                    <span class="status-badge status-published">已发布</span>
                {% elif content.workflow_status == 'pending_client_review' %}
                    <span class="status-badge status-pending">待审核</span>
                {% elif content.client_review_status == 'approved' %}
                    {% if content.workflow_status in ['ready_to_publish', 'pending_publish'] %}
                        <span class="status-badge status-approved">待发布</span>
                    {% elif content.workflow_status == 'publishing' %}
                        <span class="status-badge status-approved">发布中</span>
                    {% else %}
                        <span class="status-badge status-approved">已通过</span>
                    {% endif %}
                {% elif content.client_review_status == 'rejected' %}
                    <span class="status-badge status-rejected">已驳回</span>
                {% else %}
                    <span class="status-badge status-pending">待审核</span>
                {% endif %}
            </div>
        </div>

        <!-- 文章内容 -->
        {% if content.content %}
        <div class="article-content">
            <div class="content-text">{{ content.content }}</div>
        </div>
        {% endif %}

        <!-- 图片展示 -->
        {% set image_list = content.images.all() if content.images else [] %}
        {% set url_list = content.image_urls_list if content.image_urls_list else [] %}
        {% if image_list or url_list %}
        <div class="images-section">
            <div class="images-title">配图 ({{ (image_list|length) + (url_list|length) }}张)</div>
            <div class="images-grid">
                {% for image in image_list %}
                <div class="image-item" onclick="showImageModal('{{ image.image_path }}')">
                    <img src="{{ image.image_path }}" alt="文案配图" loading="lazy">
                </div>
                {% endfor %}
                {% for url in url_list %}
                <div class="image-item" onclick="showImageModal('{{ url }}')">
                    <img src="{{ url }}" alt="文案配图" loading="lazy">
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>

    <!-- 详细信息 -->
    <div class="details-section">
        <div class="details-title">详细信息</div>
        <div class="detail-item">
            <span class="detail-label">任务名称</span>
            <span class="detail-value">{{ content.task.name if content.task else '默认任务' }}</span>
        </div>
        <div class="detail-item">
            <span class="detail-label">创建时间</span>
            <span class="detail-value">{{ content.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</span>
        </div>
        {% if content.display_date %}
        <div class="detail-item">
            <span class="detail-label">发布日期</span>
            <span class="detail-value">{{ content.display_date.strftime('%Y-%m-%d') }}</span>
        </div>
        {% endif %}
        {% if content.topics %}
        <div class="detail-item">
            <span class="detail-label">话题标签</span>
            <span class="detail-value">{{ content.topics }}</span>
        </div>
        {% endif %}
        {% if content.location %}
        <div class="detail-item">
            <span class="detail-label">位置信息</span>
            <span class="detail-value">{{ content.location }}</span>
        </div>
        {% endif %}
    </div>

    <!-- 操作按钮 -->
    {% if content.client_review_status == 'pending_client_review' %}
    <div class="action-section">
        <div class="action-buttons">
            <button class="action-btn btn-approve" onclick="approveContent()">
                <i class="bi bi-check-lg me-2"></i>通过审核
            </button>
            <button class="action-btn btn-reject" onclick="showRejectModal()">
                <i class="bi bi-x-lg me-2"></i>驳回修改
            </button>
        </div>
    </div>
    {% endif %}

    <!-- 驳回理由模态框 -->
    <div class="modal fade" id="rejectModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">驳回理由</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">请选择或输入驳回理由：</label>
                        <div class="d-flex flex-wrap gap-2 mb-3">
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-reason"
                                    onclick="fillRejectReason('内容质量不符合要求，需要重新编写')">内容质量不达标</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-reason"
                                    onclick="fillRejectReason('标题不够吸引人，建议重新设计')">标题需要优化</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-reason"
                                    onclick="fillRejectReason('图片质量需要提升，建议更换高质量图片')">图片质量问题</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-reason"
                                    onclick="fillRejectReason('文案风格与品牌调性不符')">风格不符</button>
                        </div>
                        <textarea class="form-control" id="rejectReason" rows="4"
                                  placeholder="请输入具体的驳回理由..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="submitReject()">确认驳回</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片预览模态框 -->
    <div class="image-modal" id="imageModal" onclick="hideImageModal()">
        <button class="image-modal-close" onclick="hideImageModal()">
            <i class="bi bi-x"></i>
        </button>
        <img id="modalImage" src="" alt="图片预览">
    </div>

    <!-- 底部安全区域 -->
    <div class="safe-area-bottom"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        const shareKey = '{{ share_key }}';
        const contentId = {{ content.id }};

        // 返回上一页
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // 如果没有历史记录，返回列表页
                window.location.href = `/client-review/${shareKey}`;
            }
        }

        // 显示图片预览
        function showImageModal(imageUrl) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            modalImage.src = imageUrl;
            modal.style.display = 'flex';

            // 防止背景滚动
            document.body.style.overflow = 'hidden';
        }

        // 隐藏图片预览
        function hideImageModal() {
            const modal = document.getElementById('imageModal');
            modal.style.display = 'none';

            // 恢复背景滚动
            document.body.style.overflow = 'auto';
        }

        // 显示驳回模态框
        function showRejectModal() {
            document.getElementById('rejectReason').value = '';
            const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
            modal.show();
        }

        // 填充快捷驳回理由
        function fillRejectReason(reason) {
            const textarea = document.getElementById('rejectReason');
            const currentValue = textarea.value.trim();
            if (currentValue) {
                textarea.value = currentValue + '\n' + reason;
            } else {
                textarea.value = reason;
            }
        }

        // 通过审核
        function approveContent() {
            if (!confirm('确定要通过这篇文案吗？')) return;

            reviewContent('approve', '');
        }

        // 提交驳回
        function submitReject() {
            const reason = document.getElementById('rejectReason').value.trim();
            if (!reason) {
                alert('请输入驳回理由');
                return;
            }

            reviewContent('reject', reason);
            bootstrap.Modal.getInstance(document.getElementById('rejectModal')).hide();
        }

        // 获取访问密钥
        function getAccessKey() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('key');
        }

        // 构建API URL
        function buildApiUrl(path) {
            const accessKey = getAccessKey();
            const url = `/client-review/api/${shareKey}${path}`;
            if (accessKey) {
                const separator = path.includes('?') ? '&' : '?';
                return `${url}${separator}key=${accessKey}`;
            }
            return url;
        }

        // 审核内容
        function reviewContent(action, comment) {
            // 禁用按钮防止重复提交
            const approveBtn = document.querySelector('.btn-approve');
            const rejectBtn = document.querySelector('.btn-reject');
            if (approveBtn) approveBtn.disabled = true;
            if (rejectBtn) rejectBtn.disabled = true;

            const formData = new FormData();
            formData.append('action', action);
            formData.append('review_comment', comment);

            fetch(buildApiUrl(`/contents/${contentId}/review`), {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');

                    // 更新页面状态
                    setTimeout(() => {
                        updatePageStatus(action);
                    }, 1000);
                } else {
                    showToast(data.message, 'error');
                    // 重新启用按钮
                    if (approveBtn) approveBtn.disabled = false;
                    if (rejectBtn) rejectBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('审核失败:', error);
                showToast('网络错误，请稍后重试', 'error');
                // 重新启用按钮
                if (approveBtn) approveBtn.disabled = false;
                if (rejectBtn) rejectBtn.disabled = false;
            });
        }

        // 更新页面状态
        function updatePageStatus(action) {
            const statusBadge = document.querySelector('.status-badge');
            const actionSection = document.querySelector('.action-section');

            if (action === 'approve') {
                statusBadge.className = 'status-badge status-approved';
                statusBadge.textContent = '已通过';
            } else if (action === 'reject') {
                statusBadge.className = 'status-badge status-rejected';
                statusBadge.textContent = '已驳回';
            }

            // 隐藏操作按钮
            if (actionSection) {
                actionSection.style.display = 'none';
            }
        }

        // 显示提示信息
        function showToast(message, type = 'info') {
            // 创建Toast容器（如果不存在）
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            // 创建Toast元素
            const toastId = 'toast-' + Date.now();
            const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-primary';
            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            // 显示Toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
            toast.show();

            // 自动清理
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        }

        // 阻止图片模态框的事件冒泡
        document.getElementById('modalImage').addEventListener('click', function(e) {
            e.stopPropagation();
        });

        // 键盘事件处理
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideImageModal();
            }
        });
    </script>
</body>
</html>
