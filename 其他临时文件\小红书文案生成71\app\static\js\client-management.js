/**
 * 客户管理页面专用JavaScript
 * 解决AJAX加载页面时的事件绑定问题
 */

// 客户管理页面初始化标志
let clientPageInitialized = false;

// 统一的客户管理页面初始化函数
function initializeClientPage() {
    console.log('=== initializeClientPage 被调用 ===');
    console.log('当前URL:', window.location.href);
    console.log('clientPageInitialized:', clientPageInitialized);

    // 强制重新初始化（因为可能是AJAX加载的新内容）
    console.log('强制重新初始化客户管理页面');
    clientPageInitialized = false;

    console.log('开始初始化客户管理页面');
    clientPageInitialized = true;

    // 确保加载指示器隐藏
    hideLoadingIndicator();

    // 使用事件委托绑定所有按钮事件，避免重复绑定
    setupEventDelegation();

    console.log('客户管理页面初始化完成');

    // 测试按钮是否存在
    const testButtons = document.querySelectorAll('.toggle-status-btn, .btn-outline-primary, .btn-outline-info');
    console.log('找到的按钮数量:', testButtons.length);
    testButtons.forEach((btn, index) => {
        console.log(`按钮 ${index}:`, btn.className, btn.getAttribute('title'));
    });

    // 添加一个测试点击事件
    setTimeout(() => {
        console.log('=== 测试事件委托是否工作 ===');
        const testBtn = document.querySelector('.toggle-status-btn');
        if (testBtn) {
            console.log('找到测试按钮:', testBtn);
            console.log('按钮属性:', {
                'data-client-id': testBtn.getAttribute('data-client-id'),
                'data-status': testBtn.getAttribute('data-status'),
                'className': testBtn.className
            });
        } else {
            console.log('未找到测试按钮');
        }
    }, 500);
}

// 设置事件委托
function setupEventDelegation() {
    console.log('=== 设置客户管理页面事件委托 ===');

    // 移除可能存在的旧事件监听器
    document.removeEventListener('click', handleClientPageClick);
    document.removeEventListener('submit', handleClientPageSubmit);
    console.log('已移除旧的事件监听器');

    // 添加新的事件委托
    document.addEventListener('click', handleClientPageClick);
    document.addEventListener('submit', handleClientPageSubmit);
    console.log('已添加新的事件委托');

    // 同时在content-area上也添加事件监听器，确保AJAX加载的内容能被捕获
    const contentArea = document.querySelector('.content-area');
    if (contentArea) {
        contentArea.removeEventListener('click', handleClientPageClick);
        contentArea.addEventListener('click', handleClientPageClick);
        console.log('已在content-area上添加事件委托');
    }

    // 测试事件委托是否正确设置
    console.log('事件委托设置完成，测试点击事件...');
}

// 统一的点击事件处理函数
function handleClientPageClick(e) {
    console.log('=== handleClientPageClick 被触发 ===');
    console.log('当前URL:', window.location.pathname);
    console.log('点击的元素:', e.target);
    console.log('点击的元素类名:', e.target.className);
    console.log('点击的元素ID:', e.target.id);
    console.log('点击的元素标签:', e.target.tagName);

    // 只处理客户管理页面的事件
    if (!window.location.pathname.includes('/clients')) {
        console.log('不是客户管理页面，跳过处理');
        return;
    }
    
    // 重置按钮
    if (e.target && e.target.id === 'resetFilterBtn') {
        e.preventDefault();
        console.log('重置按钮被点击');
        
        const filterForm = document.getElementById('clientFilterForm');
        if (filterForm) {
            filterForm.querySelector('input[name="search"]').value = '';
            filterForm.querySelector('#statusFilter').value = '';
            filterForm.querySelector('#sortByFilter').value = 'created_at';
            filterForm.querySelector('#sortOrderFilter').value = 'desc';
            loadClientData(1);
        } else {
            window.location.href = '/clients/';
        }
        return;
    }
    
    // 状态切换按钮
    if (e.target.closest('.toggle-status-btn')) {
        e.preventDefault();
        const btn = e.target.closest('.toggle-status-btn');
        const clientId = btn.getAttribute('data-client-id');
        const status = JSON.parse(btn.getAttribute('data-status'));
        if (clientId) {
            console.log('状态切换按钮被点击:', clientId, status);
            toggleClientStatus(clientId, status);
        }
        return;
    }
    
    // 审核状态切换按钮
    if (e.target.closest('.toggle-review-btn')) {
        e.preventDefault();
        const btn = e.target.closest('.toggle-review-btn');
        const clientId = btn.getAttribute('data-client-id');
        const status = JSON.parse(btn.getAttribute('data-status'));
        if (clientId) {
            console.log('审核状态切换按钮被点击:', clientId, status);
            toggleClientReview(clientId, status);
        }
        return;
    }
    
    // 编辑按钮
    if (e.target.closest('.btn-outline-primary[title="编辑"]')) {
        e.preventDefault();
        const btn = e.target.closest('.btn-outline-primary[title="编辑"]');
        const row = btn.closest('tr');
        if (row) {
            const clientId = row.querySelector('td:first-child').textContent.trim();
            console.log('编辑按钮被点击:', clientId);
            showEditModal(clientId);
        }
        return;
    }
    
    // 分享链接按钮
    if (e.target.closest('.btn-outline-info[title="分享链接"]')) {
        e.preventDefault();
        const btn = e.target.closest('.btn-outline-info[title="分享链接"]');
        const row = btn.closest('tr');
        if (row) {
            const clientId = row.querySelector('td:first-child').textContent.trim();
            console.log('分享链接按钮被点击:', clientId);
            showSharesModal(clientId);
        }
        return;
    }
    
    // 使用记录按钮
    if (e.target.closest('.btn-outline-secondary[title="使用记录"]')) {
        e.preventDefault();
        const btn = e.target.closest('.btn-outline-secondary[title="使用记录"]');
        const row = btn.closest('tr');
        if (row) {
            const clientId = row.querySelector('td:first-child').textContent.trim();
            console.log('使用记录按钮被点击:', clientId);
            showUsageModal(clientId);
        }
        return;
    }
    
    // 删除按钮
    if (e.target.closest('.btn-outline-danger[title="删除"]')) {
        e.preventDefault();
        const btn = e.target.closest('.btn-outline-danger[title="删除"]');
        const row = btn.closest('tr');
        if (row) {
            const clientId = row.querySelector('td:first-child').textContent.trim();
            const clientName = row.querySelector('td:nth-child(2)').textContent.trim();
            console.log('删除按钮被点击:', clientId, clientName);
            deleteClient(clientId, clientName);
        }
        return;
    }
    
    // 新增客户按钮
    if (e.target && e.target.id === 'submitCreateClientBtn') {
        e.preventDefault();
        console.log('提交新增客户表单');
        const form = document.getElementById('createClientForm');
        if (form) {
            const formData = new FormData(form);
            const createUrl = form.getAttribute('action') || '/clients/create';
            
            fetch(createUrl, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showToast('客户创建成功');
                    bootstrap.Modal.getInstance(document.getElementById('createClientModal')).hide();
                    loadClientData(getCurrentPage());
                } else {
                    showToast('创建失败: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('创建客户失败:', error);
                showToast('创建失败，请重试', 'danger');
            });
        }
        return;
    }
    
    // 分页链接
    if (e.target.closest('.pagination-link')) {
        e.preventDefault();
        const link = e.target.closest('.pagination-link');
        const page = link.getAttribute('data-page');
        if (page) {
            console.log('分页链接被点击:', page);
            loadClientData(parseInt(page));
        }
        return;
    }
}

// 统一的表单提交事件处理函数
function handleClientPageSubmit(e) {
    // 只处理客户管理页面的事件
    if (!window.location.pathname.includes('/clients')) {
        return;
    }

    if (e.target && e.target.id === 'clientFilterForm') {
        console.log('客户筛选表单提交被拦截');
        e.preventDefault();
        e.stopPropagation();

        console.log('执行筛选操作');

        // 直接调用模板中的loadClientData函数
        if (typeof window.loadClientData === 'function') {
            console.log('使用模板中的loadClientData函数');
            window.loadClientData(1);
        } else {
            console.log('模板中的loadClientData函数不存在，使用备用方案');
            // 备用方案：手动构建URL并跳转
            const form = e.target;
            const formData = new FormData(form);
            const params = new URLSearchParams();

            for (let [key, value] of formData.entries()) {
                if (value) {
                    params.append(key, value);
                }
            }

            const url = `/clients/?${params.toString()}`;
            console.log('跳转到筛选URL:', url);
            window.location.href = url;
        }
    }
}

// 兼容性函数：为了保持与标签系统的兼容性
function rebindAllButtonEvents() {
    console.log('rebindAllButtonEvents被调用，使用新的事件委托系统');
    // 重新初始化客户管理页面
    initializeClientPage();
}

// 添加CSS规则强制隐藏加载指示器
function addHideLoadingStyle() {
    const style = document.createElement('style');
    style.textContent = `
        #loading-indicator {
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
        }
        /* 确保按钮内的加载状态正常显示 */
        button .spinner-border,
        .btn .spinner-border {
            display: inline-block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
    `;
    document.head.appendChild(style);
    console.log('添加了强制隐藏加载指示器的CSS规则');
}

// 隐藏加载指示器的通用函数
function hideLoadingIndicator() {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
        loadingIndicator.style.opacity = '0';
        loadingIndicator.style.visibility = 'hidden';
        console.log('加载指示器已隐藏');
    }
}

// 切换客户状态
function toggleClientStatus(clientId, currentStatus) {
    console.log('切换客户状态:', clientId, currentStatus);

    // 发送请求
    fetch(`/clients/${clientId}/toggle_status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 刷新当前页面内容
            if (typeof loadClientData === 'function') {
                loadClientData(getCurrentPage());
            } else {
                // 如果loadClientData不存在，刷新整个页面
                window.location.reload();
            }
            showToast(`客户状态已${data.status ? '启用' : '禁用'}`);
        } else {
            showToast('操作失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('请求失败:', error);
        showToast('操作失败，请重试', 'danger');
    });
}

// 切换客户审核状态
function toggleClientReview(clientId, currentStatus) {
    console.log('切换客户审核状态:', clientId, currentStatus);

    // 发送请求
    fetch(`/clients/${clientId}/toggle_review`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 刷新当前页面内容
            if (typeof loadClientData === 'function') {
                loadClientData(getCurrentPage());
            } else {
                // 如果loadClientData不存在，刷新整个页面
                window.location.reload();
            }
            showToast(`客户审核状态已${data.need_review ? '开启' : '关闭'}`);
        } else {
            showToast('操作失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('请求失败:', error);
        showToast('操作失败，请重试', 'danger');
    });
}

// 删除客户
function deleteClient(clientId, clientName) {
    if (confirm(`确定要删除客户 "${clientName}" 吗？此操作不可恢复。`)) {
        fetch(`/clients/${clientId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 删除成功，刷新页面
                loadClientData(getCurrentPage());
                showToast('客户删除成功');
            } else {
                showToast('删除失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('请求失败:', error);
            showToast('删除失败，请重试', 'danger');
        });
    }
}

// 获取当前页码
function getCurrentPage() {
    const activePage = document.querySelector('.page-item.active .page-link');
    if (activePage) {
        return parseInt(activePage.textContent);
    }
    return 1; // 默认返回第一页
}

// 显示Toast提示
function showToast(message, type = 'success') {
    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // 创建toast元素
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'danger' ? 'danger' : 'success'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // 显示toast
    const toastElement = document.getElementById(toastId);
    if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
        const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: 3000
        });
        toast.show();

        // 监听隐藏事件，移除DOM元素
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    } else {
        // 如果Bootstrap不可用，使用简单的显示/隐藏
        toastElement.style.display = 'block';
        setTimeout(() => {
            toastElement.remove();
        }, 3000);
    }
}

// 将函数暴露到全局作用域
window.initializeClientPage = initializeClientPage;
window.rebindAllButtonEvents = rebindAllButtonEvents;
window.handleClientPageClick = handleClientPageClick;
window.handleClientPageSubmit = handleClientPageSubmit;
window.addHideLoadingStyle = addHideLoadingStyle;
window.hideLoadingIndicator = hideLoadingIndicator;
window.toggleClientStatus = toggleClientStatus;
window.toggleClientReview = toggleClientReview;
window.deleteClient = deleteClient;
window.getCurrentPage = getCurrentPage;
window.showToast = showToast;

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.pathname.includes('/clients')) {
        console.log('DOMContentLoaded: 客户管理页面准备初始化');
        initializeClientPage();
    }
});

// 确保在所有资源加载完成后也进行初始化
window.addEventListener('load', function() {
    if (window.location.pathname.includes('/clients')) {
        console.log('window.load: 确保客户管理页面已初始化');
        initializeClientPage();
    }
});
