{% extends "base.html" %}

{% block title %}发布详情{% endblock %}

{% block page_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">文案发布详情</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('publish.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                        <a href="{{ url_for('content.detail', content_id=content.id) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-file-alt"></i> 查看文案详情
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 文案基本信息 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">基本信息</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th width="120px">ID</th>
                                            <td>{{ content.id }}</td>
                                        </tr>
                                        <tr>
                                            <th>标题</th>
                                            <td>{{ content.title }}</td>
                                        </tr>
                                        <tr>
                                            <th>客户</th>
                                            <td>{{ content.client.name if content.client else '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>任务</th>
                                            <td>{{ content.task.name if content.task else '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>批次</th>
                                            <td>{{ content.batch.name if content.batch else '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>创建时间</th>
                                            <td>{{ content.created_at.strftime('%Y-%m-%d %H:%M:%S') if content.created_at else '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>创建人</th>
                                            <td>{{ content.creator.username if content.creator else '无' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">发布信息</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th width="120px">发布状态</th>
                                            <td>
                                                {% if content.publish_status == 'unpublished' %}
                                                <span class="badge bg-primary">未发布</span>
                                                {% elif content.publish_status == 'publishing' %}
                                                <span class="badge bg-info">发布中</span>
                                                {% elif content.publish_status == 'published' %}
                                                <span class="badge bg-success">已发布</span>
                                                {% elif content.publish_status == 'failed' %}
                                                <span class="badge bg-danger">发布失败</span>
                                                {% elif content.publish_status == 'partial_published' %}
                                                <span class="badge bg-warning">部分发布</span>
                                                {% elif content.publish_status == 'publish_timeout' %}
                                                <span class="badge bg-secondary">发布超时</span>
                                                {% else %}
                                                <span class="badge bg-secondary">{{ content.publish_status }}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>工作流状态</th>
                                            <td>
                                                {% if content.workflow_status == 'pending_review' %}
                                                <span class="badge bg-primary">待初审</span>
                                                {% elif content.workflow_status == 'review_passed' %}
                                                <span class="badge bg-info">初审通过</span>
                                                {% elif content.workflow_status == 'pending_upload' %}
                                                <span class="badge bg-warning">待上传图片</span>
                                                {% elif content.workflow_status == 'image_uploaded' %}
                                                <span class="badge bg-info">图片已上传</span>
                                                {% elif content.workflow_status == 'pending_final_review' %}
                                                <span class="badge bg-warning">待最终审核</span>
                                                {% elif content.workflow_status == 'pending_client_review' %}
                                                <span class="badge bg-warning">待客户审核</span>
                                                {% elif content.workflow_status == 'client_rejected' %}
                                                <span class="badge bg-danger">客户已拒绝</span>
                                                {% elif content.workflow_status == 'client_approved' %}
                                                <span class="badge bg-success">客户已通过</span>
                                                {% elif content.workflow_status == 'pending_publish' %}
                                                <span class="badge bg-primary">待发布</span>
                                                {% elif content.workflow_status == 'published' %}
                                                <span class="badge bg-success">已发布</span>
                                                {% else %}
                                                <span class="badge bg-secondary">{{ content.workflow_status }}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>发布优先级</th>
                                            <td>
                                                {% if content.publish_priority == 'high' %}
                                                <span class="badge bg-danger">高</span>
                                                {% elif content.publish_priority == 'normal' %}
                                                <span class="badge bg-warning">中</span>
                                                {% elif content.publish_priority == 'low' %}
                                                <span class="badge bg-info">低</span>
                                                {% else %}
                                                <span class="badge bg-secondary">{{ content.publish_priority }}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>发布时间</th>
                                            <td>{{ content.publish_time.strftime('%Y-%m-%d %H:%M:%S') if content.publish_time else '无' }}</td>
                                        </tr>
                                        <tr>
                                            <th>状态更新时间</th>
                                            <td>{{ content.status_update_time.strftime('%Y-%m-%d %H:%M:%S') if content.status_update_time else '无' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 发布操作 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">发布操作</h5>
                                </div>
                                <div class="card-body">
                                    <form method="post" action="{{ url_for('publish.update', content_id=content.id) }}" class="row">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="form-label">更新发布状态</label>
                                                <select name="status" class="form-select">
                                                    <option value="">不更新</option>
                                                    <option value="unpublished" {% if content.publish_status == 'unpublished' %}selected{% endif %}>未发布</option>
                                                    <option value="publishing" {% if content.publish_status == 'publishing' %}selected{% endif %}>发布中</option>
                                                    <option value="published" {% if content.publish_status == 'published' %}selected{% endif %}>已发布</option>
                                                    <option value="failed" {% if content.publish_status == 'failed' %}selected{% endif %}>发布失败</option>
                                                    <option value="partial_published" {% if content.publish_status == 'partial_published' %}selected{% endif %}>部分发布</option>
                                                    <option value="publish_timeout" {% if content.publish_status == 'publish_timeout' %}selected{% endif %}>发布超时</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="form-label">更新优先级</label>
                                                <select name="priority" class="form-select">
                                                    <option value="">不更新</option>
                                                    <option value="high" {% if content.publish_priority == 'high' %}selected{% endif %}>高</option>
                                                    <option value="normal" {% if content.publish_priority == 'normal' %}selected{% endif %}>中</option>
                                                    <option value="low" {% if content.publish_priority == 'low' %}selected{% endif %}>低</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label class="form-label">&nbsp;</label>
                                                <button type="submit" class="btn btn-primary w-100">更新</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 发布记录 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">发布记录</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover table-striped">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>状态</th>
                                                    <th>平台</th>
                                                    <th>账号</th>
                                                    <th>发布链接</th>
                                                    <th>发布时间</th>
                                                    <th>错误信息</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for record in publish_records %}
                                                <tr>
                                                    <td>{{ record.id }}</td>
                                                    <td>
                                                        {% if record.status == 'success' %}
                                                        <span class="badge bg-success">成功</span>
                                                        {% elif record.status == 'failed' %}
                                                        <span class="badge bg-danger">失败</span>
                                                        {% else %}
                                                        <span class="badge bg-secondary">{{ record.status }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ record.platform or '无' }}</td>
                                                    <td>{{ record.account or '无' }}</td>
                                                    <td>
                                                        {% if record.publish_url %}
                                                        <a href="{{ record.publish_url }}" target="_blank">{{ record.publish_url|truncate(30) }}</a>
                                                        {% else %}
                                                        无
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ record.publish_time.strftime('%Y-%m-%d %H:%M:%S') if record.publish_time else '无' }}</td>
                                                    <td>{{ record.error_message|truncate(50) if record.error_message else '无' }}</td>
                                                </tr>
                                                {% else %}
                                                <tr>
                                                    <td colspan="7" class="text-center">暂无发布记录</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 