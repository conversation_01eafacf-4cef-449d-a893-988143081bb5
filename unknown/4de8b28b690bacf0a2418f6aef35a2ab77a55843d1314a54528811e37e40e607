"""
通知系统视图
"""
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from sqlalchemy import desc, and_, or_

from app import db
from app.models.notification import Notification
from app.models.user import User, Role
from app.models import SystemSetting
from app.utils.decorators import permission_required
from app.forms.notification import NotificationFilterForm, NotificationSettingForm, NotificationCreateForm

# 创建蓝图
notification_bp = Blueprint('notification', __name__)

@notification_bp.route('/')
@login_required
def index():
    """通知中心首页"""
    # 获取筛选参数
    form = NotificationFilterForm()
    
    # 获取筛选参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    notification_type = request.args.get('type', '')
    priority = request.args.get('priority', '')
    is_read = request.args.get('is_read', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    search = request.args.get('search', '')
    
    # 构建查询
    query = Notification.query.filter(Notification.recipient_id == current_user.id)
    
    # 应用筛选条件
    if notification_type:
        query = query.filter(Notification.type == notification_type)
    
    if priority:
        query = query.filter(Notification.priority == priority)
    
    if is_read != '':
        is_read_bool = (is_read == '1')
        query = query.filter(Notification.is_read == is_read_bool)
    
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Notification.created_at >= date_from)
        except:
            pass
    
    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d')
            # 添加一天，使得日期范围包含整个结束日期
            date_to = date_to + timedelta(days=1)
            query = query.filter(Notification.created_at < date_to)
        except:
            pass
    
    if search:
        query = query.filter(
            or_(
                Notification.title.like(f'%{search}%'),
                Notification.content.like(f'%{search}%')
            )
        )
    
    # 默认按创建时间降序排序
    query = query.order_by(desc(Notification.created_at))
    
    # 分页
    pagination = query.paginate(page=page, per_page=per_page)
    notifications = pagination.items
    
    # 获取未读通知数量
    unread_count = Notification.query.filter(
        Notification.recipient_id == current_user.id,
        Notification.is_read == False
    ).count()
    
    return render_template(
        'notification/index.html',
        form=form,
        notifications=notifications,
        pagination=pagination,
        unread_count=unread_count,
        notification_type=notification_type,
        priority=priority,
        is_read=is_read,
        date_from=date_from,
        date_to=date_to,
        search=search
    )

@notification_bp.route('/view/<int:notification_id>')
@login_required
def view(notification_id):
    """查看通知详情"""
    notification = Notification.query.get_or_404(notification_id)
    
    # 验证当前用户是否为通知接收者
    if notification.recipient_id != current_user.id:
        flash('您无权查看此通知', 'danger')
        return redirect(url_for('notification.index'))
    
    # 获取通知设置
    setting = SystemSetting.query.filter_by(key='notification_auto_mark_read').first()
    auto_mark_read = True
    if setting and setting.value == 'false':
        auto_mark_read = False
    
    # 如果设置为自动标记为已读，则标记
    if auto_mark_read and not notification.is_read:
        notification.is_read = True
        db.session.commit()
    
    return render_template(
        'notification/view.html',
        notification=notification
    )

@notification_bp.route('/mark_read/<int:notification_id>')
@login_required
def mark_read(notification_id):
    """标记通知为已读"""
    notification = Notification.query.get_or_404(notification_id)
    
    # 验证当前用户是否为通知接收者
    if notification.recipient_id != current_user.id:
        flash('您无权操作此通知', 'danger')
        return redirect(url_for('notification.index'))
    
    notification.is_read = True
    db.session.commit()
    
    flash('通知已标记为已读', 'success')
    return redirect(url_for('notification.index'))

@notification_bp.route('/mark_all_read')
@login_required
def mark_all_read():
    """标记所有通知为已读"""
    Notification.query.filter(
        Notification.recipient_id == current_user.id,
        Notification.is_read == False
    ).update({Notification.is_read: True})
    
    db.session.commit()
    
    flash('所有通知已标记为已读', 'success')
    return redirect(url_for('notification.index'))

@notification_bp.route('/system_settings', methods=['GET', 'POST'])
@login_required
@permission_required('admin_access')
def settings():
    """通知设置"""
    form = NotificationSettingForm()
    
    # 从系统设置中加载当前配置
    if request.method == 'GET':
        # 获取各项设置
        enable_all = SystemSetting.query.filter_by(key='notification_enable_all').first()
        if enable_all:
            form.enable_all.data = (enable_all.value == 'true')
        
        enable_review_status = SystemSetting.query.filter_by(key='notification_enable_review_status').first()
        if enable_review_status:
            form.enable_review_status.data = (enable_review_status.value == 'true')
        
        enable_client_operation = SystemSetting.query.filter_by(key='notification_enable_client_operation').first()
        if enable_client_operation:
            form.enable_client_operation.data = (enable_client_operation.value == 'true')
        
        enable_publish_notice = SystemSetting.query.filter_by(key='notification_enable_publish_notice').first()
        if enable_publish_notice:
            form.enable_publish_notice.data = (enable_publish_notice.value == 'true')
        
        enable_system_change = SystemSetting.query.filter_by(key='notification_enable_system_change').first()
        if enable_system_change:
            form.enable_system_change.data = (enable_system_change.value == 'true')
        
        recipient_roles = SystemSetting.query.filter_by(key='notification_recipient_roles').first()
        if recipient_roles:
            form.recipient_roles.data = recipient_roles.value
        
        display_count = SystemSetting.query.filter_by(key='notification_display_count').first()
        if display_count:
            form.display_count.data = display_count.value
        
        auto_mark_read = SystemSetting.query.filter_by(key='notification_auto_mark_read').first()
        if auto_mark_read:
            form.auto_mark_read.data = (auto_mark_read.value == 'true')
    
    if form.validate_on_submit():
        # 保存设置
        update_system_setting('notification_enable_all', str(form.enable_all.data).lower())
        update_system_setting('notification_enable_review_status', str(form.enable_review_status.data).lower())
        update_system_setting('notification_enable_client_operation', str(form.enable_client_operation.data).lower())
        update_system_setting('notification_enable_publish_notice', str(form.enable_publish_notice.data).lower())
        update_system_setting('notification_enable_system_change', str(form.enable_system_change.data).lower())
        update_system_setting('notification_recipient_roles', form.recipient_roles.data)
        update_system_setting('notification_display_count', form.display_count.data)
        update_system_setting('notification_auto_mark_read', str(form.auto_mark_read.data).lower())
        
        db.session.commit()
        flash('通知设置已保存', 'success')
        return redirect(url_for('notification.settings'))
    
    return render_template(
        'notification/settings.html',
        form=form
    )

@notification_bp.route('/create', methods=['GET', 'POST'])
@login_required
@permission_required('admin_access')
def create():
    """创建系统通知"""
    form = NotificationCreateForm()
    
    # 加载角色和用户选项
    roles = Role.query.all()
    users = User.query.filter_by(status=True).all()
    
    form.recipient_role.choices = [(0, '请选择角色')] + [(role.id, role.name) for role in roles]
    form.recipient_user.choices = [(0, '请选择用户')] + [(user.id, user.username) for user in users]
    
    if form.validate_on_submit():
        title = form.title.data
        content = form.content.data
        notification_type = form.type.data
        priority = form.priority.data
        recipient_type = form.recipient_type.data
        
        recipients = []
        
        # 根据接收者类型确定接收者
        if recipient_type == 'all':
            # 所有用户
            recipients = User.query.filter_by(status=True).all()
        elif recipient_type == 'role' and form.recipient_role.data > 0:
            # 指定角色的用户
            role = Role.query.get(form.recipient_role.data)
            if role:
                recipients = [user for user in role.users if user.status]
        elif recipient_type == 'user' and form.recipient_user.data > 0:
            # 指定用户
            user = User.query.get(form.recipient_user.data)
            if user:
                recipients = [user]
        
        # 创建通知
        for recipient in recipients:
            notification = Notification(
                title=title,
                content=content,
                type=notification_type,
                priority=priority,
                recipient_id=recipient.id
            )
            db.session.add(notification)
        
        db.session.commit()
        flash(f'已成功发送通知给{len(recipients)}个用户', 'success')
        return redirect(url_for('notification.index'))
    
    return render_template(
        'notification/create.html',
        form=form
    )

@notification_bp.route('/delete/<int:notification_id>', methods=['POST'])
@login_required
def delete(notification_id):
    """删除通知"""
    notification = Notification.query.get_or_404(notification_id)
    
    # 验证当前用户是否为通知接收者
    if notification.recipient_id != current_user.id:
        flash('您无权删除此通知', 'danger')
        return redirect(url_for('notification.index'))
    
    db.session.delete(notification)
    db.session.commit()
    
    flash('通知已删除', 'success')
    return redirect(url_for('notification.index'))

@notification_bp.route('/api/unread_count')
@login_required
def api_unread_count():
    """API：获取未读通知数量"""
    unread_count = Notification.query.filter(
        Notification.recipient_id == current_user.id,
        Notification.is_read == False
    ).count()
    
    return jsonify({
        'success': True,
        'unread_count': unread_count
    })

@notification_bp.route('/api/latest')
@login_required
def api_latest():
    """API：获取最新通知"""
    limit = request.args.get('limit', 5, type=int)
    
    notifications = Notification.query.filter(
        Notification.recipient_id == current_user.id
    ).order_by(desc(Notification.created_at)).limit(limit).all()
    
    result = []
    for notification in notifications:
        result.append({
            'id': notification.id,
            'title': notification.title,
            'type': notification.type,
            'priority': notification.priority,
            'is_read': notification.is_read,
            'created_at': notification.created_at.strftime('%Y-%m-%d %H:%M')
        })
    
    return jsonify({
        'success': True,
        'notifications': result
    })

# 辅助函数
def update_system_setting(key, value):
    """更新系统设置"""
    setting = SystemSetting.query.filter_by(key=key).first()
    if setting:
        setting.value = value
        setting.updated_by = current_user.id
    else:
        setting = SystemSetting(
            key=key,
            value=value,
            description=f'通知系统设置：{key}',
            updated_by=current_user.id
        )
        db.session.add(setting)
    
    return setting

# 通知创建辅助函数
def create_notification(title, content, notification_type, recipient_id, related_content_id=None, priority='normal'):
    """创建通知的辅助函数"""
    # 检查通知是否启用
    enable_all = SystemSetting.query.filter_by(key='notification_enable_all').first()
    if enable_all and enable_all.value == 'false':
        return None
    
    # 检查特定类型的通知是否启用
    type_key = f'notification_enable_{notification_type}'
    enable_type = SystemSetting.query.filter_by(key=type_key).first()
    if enable_type and enable_type.value == 'false':
        return None
    
    # 创建通知
    notification = Notification(
        title=title,
        content=content,
        type=notification_type,
        recipient_id=recipient_id,
        related_content_id=related_content_id,
        priority=priority
    )
    
    db.session.add(notification)
    db.session.commit()
    
    return notification 