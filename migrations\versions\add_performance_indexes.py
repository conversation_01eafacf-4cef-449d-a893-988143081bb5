"""Add performance indexes

Revision ID: add_performance_indexes
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_performance_indexes'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    """添加性能优化索引"""
    
    # 内容表索引
    # 已存在的索引，避免重复创建
# op.create_index('idx_contents_workflow_status', 'contents', ['workflow_status'])
    # 已存在的索引，避免重复创建
# op.create_index('idx_contents_client_id', 'contents', ['client_id'])

    # 已存在的索引，避免重复创建
# op.create_index('idx_contents_task_id', 'contents', ['task_id'])

    # 已存在的索引，避免重复创建
# op.create_index('idx_contents_batch_id', 'contents', ['batch_id'])

    # 已存在的索引，避免重复创建
# op.create_index('idx_contents_created_at', 'contents', ['created_at'])

    # 已存在的索引，避免重复创建
# op.create_index('idx_contents_display_date', 'contents', ['display_date'])

    # 已存在的索引，避免重复创建
# op.create_index('idx_contents_publish_time', 'contents', ['publish_time'])

    # 已存在的索引，避免重复创建
# op.create_index('idx_contents_client_review_status', 'contents', ['client_review_status'])

    
    # 复合索引
    # 已存在的复合索引，避免重复创建
# op.create_index('idx_contents_workflow_client', 'contents', ['workflow_status', 'client_id'])
    # 已存在的复合索引，避免重复创建
# op.create_index('idx_contents_workflow_created', 'contents', ['workflow_status', 'created_at'])
    # 已存在的复合索引，避免重复创建
# op.create_index('idx_contents_client_created', 'contents', ['client_id', 'created_at'])
    
    # 图片表索引
    # 已存在的索引，避免重复创建
# op.create_index('idx_content_images_content_id', 'content_images', ['content_id'])

    # 已存在的索引，避免重复创建
# op.create_index('idx_content_images_created_at', 'content_images', ['created_at'])

    
    # 内容历史表索引
    op.create_index('idx_content_history_content_id', 'content_history', ['content_id'])
    op.create_index('idx_content_history_created_at', 'content_history', ['created_at'])
    op.create_index('idx_content_history_user_id', 'content_history', ['user_id'])
    
    # 客户分享链接表索引
    op.create_index('idx_client_share_links_client_id', 'client_share_links', ['client_id'])
    op.create_index('idx_client_share_links_share_key', 'client_share_links', ['share_key'])
    op.create_index('idx_client_share_links_is_active', 'client_share_links', ['is_active'])
    op.create_index('idx_client_share_links_expires_at', 'client_share_links', ['expires_at'])
    
    # 任务表索引
    # 已存在的索引，避免重复创建
# op.create_index('idx_tasks_client_id', 'tasks', ['client_id'])

    # 已存在的索引，避免重复创建
# op.create_index('idx_tasks_created_at', 'tasks', ['created_at'])

    # 已存在的索引，避免重复创建
# op.create_index('idx_tasks_status', 'tasks', ['status'])

    
    # 批次表索引
    # 已存在的索引，避免重复创建
# op.create_index('idx_batches_task_id', 'batches', ['task_id'])

    # 已存在的索引，避免重复创建
# op.create_index('idx_batches_created_at', 'batches', ['created_at'])

    
    # 用户表索引
    # 已存在的索引，避免重复创建
# op.create_index('idx_users_username', 'users', ['username'])

    # 已存在的索引，避免重复创建
# op.create_index('idx_users_email', 'users', ['email'])

    # 已存在的索引，避免重复创建
# op.create_index('idx_users_is_active', 'users', ['is_active'])

    
    # 系统设置表索引
    # 已存在的索引，避免重复创建
# op.create_index('idx_system_settings_key', 'system_settings', ['key'])

    # 已存在的索引，避免重复创建
# op.create_index('idx_system_settings_updated_at', 'system_settings', ['updated_at'])


def downgrade():
    """移除性能优化索引"""
    
    # 内容表索引
    op.drop_index('idx_contents_workflow_status')
    op.drop_index('idx_contents_client_id')
    op.drop_index('idx_contents_task_id')
    op.drop_index('idx_contents_batch_id')
    op.drop_index('idx_contents_created_at')
    op.drop_index('idx_contents_display_date')
    op.drop_index('idx_contents_publish_time')
    op.drop_index('idx_contents_client_review_status')
    
    # 复合索引
    op.drop_index('idx_contents_workflow_client')
    op.drop_index('idx_contents_workflow_created')
    op.drop_index('idx_contents_client_created')
    
    # 图片表索引
    op.drop_index('idx_content_images_content_id')
    op.drop_index('idx_content_images_created_at')
    
    # 内容历史表索引
    op.drop_index('idx_content_history_content_id')
    op.drop_index('idx_content_history_created_at')
    op.drop_index('idx_content_history_user_id')
    
    # 客户分享链接表索引
    op.drop_index('idx_client_share_links_client_id')
    op.drop_index('idx_client_share_links_share_key')
    op.drop_index('idx_client_share_links_is_active')
    op.drop_index('idx_client_share_links_expires_at')
    
    # 任务表索引
    op.drop_index('idx_tasks_client_id')
    op.drop_index('idx_tasks_created_at')
    op.drop_index('idx_tasks_status')
    
    # 批次表索引
    op.drop_index('idx_batches_task_id')
    op.drop_index('idx_batches_created_at')
    
    # 用户表索引
    op.drop_index('idx_users_username')
    op.drop_index('idx_users_email')
    op.drop_index('idx_users_is_active')
    
    # 系统设置表索引
    op.drop_index('idx_system_settings_key')
    op.drop_index('idx_system_settings_updated_at')
