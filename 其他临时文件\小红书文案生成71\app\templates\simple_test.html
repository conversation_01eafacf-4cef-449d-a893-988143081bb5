<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试客户任务功能</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>简单测试客户任务功能</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>测试区域</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="client_select" class="form-label">选择客户:</label>
                            <select id="client_select" class="form-select">
                                <option value="">-- 请选择客户 --</option>
                                <option value="1">客户1 (康师傅)</option>
                                <option value="3">客户2 (蜜雪冰城1)</option>
                                <option value="4">客户3 (许府牛)</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="task_select" class="form-label">任务列表:</label>
                            <select id="task_select" class="form-select">
                                <option value="0">-- 创建新任务 --</option>
                            </select>
                        </div>
                        
                        <button type="button" class="btn btn-primary" onclick="testAPI()">手动测试API</button>
                        <button type="button" class="btn btn-secondary" onclick="clearLog()">清空日志</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>调试日志</h5>
                    </div>
                    <div class="card-body">
                        <pre id="debug-log" style="height: 400px; overflow-y: auto; background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; font-size: 12px;">页面加载完成，等待操作...</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const debugLog = document.getElementById('debug-log');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'color: red;' : (type === 'success' ? 'color: green;' : 'color: blue;');
            debugLog.innerHTML += `<span style="${color}">[${timestamp}] ${message}</span>\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
        }
        
        function clearLog() {
            debugLog.innerHTML = '';
        }
        
        // 客户选择变化事件
        document.getElementById('client_select').addEventListener('change', function() {
            const clientId = this.value;
            const clientName = this.options[this.selectedIndex].text;
            
            log(`客户选择变化: ID=${clientId}, 名称=${clientName}`);
            
            if (clientId && clientId !== '') {
                log(`开始获取客户 ${clientId} 的任务列表...`);
                
                const apiUrl = `/simple/contents/get-all-tasks/${clientId}`;
                log(`API URL: ${apiUrl}`);
                
                fetch(apiUrl)
                    .then(response => {
                        log(`API响应状态: ${response.status} ${response.statusText}`);
                        
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        
                        return response.json();
                    })
                    .then(data => {
                        log(`API响应成功`, 'success');
                        log(`响应数据: ${JSON.stringify(data, null, 2)}`, 'success');
                        
                        const taskSelect = document.getElementById('task_select');
                        
                        if (data.success) {
                            // 清空现有选项
                            taskSelect.innerHTML = '';
                            
                            // 添加"创建新任务"选项
                            const createNewOption = document.createElement('option');
                            createNewOption.value = '0';
                            createNewOption.textContent = '-- 创建新任务 --';
                            taskSelect.appendChild(createNewOption);
                            
                            // 添加现有任务选项
                            if (data.tasks && data.tasks.length > 0) {
                                data.tasks.forEach(task => {
                                    const option = document.createElement('option');
                                    option.value = task.id;
                                    option.textContent = task.name;
                                    taskSelect.appendChild(option);
                                });
                                log(`成功加载 ${data.tasks.length} 个任务`, 'success');
                                
                                // 列出所有任务
                                data.tasks.forEach((task, index) => {
                                    log(`  任务${index + 1}: ID=${task.id}, 名称=${task.name}`, 'success');
                                });
                            } else {
                                log('该客户没有任务');
                            }
                            
                            // 默认选择"创建新任务"
                            taskSelect.value = '0';
                        } else {
                            log(`API返回失败: ${data.message || '未知错误'}`, 'error');
                        }
                    })
                    .catch(error => {
                        log(`API请求失败: ${error.message}`, 'error');
                        log(`错误详情: ${error.stack}`, 'error');
                        
                        // 如果获取失败，至少保证有"创建新任务"选项
                        const taskSelect = document.getElementById('task_select');
                        taskSelect.innerHTML = '';
                        const createNewOption = document.createElement('option');
                        createNewOption.value = '0';
                        createNewOption.textContent = '-- 创建新任务 --';
                        taskSelect.appendChild(createNewOption);
                        taskSelect.value = '0';
                    });
            } else {
                log('清空任务选择');
                const taskSelect = document.getElementById('task_select');
                taskSelect.innerHTML = '';
                const createNewOption = document.createElement('option');
                createNewOption.value = '0';
                createNewOption.textContent = '-- 创建新任务 --';
                taskSelect.appendChild(createNewOption);
                taskSelect.value = '0';
            }
        });
        
        // 手动测试API函数
        function testAPI() {
            const clientId = document.getElementById('client_select').value;
            if (!clientId) {
                log('请先选择客户', 'error');
                return;
            }
            
            log(`手动测试API: /simple/contents/get-all-tasks/${clientId}`);
            
            fetch(`/simple/contents/get-all-tasks/${clientId}`)
                .then(response => {
                    log(`手动测试 - 响应状态: ${response.status} ${response.statusText}`);
                    return response.text();
                })
                .then(text => {
                    log(`手动测试 - 响应内容: ${text}`, 'success');
                    try {
                        const data = JSON.parse(text);
                        log(`手动测试 - 解析后的JSON: ${JSON.stringify(data, null, 2)}`, 'success');
                    } catch (e) {
                        log(`手动测试 - JSON解析失败: ${e.message}`, 'error');
                        log(`手动测试 - 原始响应可能是HTML: ${text.substring(0, 200)}...`, 'error');
                    }
                })
                .catch(error => {
                    log(`手动测试 - 请求失败: ${error.message}`, 'error');
                });
        }
        
        // 页面加载完成
        log('页面初始化完成');
    </script>
</body>
</html>
