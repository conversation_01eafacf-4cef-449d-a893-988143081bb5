{% extends "base.html" %}

{% block title %}客户管理{% endblock %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
<style>
    #clientFilterForm .form-control:focus,
    #clientFilterForm .form-select:focus {
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        border-color: #86b7fe;
    }
    
    /* 加载指示器样式 */
    #loading-indicator {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
        display: none !important; /* 强制隐藏加载指示器 */
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }
</style>
{% endblock %}

{% block content_auth %}
<!-- 加载指示器 -->
<div id="loading-indicator">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
</div>

<div class="container-fluid py-4" id="client-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">客户管理</h2>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createClientModal">
            <i class="fas fa-plus"></i> 新增客户
        </button>
    </div>
    <div class="card mb-4">
        <div class="card-header bg-light">
            <form id="clientFilterForm" class="row g-3" method="GET">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" value="{{ search }}" placeholder="搜索客户名称、联系人、电话、邮箱">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="status" id="statusFilter">
                        <option value="">全部状态</option>
                        <option value="active" {% if status == 'active' %}selected{% endif %}>启用</option>
                        <option value="inactive" {% if status == 'inactive' %}selected{% endif %}>禁用</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="sort_by" id="sortByFilter">
                        <option value="created_at" {% if sort_by == 'created_at' %}selected{% endif %}>创建时间</option>
                        <option value="name" {% if sort_by == 'name' %}selected{% endif %}>客户名称</option>
                        <option value="updated_at" {% if sort_by == 'updated_at' %}selected{% endif %}>更新时间</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="sort_order" id="sortOrderFilter">
                        <option value="desc" {% if sort_order == 'desc' %}selected{% endif %}>降序</option>
                        <option value="asc" {% if sort_order == 'asc' %}selected{% endif %}>升序</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary flex-grow-1">筛选</button>
                        <button type="button" class="btn btn-outline-secondary" id="resetFilterBtn">重置</button>
                    </div>
                </div>
            </form>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>ID</th>
                            <th>客户名称</th>
                            <th>联系人</th>
                            <th>联系方式</th>
                            <th>每日展示数量</th>
                            <th>需要客户审核</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="clientTableBody">
                        {% for client in clients %}
                        <tr>
                            <td>{{ client.id }}</td>
                            <td>{{ client.name }}</td>
                            <td>{{ client.contact or '-' }}</td>
                            <td>
                                {% if client.phone %}电话: {{ client.phone }}<br>{% endif %}
                                {% if client.email %}邮箱: {{ client.email }}{% endif %}
                                {% if not client.phone and not client.email %}-{% endif %}
                            </td>
                            <td>{{ client.daily_content_count }}</td>
                            <td>
                                <button type="button" class="btn btn-sm p-0 border-0 toggle-review-btn" data-client-id="{{ client.id }}" data-status="{{ client.need_review|tojson }}" onclick="toggleClientReview('{{ client.id }}', {{ client.need_review|tojson }})" style="cursor: pointer;">
                                    {% if client.need_review %}
                                    <span class="badge bg-primary">是</span>
                                    {% else %}
                                    <span class="badge bg-secondary">否</span>
                                    {% endif %}
                                </button>
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm p-0 border-0 toggle-status-btn" data-client-id="{{ client.id }}" data-status="{{ client.status|tojson }}" onclick="toggleClientStatus('{{ client.id }}', {{ client.status|tojson }})" style="cursor: pointer;">
                                    {% if client.status %}
                                    <span class="badge bg-success">启用</span>
                                    {% else %}
                                    <span class="badge bg-danger">禁用</span>
                                    {% endif %}
                                </button>
                            </td>
                            <td>{{ client.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-primary" onclick="showEditModal({{ client.id }})" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="showSharesModal({{ client.id }})" title="分享链接">
                                        <i class="fas fa-share-alt"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="showUsageModal({{ client.id }})" title="使用记录">
                                        <i class="fas fa-history"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" onclick="deleteClient({{ client.id }}, '{{ client.name }}')" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="9" class="text-center py-4">暂无客户数据</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% if pagination.pages > 1 %}
        <div class="card-footer">
            <nav>
                <ul class="pagination justify-content-center mb-0" id="clientPagination">
                    {% if pagination.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="#" data-page="{{ pagination.prev_num }}" onclick="loadClientData({{ pagination.prev_num }}); return false;">
                            <span>&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">&laquo;</span>
                    </li>
                    {% endif %}
                    {% for page in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                        {% if page %}
                            {% if page == pagination.page %}
                            <li class="page-item active">
                                <span class="page-link">{{ page }}</span>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="#" data-page="{{ page }}" onclick="loadClientData({{ page }}); return false;">{{ page }}</a>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    {% if pagination.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="#" data-page="{{ pagination.next_num }}" onclick="loadClientData({{ pagination.next_num }}); return false;">
                            <span>&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">&raquo;</span>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>
<!-- 新增客户模态框 -->
<div class="modal fade" id="createClientModal" tabindex="-1" aria-labelledby="createClientModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createClientModalLabel">
                    <i class="fas fa-plus"></i> 新增客户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createClientForm" method="post" action="{{ url_for('client.client_create') }}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name" class="form-label">客户名称 *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="contact" class="form-label">联系人</label>
                                <input type="text" class="form-control" id="contact" name="contact">
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone" class="form-label">联系电话</label>
                                <input type="text" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email" class="form-label">邮箱地址</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="daily_content_count" class="form-label">每日文案数量</label>
                                <input type="number" class="form-control" id="daily_content_count" name="daily_content_count" value="5" min="1">
                                <small class="form-text text-muted">每日向客户展示的文案数量</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="display_start_time" class="form-label">开始展示时间</label>
                                <input type="time" class="form-control" id="display_start_time" name="display_start_time" value="08:30">
                                <small class="form-text text-muted">每日开始展示文案的时间，留空表示全天</small>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="interval_min" class="form-label">最小间隔（分钟）</label>
                                <input type="number" class="form-control" id="interval_min" name="interval_min" value="10" min="1">
                                <small class="form-text text-muted">两条文案展示之间的最小间隔时间</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="interval_max" class="form-label">最大间隔（分钟）</label>
                                <input type="number" class="form-control" id="interval_max" name="interval_max" value="30" min="1">
                                <small class="form-text text-muted">两条文案展示之间的最大间隔时间</small>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="address" class="form-label">地址</label>
                                <input type="text" class="form-control" id="address" name="address">
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="remark" class="form-label">备注</label>
                                <textarea class="form-control" id="remark" name="remark" rows="3"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="need_review" name="need_review" value="1" checked>
                                <label class="form-check-label" for="need_review">需要审核</label>
                                <div class="form-text text-muted">启用后，文案需要客户审核才能发布</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="status" name="status" value="1" checked>
                                <label class="form-check-label" for="status">启用状态</label>
                                <div class="form-text text-muted">禁用后，客户将无法访问系统</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button type="button" class="btn btn-primary" id="submitCreateClientBtn">
                    <i class="fas fa-plus"></i> 创建客户
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑客户模态框 -->
<div class="modal fade" id="editClientModal" tabindex="-1" aria-labelledby="editClientModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editClientModalLabel">
                    <i class="fas fa-edit"></i> 编辑客户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="editClientContent">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分享链接模态框 -->
<div class="modal fade" id="sharesModal" tabindex="-1" aria-labelledby="sharesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sharesModalLabel">
                    <i class="fas fa-share-alt"></i> 分享链接
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="sharesContent">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 使用记录模态框 -->
<div class="modal fade" id="usageModal" tabindex="-1" aria-labelledby="usageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="usageModalLabel">
                    <i class="fas fa-history"></i> 使用记录
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="usageContent">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 立即执行函数，确保加载指示器在页面加载完成后立即隐藏
(function() {
    console.log('客户管理页面加载完成，立即隐藏加载指示器');
    // 确保加载指示器隐藏
    hideLoadingIndicator();
    
    // 添加CSS规则强制隐藏主页面加载指示器，但保留按钮的加载状态
    addHideLoadingStyle();
})();

// 添加CSS规则强制隐藏加载指示器
function addHideLoadingStyle() {
    const style = document.createElement('style');
    style.textContent = `
        #loading-indicator {
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
        }
        /* 确保按钮内的加载状态正常显示 */
        button .spinner-border, 
        .btn .spinner-border {
            display: inline-block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
    `;
    document.head.appendChild(style);
    console.log('添加了强制隐藏加载指示器的CSS规则');
}

// 隐藏加载指示器的通用函数
function hideLoadingIndicator() {
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
        loadingIndicator.style.opacity = '0';
        loadingIndicator.style.visibility = 'hidden';
        console.log('加载指示器已隐藏');
    }
}

    // 切换客户状态
    function toggleClientStatus(clientId, currentStatus) {
    console.log('切换客户状态:', clientId, currentStatus);
        
        // 发送请求
        fetch(`/clients/${clientId}/toggle_status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
            // 刷新当前页面内容
            loadClientData(getCurrentPage());
            showToast(`客户状态已${data.status ? '启用' : '禁用'}`);
                } else {
            showToast('操作失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('请求失败:', error);
        showToast('操作失败，请重试', 'danger');
        });
    }

    // 切换客户审核状态
    function toggleClientReview(clientId, currentStatus) {
    console.log('切换客户审核状态:', clientId, currentStatus);
        
        // 发送请求
        fetch(`/clients/${clientId}/toggle_review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
            // 刷新当前页面内容
            loadClientData(getCurrentPage());
            showToast(`客户审核状态已${data.need_review ? '开启' : '关闭'}`);
                } else {
            showToast('操作失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('请求失败:', error);
        showToast('操作失败，请重试', 'danger');
        });
    }

    // 删除客户
    function deleteClient(clientId, clientName) {
        if (confirm(`确定要删除客户 "${clientName}" 吗？此操作不可恢复。`)) {
            fetch(`/clients/${clientId}/delete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 删除成功，刷新页面
                loadClientData(getCurrentPage());
                showToast('客户删除成功');
                } else {
                showToast('删除失败: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
            showToast('删除失败，请重试', 'danger');
        });
    }
}

// 获取当前页码
function getCurrentPage() {
    const activePage = document.querySelector('.page-item.active .page-link');
    if (activePage) {
        return parseInt(activePage.textContent);
    }
    return 1; // 默认返回第一页
}

// 加载客户数据函数
function loadClientData(page) {
    console.log('加载客户数据，页码:', page);
    
    // 获取表单数据
    const filterForm = document.getElementById('clientFilterForm');
    let params;
    
    if (filterForm) {
        // 如果表单存在，使用FormData
        const formData = new FormData(filterForm);
        formData.append('page', page);
        params = new URLSearchParams(formData);
        
        // 记录筛选参数
        console.log('筛选参数:', Object.fromEntries(params.entries()));
    } else {
        // 如果表单不存在，手动构建查询参数
        params = new URLSearchParams();
        params.append('page', page);
        
        // 尝试从URL获取当前筛选条件
        const currentUrl = new URL(window.location.href);
        const searchParams = currentUrl.searchParams;
        
        // 复制现有参数
        for (const [key, value] of searchParams.entries()) {
            if (key !== 'page') { // 不复制页码，使用新传入的页码
                params.append(key, value);
            }
        }
        
        console.log('从URL获取的筛选参数:', Object.fromEntries(params.entries()));
    }
    
    // 更新URL以反映筛选条件
    const newUrl = new URL(window.location.href);
    newUrl.search = params.toString();
    window.history.replaceState({}, '', newUrl);
    
    fetch(`/clients/?${params.toString()}`, {
        method: 'GET',
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.text();
    })
    .then(html => {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        const newContent = tempDiv.querySelector('.container-fluid.py-4');
        
        if (newContent) {
            const clientContainer = document.getElementById('client-container');
            if (clientContainer) {
                clientContainer.innerHTML = newContent.innerHTML;
                console.log('客户数据加载完成');

                // 数据加载完成后，重新绑定所有按钮事件
                setTimeout(function() {
                    rebindAllButtonEvents();
                    console.log('加载数据后重新绑定了按钮事件');
                }, 100);
            } else {
                console.error('找不到client-container元素');
                throw new Error('找不到客户容器元素');
            }
        } else {
            throw new Error('无法找到内容区域');
        }
    })
    .catch(error => {
        console.error('加载客户数据失败:', error);
        showToast('加载失败: ' + error.message, 'danger');
    });
}

// 显示编辑客户模态框
function showEditModal(clientId) {
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('editClientModal'));
    modal.show();
    
    // 加载编辑表单内容
    fetch(`/clients/${clientId}/edit`, {
        method: 'GET',
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => response.text())
    .then(html => {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        
        // 提取表单内容
        const formContent = tempDiv.querySelector('form');
        if (formContent) {
            document.getElementById('editClientContent').innerHTML = '';
            document.getElementById('editClientContent').appendChild(formContent);

            // 修改表单提交方式为AJAX
            const editForm = document.getElementById('editClientContent').querySelector('form');

            // 确保表单有正确的action属性
            if (!editForm.action || editForm.action.includes('?')) {
                editForm.action = `/clients/${clientId}/edit`;
                console.log('设置表单action为:', editForm.action);
            }

            // 添加data-no-ajax属性，防止被其他系统拦截
            editForm.setAttribute('data-no-ajax', 'true');
            editForm.setAttribute('data-client-edit-form', 'true');
            // 移除可能存在的data-ajax-form属性
            editForm.removeAttribute('data-ajax-form');

            // 修改"返回列表"按钮为"取消"按钮
            const backButton = editForm.querySelector('a[href*="client_list"]');
            if (backButton) {
                backButton.outerHTML = '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><i class="fas fa-times"></i> 取消</button>';
            }

            editForm.addEventListener('submit', function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('编辑表单提交，clientId:', clientId);

                const formData = new FormData(editForm);
                const submitUrl = `/clients/${clientId}/edit`;
                console.log('提交到URL:', submitUrl);

                fetch(submitUrl, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // 关闭模态框
                        modal.hide();
                        // 刷新客户列表
                        loadClientData(getCurrentPage());
                        // 显示成功消息
                        showToast('客户信息更新成功');
                    } else {
                        showToast('更新失败: ' + (data.message || '未知错误'), 'danger');
                    }
                })
                .catch(error => {
                    console.error('编辑表单提交失败:', error);
                    showToast('请求失败: ' + error.message, 'danger');
                });
            });
        } else {
            document.getElementById('editClientContent').innerHTML = '<div class="alert alert-danger">无法加载编辑表单</div>';
        }
    })
    .catch(error => {
        document.getElementById('editClientContent').innerHTML = `<div class="alert alert-danger">加载失败: ${error.message}</div>`;
    });
}

// 显示分享链接模态框
function showSharesModal(clientId) {
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('sharesModal'));
    modal.show();
    
    // 加载分享链接内容
    fetch(`/clients/${clientId}/shares`, {
        method: 'GET',
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => response.text())
    .then(html => {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        
        // 提取内容区域
        const content = tempDiv.querySelector('.container-fluid') || tempDiv.querySelector('.card');
        if (content) {
            document.getElementById('sharesContent').innerHTML = '';
            document.getElementById('sharesContent').appendChild(content);
        } else {
            document.getElementById('sharesContent').innerHTML = '<div class="alert alert-info">没有找到分享链接</div>';
        }
    })
    .catch(error => {
        document.getElementById('sharesContent').innerHTML = `<div class="alert alert-danger">加载失败: ${error.message}</div>`;
    });
}

// 显示使用记录模态框
function showUsageModal(clientId) {
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('usageModal'));
    modal.show();
    
    // 加载使用记录内容
    fetch(`/clients/${clientId}/usage`, {
        method: 'GET',
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => response.text())
    .then(html => {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        
        // 提取内容区域
        const content = tempDiv.querySelector('.container-fluid') || tempDiv.querySelector('.card');
        if (content) {
            document.getElementById('usageContent').innerHTML = '';
            document.getElementById('usageContent').appendChild(content);
        } else {
            document.getElementById('usageContent').innerHTML = '<div class="alert alert-info">没有使用记录</div>';
        }
    })
    .catch(error => {
        document.getElementById('usageContent').innerHTML = `<div class="alert alert-danger">加载失败: ${error.message}</div>`;
    });
}

// 显示提示信息
function showToast(message, type = 'success') {
    const toastContainer = document.createElement('div');
    toastContainer.className = 'position-fixed top-0 end-0 p-3';
    toastContainer.style.zIndex = '9999';
    
    toastContainer.innerHTML = `
        <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2"></i> ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;
    
    document.body.appendChild(toastContainer);
    
    const toast = new bootstrap.Toast(toastContainer.querySelector('.toast'), {
        autohide: true,
        delay: 3000
    });
    toast.show();
    
    setTimeout(() => {
        document.body.removeChild(toastContainer);
    }, 3500);
}

// 绑定重置按钮事件和确保加载指示器初始隐藏
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，初始化客户管理功能');
    
    // 确保加载指示器隐藏
    hideLoadingIndicator();
    
    // 重新绑定所有按钮的事件处理函数
    rebindAllButtonEvents();
    
    // 绑定重置按钮事件
    document.body.addEventListener('click', function(e) {
        // 使用事件委托处理重置按钮点击
        if (e.target && e.target.id === 'resetFilterBtn') {
            e.preventDefault();
            console.log('重置按钮被点击');
            
            // 获取表单
            const filterForm = document.getElementById('clientFilterForm');
            if (filterForm) {
                // 清空所有筛选条件
                filterForm.querySelector('input[name="search"]').value = '';
                filterForm.querySelector('#statusFilter').value = '';
                filterForm.querySelector('#sortByFilter').value = 'created_at';
                filterForm.querySelector('#sortOrderFilter').value = 'desc';
                
                // 加载重置后的数据
                loadClientData(1);
            } else {
                // 如果找不到表单，直接跳转到客户列表页
                window.location.href = '/clients/';
            }
        }
    });
    
    // 注意：表单提交事件已移动到client-management.js中统一处理
});

// 重新绑定所有按钮的事件处理函数
function rebindAllButtonEvents() {
    console.log('重新绑定所有按钮的事件处理函数');
    
    // 重新绑定状态切换按钮
    document.querySelectorAll('.toggle-status-btn').forEach(btn => {
        const clientId = btn.getAttribute('data-client-id');
        const status = JSON.parse(btn.getAttribute('data-status'));
        btn.onclick = function() {
            toggleClientStatus(clientId, status);
        };
    });
    
    // 重新绑定审核状态切换按钮
    document.querySelectorAll('.toggle-review-btn').forEach(btn => {
        const clientId = btn.getAttribute('data-client-id');
        const status = JSON.parse(btn.getAttribute('data-status'));
        btn.onclick = function() {
            toggleClientReview(clientId, status);
        };
    });
    
    // 重新绑定编辑按钮
    document.querySelectorAll('.btn-outline-primary[title="编辑"]').forEach(btn => {
        btn.onclick = function() {
            const clientId = this.closest('tr').querySelector('td:first-child').textContent.trim();
            showEditModal(clientId);
        };
    });
    
    // 重新绑定分享链接按钮
    document.querySelectorAll('.btn-outline-info[title="分享链接"]').forEach(btn => {
        btn.onclick = function() {
            const clientId = this.closest('tr').querySelector('td:first-child').textContent.trim();
            showSharesModal(clientId);
        };
    });
    
    // 重新绑定使用记录按钮
    document.querySelectorAll('.btn-outline-secondary[title="使用记录"]').forEach(btn => {
        btn.onclick = function() {
            const clientId = this.closest('tr').querySelector('td:first-child').textContent.trim();
            showUsageModal(clientId);
        };
    });
    
    // 重新绑定删除按钮
    document.querySelectorAll('.btn-outline-danger[title="删除"]').forEach(btn => {
        btn.onclick = function() {
            const clientId = this.closest('tr').querySelector('td:first-child').textContent.trim();
            const clientName = this.closest('tr').querySelector('td:nth-child(2)').textContent.trim();
            deleteClient(clientId, clientName);
        };
    });
    
    // 绑定新增客户按钮
    const submitCreateClientBtn = document.getElementById('submitCreateClientBtn');
    if (submitCreateClientBtn) {
        submitCreateClientBtn.onclick = function() {
            console.log('提交新增客户表单');
            const form = document.getElementById('createClientForm');
            if (form) {
                const formData = new FormData(form);
                
                // 确保使用正确的URL
                const createUrl = form.getAttribute('action') || '/clients/create';
                console.log('提交表单到URL:', createUrl);
                
                // 发送请求到正确的URL
                fetch(createUrl, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // 关闭模态框
                        const modal = bootstrap.Modal.getInstance(document.getElementById('createClientModal'));
                        if (modal) modal.hide();
                        
                        // 刷新客户列表
                        loadClientData(1);
                        
                        // 显示成功消息
                        showToast('客户创建成功');
                        
                        // 重置表单
                        form.reset();
                    } else {
                        showToast('创建失败: ' + (data.message || '未知错误'), 'danger');
                    }
                })
                .catch(error => {
                    console.error('提交失败:', error);
                    showToast('提交失败，请重试: ' + error.message, 'danger');
                });
            }
        };
        console.log('已绑定新增客户按钮事件');
    }
    
    console.log('所有按钮事件已重新绑定');
}

// 将rebindAllButtonEvents函数暴露给全局作用域，以便标签系统可以调用
window.rebindAllButtonEvents = rebindAllButtonEvents;

// 兼容标签系统的特殊处理
if (window.tabs && typeof window.tabs.onContentLoaded === 'function') {
    // 如果存在标签系统，注册内容加载完成后的回调
    console.log('检测到标签系统，注册内容加载回调');
    window.tabs.onContentLoaded(function(url) {
        if (url.includes('/clients/')) {
            console.log('标签系统加载了客户管理页面，强制隐藏加载指示器');
            setTimeout(hideLoadingIndicator, 0);
            setTimeout(hideLoadingIndicator, 100);
            setTimeout(hideLoadingIndicator, 500);
            
            // 重新绑定按钮事件
            setTimeout(function() {
                rebindAllButtonEvents();
                console.log('标签系统加载后重新绑定了按钮事件');
            }, 200);
        }
    });
}

// 处理标签系统的特殊情况
if (window.location.href.includes('/clients/')) {
    console.log('当前在客户管理页面，设置定时器确保加载指示器隐藏');
    // 使用多个定时器确保在各种情况下都能隐藏加载指示器
    setTimeout(hideLoadingIndicator, 0);
    setTimeout(hideLoadingIndicator, 100);
    setTimeout(hideLoadingIndicator, 500);
    setTimeout(hideLoadingIndicator, 1000);
}

// 监听DOM变化
(function() {
    try {
        // 创建一个观察器实例
        const observer = new MutationObserver(function() {
            hideLoadingIndicator();
        });

        // 配置观察选项
        const config = { 
            childList: true,
            subtree: true,
            attributes: true
        };

        // 开始观察
        setTimeout(function() {
            const targetNode = document.body;
            if (targetNode) {
                observer.observe(targetNode, config);
                console.log('DOM观察器已启动');
            }
        }, 100);
    } catch (error) {
        console.error('设置DOM观察器失败:', error);
    }
})();

// 将所有函数暴露到全局作用域，以便标签系统可以调用它们
window.hideLoadingIndicator = hideLoadingIndicator;
window.toggleClientStatus = toggleClientStatus;
window.toggleClientReview = toggleClientReview;
window.deleteClient = deleteClient;
window.getCurrentPage = getCurrentPage;
window.loadClientData = loadClientData;
window.showEditModal = showEditModal;
window.showSharesModal = showSharesModal;
window.showUsageModal = showUsageModal;
window.showToast = showToast;
</script>
{% endblock %}