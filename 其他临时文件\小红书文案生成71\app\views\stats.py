"""
数据统计功能视图
"""
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from sqlalchemy import func, desc, and_, case, extract
from sqlalchemy.sql import text

from app.models import db
from app.models.content import Content
from app.models.task import Task, Batch
from app.models.client import Client
from app.models.user import User
from app.models.template import Template
from app.utils.decorators import permission_required, ajax_aware

# 创建蓝图
stats_bp = Blueprint('stats', __name__)


@stats_bp.route('/')
@login_required
@permission_required('admin_access')
@ajax_aware
def index():
    """统计概览页面"""
    # 获取基础统计数据
    content_count = Content.query.count()
    task_count = Task.query.count()
    client_count = Client.query.count()
    user_count = User.query.count()
    template_count = Template.query.count()
    
    # 文案状态分布
    status_counts = db.session.query(
        Content.workflow_status,
        func.count(Content.id).label('count')
    ).group_by(Content.workflow_status).all()
    
    status_data = {status: count for status, count in status_counts}
    
    # 最近7天文案创建趋势
    today = datetime.now().date()
    seven_days_ago = today - timedelta(days=6)
    
    daily_contents = db.session.query(
        func.date(Content.created_at).label('date'),
        func.count(Content.id).label('count')
    ).filter(
        func.date(Content.created_at) >= seven_days_ago,
        func.date(Content.created_at) <= today
    ).group_by(
        func.date(Content.created_at)
    ).order_by(
        func.date(Content.created_at)
    ).all()
    
    # 填充缺失的日期
    date_counts = {result.date: result.count for result in daily_contents}
    trend_data = []
    
    for i in range(7):
        date = seven_days_ago + timedelta(days=i)
        count = date_counts.get(date, 0)
        trend_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'count': count
        })
    
    context = {
        'title': '数据统计 - 小红书文案生成系统',
        'content_count': content_count,
        'task_count': task_count,
        'client_count': client_count,
        'user_count': user_count,
        'template_count': template_count,
        'status_data': status_data,
        'trend_data': trend_data
    }
    
    return 'stats/index.html', context


@stats_bp.route('/content')
@login_required
@permission_required('admin_access')
@ajax_aware
def content_stats():
    """文案统计页面"""
    # 获取时间范围参数
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    # 转换为日期对象
    start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
    end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # 按工作流状态统计
    workflow_stats = db.session.query(
        Content.workflow_status,
        func.count(Content.id).label('count')
    ).filter(
        func.date(Content.created_at) >= start_date_obj,
        func.date(Content.created_at) <= end_date_obj
    ).group_by(Content.workflow_status).all()
    
    # 按发布状态统计
    publish_stats = db.session.query(
        Content.publish_status,
        func.count(Content.id).label('count')
    ).filter(
        func.date(Content.created_at) >= start_date_obj,
        func.date(Content.created_at) <= end_date_obj
    ).group_by(Content.publish_status).all()
    
    # 按客户统计文案数量
    client_stats = db.session.query(
        Client.name,
        func.count(Content.id).label('count')
    ).join(Content, Content.client_id == Client.id).filter(
        func.date(Content.created_at) >= start_date_obj,
        func.date(Content.created_at) <= end_date_obj
    ).group_by(Client.id).order_by(desc('count')).limit(10).all()
    
    # 按审核人员统计审核数量
    reviewer_stats = db.session.query(
        User.username,
        func.count(Content.id).label('count')
    ).join(Content, Content.reviewer_id == User.id).filter(
        func.date(Content.review_time) >= start_date_obj,
        func.date(Content.review_time) <= end_date_obj,
        Content.reviewer_id.isnot(None)
    ).group_by(User.id).order_by(desc('count')).limit(10).all()
    
    context = {
        'title': '文案统计 - 小红书文案生成系统',
        'start_date': start_date,
        'end_date': end_date,
        'workflow_stats': workflow_stats,
        'publish_stats': publish_stats,
        'client_stats': client_stats,
        'reviewer_stats': reviewer_stats
    }
    
    return 'stats/content.html', context


@stats_bp.route('/task')
@login_required
@permission_required('admin_access')
@ajax_aware
def task_stats():
    """任务统计页面"""
    # 获取时间范围参数
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    # 转换为日期对象
    start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
    end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # 任务状态统计
    task_status_stats = db.session.query(
        Task.status,
        func.count(Task.id).label('count')
    ).filter(
        func.date(Task.created_at) >= start_date_obj,
        func.date(Task.created_at) <= end_date_obj
    ).group_by(Task.status).all()
    
    # 按客户统计任务数量
    client_task_stats = db.session.query(
        Client.name,
        func.count(Task.id).label('count')
    ).join(Task, Task.client_id == Client.id).filter(
        func.date(Task.created_at) >= start_date_obj,
        func.date(Task.created_at) <= end_date_obj
    ).group_by(Client.id).order_by(desc('count')).limit(10).all()
    
    # 任务完成率统计
    task_completion_stats = db.session.query(
        Task.id,
        Task.name,
        Task.target_count,
        Task.actual_count,
        (func.cast(Task.actual_count, db.Float) / func.nullif(Task.target_count, 0) * 100).label('completion_rate')
    ).filter(
        func.date(Task.created_at) >= start_date_obj,
        func.date(Task.created_at) <= end_date_obj,
        Task.target_count > 0
    ).order_by(desc('completion_rate')).limit(10).all()
    
    context = {
        'title': '任务统计 - 小红书文案生成系统',
        'start_date': start_date,
        'end_date': end_date,
        'task_status_stats': task_status_stats,
        'client_task_stats': client_task_stats,
        'task_completion_stats': task_completion_stats
    }
    
    return 'stats/task.html', context


@stats_bp.route('/user')
@login_required
@permission_required('admin_access')
@ajax_aware
def user_stats():
    """用户统计页面"""
    # 获取时间范围参数
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    # 转换为日期对象
    start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
    end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # 用户创建文案统计
    user_content_stats = db.session.query(
        User.username,
        func.count(Content.id).label('count')
    ).join(Content, Content.created_by == User.id).filter(
        func.date(Content.created_at) >= start_date_obj,
        func.date(Content.created_at) <= end_date_obj
    ).group_by(User.id).order_by(desc('count')).limit(10).all()
    
    # 用户审核文案统计
    user_review_stats = db.session.query(
        User.username,
        func.count(Content.id).label('count')
    ).join(Content, Content.reviewer_id == User.id).filter(
        func.date(Content.review_time) >= start_date_obj,
        func.date(Content.review_time) <= end_date_obj,
        Content.reviewer_id.isnot(None)
    ).group_by(User.id).order_by(desc('count')).limit(10).all()
    
    # 用户创建任务统计
    user_task_stats = db.session.query(
        User.username,
        func.count(Task.id).label('count')
    ).join(Task, Task.created_by == User.id).filter(
        func.date(Task.created_at) >= start_date_obj,
        func.date(Task.created_at) <= end_date_obj
    ).group_by(User.id).order_by(desc('count')).limit(10).all()
    
    context = {
        'title': '用户统计 - 小红书文案生成系统',
        'start_date': start_date,
        'end_date': end_date,
        'user_content_stats': user_content_stats,
        'user_review_stats': user_review_stats,
        'user_task_stats': user_task_stats
    }
    
    return 'stats/user.html', context


@stats_bp.route('/api/chart-data')
@login_required
@permission_required('admin_access')
def api_chart_data():
    """获取图表数据的API"""
    chart_type = request.args.get('type', 'content_trend')
    
    if chart_type == 'content_trend':
        # 最近30天文案创建趋势
        today = datetime.now().date()
        thirty_days_ago = today - timedelta(days=29)
        
        daily_contents = db.session.query(
            func.date(Content.created_at).label('date'),
            func.count(Content.id).label('count')
        ).filter(
            func.date(Content.created_at) >= thirty_days_ago,
            func.date(Content.created_at) <= today
        ).group_by(
            func.date(Content.created_at)
        ).order_by(
            func.date(Content.created_at)
        ).all()
        
        # 填充缺失的日期
        date_counts = {result.date.strftime('%Y-%m-%d'): result.count for result in daily_contents}
        trend_data = []
        
        for i in range(30):
            date = thirty_days_ago + timedelta(days=i)
            date_str = date.strftime('%Y-%m-%d')
            count = date_counts.get(date_str, 0)
            trend_data.append({
                'date': date_str,
                'count': count
            })
        
        return jsonify({
            'labels': [item['date'] for item in trend_data],
            'data': [item['count'] for item in trend_data]
        })
    
    elif chart_type == 'status_distribution':
        # 文案状态分布
        status_counts = db.session.query(
            Content.workflow_status,
            func.count(Content.id).label('count')
        ).group_by(Content.workflow_status).all()
        
        return jsonify({
            'labels': [status for status, _ in status_counts],
            'data': [count for _, count in status_counts]
        })
    
    return jsonify({'error': '未知的图表类型'}) 