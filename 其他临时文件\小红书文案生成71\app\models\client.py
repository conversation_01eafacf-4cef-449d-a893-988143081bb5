"""
客户相关模型
"""
from datetime import datetime
import json
from . import db


class Client(db.Model):
    """客户模型"""
    __tablename__ = 'clients'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    contact = db.Column(db.String(50))  # 联系人
    phone = db.Column(db.String(20))
    email = db.Column(db.String(100))
    need_review = db.Column(db.<PERSON>, default=True)  # 是否需要客户审核
    daily_content_count = db.Column(db.Integer, default=5)  # 每日展示数量
    display_start_time = db.Column(db.Time)  # 展示开始时间
    interval_min = db.Column(db.Integer, default=30)  # 最小间隔时间（分钟）
    interval_max = db.Column(db.Integer, default=120)  # 最大间隔时间（分钟）
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    status = db.Column(db.Boolean, default=True)  # True为启用，False为禁用
    ext_json = db.Column(db.Text)  # 扩展字段（JSON格式）
    
    # 关联
    tasks = db.relationship('Task', backref='client', lazy='dynamic')
    contents = db.relationship('Content', backref='client', lazy='dynamic')
    shares = db.relationship('ClientShare', backref='client', lazy='dynamic')
    
    @property
    def ext_data(self):
        """获取扩展数据"""
        if self.ext_json:
            try:
                return json.loads(self.ext_json)
            except:
                return {}
        return {}
    
    @ext_data.setter
    def ext_data(self, value):
        """设置扩展数据"""
        if value:
            self.ext_json = json.dumps(value)
        else:
            self.ext_json = None
    
    def __repr__(self):
        return f'<Client {self.name}>'


class ClientShare(db.Model):
    """客户分享链接模型"""
    __tablename__ = 'client_shares'
    
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
    access_token = db.Column(db.String(100), nullable=False, unique=True)
    password = db.Column(db.String(20))  # 访问密码
    expires_at = db.Column(db.DateTime)  # 过期时间，NULL表示永久有效
    view_permission = db.Column(db.Boolean, default=True)  # 查看权限
    edit_permission = db.Column(db.Boolean, default=True)  # 编辑权限
    review_permission = db.Column(db.Boolean, default=True)  # 审核权限
    created_at = db.Column(db.DateTime, default=datetime.now)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # 创建者关联
    creator = db.relationship('User', backref=db.backref('client_shares', lazy='dynamic'))
    
    def is_valid(self):
        """检查链接是否有效"""
        if self.expires_at is None:
            return True
        return self.expires_at > datetime.now()
    
    def __repr__(self):
        return f'<ClientShare {self.client_id}>'


class ClientShareLink(db.Model):
    """客户分享链接模型（新版）"""
    __tablename__ = 'client_share_links'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
    share_key = db.Column(db.String(64), unique=True, nullable=False)
    expires_at = db.Column(db.DateTime)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # 关联关系
    client = db.relationship('Client', backref=db.backref('share_links', lazy='dynamic'))

    def __repr__(self):
        return f'<ClientShareLink {self.share_key}>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'client_id': self.client_id,
            'share_key': self.share_key,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_expired': self.is_expired
        }

    @property
    def is_expired(self):
        """检查是否已过期"""
        if not self.expires_at:
            return False
        return self.expires_at < datetime.now()

    @property
    def share_url(self):
        """获取分享URL"""
        from flask import url_for
        return url_for('client_review.review_page', share_key=self.share_key, _external=True)

    @property
    def days_remaining(self):
        """剩余天数"""
        if not self.expires_at:
            return None
        delta = self.expires_at - datetime.now()
        return max(0, delta.days)