"""Add is_deleted, deleted_at, deleted_by fields to Content model and make task_id, client_id, batch_id nullable

Revision ID: cc0c4215bd3d
Revises: 
Create Date: 2025-07-13 13:27:25.207273

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'cc0c4215bd3d'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('batches', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='批次表'
    )

    with op.batch_alter_table('client_shares', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='客户分享链接表'
    )

    with op.batch_alter_table('clients', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='客户表'
    )

    with op.batch_alter_table('content_history', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='文案历史表'
    )

    with op.batch_alter_table('contents', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_deleted', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('deleted_at', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('deleted_by', sa.Integer(), nullable=True))
        batch_op.alter_column('client_id',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=True)
        batch_op.alter_column('task_id',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=True)
        batch_op.alter_column('batch_id',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=True)
        batch_op.create_index(batch_op.f('ix_contents_is_deleted'), ['is_deleted'], unique=False)
        batch_op.create_foreign_key(None, 'users', ['deleted_by'], ['id'])
        batch_op.drop_table_comment(
        existing_comment='文案表'
    )

    with op.batch_alter_table('notifications', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='通知表'
    )

    with op.batch_alter_table('permissions', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='权限表'
    )

    with op.batch_alter_table('publish_records', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='发布记录表'
    )

    with op.batch_alter_table('quick_reasons', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='快捷理由表'
    )

    with op.batch_alter_table('rejection_reasons', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='拒绝理由表'
    )

    with op.batch_alter_table('role_permissions', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='角色权限关联表'
    )

    with op.batch_alter_table('roles', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='角色表'
    )

    with op.batch_alter_table('system_settings', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='系统设置表'
    )

    with op.batch_alter_table('tasks', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='任务表'
    )

    with op.batch_alter_table('template_categories', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='模板分类表'
    )

    with op.batch_alter_table('template_marks', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='标记定义表'
    )

    with op.batch_alter_table('templates', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='模板表'
    )

    with op.batch_alter_table('topic_relations', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='话题关联表'
    )

    with op.batch_alter_table('topics', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='话题表'
    )

    with op.batch_alter_table('user_roles', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='用户角色关联表'
    )

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_table_comment(
        existing_comment='用户表'
    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_table_comment(
        '用户表',
        existing_comment=None
    )

    with op.batch_alter_table('user_roles', schema=None) as batch_op:
        batch_op.create_table_comment(
        '用户角色关联表',
        existing_comment=None
    )

    with op.batch_alter_table('topics', schema=None) as batch_op:
        batch_op.create_table_comment(
        '话题表',
        existing_comment=None
    )

    with op.batch_alter_table('topic_relations', schema=None) as batch_op:
        batch_op.create_table_comment(
        '话题关联表',
        existing_comment=None
    )

    with op.batch_alter_table('templates', schema=None) as batch_op:
        batch_op.create_table_comment(
        '模板表',
        existing_comment=None
    )

    with op.batch_alter_table('template_marks', schema=None) as batch_op:
        batch_op.create_table_comment(
        '标记定义表',
        existing_comment=None
    )

    with op.batch_alter_table('template_categories', schema=None) as batch_op:
        batch_op.create_table_comment(
        '模板分类表',
        existing_comment=None
    )

    with op.batch_alter_table('tasks', schema=None) as batch_op:
        batch_op.create_table_comment(
        '任务表',
        existing_comment=None
    )

    with op.batch_alter_table('system_settings', schema=None) as batch_op:
        batch_op.create_table_comment(
        '系统设置表',
        existing_comment=None
    )

    with op.batch_alter_table('roles', schema=None) as batch_op:
        batch_op.create_table_comment(
        '角色表',
        existing_comment=None
    )

    with op.batch_alter_table('role_permissions', schema=None) as batch_op:
        batch_op.create_table_comment(
        '角色权限关联表',
        existing_comment=None
    )

    with op.batch_alter_table('rejection_reasons', schema=None) as batch_op:
        batch_op.create_table_comment(
        '拒绝理由表',
        existing_comment=None
    )

    with op.batch_alter_table('quick_reasons', schema=None) as batch_op:
        batch_op.create_table_comment(
        '快捷理由表',
        existing_comment=None
    )

    with op.batch_alter_table('publish_records', schema=None) as batch_op:
        batch_op.create_table_comment(
        '发布记录表',
        existing_comment=None
    )

    with op.batch_alter_table('permissions', schema=None) as batch_op:
        batch_op.create_table_comment(
        '权限表',
        existing_comment=None
    )

    with op.batch_alter_table('notifications', schema=None) as batch_op:
        batch_op.create_table_comment(
        '通知表',
        existing_comment=None
    )

    with op.batch_alter_table('contents', schema=None) as batch_op:
        batch_op.create_table_comment(
        '文案表',
        existing_comment=None
    )
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_index(batch_op.f('ix_contents_is_deleted'))
        batch_op.alter_column('batch_id',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=False)
        batch_op.alter_column('task_id',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=False)
        batch_op.alter_column('client_id',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=False)
        batch_op.drop_column('deleted_by')
        batch_op.drop_column('deleted_at')
        batch_op.drop_column('is_deleted')

    with op.batch_alter_table('content_history', schema=None) as batch_op:
        batch_op.create_table_comment(
        '文案历史表',
        existing_comment=None
    )

    with op.batch_alter_table('clients', schema=None) as batch_op:
        batch_op.create_table_comment(
        '客户表',
        existing_comment=None
    )

    with op.batch_alter_table('client_shares', schema=None) as batch_op:
        batch_op.create_table_comment(
        '客户分享链接表',
        existing_comment=None
    )

    with op.batch_alter_table('batches', schema=None) as batch_op:
        batch_op.create_table_comment(
        '批次表',
        existing_comment=None
    )

    # ### end Alembic commands ###
