#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建管理员用户脚本
"""

from app import create_app, db
from app.models.user import User
from werkzeug.security import generate_password_hash

def create_admin():
    """创建管理员用户"""
    app = create_app('default')
    with app.app_context():
        # 检查是否已存在管理员
        admin = User.query.filter_by(username='admin').first()
        if admin:
            print('管理员用户已存在')
            return
        
        # 创建管理员用户
        admin = User(
            username='admin',
            email='<EMAIL>',
            password_hash=generate_password_hash('admin123'),
            real_name='管理员',
            phone='13800000000',
            is_active=True
        )
        
        db.session.add(admin)
        db.session.commit()
        print('管理员用户创建成功')

if __name__ == '__main__':
    create_admin() 