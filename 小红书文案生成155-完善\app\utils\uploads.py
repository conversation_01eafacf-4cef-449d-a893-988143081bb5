"""
文件上传工具模块
"""

import os
import uuid
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import current_app


class UploadManager:
    """上传管理器"""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化应用"""
        self.app = app
        # 确保上传目录存在
        upload_path = app.config.get('UPLOADED_PHOTOS_DEST')
        if not os.path.exists(upload_path):
            os.makedirs(upload_path)
    
    def save_file(self, file, folder=None):
        """保存文件
        
        Args:
            file: 文件对象
            folder: 子文件夹名称
        
        Returns:
            保存后的文件路径
        """
        if not file:
            return None
        
        # 生成安全的文件名
        filename = secure_filename(file.filename)
        # 添加时间戳和随机字符串，避免文件名冲突
        name, ext = os.path.splitext(filename)
        filename = f"{name}_{datetime.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:8]}{ext}"
        
        # 构建保存路径
        upload_path = current_app.config.get('UPLOADED_PHOTOS_DEST')
        if folder:
            upload_path = os.path.join(upload_path, folder)
            if not os.path.exists(upload_path):
                os.makedirs(upload_path)
        
        # 保存文件
        file_path = os.path.join(upload_path, filename)
        file.save(file_path)
        
        # 返回相对路径
        relative_path = os.path.join('uploads', folder, filename) if folder else os.path.join('uploads', filename)
        return relative_path


# 创建上传管理器实例
upload_manager = UploadManager() 