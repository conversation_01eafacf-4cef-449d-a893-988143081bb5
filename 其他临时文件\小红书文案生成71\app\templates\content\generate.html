{% extends "base.html" %}

{% block title %}批量生成文案{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">批量生成文案</h2>
        <a href="{{ url_for('content.content_list') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">文案生成设置</h5>
        </div>
        <div class="card-body">
            <form method="post" id="generateForm" data-ajax-form="true">
                {{ form.csrf_token }}
                <input type="hidden" name="form_validated" id="form_validated" value="0">
                
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.client_id.label(class="form-label") }}
                            {{ form.client_id(class="form-select", id="client_id") }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.task_id.label(class="form-label") }}
                            {{ form.task_id(class="form-select", id="task_id") }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.new_task_name.label(class="form-label") }}
                            {{ form.new_task_name(class="form-control", id="new_task_name") }}
                            <small class="text-muted">如选择"创建新任务"则需填写</small>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.batch_name.label(class="form-label") }}
                            {{ form.batch_name(class="form-control", id="batch_name", readonly=true) }}
                            <small class="text-muted">批次号自动生成，不可修改</small>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="form-group">
                            {{ form.template_category_id.label(class="form-label") }}
                            {{ form.template_category_id(class="form-select", id="template_category_id") }}
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-light">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">标记替换设置 <span id="marks-info" class="badge bg-info ms-2"></span></h5>
                                    <span id="template-count" class="badge bg-primary">未选择模板分类</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="marks-container">
                                    <p class="text-muted">请先选择模板分类，系统将自动加载需要替换的标记</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 话题和@用户输入区域 - 移到关键词下面 -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label mb-2">{{ form.required_topics.label.text }}</label>
                            <div class="mark-input-container">
                                <span class="mark-label">必选</span>
                                <div class="input-group flex-grow-1">
                                    <input type="text" id="required_topic_input" class="form-control form-control-sm" placeholder="输入话题后按回车添加，可直接粘贴多行文本">
                                </div>
                            </div>
                            <div id="required-topics-container" class="tags-container mb-2"></div>
                            {{ form.required_topics(class="form-control d-none", id="required_topics", rows=5) }}
                            <small class="text-muted">可以批量粘贴话题，每行一个，或用空格、逗号分隔</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label mb-2">{{ form.random_topics.label.text }}</label>
                            <div class="mark-input-container">
                                <span class="mark-label">随机</span>
                                <div class="input-group flex-grow-1">
                                    <input type="text" id="random_topic_input" class="form-control form-control-sm" placeholder="输入话题后按回车添加，可直接粘贴多行文本">
                                </div>
                            </div>
                            <div id="random-topics-container" class="tags-container mb-2"></div>
                            {{ form.random_topics(class="form-control d-none", id="random_topics", rows=5) }}
                            <small class="text-muted">可以批量粘贴话题，每行一个，或用空格、逗号分隔</small>
                        </div>
                    </div>
                </div>

                <!-- @用户输入区域 -->
                <div class="row mb-3">
                    <div class="col-md-8">
                        <div class="form-group">
                            <label class="form-label mb-2">{{ form.at_users.label.text }}</label>
                            <div class="mark-input-container">
                                <span class="mark-label">@用户</span>
                                <div class="input-group flex-grow-1">
                                    <input type="text" id="at_user_input" class="form-control form-control-sm" placeholder="输入用户名后按回车添加，可直接粘贴多行文本">
                                </div>
                            </div>
                            <div id="at-users-container" class="tags-container mb-2"></div>
                            {{ form.at_users(class="form-control d-none", id="at_users", rows=3) }}
                            <small class="text-muted">可以批量粘贴用户名，每行一个，或用空格、逗号分隔，不需要添加@符号</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label mb-2">{{ form.location.label.text }}</label>
                            <div class="mark-input-container">
                                <span class="mark-label">定位</span>
                                <div class="input-group flex-grow-1">
                                    <input type="text" id="location_input" class="form-control form-control-sm" placeholder="输入定位后按回车添加，可直接粘贴多行文本">
                                </div>
                            </div>
                            <div id="locations-container" class="tags-container mb-2"></div>
                            {{ form.location(class="form-control d-none", id="location", rows=3) }}
                            <small class="text-muted">可以批量粘贴定位，每行一个，或用空格、逗号分隔</small>
                        </div>
                    </div>
                </div>
                
                <!-- 设置类选项移到最下面 -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">最大话题数量</label>
                            <input type="number" class="form-control" id="max_topics_count" name="max_topics_count" min="1" max="10" value="10">
                            <small class="text-muted">小红书最多允许10个话题</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">随机@用户数量</label>
                            <input type="number" class="form-control" id="random_at_users_count" name="random_at_users_count" min="0" max="10" value="1">
                            <small class="text-muted">每篇文章随机选择的@用户数量</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.publish_priority.label(class="form-label") }}
                            {{ form.publish_priority(class="form-select", id="publish_priority") }}
                        </div>
                    </div>
                </div>
                
                <!-- 生成设置区域 -->
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">生成设置</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.count.label(class="form-label") }}
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="count" name="count" min="1" max="1000" value="1">
                                        <button type="button" class="btn btn-outline-primary" id="validateCountBtn">验证</button>
                                    </div>
                                    <small class="text-muted">最多一次生成1000篇文案</small>
                                    <div id="count-feedback" class="mt-2"></div>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label class="form-label">重复控制</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="duplicate_control" id="no_duplicate_task" value="no_duplicate_task" checked>
                                        <label class="form-check-label" for="no_duplicate_task">
                                            不允许在当前任务中重复使用模板（推荐）
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="duplicate_control" id="allow_duplicate" value="allow_duplicate">
                                        <label class="form-check-label" for="allow_duplicate">
                                            允许重复使用模板
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="duplicate_control" id="no_duplicate_all" value="no_duplicate_all">
                                        <label class="form-check-label" for="no_duplicate_all">
                                            不允许在当前客户的所有任务中重复使用模板
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="custom-info-panel">
                                    <h6>模板可用情况</h6>
                                    <div id="template-availability">
                                        <p>请先选择模板分类查看预览</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 避免重复内容选项 -->
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="form-check">
                            {{ form.avoid_duplicates(class="form-check-input", id="avoid_duplicates") }}
                            {{ form.avoid_duplicates.label(class="form-check-label") }}
                            <small class="text-muted ms-2">避免生成重复内容</small>
                        </div>
                    </div>
                </div>
                
                <!-- 隐藏的关键词字段，用于提交表单 -->
                <div class="d-none">
                    {{ form.keywords(id="keywords") }}
                    <input type="hidden" name="allow_template_duplicate" id="allow_template_duplicate" value="0">
                </div>
                
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="custom-info-panel">
                            <h5>生成预览</h5>
                            <div id="generatePreview">
                                <p>请选择模板分类查看预览</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" onclick="window.history.back();">取消</button>
                            <button type="submit" class="btn btn-primary">生成文案</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        min-height: 36px;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        padding: 5px;
        background-color: #f8f9fa;
    }
    
    .tag {
        display: inline-flex;
        align-items: center;
        padding: 2px 8px;
        background-color: #0d6efd;
        color: white;
        border-radius: 4px;
        font-size: 0.875rem;
    }
    
    .tag .remove {
        margin-left: 5px;
        cursor: pointer;
    }
    
    /* 标记名称标签样式 */
    .mark-label {
        display: inline-flex;
        align-items: center;
        padding: 2px 8px;
        background-color: #e9f5ff;
        color: #0066cc;
        border-radius: 4px;
        font-size: 0.875rem;
        border: 1px solid #b3d7ff;
        font-weight: bold;
        margin-right: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        white-space: nowrap; /* 确保标签不会换行 */
    }
    
    /* 紧凑型表单组样式 */
    .compact-form-group {
        margin-bottom: 10px;
    }
    
    .compact-form-group .input-group {
        display: flex;
        align-items: center;
    }
    
    .compact-form-group .form-label {
        margin-bottom: 0.25rem;
        white-space: nowrap; /* 确保标签不会换行 */
    }
    
    /* 标记输入区域样式 */
    .mark-input-container {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
    }
    
    /* 标记信息样式 */
    .marks-info {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 10px;
    }
    
    /* 确保所有标签不换行 */
    .form-label {
        white-space: nowrap;
    }

    /* 自定义信息面板样式 - 永不消失 */
    .custom-info-panel {
        min-height: 120px !important;
        padding: 1rem !important;
        margin-bottom: 1rem !important;
        background-color: #d1ecf1 !important;
        border: 1px solid #bee5eb !important;
        border-radius: 0.375rem !important;
        color: #0c5460 !important;
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
        position: relative !important;
        z-index: 1 !important;
        /* 禁用所有可能的动画和过渡 */
        transition: none !important;
        animation: none !important;
        transform: none !important;
    }

    /* 确保标题样式 */
    .custom-info-panel h5,
    .custom-info-panel h6 {
        margin-bottom: 0.75rem !important;
        color: #0c5460 !important;
        font-weight: 600 !important;
    }

    /* 模板可用情况区域样式 */
    #template-availability {
        min-height: 80px !important;
        padding: 10px 0 !important;
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
    }

    /* 生成预览区域样式 */
    #generatePreview {
        min-height: 100px !important;
        padding: 10px 0 !important;
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
    }

    /* 防止任何隐藏效果 */
    .custom-info-panel[style*="display: none"],
    .custom-info-panel[style*="visibility: hidden"],
    .custom-info-panel[style*="opacity: 0"] {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* 确保内容区域也不会隐藏 */
    .custom-info-panel .row,
    .custom-info-panel .col-12,
    .custom-info-panel .col-md-6,
    .custom-info-panel div {
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* 关键词标记状态样式 */
    .keyword-status-complete {
        color: #198754 !important;
        font-weight: 600;
    }

    .keyword-status-incomplete {
        color: #fd7e14 !important;
        font-weight: 600;
    }

    .keyword-status-missing {
        color: #dc3545 !important;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化客户选择
        const clientSelect = document.getElementById('client_id');
        if (clientSelect && clientSelect.options.length > 0) {
            // 如果有客户选项，自动选择第一个
            if (!clientSelect.value) {
                clientSelect.selectedIndex = 0;
                // 触发change事件加载任务
                clientSelect.dispatchEvent(new Event('change'));
            }
        }
        
        // 初始化客户选择变更事件
        initClientChange();
        
        // 初始化任务选择变更事件
        initTaskChange();
        
        // 初始化新任务名称失焦事件
        initNewTaskNameBlur();
        
        // 初始化模板分类选择变更事件
        initTemplateCategoryChange();
        
        // 初始化标签输入
        initTagInputs();
        
        // 初始化验证按钮
        document.getElementById('validateCountBtn').addEventListener('click', validateGenerateCount);
        
        // 初始化重复控制单选按钮
        initDuplicateControlChange();

        // 绑定预览更新事件
        bindPreviewUpdateEvents();

        // 立即显示初始内容，确保蓝色区域可见
        setTimeout(() => {
            updateGeneratePreview();
            updateTemplateAvailability();
            protectInfoAreas();
        }, 100);

        // 自动选择模板分类（如果有值）
        const templateCategorySelect = document.getElementById('template_category_id');
        if (templateCategorySelect && templateCategorySelect.options.length > 0) {
            // 如果已经有选中值，触发change事件
            if (templateCategorySelect.value) {
                templateCategorySelect.dispatchEvent(new Event('change'));
            }
            // 如果没有选中值，选择第一个选项
            else if (templateCategorySelect.options.length > 0) {
                templateCategorySelect.selectedIndex = 0;
                templateCategorySelect.dispatchEvent(new Event('change'));
            }
        }
    });
    
    // 初始化客户选择变更事件
    function initClientChange() {
        const clientSelect = document.getElementById('client_id');
        if (clientSelect) {
            clientSelect.addEventListener('change', function() {
                const clientId = this.value;
                if (clientId) {
                    // 加载该客户的任务列表
                    fetch(`/contents/get-tasks/${clientId}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                const taskSelect = document.getElementById('task_id');
                                // 清空现有选项
                                taskSelect.innerHTML = '';
                                
                                // 添加"创建新任务"选项
                                const newOption = document.createElement('option');
                                newOption.value = 0;
                                newOption.textContent = '-- 创建新任务 --';
                                taskSelect.appendChild(newOption);
                                
                                // 添加任务选项
                                data.tasks.forEach(task => {
                                    const option = document.createElement('option');
                                    option.value = task.id;
                                    option.textContent = task.name;
                                    taskSelect.appendChild(option);
                                });
                                
                                // 触发任务选择变更事件
                                taskSelect.dispatchEvent(new Event('change'));
                                
                                // 如果选择了"不允许在当前客户的所有任务中重复使用模板"选项，更新模板可用情况
                                const duplicateControl = document.querySelector('input[name="duplicate_control"]:checked')?.value;
                                if (duplicateControl === 'no_duplicate_all') {
                                    // 延迟一点执行，确保其他相关状态已更新
                                    setTimeout(() => {
                                        updateTemplateAvailability();
                                    }, 100);
                                }
                            }
                        })
                        .catch(error => {
                            console.error('获取任务列表失败:', error);
                        });
                }
            });
        }
    }
    
    // 初始化任务选择变更事件
    function initTaskChange() {
        const taskSelect = document.getElementById('task_id');
        if (taskSelect) {
            taskSelect.addEventListener('change', function() {
                const taskId = this.value;
                const newTaskNameContainer = document.getElementById('new_task_name').parentElement;
                
                if (taskId == 0) {
                    // 选择了创建新任务，显示任务名称输入框
                    newTaskNameContainer.style.display = 'block';
                    
                    // 设置默认任务名称为"年年年年年月月日日任务"格式
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = String(now.getMonth() + 1).padStart(2, '0');
                    const day = String(now.getDate()).padStart(2, '0');
                    const defaultTaskName = `${year}年${month}月${day}日任务`;
                    document.getElementById('new_task_name').value = defaultTaskName;
                    
                    // 触发失焦事件，查询批次信息
                    document.getElementById('new_task_name').dispatchEvent(new Event('blur'));

                    // 触发模板可用性更新
                    setTimeout(() => {
                        updateTemplateAvailability();
                        updateGeneratePreview();
                    }, 100);
                } else {
                    // 选择了现有任务，隐藏任务名称输入框
                    newTaskNameContainer.style.display = 'none';
                    
                    // 查询该任务的批次信息
                    const clientId = document.getElementById('client_id').value;
                    if (clientId && taskId) {
                        // 查询批次数量
                        fetch(`/contents/get-batches/${taskId}`)
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // 设置批次名称为下一个批次
                                    const batchCount = data.batches.length;
                                    document.getElementById('batch_name').value = `批次 ${batchCount + 1}`;
                                }
                            })
                            .catch(error => {
                                console.error('获取批次信息失败:', error);
                            });
                    }

                    // 触发模板可用性更新
                    setTimeout(() => {
                        updateTemplateAvailability();
                        updateGeneratePreview();
                    }, 100);
                }
            });
        }
    }
    
    // 初始化新任务名称失焦事件
    function initNewTaskNameBlur() {
        const newTaskNameInput = document.getElementById('new_task_name');
        if (newTaskNameInput) {
            newTaskNameInput.addEventListener('blur', function() {
                const taskId = document.getElementById('task_id').value;
                const clientId = document.getElementById('client_id').value;
                const taskName = this.value.trim();
                
                // 只有在选择创建新任务且有客户ID和任务名称时查询
                if (taskId == 0 && clientId && taskName) {
                    // 查询是否存在同名任务及批次信息
                    fetch(`/contents/get-batch-info?client_id=${clientId}&task_name=${encodeURIComponent(taskName)}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // 设置批次名称
                                document.getElementById('batch_name').value = data.batch_name;
                                
                                // 如果存在同名任务，显示提示
                                if (data.task_exists) {
                                    console.log(`发现同名任务: ID=${data.task_id}, 名称=${data.task_name}, 批次=${data.batch_name}`);
                                    // 可以添加提示信息
                                    showToast(`发现同名任务，将使用批次 ${data.batch_count + 1}`);
                                    
                                    // 如果存在同名任务，重新检查模板可用性
                                    updateTemplateAvailability();
                                }
                            }
                        })
                        .catch(error => {
                            console.error('获取批次信息失败:', error);
                        });
                }
            });
            
            // 添加输入事件监听器，当任务名称变更时重新检查模板可用性
            newTaskNameInput.addEventListener('input', function() {
                // 延迟执行，避免频繁请求
                clearTimeout(this._updateTimer);
                this._updateTimer = setTimeout(() => {
                    const taskId = document.getElementById('task_id').value;
                    const taskName = this.value.trim();
                    console.log(`新任务名称输入变化: taskId=${taskId}, taskName="${taskName}"`);

                    if (taskId == 0) {
                        const duplicateControl = document.querySelector('input[name="duplicate_control"]:checked')?.value;
                        console.log(`重复控制设置: ${duplicateControl}`);

                        // 如果选择了重复控制选项，需要检查模板可用性
                        if (duplicateControl === 'no_duplicate_task' || duplicateControl === 'no_duplicate_all') {
                            console.log('触发模板可用性更新');
                            updateTemplateAvailability();
                        }

                        // 同时更新生成预览
                        updateGeneratePreview();
                    }
                }, 500);
            });
        }
    }
    
    // 初始化模板分类选择变更事件
    function initTemplateCategoryChange() {
        const templateCategorySelect = document.getElementById('template_category_id');
        if (templateCategorySelect) {
            templateCategorySelect.addEventListener('change', function() {
                const categoryId = this.value;
                if (categoryId) {
                    // 先清空标记容器和关键词字段，无论请求结果如何
                    const marksContainer = document.getElementById('marks-container');
                    marksContainer.innerHTML = '<p class="text-muted">正在加载标记...</p>';
                    
                    // 清空关键词隐藏字段
                    document.getElementById('keywords').value = '';
                    
                    // 获取该分类下的所有标记
                    fetch(`/contents/get-template-marks/${categoryId}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            // 再次清空标记容器，确保不会有残留
                            marksContainer.innerHTML = '';
                            
                            console.log('获取标记响应数据:', data);
                            
                            if (data.success) {
                                // 更新标记信息
                                const marksInfo = document.getElementById('marks-info');
                                if (marksInfo) {
                                    marksInfo.textContent = data.marks && data.marks.length > 0 
                                        ? `共 ${data.marks.length} 个标记` 
                                        : '无标记';
                                }
                                
                                // 更新模板数量
                                const templateCount = document.getElementById('template-count');
                                if (templateCount) {
                                    templateCount.textContent = data.template_count > 0 
                                        ? `共 ${data.template_count} 个模板` 
                                        : '无可用模板';
                                }
                                
                                if (data.marks && data.marks.length > 0) {
                                    // 创建标记输入区域
                                    data.marks.forEach(mark => {
                                        createMarkInput(mark, marksContainer);
                                    });
                                } else {
                                    marksContainer.innerHTML = '<p class="text-muted">该分类下的模板没有需要替换的标记</p>';
                                }
                                
                                // 安全地调用updateTemplateAvailability函数
                                if (typeof updateTemplateAvailability === 'function') {
                                    try {
                                        updateTemplateAvailability();
                                    } catch (e) {
                                        console.error('调用updateTemplateAvailability失败:', e);
                                    }
                                } else {
                                    console.warn('updateTemplateAvailability函数未定义');
                                }
                            } else {
                                // 处理请求成功但返回错误的情况
                                marksContainer.innerHTML = `<p class="text-danger">${data.message || '获取标记失败'}</p>`;
                                
                                const marksInfo = document.getElementById('marks-info');
                                if (marksInfo) {
                                    marksInfo.textContent = '无标记';
                                }
                                
                                const templateCount = document.getElementById('template-count');
                                if (templateCount) {
                                    templateCount.textContent = '无可用模板';
                                }
                                
                                console.error('获取标记失败:', data.message);
                            }
                        })
                        .catch(error => {
                            // 处理请求失败的情况
                            console.error('获取标记出错:', error);
                            marksContainer.innerHTML = `<p class="text-danger">获取标记出错: ${error.message}，请重试</p>`;
                            
                            const marksInfo = document.getElementById('marks-info');
                            if (marksInfo) {
                                marksInfo.textContent = '加载失败';
                            }
                        });
                } else {
                    // 没有选择模板分类，清空标记容器
                    const marksContainer = document.getElementById('marks-container');
                    marksContainer.innerHTML = '<p class="text-muted">请先选择模板分类</p>';
                    
                    // 清空关键词隐藏字段
                    document.getElementById('keywords').value = '';
                    
                    // 清空标记信息
                    const marksInfo = document.getElementById('marks-info');
                    if (marksInfo) {
                        marksInfo.textContent = '';
                    }
                    
                    // 清空模板数量
                    const templateCount = document.getElementById('template-count');
                    if (templateCount) {
                        templateCount.textContent = '未选择模板分类';
                    }
                }
            });
        }
    }
    
    // 初始化标签输入
    function initTagInputs() {
        const requiredTopicInput = document.getElementById('required_topic_input');
        if (requiredTopicInput) {
            // 键盘事件 - 回车添加
            requiredTopicInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ',') {
                    e.preventDefault();
                    addRequiredTopic();
                }
            });
            
            // 失去焦点事件 - 自动添加
            requiredTopicInput.addEventListener('blur', function() {
                if (this.value.trim()) {
                    addRequiredTopic();
                }
            });
            
            // 粘贴事件 - 批量添加
            requiredTopicInput.addEventListener('paste', function(e) {
                // 获取粘贴的文本
                const clipboardData = e.clipboardData || window.clipboardData;
                const pastedText = clipboardData.getData('text');
                
                // 如果包含换行符或多个分隔符，阻止默认粘贴行为并处理多行文本
                if (pastedText && (pastedText.includes('\n') || pastedText.includes(',') || pastedText.includes('，') || pastedText.includes(' '))) {
                    e.preventDefault();
                    
                    // 按多种分隔符分割
                    const items = pastedText.split(/[\n,，;；、\s]+/);
                    
                    // 添加每一项作为标签
                    items.forEach(item => {
                        const trimmedItem = item.trim();
                        if (trimmedItem) {
                            addTag(trimmedItem, 'required-topics-container', 'required_topics');
                        }
                    });
                }
            });
        }
        
        const randomTopicInput = document.getElementById('random_topic_input');
        if (randomTopicInput) {
            // 键盘事件 - 回车添加
            randomTopicInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ',') {
                    e.preventDefault();
                    addRandomTopic();
                }
            });
            
            // 失去焦点事件 - 自动添加
            randomTopicInput.addEventListener('blur', function() {
                if (this.value.trim()) {
                    addRandomTopic();
                }
            });
            
            // 粘贴事件 - 批量添加
            randomTopicInput.addEventListener('paste', function(e) {
                // 获取粘贴的文本
                const clipboardData = e.clipboardData || window.clipboardData;
                const pastedText = clipboardData.getData('text');
                
                // 如果包含换行符或多个分隔符，阻止默认粘贴行为并处理多行文本
                if (pastedText && (pastedText.includes('\n') || pastedText.includes(',') || pastedText.includes('，') || pastedText.includes(' '))) {
                    e.preventDefault();
                    
                    // 按多种分隔符分割
                    const items = pastedText.split(/[\n,，;；、\s]+/);
                    
                    // 添加每一项作为标签
                    items.forEach(item => {
                        const trimmedItem = item.trim();
                        if (trimmedItem) {
                            addTag(trimmedItem, 'random-topics-container', 'random_topics');
                        }
                    });
                }
            });
        }
        
        const atUserInput = document.getElementById('at_user_input');
        if (atUserInput) {
            // 键盘事件 - 回车添加
            atUserInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ',') {
                    e.preventDefault();
                    addAtUser();
                }
            });
            
            // 失去焦点事件 - 自动添加
            atUserInput.addEventListener('blur', function() {
                if (this.value.trim()) {
                    addAtUser();
                }
            });
            
            // 粘贴事件 - 批量添加
            atUserInput.addEventListener('paste', function(e) {
                // 获取粘贴的文本
                const clipboardData = e.clipboardData || window.clipboardData;
                const pastedText = clipboardData.getData('text');
                
                // 如果包含换行符或多个分隔符，阻止默认粘贴行为并处理多行文本
                if (pastedText && (pastedText.includes('\n') || pastedText.includes(',') || pastedText.includes('，') || pastedText.includes(' '))) {
                    e.preventDefault();
                    
                    // 按多种分隔符分割
                    const items = pastedText.split(/[\n,，;；、\s]+/);
                    
                    // 添加每一项作为标签
                    items.forEach(item => {
                        const trimmedItem = item.trim();
                        if (trimmedItem) {
                            addTag(trimmedItem, 'at-users-container', 'at_users');
                        }
                    });
                }
            });
        }
        
        const locationInput = document.getElementById('location_input');
        if (locationInput) {
            // 键盘事件 - 回车添加
            locationInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ',') {
                    e.preventDefault();
                    addLocation();
                }
            });
            
            // 失去焦点事件 - 自动添加
            locationInput.addEventListener('blur', function() {
                if (this.value.trim()) {
                    addLocation();
                }
            });
            
            // 粘贴事件 - 批量添加
            locationInput.addEventListener('paste', function(e) {
                // 获取粘贴的文本
                const clipboardData = e.clipboardData || window.clipboardData;
                const pastedText = clipboardData.getData('text');
                
                // 如果包含换行符或多个分隔符，阻止默认粘贴行为并处理多行文本
                if (pastedText && (pastedText.includes('\n') || pastedText.includes(',') || pastedText.includes('，') || pastedText.includes(' '))) {
                    e.preventDefault();
                    
                    // 按多种分隔符分割
                    const items = pastedText.split(/[\n,，;；、\s]+/);
                    
                    // 添加每一项作为标签
                    items.forEach(item => {
                        const trimmedItem = item.trim();
                        if (trimmedItem) {
                            addTag(trimmedItem, 'locations-container', 'location');
                        }
                    });
                }
            });
        }
    }
    
    // 添加必选话题
    function addRequiredTopic() {
        const input = document.getElementById('required_topic_input');
        const value = input.value.trim();
        
        if (value) {
            // 添加到标签容器
            addTag(value, 'required-topics-container', 'required_topics');
            input.value = '';
        }
    }
    
    // 添加随机话题
    function addRandomTopic() {
        const input = document.getElementById('random_topic_input');
        const value = input.value.trim();
        
        if (value) {
            // 添加到标签容器
            addTag(value, 'random-topics-container', 'random_topics');
            input.value = '';
        }
    }
    
    // 添加@用户
    function addAtUser() {
        const input = document.getElementById('at_user_input');
        const value = input.value.trim();
        
        if (value) {
            // 添加到标签容器
            addTag(value, 'at-users-container', 'at_users');
            input.value = '';
        }
    }
    
    // 添加定位
    function addLocation() {
        const input = document.getElementById('location_input');
        const value = input.value.trim();
        
        if (value) {
            // 添加到标签容器
            addTag(value, 'locations-container', 'location');
            input.value = '';
        }
    }
    
    // 通用添加标签函数
    function addTag(value, containerId, hiddenFieldId) {
        const container = document.getElementById(containerId);
        const hiddenField = document.getElementById(hiddenFieldId);
        
        // 检查是否已存在相同标签
        const existingTags = container.querySelectorAll('.tag');
        for (let i = 0; i < existingTags.length; i++) {
            if (existingTags[i].textContent.trim().replace('×', '') === value) {
                return; // 已存在相同标签，不添加
            }
        }
        
        // 创建新标签
        const tag = document.createElement('span');
        tag.className = 'tag';
        tag.innerHTML = `${value} <span class="remove" onclick="removeTag(this, '${hiddenFieldId}')">×</span>`;
        
        // 添加到容器
        container.appendChild(tag);
        
        // 更新隐藏字段
        updateHiddenField(containerId, hiddenFieldId);
    }
    
    // 移除标签
    function removeTag(element, hiddenFieldId) {
        const tag = element.parentNode;
        const container = tag.parentNode;
        
        // 移除标签
        container.removeChild(tag);
        
        // 更新隐藏字段
        updateHiddenField(container.id, hiddenFieldId);
    }
    
    // 更新隐藏字段
    function updateHiddenField(containerId, hiddenFieldId) {
        const container = document.getElementById(containerId);
        const hiddenField = document.getElementById(hiddenFieldId);
        
        // 获取所有标签文本
        const tags = container.querySelectorAll('.tag');
        const values = Array.from(tags).map(tag => tag.textContent.trim().replace('×', ''));
        
        // 更新隐藏字段
        hiddenField.value = values.join('\n');
    }
    
    // 异步验证生成数量
    function validateGenerateCountAsync() {
        return new Promise((resolve, reject) => {
            try {
                const count = parseInt(document.getElementById('count').value);
                const categoryId = document.getElementById('template_category_id').value;
                const taskId = document.getElementById('task_id').value;
                const duplicateControl = document.querySelector('input[name="duplicate_control"]:checked')?.value || 'no_duplicate_task';

                if (!categoryId) {
                    showToast('请先选择模板分类', 'warning');
                    resolve(false);
                    return;
                }

                console.log(`自动验证生成数量: count=${count}, categoryId=${categoryId}, taskId=${taskId || 0}, duplicateControl=${duplicateControl}`);

                // 构建API URL
                let apiUrl = `/contents/get-template-availability/${categoryId}/${taskId || 0}/${duplicateControl}`;

                // 构建查询参数
                let queryParams = [];

                // 获取客户ID
                const clientId = document.getElementById('client_id')?.value;

                // 如果是新任务，传递任务名称参数
                if ((taskId === '' || taskId === '0' || !taskId)) {
                    // 获取任务名称
                    const taskName = document.getElementById('new_task_name')?.value?.trim();
                    if (taskName) {
                        queryParams.push(`task_name=${encodeURIComponent(taskName)}`);
                        console.log(`验证时使用新任务名称: ${taskName}`);
                    }

                    // 对于新任务，如果有重复控制要求，都需要传递客户ID
                    if (duplicateControl === 'no_duplicate_task' || duplicateControl === 'no_duplicate_all') {
                        if (clientId) {
                            queryParams.push(`client_id=${clientId}`);
                        } else {
                            showToast('请先选择客户，以检查模板可用情况', 'warning');
                            resolve(false);
                            return;
                        }
                    }
                } else {
                    // 现有任务，如果是客户级别的重复控制，也需要客户ID
                    if (duplicateControl === 'no_duplicate_all') {
                        if (clientId) {
                            queryParams.push(`client_id=${clientId}`);
                        }
                    }
                }

                // 添加查询参数到URL
                if (queryParams.length > 0) {
                    apiUrl += `?${queryParams.join('&')}`;
                }

                // 获取模板可用情况
                fetch(apiUrl)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('自动验证响应数据:', data);

                        if (data.success) {
                            if (duplicateControl === 'allow_duplicate') {
                                // 允许重复使用模板
                                console.log('允许重复使用模板，验证通过');
                                resolve(true);
                            } else if (count > data.available_templates) {
                                // 需要生成的数量超过可用模板数
                                const message = `需要生成 ${count} 篇文案，但只有 ${data.available_templates} 个可用模板。请选择"允许重复使用模板"或减少生成数量至 ${data.available_templates} 篇。`;
                                showToast(message, 'warning');
                                resolve(false);
                            } else {
                                // 可用模板足够
                                console.log('可用模板充足，验证通过');
                                resolve(true);
                            }
                        } else {
                            showToast(`验证失败: ${data.message || '未知错误'}`, 'danger');
                            resolve(false);
                        }
                    })
                    .catch(error => {
                        console.error('自动验证失败:', error);
                        showToast(`验证失败: ${error.message}`, 'danger');
                        resolve(false);
                    });
            } catch (error) {
                console.error(`自动验证出错: ${error.message}`);
                reject(error);
            }
        });
    }

    // 手动验证生成数量（保留原功能）
    function validateGenerateCount() {
        try {
            const count = parseInt(document.getElementById('count').value);
            const categoryId = document.getElementById('template_category_id').value;
            const taskId = document.getElementById('task_id').value;
            const duplicateControl = document.querySelector('input[name="duplicate_control"]:checked')?.value || 'no_duplicate_task';
            
            if (!categoryId) {
                showToast('请先选择模板分类', 'warning');
                return;
            }
            
            // 显示加载中提示
            const countFeedback = document.getElementById('count-feedback');
            if (countFeedback) {
                countFeedback.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-spinner fa-spin"></i> 正在验证可用模板数量...
                    </div>
                `;
            }
            
            console.log(`验证生成数量: count=${count}, categoryId=${categoryId}, taskId=${taskId || 0}, duplicateControl=${duplicateControl}`);
            
            // 构建API URL
            let apiUrl = `/contents/get-template-availability/${categoryId}/${taskId || 0}/${duplicateControl}`;
            
            // 构建查询参数
            let queryParams = [];

            // 获取客户ID
            const clientId = document.getElementById('client_id')?.value;

            // 如果是新任务，传递任务名称参数
            if ((taskId === '' || taskId === '0' || !taskId)) {
                // 获取任务名称
                const taskName = document.getElementById('new_task_name')?.value?.trim();
                if (taskName) {
                    queryParams.push(`task_name=${encodeURIComponent(taskName)}`);
                    console.log(`验证时使用新任务名称: ${taskName}`);
                }

                // 对于新任务，如果有重复控制要求，都需要传递客户ID
                if (duplicateControl === 'no_duplicate_task' || duplicateControl === 'no_duplicate_all') {
                    if (clientId) {
                        queryParams.push(`client_id=${clientId}`);
                    } else {
                        // 如果没有选择客户，显示提示信息
                        if (countFeedback) {
                            countFeedback.innerHTML = `
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i> 请先选择客户，以检查模板可用情况
                                </div>
                            `;
                        }
                        return;
                    }
                }
            } else {
                // 现有任务，如果是客户级别的重复控制，也需要客户ID
                if (duplicateControl === 'no_duplicate_all') {
                    if (clientId) {
                        queryParams.push(`client_id=${clientId}`);
                    }
                }
            }
            
            // 添加查询参数到URL
            if (queryParams.length > 0) {
                apiUrl += `?${queryParams.join('&')}`;
            }
            
            // 获取模板可用情况
            fetch(apiUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('验证生成数量响应数据:', data);
                    
                    if (data.success) {
                        if (countFeedback) {
                            if (duplicateControl === 'allow_duplicate') {
                                // 允许重复使用模板
                                countFeedback.innerHTML = `
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle"></i> 允许重复使用模板，可以生成 ${count} 篇文案
                                    </div>
                                `;
                                document.getElementById('form_validated').value = '1';
                            } else if (count > data.available_templates) {
                                // 需要生成的数量超过可用模板数
                                countFeedback.innerHTML = `
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i> 警告：需要生成 ${count} 篇文案，但只有 ${data.available_templates} 个可用模板，将会有重复模板
                                    </div>
                                `;
                                document.getElementById('form_validated').value = '1';
                            } else {
                                // 可用模板足够
                                countFeedback.innerHTML = `
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle"></i> 可用模板充足，可以生成 ${count} 篇文案
                                    </div>
                                `;
                                document.getElementById('form_validated').value = '1';
                            }
                        }
                    } else {
                        if (countFeedback) {
                            countFeedback.innerHTML = `
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle"></i> 验证失败: ${data.message || '未知错误'}
                                </div>
                            `;
                        }
                        console.error('验证生成数量失败:', data.message);
                    }
                })
                .catch(error => {
                    console.error('验证生成数量失败:', error);
                    if (countFeedback) {
                        countFeedback.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle"></i> 验证失败: ${error.message}
                            </div>
                        `;
                    }
                });
        } catch (error) {
            console.error(`验证生成数量出错: ${error.message}`);
            showToast(`验证失败: ${error.message}`, 'danger');
        }
    }
    
    // 显示中央提示框
    function showToast(message, type = 'info') {
        // 创建提示框
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0 position-fixed top-50 start-50 translate-middle`;
        toast.style.zIndex = '9999';
        toast.style.minWidth = '300px';
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        
        // 创建提示框内容
        const toastBody = document.createElement('div');
        toastBody.className = 'd-flex';
        
        const toastContent = document.createElement('div');
        toastContent.className = 'toast-body';
        toastContent.textContent = message;
        
        const closeButton = document.createElement('button');
        closeButton.type = 'button';
        closeButton.className = 'btn-close btn-close-white me-2 m-auto';
        closeButton.setAttribute('data-bs-dismiss', 'toast');
        closeButton.setAttribute('aria-label', '关闭');
        
        toastBody.appendChild(toastContent);
        toastBody.appendChild(closeButton);
        toast.appendChild(toastBody);
        
        // 添加到文档
        document.body.appendChild(toast);
        
        // 显示提示框
        const bsToast = new bootstrap.Toast(toast, {
            delay: 3000,
            autohide: true
        });
        bsToast.show();
        
        // 提示框关闭后移除
        toast.addEventListener('hidden.bs.toast', function() {
            document.body.removeChild(toast);
        });
    }
    
    // 验证表单
    function validateForm() {
        return new Promise((resolve, reject) => {
            try {
                // 基本字段验证
                const clientSelect = document.getElementById('client_id');
                const taskSelect = document.getElementById('task_id');
                const newTaskNameInput = document.getElementById('new_task_name');
                const batchNameInput = document.getElementById('batch_name');
                const categorySelect = document.getElementById('template_category_id');
                const countInput = document.getElementById('count');
                const keywordsField = document.getElementById('keywords');

                // 检查客户选择
                if (!clientSelect.value) {
                    showToast('请选择客户', 'warning');
                    resolve(false);
                    return;
                }

                // 检查任务选择
                if (taskSelect.value == '0' && !newTaskNameInput.value.trim()) {
                    showToast('请输入新任务名称', 'warning');
                    resolve(false);
                    return;
                }

                // 检查批次名称
                if (!batchNameInput.value.trim()) {
                    showToast('请输入批次名称', 'warning');
                    resolve(false);
                    return;
                }

                // 检查模板分类
                if (!categorySelect.value) {
                    showToast('请选择模板分类', 'warning');
                    resolve(false);
                    return;
                }

                // 检查生成数量
                if (!countInput.value || parseInt(countInput.value) <= 0) {
                    showToast('请设置有效的生成数量', 'warning');
                    resolve(false);
                    return;
                }

                // 检查关键词标记
                const totalMarks = getTotalMarksCount();
                const filledMarks = getFilledMarksCount();

                if (totalMarks === 0) {
                    showToast('请先选择模板分类以加载标记字段', 'warning');
                    resolve(false);
                    return;
                } else if (filledMarks < totalMarks) {
                    const unfilledCount = totalMarks - filledMarks;
                    showToast(`还有 ${unfilledCount} 个关键词标记未填写，请完善所有标记内容`, 'warning');
                    resolve(false);
                    return;
                }

                // 自动验证生成数量
                console.log('开始自动验证生成数量...');
                validateGenerateCountAsync()
                    .then(isValid => {
                        if (isValid) {
                            console.log('验证通过，可以提交');
                            resolve(true);
                        } else {
                            console.log('验证失败，阻止提交');
                            resolve(false);
                        }
                    })
                    .catch(error => {
                        console.error('验证过程出错:', error);
                        showToast('验证过程出错，请重试', 'danger');
                        resolve(false);
                    });

            } catch (error) {
                console.error('表单验证出错:', error);
                showToast('表单验证出错，请重试', 'danger');
                resolve(false);
            }
        });
    }

    // 创建标记输入区域
    function createMarkInput(mark, container) {
        if (!mark) {
            console.error('尝试创建无效的标记输入区域，标记名称为空');
            return;
        }
        
        console.log(`创建标记输入区域: ${mark}`);
        
        try {
            // 创建标记输入组 - 使用两列布局
            const markGroup = document.createElement('div');
            markGroup.className = 'col-md-6 mb-3'; // 使用Bootstrap的列布局
            
            // 创建输入区域
            const inputContainer = document.createElement('div');
            inputContainer.className = 'mark-input-container';
            
            // 创建标记标签
            const markLabel = document.createElement('span');
            markLabel.className = 'mark-label';
            markLabel.textContent = mark;
            
            // 创建输入组
            const inputGroup = document.createElement('div');
            inputGroup.className = 'input-group flex-grow-1';
            
            // 创建输入框 - 使用单行输入框
            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'form-control';
            input.placeholder = `输入后回车添加`;
            input.dataset.mark = mark;
            
            // 创建标签容器
            const tagsContainer = document.createElement('div');
            tagsContainer.className = 'tags-container mt-1';
            tagsContainer.dataset.mark = mark;
            
            // 创建隐藏的值字段
            const hiddenValue = document.createElement('input');
            hiddenValue.type = 'hidden';
            hiddenValue.dataset.markValue = mark;
            
            // 添加输入事件监听
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ',') {
                    e.preventDefault();
                    addMarkTag(this.value.trim(), mark);
                    this.value = '';
                }
            });
            
            // 添加失去焦点事件
            input.addEventListener('blur', function() {
                if (this.value.trim()) {
                    addMarkTag(this.value.trim(), mark);
                    this.value = '';
                }
            });

            // 添加输入事件，实时检查（但不添加标签）
            input.addEventListener('input', debounce(function() {
                // 当用户正在输入时，也触发预览更新
                // 这样可以实时反映关键词设置的状态
                updateGeneratePreview();
            }, 300));
            
            // 将所有元素组合起来
            inputGroup.appendChild(input);
            inputContainer.appendChild(markLabel);
            inputContainer.appendChild(inputGroup);
            
            markGroup.appendChild(inputContainer);
            markGroup.appendChild(tagsContainer);
            markGroup.appendChild(hiddenValue);
            
            // 添加到容器 - 如果是第一个元素，创建一个行容器
            if (container.children.length === 0 || container.lastChild.tagName !== 'DIV' || !container.lastChild.classList.contains('row')) {
                const rowContainer = document.createElement('div');
                rowContainer.className = 'row';
                container.appendChild(rowContainer);
                rowContainer.appendChild(markGroup);
            } else {
                // 添加到现有的行容器
                container.lastChild.appendChild(markGroup);
            }
            
            // 支持批量粘贴
            input.addEventListener('paste', function(e) {
                // 阻止默认粘贴行为
                e.preventDefault();
                
                // 获取粘贴的文本
                const pastedText = (e.clipboardData || window.clipboardData).getData('text');
                
                // 处理粘贴的文本
                if (pastedText) {
                    // 按行分割
                    const lines = pastedText.split(/[\n,]/);
                    
                    // 添加每一行作为标签
                    lines.forEach(line => {
                        const trimmedLine = line.trim();
                        if (trimmedLine) {
                            addMarkTag(trimmedLine, mark);
                        }
                    });
                }
            });
        } catch (error) {
            console.error(`创建标记输入区域出错: ${error.message}`);
            container.innerHTML += `<p class="text-danger">创建标记 ${mark} 输入区域失败</p>`;
        }
    }
    
    // 添加标记标签
    function addMarkTag(value, mark) {
        if (!value) return;
        
        try {
            // 获取标签容器
            const tagsContainer = document.querySelector(`.tags-container[data-mark="${mark}"]`);
            if (!tagsContainer) {
                console.error(`找不到标记 ${mark} 的标签容器`);
                return;
            }
            
            // 检查是否已存在相同标签
            const existingTags = tagsContainer.querySelectorAll('.tag');
            for (let i = 0; i < existingTags.length; i++) {
                if (existingTags[i].textContent.trim().replace('×', '') === value) {
                    return; // 已存在相同标签，不添加
                }
            }
            
            // 创建新标签
            const tag = document.createElement('span');
            tag.className = 'tag';
            tag.innerHTML = `${value} <span class="remove" onclick="removeMarkTag(this, '${mark}')">×</span>`;
            
            // 添加到容器
            tagsContainer.appendChild(tag);
            
            // 更新隐藏字段
            updateKeywords();
        } catch (error) {
            console.error(`添加标记标签出错: ${error.message}`);
        }
    }
    
    // 移除标记标签
    function removeMarkTag(element, mark) {
        try {
            const tag = element.parentNode;
            const tagsContainer = tag.parentNode;
            
            // 移除标签
            tagsContainer.removeChild(tag);
            
            // 更新隐藏字段
            updateKeywords();
        } catch (error) {
            console.error(`移除标记标签出错: ${error.message}`);
        }
    }
    
    // 获取总标记数量
    function getTotalMarksCount() {
        try {
            const markContainers = document.querySelectorAll('.tags-container[data-mark]');
            return markContainers.length;
        } catch (error) {
            console.error('获取总标记数量出错:', error);
            return 0;
        }
    }

    // 获取已填写标记数量
    function getFilledMarksCount() {
        try {
            const markContainers = document.querySelectorAll('.tags-container[data-mark]');
            let filledCount = 0;

            markContainers.forEach(container => {
                const tags = container.querySelectorAll('.tag');
                if (tags.length > 0) {
                    filledCount++;
                }
            });

            return filledCount;
        } catch (error) {
            console.error('获取已填写标记数量出错:', error);
            return 0;
        }
    }

    // 更新关键词隐藏字段
    function updateKeywords() {
        try {
            const keywordsField = document.getElementById('keywords');
            if (!keywordsField) {
                console.error('找不到关键词隐藏字段');
                return;
            }
            
            const markContainers = document.querySelectorAll('.tags-container[data-mark]');
            
            let keywordsText = '';
            markContainers.forEach(container => {
                const mark = container.dataset.mark;
                if (!mark) {
                    console.warn('标签容器缺少data-mark属性');
                    return;
                }
                
                const tags = container.querySelectorAll('.tag');
                
                if (tags.length > 0) {
                    const values = Array.from(tags).map(tag => {
                        const text = tag.textContent.trim();
                        return text.replace('×', '').trim();
                    });
                    keywordsText += `${mark}: ${values.join(', ')}\n`;
                }
            });
            
            keywordsField.value = keywordsText.trim();
            console.log('更新关键词字段:', keywordsField.value);

            // 触发预览更新
            updateGeneratePreview();

        } catch (error) {
            console.error(`更新关键词字段出错: ${error.message}`);
        }
    }
    
    // 更新生成预览
    function updateGeneratePreview() {
        try {
            const previewDiv = document.getElementById('generatePreview');
            if (!previewDiv) return;

            // 获取当前设置
            const clientSelect = document.getElementById('client_id');
            const taskSelect = document.getElementById('task_id');
            const categorySelect = document.getElementById('template_category_id');
            const countInput = document.getElementById('count');
            const taskNameInput = document.getElementById('new_task_name');
            const duplicateControl = document.querySelector('input[name="duplicate_control"]:checked');
            const avoidDuplicates = document.getElementById('avoid_duplicates');

            // 构建预览内容
            let html = '<div class="row">';

            // 基本设置
            html += '<div class="col-md-6">';
            html += '<h6><i class="fas fa-cog"></i> 基本设置</h6>';
            html += '<ul class="list-unstyled mb-0">';

            if (clientSelect && clientSelect.value && clientSelect.value !== '0') {
                const clientText = clientSelect.options[clientSelect.selectedIndex].text;
                html += `<li><strong>客户：</strong> ${clientText}</li>`;
            } else {
                html += '<li><span class="text-warning"><strong>客户：</strong> 未选择</span></li>';
            }

            // 检查任务设置
            const isCreateNewTask = taskSelect && taskSelect.value === '0';
            const hasExistingTask = taskSelect && taskSelect.value && taskSelect.value !== '0';
            const newTaskName = taskNameInput ? taskNameInput.value.trim() : '';

            // 调试信息
            console.log('任务检查调试:', {
                taskSelectValue: taskSelect ? taskSelect.value : 'null',
                isCreateNewTask: isCreateNewTask,
                hasExistingTask: hasExistingTask,
                newTaskName: newTaskName,
                taskNameInputVisible: taskNameInput ? getComputedStyle(taskNameInput.parentElement).display !== 'none' : false
            });

            if (hasExistingTask) {
                const taskText = taskSelect.options[taskSelect.selectedIndex].text;
                html += `<li><strong>任务：</strong> ${taskText} <small class="text-muted">(现有任务)</small></li>`;
            } else if (isCreateNewTask && newTaskName) {
                html += `<li><strong>任务：</strong> ${newTaskName} <small class="text-muted">(新建任务)</small></li>`;
            } else if (isCreateNewTask && !newTaskName) {
                html += '<li><span class="text-warning"><strong>任务：</strong> 未设置 <small>(请输入新任务名称)</small></span></li>';
            } else {
                html += '<li><span class="text-warning"><strong>任务：</strong> 未设置 <small>(请选择现有任务或创建新任务)</small></span></li>';
            }

            if (categorySelect && categorySelect.value) {
                const categoryText = categorySelect.options[categorySelect.selectedIndex].text;
                html += `<li><strong>模板分类：</strong> ${categoryText}</li>`;
            } else {
                html += '<li><span class="text-warning"><strong>模板分类：</strong> 未选择</span></li>';
            }

            if (countInput && countInput.value) {
                html += `<li><strong>生成数量：</strong> ${countInput.value} 篇</li>`;
            } else {
                html += '<li><span class="text-warning"><strong>生成数量：</strong> 未设置</span></li>';
            }

            html += '</ul>';
            html += '</div>';

            // 高级设置
            html += '<div class="col-md-6">';
            html += '<h6><i class="fas fa-sliders-h"></i> 高级设置</h6>';
            html += '<ul class="list-unstyled mb-0">';

            if (duplicateControl) {
                const duplicateText = duplicateControl.nextElementSibling.textContent.trim();
                html += `<li><strong>重复控制：</strong> ${duplicateText}</li>`;
            }

            if (avoidDuplicates) {
                const avoidText = avoidDuplicates.checked ? '是' : '否';
                html += `<li><strong>避免重复内容：</strong> ${avoidText}</li>`;
            }

            // 检查关键词设置
            const keywordsField = document.getElementById('keywords');
            const totalMarks = getTotalMarksCount();
            const filledMarks = getFilledMarksCount();

            if (totalMarks > 0) {
                let statusClass, statusIcon, statusText;

                if (filledMarks === totalMarks) {
                    // 全部完成 - 绿色
                    statusClass = 'keyword-status-complete';
                    statusIcon = '<i class="fas fa-check-circle"></i>';
                    statusText = `${filledMarks}/${totalMarks}`;
                } else if (filledMarks > 0) {
                    // 部分完成 - 橙色
                    statusClass = 'keyword-status-incomplete';
                    statusIcon = '<i class="fas fa-exclamation-triangle"></i>';
                    statusText = `${filledMarks}/${totalMarks}`;
                } else {
                    // 未开始 - 红色
                    statusClass = 'keyword-status-missing';
                    statusIcon = '<i class="fas fa-times-circle"></i>';
                    statusText = `${filledMarks}/${totalMarks}`;
                }

                html += `<li><strong>关键词标记：</strong> <span class="${statusClass}">${statusIcon} ${statusText}</span> <small class="text-muted">(模板中的替换标记)</small></li>`;
            } else {
                html += '<li><span class="text-muted"><strong>关键词标记：</strong> 未设置 <small>(模板替换内容)</small></span></li>';
            }

            html += '</ul>';
            html += '</div>';
            html += '</div>';

            // 详细检查所有必要条件
            const errors = [];
            const warnings = [];

            // 检查客户
            if (!clientSelect || !clientSelect.value || clientSelect.value === '0') {
                errors.push('请选择客户');
            }

            // 检查任务（使用前面已声明的变量）
            const isNewTask = isCreateNewTask;  // 使用前面声明的变量
            const hasTaskName = taskNameInput && taskNameInput.value.trim();
            // 使用前面已声明的 hasExistingTask 变量

            if (!hasExistingTask && (!isNewTask || !hasTaskName)) {
                if (isNewTask) {
                    errors.push('请输入新任务名称');
                } else {
                    errors.push('请选择现有任务或创建新任务');
                }
            }

            // 检查模板分类
            if (!categorySelect || !categorySelect.value) {
                errors.push('请选择模板分类');
            }

            // 检查生成数量
            if (!countInput || !countInput.value || parseInt(countInput.value) <= 0) {
                errors.push('请设置生成数量');
            }

            // 检查关键词标记（重用前面声明的变量）
            if (totalMarks === 0) {
                errors.push('请先选择模板分类以加载标记字段');
            } else if (filledMarks < totalMarks) {
                const unfilledCount = totalMarks - filledMarks;
                errors.push(`还有 ${unfilledCount} 个关键词标记未填写，请完善所有标记内容`);
            }

            // 检查模板可用性
            const generateCount = countInput ? parseInt(countInput.value) : 0;
            const duplicateControlValue = duplicateControl ? duplicateControl.value : null;

            // 从模板可用情况区域获取当前的可用模板信息
            const availabilityDiv = document.getElementById('template-availability');
            if (availabilityDiv && duplicateControlValue !== 'allow_duplicate' && generateCount > 0) {
                const availabilityText = availabilityDiv.textContent || '';

                // 尝试从文本中提取可用模板数量
                const availableMatch = availabilityText.match(/剩余\s*(\d+)\s*个可用模板/);
                if (availableMatch) {
                    const availableTemplates = parseInt(availableMatch[1]);

                    if (availableTemplates === 0) {
                        warnings.push(`需要生成 ${generateCount} 篇，但没有可用模板。请选择"允许重复使用模板"或减少生成数量`);
                    } else if (availableTemplates < generateCount) {
                        warnings.push(`需要生成 ${generateCount} 篇，但只有 ${availableTemplates} 个可用模板。将会有重复模板`);
                    }
                }
            }

            // 生成最终提示
            if (errors.length > 0) {
                html += '<div class="mt-3 alert alert-danger alert-sm py-2">';
                html += '<i class="fas fa-exclamation-circle"></i> <strong>无法提交，请完善以下设置：</strong><br>';
                html += '<ul class="mb-0 mt-1">';
                errors.forEach(error => {
                    html += `<li>${error}</li>`;
                });
                html += '</ul>';
                html += '</div>';
            } else if (warnings.length > 0) {
                html += '<div class="mt-3 alert alert-warning alert-sm py-2">';
                html += '<i class="fas fa-exclamation-triangle"></i> <strong>注意以下问题：</strong><br>';
                html += '<ul class="mb-0 mt-1">';
                warnings.forEach(warning => {
                    html += `<li>${warning}</li>`;
                });
                html += '</ul>';
                html += '<small class="d-block mt-1">可以提交，但建议先处理上述问题</small>';
                html += '</div>';
            } else {
                html += '<div class="mt-3 alert alert-success alert-sm py-2">';
                html += '<i class="fas fa-check-circle"></i> <strong>✅ 所有设置已完成，可以开始生成文案！</strong>';
                html += '</div>';
            }

            previewDiv.innerHTML = html;

            // 确保父容器始终可见
            const parentPanel = previewDiv.closest('.custom-info-panel');
            if (parentPanel) {
                parentPanel.style.display = 'block';
                parentPanel.style.visibility = 'visible';
                parentPanel.style.opacity = '1';
                console.log('强制显示生成预览面板');
            }

        } catch (error) {
            console.error('更新生成预览出错:', error);
        }
    }

    // 更新模板可用情况
    function updateTemplateAvailability() {
        try {
            const categoryId = document.getElementById('template_category_id')?.value;
            const taskId = document.getElementById('task_id')?.value;
            const duplicateControl = document.querySelector('input[name="duplicate_control"]:checked')?.value || 'no_duplicate_task';

            if (!categoryId) {
                console.log('未选择模板分类，不更新模板可用情况');
                return;
            }

            console.log(`获取模板可用情况: categoryId=${categoryId}, taskId=${taskId || 0}, duplicateControl=${duplicateControl}`);

            // 构建API URL
            let apiUrl = `/contents/get-template-availability/${categoryId}/${taskId || 0}/${duplicateControl}`;

            // 构建查询参数
            let queryParams = [];

            // 获取客户ID
            const clientId = document.getElementById('client_id')?.value;

            // 如果是新任务，传递任务名称参数
            if ((taskId === '' || taskId === '0' || !taskId)) {
                // 获取任务名称
                const taskName = document.getElementById('new_task_name')?.value?.trim();
                if (taskName) {
                    queryParams.push(`task_name=${encodeURIComponent(taskName)}`);
                    console.log(`新任务名称: ${taskName}`);
                }

                // 对于新任务，如果有重复控制要求，都需要传递客户ID
                if (duplicateControl === 'no_duplicate_task' || duplicateControl === 'no_duplicate_all') {
                    if (clientId) {
                        queryParams.push(`client_id=${clientId}`);
                    } else {
                        // 如果没有选择客户，显示提示信息
                        const availabilityDiv = document.getElementById('template-availability');
                        if (availabilityDiv) {
                            availabilityDiv.innerHTML = `<p class="text-warning">请先选择客户，以检查模板可用情况</p>`;
                        }
                        return;
                    }
                }
            } else {
                // 现有任务，如果是客户级别的重复控制，也需要客户ID
                if (duplicateControl === 'no_duplicate_all') {
                    if (clientId) {
                        queryParams.push(`client_id=${clientId}`);
                    }
                }
            }
            
            // 添加查询参数到URL
            if (queryParams.length > 0) {
                apiUrl += `?${queryParams.join('&')}`;
            }
            
            // 获取模板可用情况
            fetch(apiUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('模板可用情况响应数据:', data);
                    
                    if (data.success) {
                        const availabilityDiv = document.getElementById('template-availability');
                        if (availabilityDiv) {
                            // 构建模板可用情况提示
                            let html = `<p>当前分类共有 <strong>${data.total_templates}</strong> 个模板</p>`;
                            
                            if (duplicateControl === 'allow_duplicate') {
                                html += `<p class="text-success"><i class="fas fa-check-circle"></i> 允许重复使用模板，您可以生成任意数量的文章 (最多1000篇)。</p>`;
                            } else {
                                // 根据可用模板数量设置不同的颜色和样式
                                if (data.available_templates === 0) {
                                    html += `<p>已使用 <strong>${data.used_templates}</strong> 个模板，剩余 <strong class="text-danger bg-danger bg-opacity-10 px-2 py-1 rounded">${data.available_templates} 个可用模板</strong>。</p>`;
                                    html += `<p class="text-danger"><i class="fas fa-exclamation-triangle"></i> <strong>警告：所有模板已被使用！</strong><br>如需生成更多文案，请选择"允许重复使用模板"选项。</p>`;
                                } else if (data.available_templates < 5) {
                                    html += `<p>已使用 <strong>${data.used_templates}</strong> 个模板，剩余 <strong class="text-warning bg-warning bg-opacity-10 px-2 py-1 rounded">${data.available_templates} 个可用模板</strong>。</p>`;
                                    html += `<p class="text-warning"><i class="fas fa-exclamation-circle"></i> 注意：可用模板数量较少，如需生成更多文案，可以选择"允许重复使用模板"选项。</p>`;
                                } else {
                                    html += `<p>已使用 <strong>${data.used_templates}</strong> 个模板，剩余 <strong class="text-success bg-success bg-opacity-10 px-2 py-1 rounded">${data.available_templates} 个可用模板</strong>。</p>`;
                                    html += `<p class="text-success"><i class="fas fa-check-circle"></i> 可用模板充足。</p>`;
                                }
                            }
                            
                            availabilityDiv.innerHTML = html;

                            // 确保父容器始终可见
                            const parentPanel = availabilityDiv.closest('.custom-info-panel');
                            if (parentPanel) {
                                parentPanel.style.display = 'block';
                                parentPanel.style.visibility = 'visible';
                                parentPanel.style.opacity = '1';
                                console.log('强制显示模板可用情况面板');
                            }
                        }
                    } else {
                        console.error('获取模板可用情况失败:', data.message);

                        // 显示错误信息
                        const availabilityDiv = document.getElementById('template-availability');
                        if (availabilityDiv) {
                            availabilityDiv.innerHTML = `<p class="text-warning">获取模板可用情况失败: ${data.message}</p>`;

                            // 确保父容器始终可见
                            const parentPanel = availabilityDiv.closest('.custom-info-panel');
                            if (parentPanel) {
                                parentPanel.style.display = 'block';
                                parentPanel.style.visibility = 'visible';
                                parentPanel.style.opacity = '1';
                                console.log('强制显示模板可用情况面板（错误情况）');
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('获取模板可用情况失败:', error);
                    
                    // 出错时显示默认信息
                    const availabilityDiv = document.getElementById('template-availability');
                    if (availabilityDiv) {
                        availabilityDiv.innerHTML = `<p class="text-warning">获取模板可用情况失败，请重试</p>`;
                    }
                });
        } catch (error) {
            console.error(`更新模板可用情况出错: ${error.message}`);
        }
    }
    
    // 初始化重复控制单选按钮
    function initDuplicateControlChange() {
        document.querySelectorAll('input[name="duplicate_control"]').forEach(radio => {
            radio.addEventListener('change', function() {
                try {
                    // 更新隐藏字段
                    document.getElementById('allow_template_duplicate').value =
                        (this.value === 'allow_duplicate') ? '1' :
                        (this.value === 'no_duplicate_all') ? '3' : '0';
                    
                    // 清空验证反馈
                    const countFeedback = document.getElementById('count-feedback');
                    if (countFeedback) {
                        countFeedback.innerHTML = '';
                    }
                    
                    // 更新允许重复使用模板的情况提示
                    if (this.value === 'allow_duplicate') {
                        const availabilityDiv = document.getElementById('template-availability');
                        if (availabilityDiv) {
                            const categoryId = document.getElementById('template_category_id')?.value;
                            if (categoryId) {
                                // 获取模板总数
                                const totalTemplates = availabilityDiv.textContent.match(/共有\s*(\d+)\s*个模板/);
                                const count = totalTemplates ? parseInt(totalTemplates[1]) : '未知数量';
                                
                                availabilityDiv.innerHTML = `
                                    <p>当前分类共有模板${count}个，允许重复使用。</p>
                                    <p>您可以生成任意数量的文章 (最多100篇)。</p>
                                `;
                            } else {
                                availabilityDiv.innerHTML = '<p>请先选择模板分类</p>';
                            }
                        }
                    }
                    
                    // 更新模板可用情况
                    updateTemplateAvailability();
                } catch (error) {
                    console.error(`重复控制变更处理出错: ${error.message}`);
                }
            });
        });
    }

    // 绑定预览更新事件
    function bindPreviewUpdateEvents() {
        // 监听所有可能影响预览的元素
        const elementsToWatch = [
            'client_id',
            'task_id',
            'new_task_name',
            'template_category_id',
            'count',
            'avoid_duplicates'
        ];

        elementsToWatch.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', updateGeneratePreview);
                if (element.type === 'text' || element.type === 'number') {
                    element.addEventListener('input', debounce(updateGeneratePreview, 300));
                }
            }
        });

        // 监听重复控制单选按钮
        const duplicateControls = document.querySelectorAll('input[name="duplicate_control"]');
        duplicateControls.forEach(radio => {
            radio.addEventListener('change', updateGeneratePreview);
        });

        // 监听关键词字段变化
        const keywordsField = document.getElementById('keywords');
        if (keywordsField) {
            keywordsField.addEventListener('input', debounce(updateGeneratePreview, 300));
        }

        // 初始更新预览
        updateGeneratePreview();
    }

    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 保护蓝色提示区域，确保永不消失
    function protectInfoAreas() {
        const infoAreas = document.querySelectorAll('.custom-info-panel');

        console.log(`找到 ${infoAreas.length} 个信息面板需要保护`);

        infoAreas.forEach((area, index) => {
            console.log(`保护信息面板 ${index + 1}`);

            // 强制设置样式
            area.style.display = 'block';
            area.style.visibility = 'visible';
            area.style.opacity = '1';
            area.style.position = 'relative';
            area.style.zIndex = '1';

            // 监听属性变化，防止被隐藏
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes') {
                        const target = mutation.target;
                        if (target.classList.contains('custom-info-panel')) {
                            console.log('检测到信息面板属性变化，强制恢复显示');
                            // 强制恢复显示
                            target.style.display = 'block';
                            target.style.visibility = 'visible';
                            target.style.opacity = '1';
                        }
                    }
                });
            });

            // 开始观察
            observer.observe(area, {
                attributes: true,
                attributeFilter: ['style', 'class']
            });
        });

        // 定期检查并强制显示
        const protectionInterval = setInterval(() => {
            const currentAreas = document.querySelectorAll('.custom-info-panel');
            currentAreas.forEach((area, index) => {
                if (area.style.display === 'none' ||
                    area.style.visibility === 'hidden' ||
                    area.style.opacity === '0' ||
                    area.style.opacity === '') {
                    console.log(`强制显示信息面板 ${index + 1}`);
                    area.style.display = 'block';
                    area.style.visibility = 'visible';
                    area.style.opacity = '1';
                }
            });
        }, 500); // 每500毫秒检查一次

        // 返回清理函数（如果需要的话）
        return () => {
            clearInterval(protectionInterval);
        };
    }

    // 页面加载完成后立即保护提示区域
    document.addEventListener('DOMContentLoaded', function() {
        console.log('页面加载完成，开始保护信息面板');

        // 立即保护
        protectInfoAreas();

        // 立即显示初始内容
        setTimeout(() => {
            console.log('显示初始内容');
            updateGeneratePreview();
            updateTemplateAvailability();
        }, 100);

        // 多次延迟保护，确保所有动态内容加载完成
        setTimeout(() => {
            console.log('第二次保护');
            protectInfoAreas();
        }, 500);

        setTimeout(() => {
            console.log('第三次保护');
            protectInfoAreas();
        }, 1000);

        setTimeout(() => {
            console.log('第四次保护');
            protectInfoAreas();
        }, 3000);

        setTimeout(() => {
            console.log('第五次保护');
            protectInfoAreas();
        }, 5000);
    });

    // 绑定表单提交事件
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('generateForm');
        if (form) {
            form.addEventListener('submit', handleFormSubmit);
        }
    });

    // 防重复提交标志
    let isSubmitting = false;

    // 处理表单提交
    function handleFormSubmit(event) {
        event.preventDefault(); // 阻止默认提交

        // 防重复提交检查
        if (isSubmitting) {
            console.log('表单正在提交中，忽略重复提交');
            return false;
        }

        console.log('开始表单提交验证...');
        isSubmitting = true;

        // 显示提交中状态
        const submitBtn = document.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 验证中...';

        // 执行异步验证
        validateForm()
            .then(isValid => {
                if (isValid) {
                    console.log('验证通过，提交表单');
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';

                    // 设置验证标记
                    document.getElementById('form_validated').value = '1';

                    // 使用原生提交，避免重复触发事件
                    const form = document.getElementById('generateForm');
                    // 移除事件监听器，避免重复提交
                    form.removeEventListener('submit', handleFormSubmit);
                    form.submit();
                } else {
                    console.log('验证失败，阻止提交');
                    // 恢复按钮状态
                    submitBtn.disabled = false;
                    submitBtn.textContent = originalText;
                    // 重置提交标志
                    isSubmitting = false;
                }
            })
            .catch(error => {
                console.error('验证过程出错:', error);
                showToast('验证过程出错，请重试', 'danger');
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
                // 重置提交标志
                isSubmitting = false;
            });

        return false; // 阻止默认提交
    }
</script>
{% endblock %}