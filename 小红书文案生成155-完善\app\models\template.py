"""
模板相关模型
"""
from datetime import datetime
from . import db


class TemplateCategory(db.Model):
    """模板分类模型"""
    __tablename__ = 'template_categories'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    parent_id = db.Column(db.Integer, db.ForeignKey('template_categories.id'))
    sort_order = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 父分类关联（自引用关系）
    parent = db.relationship('TemplateCategory', remote_side=[id], backref=db.backref('children', lazy='dynamic'))
    # 该分类下的模板
    templates = db.relationship('Template', backref='category', lazy='dynamic')
    
    def __repr__(self):
        return f'<TemplateCategory {self.name}>'


class Template(db.Model):
    """模板模型"""
    __tablename__ = 'templates'
    
    id = db.Column(db.Integer, primary_key=True)
    category_id = db.Column(db.Integer, db.ForeignKey('template_categories.id'), nullable=False)
    title = db.Column(db.String(100), nullable=False)
    content = db.Column(db.Text, nullable=False)
    marks = db.Column(db.JSON, comment='模板中的标记列表，JSON格式存储')  # JSON字段存储标记列表
    creator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    status = db.Column(db.Boolean, default=True)  # True为启用，False为禁用
    
    # 创建者关联
    creator = db.relationship('User', backref=db.backref('templates', lazy='dynamic'))

    def extract_marks(self):
        """从模板标题和内容中提取标记"""
        import re

        marks = set()

        # 从标题中提取标记
        if self.title:
            title_marks = re.findall(r'\{([^}]+)\}', self.title)
            marks.update(title_marks)

        # 从内容中提取标记
        if self.content:
            content_marks = re.findall(r'\{([^}]+)\}', self.content)
            marks.update(content_marks)

        return sorted(list(marks))

    def update_marks(self):
        """更新模板的标记字段"""
        marks_list = self.extract_marks()

        # 将标记列表保存到JSON字段
        if marks_list:
            # JSON字段可以直接接受Python列表
            self.marks = marks_list
        else:
            self.marks = None

        return marks_list

    def get_marks_list(self):
        """获取标记列表"""
        if self.marks:
            # JSON字段可能返回Python列表或字符串
            print(f"DEBUG - marks字段值: {self.marks}, 类型: {type(self.marks)}")
            if isinstance(self.marks, list):
                # 直接是Python列表
                print(f"DEBUG - 直接返回Python列表: {self.marks}")
                return self.marks
            elif isinstance(self.marks, str):
                # 字符串格式的JSON，需要解析
                try:
                    import json
                    parsed = json.loads(self.marks)
                    print(f"DEBUG - JSON解析成功: {parsed}")
                    if isinstance(parsed, list):
                        return parsed
                    else:
                        # 如果解析结果不是列表，从内容中实时提取
                        print(f"DEBUG - 解析结果不是列表: {type(parsed)}")
                        return self.extract_marks()
                except Exception as e:
                    print(f"DEBUG - JSON解析失败: {e}, marks内容: {self.marks}, marks类型: {type(self.marks)}")
                    # 如果解析失败，从内容中实时提取
                    extracted = self.extract_marks()
                    print(f"DEBUG - 实时提取的标记: {extracted}")
                    return extracted
            else:
                # 其他类型，从内容中实时提取
                print(f"DEBUG - marks字段类型未知: {type(self.marks)}, 值: {self.marks}")
                return self.extract_marks()
        else:
            # 如果marks字段为空，从内容中实时提取
            return self.extract_marks()

    def save(self):
        """保存模板时自动提取标记"""
        # 自动提取并更新标记
        self.update_marks()

        # 保存到数据库
        db.session.add(self)
        db.session.commit()

        return self



    def __repr__(self):
        return f'<Template {self.title}>'


class TemplateMark(db.Model):
    """标记定义模型"""
    __tablename__ = 'template_marks'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)  # 添加唯一性约束
    description = db.Column(db.String(200))
    type = db.Column(db.String(20), default='text')  # text, topic, location, user 等
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    def __repr__(self):
        return f'<TemplateMark {self.name}>' 