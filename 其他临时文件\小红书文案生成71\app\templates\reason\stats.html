{% extends "base.html" %}

{% block title %}拒绝理由统计{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">拒绝理由统计</h2>
        <div class="btn-group">
            <a href="{{ url_for('reason.reason_list') }}" class="btn btn-primary">
                <i class="fas fa-list"></i> 快捷理由列表
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">最常用拒绝理由</h5>
                </div>
                <div class="card-body">
                    {% if top_reasons %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>拒绝理由</th>
                                    <th width="100">使用次数</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for reason in top_reasons %}
                                <tr>
                                    <td>{{ reason.reason }}</td>
                                    <td>{{ reason.count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted text-center py-4">暂无拒绝理由数据</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">审核人员拒绝统计</h5>
                </div>
                <div class="card-body">
                    {% if user_stats %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th width="100">拒绝次数</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stat in user_stats %}
                                <tr>
                                    <td>{{ stat.username }}</td>
                                    <td>{{ stat.count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted text-center py-4">暂无用户拒绝数据</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">客户被拒绝统计</h5>
                </div>
                <div class="card-body">
                    {% if client_stats %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>客户名称</th>
                                    <th width="100">被拒绝次数</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stat in client_stats %}
                                <tr>
                                    <td>{{ stat.name }}</td>
                                    <td>{{ stat.count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted text-center py-4">暂无客户被拒绝数据</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
{% endblock %} 