{% extends "base.html" %}

{% block title %}快捷理由管理{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">快捷理由管理</h2>
        <div class="btn-group">
            <a href="{{ url_for('reason.reason_create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> 创建快捷理由
            </a>
            <a href="{{ url_for('reason.rejection_stats') }}" class="btn btn-info">
                <i class="fas fa-chart-bar"></i> 拒绝理由统计
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">快捷理由列表</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="60">ID</th>
                            <th>理由内容</th>
                            <th width="100">排序</th>
                            <th width="150">创建时间</th>
                            <th width="150">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for reason in reasons %}
                        <tr>
                            <td>{{ reason.id }}</td>
                            <td>{{ reason.content }}</td>
                            <td>{{ reason.sort_order }}</td>
                            <td>{{ reason.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('reason.reason_edit', reason_id=reason.id) }}" class="btn btn-outline-primary" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-danger" onclick="confirmDelete({{ reason.id }})" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="5" class="text-center py-4">暂无快捷理由数据</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        {% if pagination.pages > 1 %}
        <div class="card-footer">
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    {% if pagination.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('reason.reason_list', page=pagination.prev_num) }}">
                            <span>&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">&laquo;</span>
                    </li>
                    {% endif %}
                    
                    {% for page in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                        {% if page %}
                            {% if page == pagination.page %}
                            <li class="page-item active">
                                <span class="page-link">{{ page }}</span>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('reason.reason_list', page=page) }}">{{ page }}</a>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if pagination.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('reason.reason_list', page=pagination.next_num) }}">
                            <span>&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">&raquo;</span>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个快捷理由吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" action="">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 确认删除
    function confirmDelete(reasonId) {
        document.getElementById('deleteForm').action = `/reasons/${reasonId}/delete`;
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }
</script>
{% endblock %} 