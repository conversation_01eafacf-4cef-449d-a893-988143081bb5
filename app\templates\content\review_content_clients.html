<!-- 初审文案客户列表页面 -->
<style>
.status-badge {
    font-size: 0.85rem;
    padding: 0.4rem 0.8rem;
    border-radius: 0.5rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
}

.status-badge i {
    font-size: 0.9rem;
}

.badge-gap {
    gap: 0.5rem;
}

.table td {
    vertical-align: middle;
}
</style>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="bi bi-clipboard-check"></i> 初审文案</h2>
            <p class="text-muted">选择客户查看待审核的文案</p>
        </div>
    </div>

    <!-- 客户列表 -->
    <div class="card">
        <div class="card-body">
            {% if clients_with_pending %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>客户名称</th>
                                <th>文案状态统计</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for client in clients_with_pending %}
                            <tr>
                                <td>
                                    <strong>{{ client.name }}</strong>
                                </td>
                                <td>
                                    <div class="d-flex flex-wrap badge-gap">
                                        {% if client.draft_count > 0 %}
                                        <span class="badge bg-primary status-badge">
                                            <i class="bi bi-file-text me-1"></i>
                                            {{ client.draft_count }} 篇草稿
                                        </span>
                                        {% endif %}
                                        {% if client.rejected_count > 0 %}
                                        <span class="badge bg-danger status-badge">
                                            <i class="bi bi-x-circle me-1"></i>
                                            {{ client.rejected_count }} 篇被驳回
                                        </span>
                                        {% endif %}
                                        {% if client.draft_count == 0 and client.rejected_count == 0 %}
                                        <span class="badge bg-success status-badge">
                                            <i class="bi bi-check-circle me-1"></i>
                                            暂无待处理
                                        </span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <a href="{{ url_for('main_simple.review_client_content_detail', client_id=client.id) }}"
                                       class="btn btn-primary btn-sm">
                                        <i class="bi bi-eye"></i> 查看文案
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-clipboard-check text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">暂无待审核文案</h4>
                    <p class="text-muted">所有客户的文案都已审核完成</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}
</style>
