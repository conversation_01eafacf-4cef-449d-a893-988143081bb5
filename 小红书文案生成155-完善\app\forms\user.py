from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, PasswordField, EmailField, SelectMultipleField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email, Length, EqualTo, Optional

from app.models.user import Role, Permission

class UserCreateForm(FlaskForm):
    """创建用户表单"""
    username = StringField('用户名', validators=[DataRequired(), Length(1, 50)])
    password = PasswordField('密码', validators=[DataRequired(), Length(6, 50)])
    confirm_password = PasswordField('确认密码', validators=[DataRequired(), EqualTo('password', message='两次输入的密码不一致')])
    email = EmailField('电子邮箱', validators=[DataRequired(), Email(), Length(1, 100)])
    real_name = StringField('真实姓名', validators=[Optional(), Length(0, 50)])
    phone = StringField('电话', validators=[Optional(), Length(0, 20)])

    # 基于菜单的权限选择
    dashboard_access = BooleanField('控制面板')
    content_generate = BooleanField('生成文案')
    content_manage = BooleanField('文案管理')
    template_manage = BooleanField('模板管理')
    topic_manage = BooleanField('话题管理')
    client_manage = BooleanField('客户管理')
    task_manage = BooleanField('任务管理')
    content_generate = BooleanField('内容生成')
    publish_manage = BooleanField('发布管理')
    supplement_manage = BooleanField('文案补充')
    display_manage = BooleanField('文案展示')
    notification_manage = BooleanField('通知中心')
    stats_view = BooleanField('数据统计')
    export_manage = BooleanField('导入导出')
    user_manage = BooleanField('用户管理')
    system_settings = BooleanField('系统设置')

    is_active = BooleanField('是否启用', default=True)
    submit = SubmitField('创建用户')

class UserEditForm(FlaskForm):
    """编辑用户表单"""
    username = StringField('用户名', validators=[DataRequired(), Length(1, 50)])
    email = EmailField('电子邮箱', validators=[DataRequired(), Email(), Length(1, 100)])
    real_name = StringField('真实姓名', validators=[Optional(), Length(0, 50)])
    phone = StringField('电话', validators=[Optional(), Length(0, 20)])

    # 基于菜单的权限选择
    dashboard_access = BooleanField('控制面板')
    content_generate = BooleanField('生成文案')
    content_manage = BooleanField('文案管理')
    template_manage = BooleanField('模板管理')
    topic_manage = BooleanField('话题管理')
    client_manage = BooleanField('客户管理')
    task_manage = BooleanField('任务管理')
    publish_manage = BooleanField('发布管理')
    supplement_manage = BooleanField('文案补充')
    display_manage = BooleanField('文案展示')
    notification_manage = BooleanField('通知中心')
    stats_view = BooleanField('数据统计')
    export_manage = BooleanField('导入导出')
    user_manage = BooleanField('用户管理')
    system_settings = BooleanField('系统设置')

    is_active = BooleanField('是否启用', default=True)
    submit = SubmitField('保存修改')

class UserPasswordForm(FlaskForm):
    """修改用户密码表单"""
    password = PasswordField('新密码', validators=[DataRequired(), Length(6, 50)])
    confirm_password = PasswordField('确认密码', validators=[DataRequired(), EqualTo('password', message='两次输入的密码不一致')])
    submit = SubmitField('修改密码')

class UserRoleForm(FlaskForm):
    """用户角色分配表单"""
    roles = SelectMultipleField('角色', coerce=int)
    submit = SubmitField('分配角色')

    def __init__(self, *args, **kwargs):
        super(UserRoleForm, self).__init__(*args, **kwargs)
        self.roles.choices = [(role.id, role.name) for role in Role.query.order_by(Role.name).all()]

class UserPermissionForm(FlaskForm):
    """用户菜单权限分配表单"""
    # 基于菜单的权限选择
    dashboard_access = BooleanField('控制面板')
    content_generate = BooleanField('生成文案')
    content_manage = BooleanField('文案管理')
    template_manage = BooleanField('模板管理')
    topic_manage = BooleanField('话题管理')
    client_manage = BooleanField('客户管理')
    task_manage = BooleanField('任务管理')
    publish_manage = BooleanField('发布管理')
    supplement_manage = BooleanField('文案补充')
    display_manage = BooleanField('文案展示')
    notification_manage = BooleanField('通知中心')
    stats_view = BooleanField('数据统计')
    export_manage = BooleanField('导入导出')
    user_manage = BooleanField('用户管理')
    system_settings = BooleanField('系统设置')
    submit = SubmitField('保存权限')