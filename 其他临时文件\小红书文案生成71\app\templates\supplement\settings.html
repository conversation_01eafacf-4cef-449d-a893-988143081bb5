{% extends "base.html" %}

{% block title %}补充设置{% endblock %}

{% block page_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">补充设置</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('supplement.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <h5><i class="fas fa-info-circle"></i> 补充来源说明：</h5>
                        <ul>
                            <li><strong>从未分配文案池补充</strong>：从未分配给任何任务的文案中选择进行补充</li>
                            <li><strong>生成新文案</strong>：根据模板和关键词生成新的文案进行补充（目前尚未实现）</li>
                        </ul>
                    </div>
                    
                    <form method="post" action="{{ url_for('supplement.settings') }}">
                        {{ form.csrf_token }}
                        <div class="mb-3">
                            <label class="form-label">{{ form.source.label }}</label>
                            <div class="form-check">
                                {{ form.source(class="form-check-input") }}
                            </div>
                            {% if form.source.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.source.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                            <a href="{{ url_for('supplement.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 