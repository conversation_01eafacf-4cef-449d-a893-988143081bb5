#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
添加客户管理所需的权限
"""

from app import create_app
from app.models import db, Permission, Role

def main():
    app = create_app()
    with app.app_context():
        # 检查并添加客户创建权限
        if not Permission.query.filter_by(name='client_create').first():
            permission = Permission(name='client_create', description='创建客户权限')
            db.session.add(permission)
            print('已添加权限: client_create - 创建客户权限')
        
        # 检查并添加客户编辑权限
        if not Permission.query.filter_by(name='client_edit').first():
            permission = Permission(name='client_edit', description='编辑客户权限')
            db.session.add(permission)
            print('已添加权限: client_edit - 编辑客户权限')
        
        # 检查并添加客户删除权限
        if not Permission.query.filter_by(name='client_delete').first():
            permission = Permission(name='client_delete', description='删除客户权限')
            db.session.add(permission)
            print('已添加权限: client_delete - 删除客户权限')
        
        # 检查并添加客户分享权限
        if not Permission.query.filter_by(name='client_share_view').first():
            permission = Permission(name='client_share_view', description='查看客户分享链接权限')
            db.session.add(permission)
            print('已添加权限: client_share_view - 查看客户分享链接权限')
        
        if not Permission.query.filter_by(name='client_share_create').first():
            permission = Permission(name='client_share_create', description='创建客户分享链接权限')
            db.session.add(permission)
            print('已添加权限: client_share_create - 创建客户分享链接权限')
        
        if not Permission.query.filter_by(name='client_share_delete').first():
            permission = Permission(name='client_share_delete', description='删除客户分享链接权限')
            db.session.add(permission)
            print('已添加权限: client_share_delete - 删除客户分享链接权限')
        
        # 将这些权限添加到管理员角色
        admin_role = Role.query.filter_by(name='admin').first()
        if admin_role:
            # 获取所有新添加的权限
            new_permissions = [
                Permission.query.filter_by(name='client_create').first(),
                Permission.query.filter_by(name='client_edit').first(),
                Permission.query.filter_by(name='client_delete').first(),
                Permission.query.filter_by(name='client_share_view').first(),
                Permission.query.filter_by(name='client_share_create').first(),
                Permission.query.filter_by(name='client_share_delete').first()
            ]
            
            # 添加到管理员角色
            for perm in new_permissions:
                if perm and perm not in admin_role.permissions:
                    admin_role.permissions.append(perm)
                    print(f'已将权限 {perm.name} 添加到管理员角色')
        
        # 提交更改
        db.session.commit()
        print('权限添加完成')

if __name__ == '__main__':
    main() 