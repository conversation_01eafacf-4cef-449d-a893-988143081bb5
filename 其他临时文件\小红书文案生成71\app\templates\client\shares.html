{% extends "base.html" %}

{% block title %}客户分享链接 - {{ client.name }}{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">客户分享链接 - {{ client.name }}</h2>
        <a href="{{ url_for('client.share_create', client_id=client.id) }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 创建分享链接
        </a>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">分享链接列表</h5>
                <a href="{{ url_for('client.client_list') }}" class="btn btn-sm btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回客户列表
                </a>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>ID</th>
                            <th>访问链接</th>
                            <th>访问密码</th>
                            <th>权限</th>
                            <th>有效期至</th>
                            <th>创建时间</th>
                            <th>创建者</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for share in shares %}
                        <tr {% if request.args.get('new_share_id')|int == share.id %}class="table-success"{% endif %}>
                            <td>{{ share.id }}</td>
                            <td>
                                <div class="input-group">
                                    <input type="text" class="form-control form-control-sm" 
                                           value="{{ url_for('client.share_access', token=share.access_token, _external=True) }}{% if share.password %}?key={{ share.password }}{% endif %}" 
                                           id="shareLink{{ share.id }}" readonly>
                                    <button class="btn btn-sm btn-outline-primary" type="button" 
                                            onclick="copyToClipboard('shareLink{{ share.id }}')" title="复制链接">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </td>
                            <td>
                                {% if share.password %}
                                <span class="badge bg-info">{{ share.password }}</span>
                                {% else %}
                                <span class="badge bg-secondary">无密码</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if share.view_permission %}<span class="badge bg-success">查看</span>{% endif %}
                                {% if share.edit_permission %}<span class="badge bg-primary">编辑</span>{% endif %}
                                {% if share.review_permission %}<span class="badge bg-warning text-dark">审核</span>{% endif %}
                            </td>
                            <td>
                                {% if share.expires_at %}
                                {{ share.expires_at.strftime('%Y-%m-%d %H:%M') }}
                                {% if share.is_valid() %}
                                <span class="badge bg-success">有效</span>
                                {% else %}
                                <span class="badge bg-danger">已过期</span>
                                {% endif %}
                                {% else %}
                                <span class="badge bg-info">永久有效</span>
                                {% endif %}
                            </td>
                            <td>{{ share.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>{{ share.creator.username if share.creator else '-' }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('client.share_access', token=share.access_token) }}{% if share.password %}?key={{ share.password }}{% endif %}" 
                                       class="btn btn-outline-primary" title="访问链接" target="_blank">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="deleteShare({{ share.id }})" title="删除链接">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="8" class="text-center py-4">暂无分享链接</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    {% if request.args.get('new_share_url') %}
    <!-- 新创建的分享链接信息 -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-check-circle me-2"></i>分享链接创建成功</h5>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <label class="form-label">完整分享链接</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="fullShareLink" value="{{ request.args.get('new_share_url') }}" readonly>
                    <button class="btn btn-outline-primary" type="button" onclick="copyToClipboard('fullShareLink')">
                        <i class="fas fa-copy"></i> 复制链接
                    </button>
                </div>
                <div class="form-text">此链接包含访问密码，可直接分享给客户使用。</div>
            </div>
            
            {% if request.args.get('new_share_password') %}
            <div class="mb-3">
                <label class="form-label">访问密码</label>
                <div class="input-group">
                    <input type="text" class="form-control" value="{{ request.args.get('new_share_password') }}" readonly>
                    <button class="btn btn-outline-primary" type="button" onclick="copyToClipboard('passwordField')">
                        <i class="fas fa-copy"></i> 复制密码
                    </button>
                </div>
                <div class="form-text">请将此密码告知客户，用于访问分享链接。</div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
    // 复制文本到剪贴板
    function copyToClipboard(elementId) {
        var copyText = document.getElementById(elementId);
        copyText.select();
        copyText.setSelectionRange(0, 99999);
        document.execCommand("copy");
        
        // 显示复制成功提示
        var toast = document.createElement("div");
        toast.className = "position-fixed top-0 end-0 p-3";
        toast.style.zIndex = "1050";
        toast.innerHTML = `
            <div class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-check-circle me-2"></i> 已复制到剪贴板
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;
        document.body.appendChild(toast);
        
        var bsToast = new bootstrap.Toast(toast.querySelector('.toast'));
        bsToast.show();
        
        setTimeout(function() {
            document.body.removeChild(toast);
        }, 3000);
    }
    
    // 删除分享链接
    function deleteShare(shareId) {
        if (confirm('确定要删除此分享链接吗？此操作不可恢复！')) {
            fetch(`/clients/share/${shareId}/delete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('删除失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败，请重试');
            });
        }
    }
    
    // 自动复制新创建的链接
    document.addEventListener('DOMContentLoaded', function() {
        {% if request.args.get('new_share_url') %}
        copyToClipboard('fullShareLink');
        {% endif %}
    });
</script>
{% endblock %} 