# 小红书文案管理系统 - 状态标记说明

## 📋 概述

本文档详细说明了系统中各种状态的判断逻辑，特别是文案初审页面和图片上传页面的状态显示规则。

---

## 🔍 核心字段说明

### 主要状态字段
- **`workflow_status`**: 工作流状态（主要状态）
- **`internal_review_status`**: 内部审核状态（详细状态）
- **`client_review_status`**: 客户审核状态
- **`image_completed`**: 图片是否完成（0=未完成，1=已完成）
- **`content_completed`**: 文案是否完成（0=未完成，1=已完成）

---

## 📄 文案初审页面状态判断

**页面路径**: `/simple/review-content`

### 1. 草稿状态 📝
**显示条件**:
```
workflow_status == 'draft' 
AND client_review_status != 'rejected' 
AND internal_review_status != 'rejected'
AND internal_review_status NOT IN ['final_rej_text', 'final_rej_both', 'final_rej_text_ok']
```

**什么情况下出现**:
- ✅ 新生成的文案（初始状态）
- ✅ 用户手动编辑后保存的文案
- ✅ 长度超出限制的文案（`internal_review_status = 'length_exceeded'`）

**不会出现的情况**:
- ❌ 被内部驳回的文案
- ❌ 被客户驳回的文案
- ❌ 最终审核驳回的文案

### 2. 内部驳回状态 ⚠️
**显示条件**:
```
(workflow_status == 'draft' AND internal_review_status == 'rejected')
OR (workflow_status == 'draft' AND internal_review_status == 'final_rej_text')
OR (workflow_status == 'draft' AND internal_review_status == 'final_rej_both')
OR (workflow_status == 'draft' AND internal_review_status == 'final_rej_text_ok')
OR (workflow_status == 'pending_review' AND internal_review_status IN ['rejected', 'final_rej_text', 'final_rej_both', 'final_rej_text_ok'])
```

**什么情况下出现**:
- ✅ 初审被驳回的文案
- ✅ 最终审核驳回（文案问题）的文案
- ✅ 最终审核驳回（两者都有问题）的文案
- ✅ 最终审核驳回（文案问题但图片已修复）的文案

### 3. 客户驳回状态 🔴
**显示条件**:
```
workflow_status == 'draft' AND client_review_status == 'rejected'
```

**什么情况下出现**:
- ✅ 客户审核拒绝后，回退到初审页面的文案

---

## 🖼️ 图片上传页面状态判断

**页面路径**: `/simple/image-upload/client/{client_id}`

### 1. 图片待上传状态 📤
**显示条件**:
```
workflow_status == 'first_reviewed' AND 图片数量 == 0
```

**什么情况下出现**:
- ✅ 初审通过但还没有上传任何图片的文案
- ✅ 最终审核驳回（图片问题）后，重新上传前的状态

### 2. 图片已上传状态 ✅
**显示条件**:
```
(workflow_status == 'first_reviewed' AND 图片数量 > 0)
OR workflow_status == 'image_uploaded'
OR workflow_status == 'final_review'
```

**什么情况下出现**:
- ✅ 初审通过且已上传图片的文案
- ✅ 已提交图片审核的文案
- ✅ 进入最终审核的文案

### 3. 客户驳回状态 🔴
**显示条件**:
```
client_review_status == 'rejected'
```

**什么情况下出现**:
- ✅ 客户审核拒绝的文案（需要重新处理图片）

### 4. 内部驳回状态 ⚠️
**显示条件**:
```
(internal_review_status == 'rejected' OR internal_review_status LIKE 'final_rej%')
AND image_completed != 1
```

**什么情况下出现**:
- ✅ 最终审核驳回（图片问题）的文案
- ✅ 最终审核驳回（两者都有问题）且图片未完成的文案

---

## 🔄 状态流转图

```
新生成文案
    ↓
[草稿] workflow_status='draft', internal_review_status='pending'
    ↓ (初审通过)
[初审通过] workflow_status='first_reviewed'
    ↓ (上传图片)
[图片已上传] workflow_status='first_reviewed' + 有图片
    ↓ (提交图片审核)
[待最终审核] workflow_status='final_review'
    ↓ (最终审核)
[完成] → 客户审核 → 发布
```

### 驳回流转
```
最终审核驳回 → workflow_status='draft' + internal_review_status='final_rej_*'
    ↓
[内部驳回] 显示在初审页面
    ↓ (重新审核通过)
[初审通过] → 图片上传流程
```

---

## ⚠️ 常见问题和注意事项

### 1. 为什么文案显示在"内部驳回"而不是"草稿"？
- 检查 `internal_review_status` 是否包含 `rejected` 或 `final_rej` 开头的值
- 最终审核驳回的文案会自动设置相应的驳回状态

### 2. 为什么上传图片后还显示"图片待上传"？
- 检查前端模板的状态判断逻辑是否基于图片数量
- 确保 `item.image_count > 0` 的判断正确

### 3. 状态不一致的排查步骤
1. 检查 `workflow_status` 的值
2. 检查 `internal_review_status` 的值
3. 检查 `client_review_status` 的值
4. 检查图片数量和完成标记
5. 对照本文档的判断条件

---

## � 客户驳回状态详细逻辑

### 客户驳回状态类型

#### 1. `client_rej_both` - 两者都有问题
**初始状态**：
- `workflow_status = 'draft'`
- `client_review_status = 'rejected'`
- `internal_review_status = 'client_rej_both'`
- `content_completed = 0`
- `image_completed = 0`

**显示位置**：
- ✅ 文案审核页面（初审页面）
- ✅ 图片上传页面

#### 2. `client_rej_text` - 只有文案问题
**初始状态**：
- `workflow_status = 'draft'`
- `client_review_status = 'rejected'`
- `internal_review_status = 'client_rej_text'`
- `content_completed = 0`
- `image_completed = 1`（图片已完成）

**显示位置**：
- ✅ 文案审核页面（初审页面）
- ❌ 图片上传页面（因为图片已完成）

#### 3. `client_rej_img` - 只有图片问题
**初始状态**：
- `workflow_status = 'first_reviewed'`
- `client_review_status = 'rejected'`
- `internal_review_status = 'client_rej_img'`
- `content_completed = 1`（文案已完成）
- `image_completed = 0`

**显示位置**：
- ❌ 文案审核页面（因为文案已完成）
- ✅ 图片上传页面

### 状态流转逻辑

#### 场景1：两者都有问题 → 先提交图片
```
初始：client_rej_both (draft + rejected + client_rej_both)
  ↓ 图片提交
检查：content_completed == 1？
  ├─ 是 → final_review (图片和文案都完成，进入终审)
  └─ 否 → client_rej_text (draft + rejected + client_rej_text + image_completed=1)
```

**关键点**：
- 图片提交后，如果文案未完成，状态变为 `client_rej_text`
- 此时文案仍显示在文案审核页面，但**不再显示在图片上传页面**
- 查询条件排除：`internal_review_status == 'client_rej_text' AND image_completed == 1`

#### 场景2：两者都有问题 → 先提交文案
```
初始：client_rej_both (draft + rejected + client_rej_both)
  ↓ 文案提交
检查：image_completed == 1？
  ├─ 是 → final_review (图片和文案都完成，进入终审)
  └─ 否 → client_rej_img (first_reviewed + rejected + client_rej_img + content_completed=1)
```

**关键点**：
- 文案提交后，如果图片未完成，状态变为 `client_rej_img`
- 此时文案不再显示在文案审核页面，但**显示在图片上传页面**

#### 场景3：只有文案问题 → 提交文案
```
初始：client_rej_text (draft + rejected + client_rej_text + image_completed=1)
  ↓ 文案提交
直接 → final_review (图片已完成，文案也完成，进入终审)
```

#### 场景4：只有图片问题 → 提交图片
```
初始：client_rej_img (first_reviewed + rejected + client_rej_img + content_completed=1)
  ↓ 图片提交
直接 → final_review (文案已完成，图片也完成，进入终审)
```

### 图片上传页面查询条件

```python
db.or_(
    # 正常的图片上传流程
    Content.workflow_status.in_(['first_reviewed', 'image_uploaded']),

    # 客户驳回（图片问题）- 排除已经处理过图片的情况
    db.and_(
        Content.workflow_status == 'draft',
        Content.client_review_status == 'rejected',
        db.not_(
            db.and_(
                Content.internal_review_status == 'client_rej_text',
                Content.image_completed == 1
            )
        )
    ),

    # 内部驳回（图片问题）
    Content.internal_review_status == 'rejected',
    Content.internal_review_status.like('final_rej%')
)
```

**关键排除条件**：
- `NOT (internal_review_status == 'client_rej_text' AND image_completed == 1)`
- 这确保了图片已处理完成的文案不会重复显示在图片上传页面

### 文案审核页面查询条件

```python
db.or_(
    # 草稿状态
    db.and_(
        Content.workflow_status == 'draft',
        Content.internal_review_status == 'pending'
    ),

    # 内部驳回状态
    Content.internal_review_status.like('%rej%'),

    # 客户驳回（文案问题）
    db.and_(
        Content.workflow_status == 'draft',
        Content.client_review_status == 'rejected',
        Content.internal_review_status == 'client_rej_text'
    ),

    # 客户驳回（两者都有问题）- 文案未完成时显示
    db.and_(
        Content.workflow_status == 'draft',
        Content.client_review_status == 'rejected',
        Content.internal_review_status == 'client_rej_both',
        Content.content_completed != 1
    )
)
```

### 重要注意事项

1. **状态互斥性**：
   - 文案完成后不显示在文案审核页面
   - 图片完成后不显示在图片上传页面
   - 避免重复处理

2. **查询条件一致性**：
   - 主页面、详情页面、统计页面的查询条件必须完全一致
   - 统计数字必须与实际显示的文案数量匹配

3. **状态检查顺序**：
   - 图片提交时：先检查 `content_completed` 状态
   - 文案提交时：先检查 `image_completed` 状态
   - 只有当对方也完成时，才进入终审

4. **关键状态标记**：
   - `content_completed = 1`：文案已完成
   - `image_completed = 1`：图片已完成
   - 这两个标记决定了文案是否应该继续显示在对应页面

### 常见问题排查

#### 问题1：图片提交成功但刷新后又显示
**症状**：图片提交提示成功，文案暂时消失，但刷新页面后又出现

**排查步骤**：
1. 检查文案的当前状态：
   ```sql
   SELECT workflow_status, client_review_status, internal_review_status,
          content_completed, image_completed
   FROM contents WHERE id = {content_id};
   ```

2. 如果状态是：
   - `workflow_status = 'draft'`
   - `client_review_status = 'rejected'`
   - `internal_review_status = 'client_rej_text'`
   - `image_completed = 1`

3. **解决方案**：确保查询条件包含排除逻辑：
   ```python
   db.not_(
       db.and_(
           Content.internal_review_status == 'client_rej_text',
           Content.image_completed == 1
       )
   )
   ```

#### 问题2：文案提交后仍显示在文案审核页面
**症状**：文案提交成功，但仍然显示在初审页面

**排查步骤**：
1. 检查 `content_completed` 是否正确设置为 1
2. 检查 `workflow_status` 是否正确更新
3. 如果图片已完成，应该进入 `final_review` 状态

#### 问题3：统计数字与实际显示不符
**症状**：页面显示的文案数量与统计数字不匹配

**解决方案**：
- 确保统计查询条件与页面查询条件完全一致
- 特别注意排除条件必须在统计中也要应用

### 调试示例

#### 示例1：两者都有问题的完整流程
```
1. 初始状态：
   workflow_status: 'draft'
   client_review_status: 'rejected'
   internal_review_status: 'client_rej_both'
   content_completed: 0
   image_completed: 0
   显示：文案审核页面 ✅, 图片上传页面 ✅

2. 用户提交图片：
   workflow_status: 'draft' (保持)
   client_review_status: 'rejected' (保持)
   internal_review_status: 'client_rej_text' (改变)
   content_completed: 0 (保持)
   image_completed: 1 (改变)
   显示：文案审核页面 ✅, 图片上传页面 ❌ (被排除条件过滤)

3. 用户提交文案：
   workflow_status: 'final_review' (改变)
   client_review_status: 'pending' (重置)
   internal_review_status: 'pending' (重置)
   content_completed: 1 (改变)
   image_completed: 1 (保持)
   显示：文案审核页面 ❌, 图片上传页面 ❌ (进入终审)
```

#### 示例2：只有文案问题的流程
```
1. 初始状态：
   workflow_status: 'draft'
   client_review_status: 'rejected'
   internal_review_status: 'client_rej_text'
   content_completed: 0
   image_completed: 1
   显示：文案审核页面 ✅, 图片上传页面 ❌

2. 用户提交文案：
   workflow_status: 'final_review' (改变)
   client_review_status: 'pending' (重置)
   internal_review_status: 'pending' (重置)
   content_completed: 1 (改变)
   image_completed: 1 (保持)
   显示：文案审核页面 ❌, 图片上传页面 ❌ (进入终审)
```

---

## � 重要修复记录

### 修复4：客户驳回状态流转完善（2025-07-31）

**问题描述**：
- 客户驳回"两者都有问题"的文案，用户先提交图片后，文案暂时消失但刷新后又出现
- 图片提交成功，状态确实改变，但查询条件仍然匹配，导致重复显示

**根本原因**：
- 图片提交后状态变为：`workflow_status='draft'` + `client_review_status='rejected'` + `internal_review_status='client_rej_text'` + `image_completed=1`
- 原查询条件：`workflow_status == 'draft' AND client_review_status == 'rejected'` 仍然匹配
- 缺少排除已处理图片的逻辑

**修复方案**：
1. **图片上传页面查询条件**：添加排除条件
   ```python
   db.not_(
       db.and_(
           Content.internal_review_status == 'client_rej_text',
           Content.image_completed == 1
       )
   )
   ```

2. **统计逻辑同步**：确保统计查询与页面查询条件一致

3. **状态检查增强**：在 `client_rej_both` 处理中增加文案状态检查
   ```python
   if content.content_completed == 1:
       # 文案已完成，直接进入终审
       content.workflow_status = 'final_review'
   else:
       # 文案未完成，继续等待文案处理
       content.internal_review_status = 'client_rej_text'
   ```

**修复效果**：
- ✅ 图片提交后立即从图片上传页面消失
- ✅ 避免"提交成功但刷新后又显示"的问题
- ✅ 状态流转清晰，用户体验一致
- ✅ 统计数字与实际显示匹配

**影响范围**：
- 主页面：`/simple/image-upload`
- 客户详情页面：`/simple/image-upload/client/{id}`
- 统计逻辑：客户驳回数量统计
- API：图片提交API的状态处理逻辑

**测试验证**：
1. 客户驳回"两者都有问题" → 先提交图片 → 文案消失且不再重现 ✅
2. 客户驳回"两者都有问题" → 先提交文案 → 正常流转 ✅
3. 客户驳回"只有文案问题" → 提交文案 → 直接进入终审 ✅
4. 客户驳回"只有图片问题" → 提交图片 → 直接进入终审 ✅

---

## ��🛠️ 调试工具

### 查看文案状态的API
```
GET /simple/api/debug/content-status/{content_id}
```

返回文案的详细状态信息，用于调试状态判断问题。

---

## � 驳回后的复杂状态组合

### 最终审核驳回后的状态变化

#### 1. 最终审核驳回（文案问题）🔴
**初始驳回状态**:
```
workflow_status = 'draft'
internal_review_status = 'final_rej_text'
content_completed = 0
image_completed = 1  # 图片没问题
```
**显示位置**: 文案初审页面（内部驳回）

**处理文案后**:
```
workflow_status = 'first_reviewed'
internal_review_status = 'final_rej_img'  # 改为等待图片处理
content_completed = 1
image_completed = 1
```
**显示位置**: 图片上传页面（图片已上传）

#### 2. 最终审核驳回（图片问题）🖼️
**初始驳回状态**:
```
workflow_status = 'first_reviewed'
internal_review_status = 'final_rej_img'
content_completed = 1  # 文案没问题
image_completed = 0
```
**显示位置**: 图片上传页面（内部驳回）

**处理图片后**:
```
workflow_status = 'final_review'
internal_review_status = 'pending'
content_completed = 1
image_completed = 1
```
**显示位置**: 进入最终审核

#### 3. 最终审核驳回（两者都有问题）⚠️
**初始驳回状态**:
```
workflow_status = 'draft'
internal_review_status = 'final_rej_both'
content_completed = 0
image_completed = 0
```
**显示位置**: 文案初审页面（内部驳回）

**先处理文案**:
```
workflow_status = 'first_reviewed'
internal_review_status = 'final_rej_img'  # 改为等待图片处理
content_completed = 1
image_completed = 0
```
**显示位置**: 图片上传页面（内部驳回）

**先处理图片**:
```
workflow_status = 'draft'
internal_review_status = 'final_rej_text_ok'  # 图片已修复但文案待处理
content_completed = 0
image_completed = 1
```
**显示位置**: 文案初审页面（内部驳回）

**两者都处理完**:
```
workflow_status = 'final_review'
internal_review_status = 'pending'
content_completed = 1
image_completed = 1
```
**显示位置**: 进入最终审核

### 客户驳回后的状态变化

#### 1. 客户驳回（文案问题）🔴
**初始驳回状态**:
```
workflow_status = 'draft'
client_review_status = 'rejected'
internal_review_status = 'client_rej_text'
content_completed = 0
image_completed = 1  # 图片没问题
```
**显示位置**: 文案初审页面（客户驳回）

**处理文案后**:
```
workflow_status = 'first_reviewed'
client_review_status = 'pending'  # 重置
internal_review_status = 'client_rej_img'  # 改为等待图片处理
content_completed = 1
image_completed = 1
```
**显示位置**: 图片上传页面（图片已上传）

#### 2. 客户驳回（图片问题）🖼️
**初始驳回状态**:
```
workflow_status = 'first_reviewed'
client_review_status = 'rejected'
internal_review_status = 'client_rej_img'
content_completed = 1  # 文案没问题
image_completed = 0
```
**显示位置**: 图片上传页面（客户驳回）

**处理图片后**:
```
workflow_status = 'final_review'
client_review_status = 'pending'  # 重置
internal_review_status = 'pending'
content_completed = 1
image_completed = 1
```
**显示位置**: 进入最终审核

#### 3. 客户驳回（两者都有问题）⚠️
**初始驳回状态**:
```
workflow_status = 'draft'
client_review_status = 'rejected'
internal_review_status = 'client_rej_both'
content_completed = 0
image_completed = 0
```
**显示位置**: 文案初审页面（客户驳回）

**先处理文案**:
```
workflow_status = 'first_reviewed'
client_review_status = 'pending'  # 重置
internal_review_status = 'client_rej_img'  # 改为等待图片处理
content_completed = 1
image_completed = 0
```
**显示位置**: 图片上传页面（客户驳回）

**先处理图片**:
```
workflow_status = 'draft'
client_review_status = 'rejected'
internal_review_status = 'client_rej_text_ok'  # 图片已修复但文案待处理
content_completed = 0
image_completed = 1
```
**显示位置**: 文案初审页面（客户驳回）

**两者都处理完**:
```
workflow_status = 'final_review'
client_review_status = 'pending'  # 重置
internal_review_status = 'pending'
content_completed = 1
image_completed = 1
```
**显示位置**: 进入最终审核

---

## �️ 详细状态流转图

### 最终审核驳回流转图
```
最终审核驳回
    ├── 文案问题 (final_rej_text)
    │   └── [文案初审页面-内部驳回] → 处理文案 → [图片上传页面-图片已上传] → 进入最终审核
    │
    ├── 图片问题 (final_rej_img)
    │   └── [图片上传页面-内部驳回] → 处理图片 → 进入最终审核
    │
    └── 两者问题 (final_rej_both)
        ├── 先处理文案 → [图片上传页面-内部驳回] → 处理图片 → 进入最终审核
        ├── 先处理图片 → [文案初审页面-内部驳回] → 处理文案 → [图片上传页面-图片已上传] → 进入最终审核
        └── 同时处理 → 进入最终审核
```

### 客户驳回流转图
```
客户驳回
    ├── 文案问题 (client_rej_text)
    │   └── [文案初审页面-客户驳回] → 处理文案 → [图片上传页面-图片已上传] → 进入最终审核
    │
    ├── 图片问题 (client_rej_img)
    │   └── [图片上传页面-客户驳回] → 处理图片 → 进入最终审核
    │
    └── 两者问题 (client_rej_both)
        ├── 先处理文案 → [图片上传页面-客户驳回] → 处理图片 → 进入最终审核
        ├── 先处理图片 → [文案初审页面-客户驳回] → 处理文案 → [图片上传页面-图片已上传] → 进入最终审核
        └── 同时处理 → 进入最终审核
```

---

## 🔍 关键状态标识符说明

### internal_review_status 详细说明
| 状态值 | 含义 | 出现场景 |
|--------|------|----------|
| `pending` | 待审核 | 新生成文案、正常流程 |
| `rejected` | 初审驳回 | 初审不通过 |
| `final_rej_text` | 最终审核驳回-文案问题 | 最终审核认为文案有问题 |
| `final_rej_img` | 最终审核驳回-图片问题 | 最终审核认为图片有问题 |
| `final_rej_both` | 最终审核驳回-两者问题 | 最终审核认为文案和图片都有问题 |
| `final_rej_text_ok` | 图片已修复但文案待处理 | 两者问题中先处理了图片 |
| `client_rej_text` | 客户驳回-文案问题 | 客户认为文案有问题 |
| `client_rej_img` | 客户驳回-图片问题 | 客户认为图片有问题 |
| `client_rej_both` | 客户驳回-两者问题 | 客户认为文案和图片都有问题 |
| `client_rej_text_ok` | 图片已修复但文案待处理 | 客户驳回两者问题中先处理了图片 |

### 完成标记说明
- **`content_completed`**:
  - `0` = 文案需要重新编辑
  - `1` = 文案已完成/无问题
- **`image_completed`**:
  - `0` = 图片需要重新上传
  - `1` = 图片已完成/无问题

---

## ��📊 状态示例对照表

### 文案初审页面状态示例

| 场景 | workflow_status | internal_review_status | client_review_status | content_completed | image_completed | 显示状态 |
|------|----------------|----------------------|---------------------|------------------|----------------|----------|
| 新生成的文案 | `draft` | `pending` | `pending` | 0 | 0 | 草稿 |
| 长度超限的文案 | `draft` | `length_exceeded` | `pending` | 0 | 0 | 草稿 |
| 初审驳回的文案 | `draft` | `rejected` | `pending` | 0 | 0 | 内部驳回 |
| 最终审核驳回(文案问题) | `draft` | `final_rej_text` | `pending` | 0 | 1 | 内部驳回 |
| 最终审核驳回(两者问题) | `draft` | `final_rej_both` | `pending` | 0 | 0 | 内部驳回 |
| 最终审核驳回(图片已修复) | `draft` | `final_rej_text_ok` | `pending` | 0 | 1 | 内部驳回 |
| 客户驳回(文案问题) | `draft` | `client_rej_text` | `rejected` | 0 | 1 | 客户驳回 |
| 客户驳回(两者问题) | `draft` | `client_rej_both` | `rejected` | 0 | 0 | 客户驳回 |
| 客户驳回(图片已修复) | `draft` | `client_rej_text_ok` | `rejected` | 0 | 1 | 客户驳回 |
| 初审通过的文案 | `first_reviewed` | `first_approved` | `pending` | 1 | 0 | 不显示 |

### 图片上传页面状态示例

| 场景 | workflow_status | internal_review_status | client_review_status | 图片数量 | content_completed | image_completed | 显示状态 |
|------|----------------|----------------------|---------------------|----------|------------------|----------------|----------|
| 初审通过，未上传图片 | `first_reviewed` | `first_approved` | `pending` | 0 | 1 | 0 | 图片待上传 |
| 初审通过，已上传图片 | `first_reviewed` | `first_approved` | `pending` | >0 | 1 | 0 | 图片已上传 |
| 已提交图片审核 | `final_review` | `pending` | `pending` | >0 | 1 | 1 | 图片已上传 |
| 最终审核驳回(图片问题) | `first_reviewed` | `final_rej_img` | `pending` | 0 | 1 | 0 | 内部驳回 |
| 最终审核驳回(文案已修复) | `first_reviewed` | `final_rej_img` | `pending` | >0 | 1 | 1 | 图片已上传 |
| 最终审核驳回(两者问题-文案先修复) | `first_reviewed` | `final_rej_img` | `pending` | 0 | 1 | 0 | 内部驳回 |
| 客户驳回(图片问题) | `first_reviewed` | `client_rej_img` | `rejected` | 0 | 1 | 0 | 客户驳回 |
| 客户驳回(文案已修复) | `first_reviewed` | `client_rej_img` | `pending` | >0 | 1 | 1 | 图片已上传 |
| 客户驳回(两者问题-文案先修复) | `first_reviewed` | `client_rej_img` | `rejected` | 0 | 1 | 0 | 客户驳回 |

---

## 🔧 状态修复指南

### 常见状态异常及修复方法

#### 1. 文案卡在"草稿"状态无法审核
**可能原因**:
- 文案长度超出限制
- 系统设置问题

**排查步骤**:
```sql
SELECT id, title, workflow_status, internal_review_status,
       LENGTH(title) as title_length, LENGTH(content) as content_length
FROM contents WHERE id = {content_id};
```

**修复方法**:
- 如果长度超限：编辑文案内容
- 如果系统问题：手动更新状态

#### 2. 图片上传后仍显示"待上传"
**可能原因**:
- 前端统计逻辑错误
- 图片记录未正确保存

**排查步骤**:
```sql
SELECT c.id, c.workflow_status, COUNT(ci.id) as image_count
FROM contents c
LEFT JOIN content_images ci ON c.id = ci.content_id AND ci.is_deleted = 0
WHERE c.id = {content_id}
GROUP BY c.id;
```

**修复方法**:
- 检查图片上传是否成功
- 刷新页面重新加载统计

#### 3. 状态显示不一致
**可能原因**:
- 前后端状态判断逻辑不同步
- 缓存问题

**修复方法**:
- 重启应用程序
- 清除浏览器缓存
- 检查模板文件的状态判断逻辑

### 4. 客户驳回文案在列表页显示但详情页不显示 🚨
**典型表现**:
- 图片上传客户列表页面显示"客户驳回: 1"
- 但点击进入具体客户详情页面，看不到被客户驳回的文案
- 统计数字正确，但文案列表为空或缺少驳回的文案

**问题原因**:
- 客户列表页面的统计查询包含了客户驳回条件
- 但客户详情页面的查询条件缺少了 `client_review_status == 'rejected'`
- 导致统计和实际显示不一致

**排查步骤**:
```sql
-- 检查客户驳回的文案是否存在
SELECT id, title, workflow_status, client_review_status, internal_review_status
FROM contents
WHERE client_id = {client_id}
  AND client_review_status = 'rejected'
  AND image_editor_id = {user_id}
  AND is_deleted = 0;
```

**修复方法**:
- 在客户详情页面的查询条件中添加客户驳回条件
- 确保查询条件、统计逻辑、筛选逻辑三者保持一致

**正确的查询条件**:
```python
db.or_(
    # 初审通过，需要上传图片
    Content.workflow_status == 'first_reviewed',
    # 终审驳回，需要重新处理图片
    Content.internal_review_status.like('final_rej%'),
    # 客户驳回，需要重新处理图片  ← 这个条件之前缺失
    Content.client_review_status == 'rejected'
)
```

### 5. 客户驳回后状态流转逻辑错误 🚨
**典型表现**:
- 客户驳回了文案和图片（两者都有问题）
- 用户先处理文案并提交 → 文案和图片都消失了
- 应该是：文案消失，图片还在图片上传页面等待处理

**问题原因**:
- 客户驳回后，系统错误地认为如果 `image_completed == 1`，就可以直接进入最终审核
- 但实际上客户驳回后，无论之前图片是否完成，都需要重新处理

**排查步骤**:
```sql
-- 检查客户驳回文案的完成标记
SELECT id, workflow_status, internal_review_status, client_review_status,
       content_completed, image_completed
FROM contents
WHERE client_review_status = 'rejected'
  AND internal_review_status LIKE 'client_rej%';
```

**修复方法**:
- 客户驳回后，无论之前的完成状态如何，都需要重新设置完成标记
- 处理文案后：`content_completed = 1, image_completed = 0`
- 处理图片后：`image_completed = 1`，然后检查文案状态

**正确的状态流转逻辑**:
```python
# 客户驳回（文案问题）处理后
content.workflow_status = 'first_reviewed'
content.internal_review_status = 'client_rej_img'
content.content_completed = 1  # 文案已完成
content.image_completed = 0    # 图片需要重新处理
```

### 6. 客户驳回图片问题后提交图片状态错误 🚨
**典型表现**:
- 客户驳回了图片问题（`client_rej_img` 状态）
- 用户在图片上传页面提交图片后，图片仍然显示在页面上
- 需要再次提交才会消失，进入最终审核

**问题原因**:
- 图片提交API中缺少对 `client_rej_img` 状态的处理
- 只处理了 `client_rej_both`、`client_rej_text` 等状态
- 导致 `client_rej_img` 状态的文案提交后状态没有正确更新

**排查步骤**:
```sql
-- 检查客户驳回图片问题的文案状态
SELECT id, workflow_status, internal_review_status, client_review_status,
       content_completed, image_completed
FROM contents
WHERE internal_review_status = 'client_rej_img'
  AND client_review_status = 'rejected';
```

**修复方法**:
- 在图片提交API中添加对 `client_rej_img` 状态的处理
- 提交后应该进入 `final_review` 状态并重置客户审核状态

**正确的处理逻辑**:
```python
elif content.internal_review_status == 'client_rej_img':
    # 客户驳回（图片问题）：图片提交后，进入最终审核
    content.workflow_status = 'final_review'
    content.client_review_status = 'pending'  # 重置客户审核状态
    content.internal_review_status = 'pending'  # 重置内部审核状态
    content.image_completed = 1
```

### 7. 统计数字正确但状态标签显示错误 🚨
**典型表现**:
- 统计卡片显示"内部驳回: 1"
- 但列表中所有文案都显示"待上传图片"
- 实际应该有文案显示"内部驳回"标签

**问题原因**:
- 后端统计逻辑考虑了 `image_completed` 字段
- 前端模板显示逻辑没有考虑 `image_completed` 字段
- 导致统计和显示使用了不同的判断条件

**排查步骤**:
```sql
-- 查看具体文案的完成标记
SELECT id, workflow_status, internal_review_status,
       image_completed, content_completed,
       (SELECT COUNT(*) FROM content_images WHERE content_id = c.id AND is_deleted = 0) as image_count
FROM contents c
WHERE client_id = {client_id}
  AND (internal_review_status LIKE 'final_rej%' OR internal_review_status = 'rejected')
  AND image_completed != 1;
```

**修复方法**:
- 检查前端模板中状态显示的判断逻辑
- 确保前端判断条件与后端统计条件一致
- 特别注意 `image_completed` 和 `content_completed` 字段的使用

**正确的前端判断逻辑**:
```jinja2
{% if item.content.client_review_status == 'rejected' %}
    <span class="badge bg-danger">客户驳回</span>
{% elif (item.content.internal_review_status == 'rejected' or
         (item.content.internal_review_status and
          item.content.internal_review_status.startswith('final_rej')))
         and item.content.image_completed != 1 %}
    <span class="badge bg-warning">内部驳回</span>
{% elif item.content.workflow_status == 'first_reviewed' %}
    {% if item.image_count > 0 %}
        <span class="badge bg-primary">图片已上传</span>
    {% else %}
        <span class="badge bg-success">待上传图片</span>
    {% endif %}
{% endif %}
```

---

## 📝 开发注意事项

### 1. 修改状态判断逻辑时
- ✅ 同时更新前端模板和后端查询逻辑
- ✅ 确保统计数字和列表显示一致
- ✅ 测试所有相关页面的状态显示
- ⚠️ **特别注意**: 前端模板的状态显示必须与后端统计逻辑保持一致
- ⚠️ **完成标记**: 驳回状态的判断必须同时考虑 `image_completed` 和 `content_completed` 字段

### 2. 添加新状态时
- ✅ 更新数据库字段注释
- ✅ 更新本文档的状态说明
- ✅ 添加相应的前端显示逻辑

### 3. 状态流转时
- ✅ 确保状态转换的原子性
- ✅ 记录状态变更日志
- ✅ 通知相关用户状态变化

---

## 🚀 快速参考

### 状态判断速查表

#### 文案初审页面 (`/simple/review-content`)
```
草稿 = workflow_status='draft' + 无驳回状态
内部驳回 = workflow_status='draft' + internal_review_status包含'rejected'或'final_rej'
客户驳回 = workflow_status='draft' + client_review_status='rejected'
```

#### 图片上传页面 (`/simple/image-upload/client/{id}`)
```
图片待上传 = workflow_status='first_reviewed' + 图片数量=0
图片已上传 = workflow_status='first_reviewed' + 图片数量>0
内部驳回 = internal_review_status包含'final_rej' + image_completed!=1
```

### 常见错误检查清单

#### 状态显示不一致检查
1. **统计数字与列表显示是否匹配**？
2. **前端模板是否考虑了 `image_completed` 字段**？
3. **驳回状态的判断优先级是否正确**？
4. **是否同时检查了 `internal_review_status` 和完成标记**？

#### 快速验证命令
```sql
-- 检查统计逻辑是否正确
SELECT
  COUNT(CASE WHEN workflow_status = 'first_reviewed' AND
    (SELECT COUNT(*) FROM content_images WHERE content_id = c.id AND is_deleted = 0) = 0
    THEN 1 END) as pending_upload,
  COUNT(CASE WHEN workflow_status = 'first_reviewed' AND
    (SELECT COUNT(*) FROM content_images WHERE content_id = c.id AND is_deleted = 0) > 0
    THEN 1 END) as uploaded,
  COUNT(CASE WHEN (internal_review_status = 'rejected' OR internal_review_status LIKE 'final_rej%')
    AND image_completed != 1 THEN 1 END) as internal_rejected
FROM contents c WHERE client_id = {client_id} AND is_deleted = 0;
```

### 常用SQL查询

#### 查看文案详细状态
```sql
SELECT id, title, workflow_status, internal_review_status,
       client_review_status, image_completed, content_completed,
       (SELECT COUNT(*) FROM content_images WHERE content_id = c.id AND is_deleted = 0) as image_count
FROM contents c WHERE id = {content_id};
```

#### 查看状态分布统计
```sql
SELECT workflow_status, internal_review_status, COUNT(*) as count
FROM contents
WHERE client_id = {client_id} AND is_deleted = 0
GROUP BY workflow_status, internal_review_status
ORDER BY count DESC;
```

### 驳回状态排查指南

#### 快速判断文案当前应该在哪个页面
1. **查看基础状态**:
   ```sql
   SELECT workflow_status, internal_review_status, client_review_status,
          content_completed, image_completed
   FROM contents WHERE id = {content_id};
   ```

2. **根据状态组合判断**:
   - `workflow_status = 'draft'` → 文案初审页面
   - `workflow_status = 'first_reviewed'` → 图片上传页面
   - `workflow_status = 'final_review'` → 最终审核页面

3. **驳回状态细分**:
   - `internal_review_status` 包含 `final_rej` → 最终审核驳回
   - `internal_review_status` 包含 `client_rej` → 客户驳回
   - `client_review_status = 'rejected'` → 客户驳回状态

#### 常见状态修复场景

**场景1: 文案卡在错误页面**
```sql
-- 检查是否有未同步的完成标记
SELECT id, workflow_status, content_completed, image_completed,
       (SELECT COUNT(*) FROM content_images WHERE content_id = c.id AND is_deleted = 0) as actual_image_count
FROM contents c WHERE id = {content_id};
```

**场景2: 驳回后状态混乱**
```sql
-- 查看完整的驳回历史和当前状态
SELECT id, workflow_status, internal_review_status, client_review_status,
       content_completed, image_completed, review_notes, updated_at
FROM contents WHERE id = {content_id};
```

### 紧急修复命令

#### 重置异常状态的文案
```sql
-- 将卡住的文案重置为草稿状态
UPDATE contents
SET workflow_status = 'draft',
    internal_review_status = 'pending',
    content_completed = 0,
    image_completed = 0,
    updated_at = NOW()
WHERE id = {content_id};
```

#### 修复驳回状态的完成标记
```sql
-- 最终审核驳回(文案问题) - 图片应该是完成的
UPDATE contents
SET image_completed = 1, content_completed = 0
WHERE internal_review_status = 'final_rej_text';

-- 最终审核驳回(图片问题) - 文案应该是完成的
UPDATE contents
SET content_completed = 1, image_completed = 0
WHERE internal_review_status = 'final_rej_img';
```

#### 批量修复图片完成标记
```sql
-- 根据实际图片数量更新完成标记
UPDATE contents c
SET image_completed = CASE
    WHEN (SELECT COUNT(*) FROM content_images WHERE content_id = c.id AND is_deleted = 0) > 0 THEN 1
    ELSE 0
END
WHERE workflow_status = 'first_reviewed';
```

---

## 📞 技术支持

如果遇到状态相关问题：

1. **首先**: 查看本文档的排查指南
2. **然后**: 使用调试API检查状态详情
3. **最后**: 联系开发团队并提供具体的文案ID和错误描述

**调试API**: `GET /simple/api/debug/content-status/{content_id}`

---

## � 完整状态流转详细说明

### �️ 状态流转总览图

```
📝 文案创建
    ↓
[草稿状态] draft + pending
    ↓ 初审通过
[初审通过] first_reviewed + first_approved
    ↓ 上传图片
[图片已上传] first_reviewed + 有图片
    ↓ 提交图片审核
[待终审] final_review + pending
    ↓ 终审通过
[客户审核] pending_client_review (需要客户审核)
    ↓ 客户通过
[待发布] pending_publish / ready_to_publish
    ↓
[已发布] published

驳回流转:
终审驳回 → 根据问题类型回到对应阶段
客户驳回 → 根据问题类型回到对应阶段
```

### �📝 文案生命周期完整流程

#### 1. 文案创建阶段 🆕
**操作**: 生成新文案
**状态变化**:
```
workflow_status: 'draft'
internal_review_status: 'pending'
client_review_status: 'pending'
content_completed: 0
image_completed: 0
```
**显示位置**: 文案初审页面 - 草稿状态

#### 2. 初审阶段 📋

##### 2.1 初审通过 ✅
**操作**: 在初审页面点击"通过"
**状态变化**:
```
workflow_status: 'first_reviewed'
internal_review_status: 'first_approved'
content_completed: 1
```
**显示位置**: 图片上传页面 - 图片待上传状态

##### 2.2 初审驳回 ❌
**操作**: 在初审页面点击"驳回"
**状态变化**:
```
workflow_status: 'draft'
internal_review_status: 'rejected'
content_completed: 0
```
**显示位置**: 文案初审页面 - 内部驳回状态

#### 3. 图片上传阶段 🖼️

##### 3.1 上传图片 📤
**操作**: 在图片上传页面上传图片文件
**状态变化**: 无状态变化，仅添加图片记录
**显示位置**: 图片上传页面 - 图片已上传状态

##### 3.2 提交图片审核 📋
**操作**: 在图片上传页面点击"提交审核"
**状态变化**:
```
workflow_status: 'final_review' 或 'image_uploaded'
internal_review_status: 'pending'
image_completed: 1
```
**显示位置**: 终审页面 - 待最终审核状态

#### 4. 终审阶段 🔍

##### 4.1 终审通过 ✅
**操作**: 在终审页面点击"通过"
**状态变化**:
- **需要客户审核**:
  ```
  workflow_status: 'pending_client_review'
  internal_review_status: 'final_approved'
  ```
- **不需要客户审核**:
  ```
  workflow_status: 'pending_publish' 或 'ready_to_publish'
  client_review_status: 'approved'
  internal_review_status: 'final_approved'
  ```

##### 4.2 终审驳回 - 文案问题 📝❌
**操作**: 在终审页面驳回并选择"文案问题"
**状态变化**:
```
workflow_status: 'draft'
internal_review_status: 'final_rej_text'
content_completed: 0
image_completed: 1
```
**显示位置**: 文案初审页面 - 内部驳回状态

##### 4.3 终审驳回 - 图片问题 🖼️❌
**操作**: 在终审页面驳回并选择"图片问题"
**状态变化**:
```
workflow_status: 'first_reviewed'
internal_review_status: 'final_rej_img'
content_completed: 1
image_completed: 0
```
**显示位置**: 图片上传页面 - 内部驳回状态

##### 4.4 终审驳回 - 两者都有问题 ⚠️❌
**操作**: 在终审页面驳回并选择"两者都有问题"
**状态变化**:
```
workflow_status: 'draft'
internal_review_status: 'final_rej_both'
content_completed: 0
image_completed: 0
```
**显示位置**: 文案初审页面 - 内部驳回状态 + 图片上传页面 - 内部驳回状态

#### 5. 客户审核阶段 👥

##### 5.1 客户审核通过 ✅
**操作**: 客户在审核页面点击"通过"
**状态变化**:
```
workflow_status: 'pending_publish' 或 'ready_to_publish'
client_review_status: 'approved'
```

##### 5.2 客户驳回 - 文案问题 📝❌
**操作**: 客户驳回并选择"文案问题"
**状态变化**:
```
workflow_status: 'draft'
client_review_status: 'rejected'
internal_review_status: 'client_rej_text'
content_completed: 0
image_completed: 1
```
**显示位置**: 文案初审页面 - 客户驳回状态

##### 5.3 客户驳回 - 图片问题 🖼️❌
**操作**: 客户驳回并选择"图片问题"
**状态变化**:
```
workflow_status: 'first_reviewed'
client_review_status: 'rejected'
internal_review_status: 'client_rej_img'
content_completed: 1
image_completed: 0
```
**显示位置**: 图片上传页面 - 客户驳回状态

##### 5.4 客户驳回 - 两者都有问题 ⚠️❌
**操作**: 客户驳回并选择"两者都有问题"
**状态变化**:
```
workflow_status: 'draft'
client_review_status: 'rejected'
internal_review_status: 'client_rej_both'
content_completed: 0
image_completed: 0
```
**显示位置**: 文案初审页面 - 客户驳回状态 + 图片上传页面 - 客户驳回状态

### 🔄 驳回后重新处理流程

#### 终审驳回后的处理流程

##### 场景1: 终审驳回(文案问题) → 重新编辑文案
**初始状态**: `draft` + `final_rej_text` + `content_completed=0` + `image_completed=1`
**操作**: 编辑文案并提交初审
**状态变化**:
```
workflow_status: 'first_reviewed'
internal_review_status: 'final_rej_img'
content_completed: 1
image_completed: 1
```
**显示位置**: 图片上传页面 - 图片已上传状态

##### 场景2: 终审驳回(图片问题) → 重新上传图片
**初始状态**: `first_reviewed` + `final_rej_img` + `content_completed=1` + `image_completed=0`
**操作**: 上传图片并提交审核
**状态变化**:
```
workflow_status: 'final_review'
internal_review_status: 'pending'
content_completed: 1
image_completed: 1
```
**显示位置**: 终审页面 - 待最终审核状态

##### 场景3: 终审驳回(两者问题) → 先处理文案
**初始状态**: `draft` + `final_rej_both` + `content_completed=0` + `image_completed=0`
**操作**: 编辑文案并提交初审
**状态变化**:
```
workflow_status: 'first_reviewed'
internal_review_status: 'final_rej_img'
content_completed: 1
image_completed: 0
```
**显示位置**: 图片上传页面 - 内部驳回状态

##### 场景4: 终审驳回(两者问题) → 先处理图片
**初始状态**: `draft` + `final_rej_both` + `content_completed=0` + `image_completed=0`
**操作**: 上传图片并提交审核
**状态变化**:
```
workflow_status: 'draft'
internal_review_status: 'final_rej_text_ok'
content_completed: 0
image_completed: 1
```
**显示位置**: 文案初审页面 - 内部驳回状态

#### 客户驳回后的处理流程

##### 场景1: 客户驳回(文案问题) → 重新编辑文案
**初始状态**: `draft` + `client_rej_text` + `rejected` + `content_completed=0` + `image_completed=1`
**操作**: 编辑文案并提交初审
**状态变化**:
```
workflow_status: 'first_reviewed'
client_review_status: 'pending'
internal_review_status: 'client_rej_img'
content_completed: 1
image_completed: 1
```
**显示位置**: 图片上传页面 - 图片已上传状态

##### 场景2: 客户驳回(图片问题) → 重新上传图片
**初始状态**: `first_reviewed` + `client_rej_img` + `rejected` + `content_completed=1` + `image_completed=0`
**操作**: 上传图片并提交审核
**状态变化**:
```
workflow_status: 'final_review'
client_review_status: 'pending'
internal_review_status: 'pending'
content_completed: 1
image_completed: 1
```
**显示位置**: 终审页面 - 待最终审核状态

##### 场景3: 客户驳回(两者问题) → 先处理文案
**初始状态**: `draft` + `client_rej_both` + `rejected` + `content_completed=0` + `image_completed=0`
**操作**: 编辑文案并提交初审
**状态变化**:
```
workflow_status: 'first_reviewed'
client_review_status: 'pending'
internal_review_status: 'client_rej_img'
content_completed: 1
image_completed: 0
```
**显示位置**: 图片上传页面 - 客户驳回状态

##### 场景4: 客户驳回(两者问题) → 先处理图片
**初始状态**: `draft` + `client_rej_both` + `rejected` + `content_completed=0` + `image_completed=0`
**操作**: 上传图片并提交审核
**状态变化**:
```
workflow_status: 'draft'
client_review_status: 'rejected'
internal_review_status: 'client_rej_text_ok'
content_completed: 0
image_completed: 1
```
**显示位置**: 文案初审页面 - 客户驳回状态

### 📊 状态对照表

#### 文案管理页面状态对照
| 操作场景 | workflow_status | internal_review_status | client_review_status | content_completed | image_completed | 显示状态 |
|----------|----------------|----------------------|---------------------|------------------|----------------|----------|
| 新创建文案 | `draft` | `pending` | `pending` | 0 | 0 | 草稿 |
| 初审驳回 | `draft` | `rejected` | `pending` | 0 | 0 | 内部驳回 |
| 终审驳回(文案) | `draft` | `final_rej_text` | `pending` | 0 | 1 | 内部驳回 |
| 终审驳回(两者) | `draft` | `final_rej_both` | `pending` | 0 | 0 | 内部驳回 |
| 终审驳回(图片已修复) | `draft` | `final_rej_text_ok` | `pending` | 0 | 1 | 内部驳回 |
| 客户驳回(文案) | `draft` | `client_rej_text` | `rejected` | 0 | 1 | 客户驳回 |
| 客户驳回(两者) | `draft` | `client_rej_both` | `rejected` | 0 | 0 | 客户驳回 |
| 客户驳回(图片已修复) | `draft` | `client_rej_text_ok` | `rejected` | 0 | 1 | 客户驳回 |

#### 图片管理页面状态对照
| 操作场景 | workflow_status | internal_review_status | client_review_status | 图片数量 | content_completed | image_completed | 显示状态 |
|----------|----------------|----------------------|---------------------|----------|------------------|----------------|----------|
| 初审通过(无图片) | `first_reviewed` | `first_approved` | `pending` | 0 | 1 | 0 | 图片待上传 |
| 初审通过(有图片) | `first_reviewed` | `first_approved` | `pending` | >0 | 1 | 0 | 图片已上传 |
| 图片已提交审核 | `final_review` | `pending` | `pending` | >0 | 1 | 1 | 图片已上传 |
| 终审驳回(图片) | `first_reviewed` | `final_rej_img` | `pending` | 0 | 1 | 0 | 内部驳回 |
| 终审驳回(文案已修复) | `first_reviewed` | `final_rej_img` | `pending` | >0 | 1 | 1 | 图片已上传 |
| 客户驳回(图片) | `first_reviewed` | `client_rej_img` | `rejected` | 0 | 1 | 0 | 客户驳回 |
| 客户驳回(文案已修复) | `first_reviewed` | `client_rej_img` | `pending` | >0 | 1 | 1 | 图片已上传 |

### 🎯 快速状态判断指南

#### 根据状态字段快速判断文案位置
```
📍 文案在哪个页面？
├─ workflow_status = 'draft' → 文案初审页面
├─ workflow_status = 'first_reviewed' → 图片上传页面
├─ workflow_status = 'final_review' → 终审页面
├─ workflow_status = 'pending_client_review' → 客户审核页面
└─ workflow_status = 'pending_publish' → 待发布页面

🏷️ 显示什么状态标签？
├─ internal_review_status 包含 'rej' → 内部驳回
├─ client_review_status = 'rejected' → 客户驳回
├─ workflow_status = 'first_reviewed' + 图片数量 = 0 → 图片待上传
├─ workflow_status = 'first_reviewed' + 图片数量 > 0 → 图片已上传
└─ 其他情况 → 根据workflow_status显示对应状态

🔄 驳回后去哪里？
├─ final_rej_text → 文案初审页面
├─ final_rej_img → 图片上传页面
├─ final_rej_both → 文案初审页面 + 图片上传页面
├─ client_rej_text → 文案初审页面
├─ client_rej_img → 图片上传页面
└─ client_rej_both → 文案初审页面 + 图片上传页面
```

### 📋 状态检查清单

#### 文案不显示在预期页面时的检查步骤
1. **检查基础状态**:
   ```sql
   SELECT workflow_status, internal_review_status, client_review_status,
          content_completed, image_completed
   FROM contents WHERE id = {content_id};
   ```

2. **检查完成标记**:
   - `content_completed = 1` → 文案已完成，不应在文案审核页面
   - `image_completed = 1` → 图片已完成，不应在图片上传页面

3. **检查驳回状态优先级**:
   - 客户驳回 > 内部驳回 > 正常状态
   - 驳回状态必须结合完成标记判断显示位置

4. **检查查询条件**:
   - 页面查询条件是否包含所有必要的状态判断
   - 统计逻辑是否与页面查询条件一致

#### 常见状态异常及解决方案
| 问题描述 | 可能原因 | 解决方案 |
|----------|----------|----------|
| 文案提交后仍显示在原页面 | 完成标记未更新 | 检查并更新 `content_completed` 或 `image_completed` |
| 统计数字与列表不符 | 查询条件不一致 | 统一页面查询和统计查询的条件 |
| 驳回文案显示在错误页面 | 状态判断优先级错误 | 检查前端模板的状态判断逻辑 |
| 图片提交后又重新显示 | 缺少排除条件 | 添加已完成状态的排除逻辑 |

---

## �📝 版本更新记录

### v2.0 (2025-07-31)
- ✅ 新增完整的状态流转详细说明
- ✅ 添加文案生命周期完整流程图
- ✅ 详细说明每个操作的状态变化
- ✅ 新增驳回后重新处理的完整流程
- ✅ 添加状态对照表，便于快速查询
- ✅ 涵盖所有可能的状态组合和流转路径

### v1.4 (2025-07-30)
- ✅ 修复了客户驳回图片问题后提交图片的状态处理
- ✅ 添加了对 `client_rej_img` 状态的完整处理逻辑
- ✅ 确保图片提交后正确进入最终审核并从图片上传页面消失

### v1.3 (2025-07-30)
- ✅ 修复了客户驳回后状态流转逻辑错误的问题
- ✅ 确保客户驳回后处理文案时，图片仍需要重新处理
- ✅ 修正了文案处理后直接进入最终审核的错误逻辑

### v1.2 (2025-07-30)
- ✅ 修复了客户驳回文案在详情页不显示的问题
- ✅ 统一了客户列表页和详情页的查询条件
- ✅ 添加了查询条件缺失问题的排查指南

### v1.1 (2025-07-30)
- ✅ 添加了驳回后复杂状态组合的详细说明
- ✅ 新增状态显示不一致问题的排查指南
- ✅ 添加了统计数字正确但状态标签错误的修复方法
- ✅ 完善了前端模板状态判断逻辑的最佳实践
- ✅ 新增常见错误检查清单和快速验证命令

### v1.0 (2025-07-30)
- ✅ 初始版本，包含基础状态判断逻辑
- ✅ 文案初审和图片上传页面状态说明
- ✅ 基础的排查和修复指南

---

**最后更新**: 2025-07-31
**版本**: v2.0
**维护者**: 开发团队
