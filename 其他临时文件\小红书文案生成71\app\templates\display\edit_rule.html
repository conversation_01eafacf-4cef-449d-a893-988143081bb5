{% extends "base.html" %}

{% block title %}编辑展示规则{% endblock %}

{% block page_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">编辑展示规则 - {{ client.name }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('display.rules') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回规则列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        
                        <div class="mb-3">
                            <label class="form-label">客户名称</label>
                            <input type="text" class="form-control" value="{{ client.name }}" readonly>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.daily_content_count.label(class="form-label") }}
                            {{ form.daily_content_count(class="form-control") }}
                            {% if form.daily_content_count.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.daily_content_count.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">每日展示的文案数量</small>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.display_start_time.label(class="form-label") }}
                            {{ form.display_start_time(class="form-control", type="time") }}
                            {% if form.display_start_time.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.display_start_time.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">每日开始展示文案的时间，留空表示默认09:00</small>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.interval_min.label(class="form-label") }}
                                {{ form.interval_min(class="form-control") }}
                                {% if form.interval_min.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.interval_min.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">文案之间的最小间隔时间（分钟）</small>
                            </div>
                            <div class="col-md-6">
                                {{ form.interval_max.label(class="form-label") }}
                                {{ form.interval_max(class="form-control") }}
                                {% if form.interval_max.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.interval_max.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">文案之间的最大间隔时间（分钟）</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存规则
                            </button>
                            <a href="{{ url_for('display.rules') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 