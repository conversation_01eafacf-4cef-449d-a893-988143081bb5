{% extends "base.html" %}

{% block title %}任务详情{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="card mb-4">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h3 class="mb-0">任务详情</h3>
                <div>
                    <a href="{{ url_for('task.task_list') }}" class="btn btn-sm btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                    {% if current_user.has_permission('task_edit') %}
                    <a href="{{ url_for('task.task_edit', task_id=task.id) }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-edit"></i> 编辑任务
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <div class="task-header mb-4">
                        <h2 class="mb-2">{{ task.name }}</h2>
                        <div class="task-meta text-muted small mb-3">
                            <div><strong>客户：</strong>{{ task.client.name }}</div>
                            <div><strong>创建时间：</strong>{{ task.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                            <div><strong>目标文案数量：</strong>{{ task.target_count or '未设置' }}</div>
                            <div><strong>实际文案数量：</strong>{{ task.actual_count }}</div>
                            <div><strong>状态：</strong>
                                {% if task.status == 'processing' %}
                                <span class="badge bg-primary">进行中</span>
                                {% elif task.status == 'completed' %}
                                <span class="badge bg-success">已完成</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ task.status }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    {% if task.description %}
                    <div class="task-description mb-4">
                        <h5 class="mb-2">任务描述</h5>
                        <div class="card">
                            <div class="card-body">
                                {{ task.description|nl2br }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="task-content-stats mb-4">
                        <h5 class="mb-2">文案状态统计</h5>
                        <div class="card">
                            <div class="card-body">
                                {% if content_count > 0 %}
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <strong>总文案数量：</strong> {{ content_count }}
                                        </div>
                                        <div class="progress mb-3" style="height: 20px;">
                                            <div class="progress-bar bg-success" role="progressbar" 
                                                 style="width: {{ (status_counts.get('published', 0) / content_count * 100)|round|int if content_count else 0 }}%;" 
                                                 aria-valuenow="{{ status_counts.get('published', 0) }}" 
                                                 aria-valuemin="0" 
                                                 aria-valuemax="{{ content_count }}">
                                                已发布 ({{ status_counts.get('published', 0) }})
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <span class="badge bg-secondary">草稿</span> {{ status_counts.get('draft', 0) }}
                                        </div>
                                        <div class="mb-2">
                                            <span class="badge bg-warning">待审核</span> {{ status_counts.get('pending_review', 0) + status_counts.get('pending_final_review', 0) + status_counts.get('pending_client_review', 0) }}
                                        </div>
                                        <div class="mb-2">
                                            <span class="badge bg-info">待发布</span> {{ status_counts.get('pending_publish', 0) }}
                                        </div>
                                        <div class="mb-2">
                                            <span class="badge bg-success">已发布</span> {{ status_counts.get('published', 0) }}
                                        </div>
                                    </div>
                                </div>
                                {% else %}
                                <p class="text-muted">暂无文案数据</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">批次管理</h5>
                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#createBatchModal">
                                    <i class="fas fa-plus"></i> 添加批次
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                {% for batch in batches %}
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">{{ batch.name }}</h6>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('task.batch_edit', batch_id=batch.id) }}" class="btn btn-outline-primary" title="编辑批次">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if current_user.has_permission('batch_delete') %}
                                            <button type="button" class="btn btn-outline-danger" onclick="confirmDeleteBatch({{ batch.id }})" title="删除批次">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="text-muted small mt-1">
                                        <div>文案数量: {{ batch.content_count }}</div>
                                        <div>创建时间: {{ batch.created_at.strftime('%Y-%m-%d') }}</div>
                                    </div>
                                </div>
                                {% else %}
                                <div class="list-group-item text-center py-3">
                                    <p class="text-muted mb-0">暂无批次数据</p>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">快捷操作</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('content.content_create') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-plus"></i> 创建文案
                                </a>
                                
                                {% if task.status == 'processing' %}
                                <form method="post" action="{{ url_for('task.task_edit', task_id=task.id) }}">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <input type="hidden" name="name" value="{{ task.name }}">
                                    <input type="hidden" name="client_id" value="{{ task.client_id }}">
                                    <input type="hidden" name="description" value="{{ task.description or '' }}">
                                    <input type="hidden" name="target_count" value="{{ task.target_count or '' }}">
                                    <input type="hidden" name="status" value="completed">
                                    <button type="submit" class="btn btn-outline-success w-100">
                                        <i class="fas fa-check"></i> 标记为已完成
                                    </button>
                                </form>
                                {% endif %}
                                
                                {% if current_user.has_permission('task_delete') and content_count == 0 %}
                                <button type="button" class="btn btn-outline-danger" onclick="confirmDelete({{ task.id }})">
                                    <i class="fas fa-trash"></i> 删除任务
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建批次模态框 -->
<div class="modal fade" id="createBatchModal" tabindex="-1" aria-labelledby="createBatchModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createBatchModalLabel">创建批次</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{{ url_for('task.batch_create') }}">
                <div class="modal-body">
                    {{ batch_form.csrf_token }}
                    {{ batch_form.task_id }}
                    
                    <div class="mb-3">
                        {{ batch_form.name.label(class="form-label") }}
                        {{ batch_form.name(class="form-control", placeholder="输入批次名称") }}
                    </div>
                    
                    <div class="mb-3">
                        {{ batch_form.content_count.label(class="form-label") }}
                        {{ batch_form.content_count(class="form-control", type="number", min="0", value="0") }}
                        <div class="form-text text-muted">可以先设为0，后续根据实际情况更新</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">创建批次</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 删除任务确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个任务吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" action="">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 删除批次确认模态框 -->
<div class="modal fade" id="deleteBatchModal" tabindex="-1" aria-labelledby="deleteBatchModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteBatchModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个批次吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteBatchForm" method="post" action="">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 确认删除任务
    function confirmDelete(taskId) {
        document.getElementById('deleteForm').action = `/tasks/${taskId}/delete`;
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }
    
    // 确认删除批次
    function confirmDeleteBatch(batchId) {
        document.getElementById('deleteBatchForm').action = `/batches/${batchId}/delete`;
        const modal = new bootstrap.Modal(document.getElementById('deleteBatchModal'));
        modal.show();
    }
</script>
{% endblock %} 