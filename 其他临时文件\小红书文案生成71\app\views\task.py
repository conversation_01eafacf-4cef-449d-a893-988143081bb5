"""
任务管理视图
"""
from datetime import datetime
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort
from flask_login import login_required, current_user
from sqlalchemy import desc, and_, or_

from app.models import db
from app.models.task import Task, Batch
from app.models.client import Client
from app.models.content import Content
from app.forms.task import TaskForm, BatchForm, TaskFilterForm, BatchFilterForm, TaskBatchActionForm
from app.utils.decorators import permission_required

# 创建蓝图
task_bp = Blueprint('task', __name__)


@task_bp.route('/')
@login_required
@permission_required('task_view')
def task_list():
    """任务列表"""
    form = TaskFilterForm()
    
    # 初始化表单选择项
    form.client_id.choices = [(0, '全部客户')] + [(c.id, c.name) for c in Client.query.filter_by(status=True).all()]
    
    # 获取筛选参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    client_id = request.args.get('client_id', 0, type=int)
    status = request.args.get('status', '')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    search = request.args.get('search', '')
    sort_by = request.args.get('sort_by', 'created_at')
    sort_order = request.args.get('sort_order', 'desc')
    
    # 构建查询
    query = Task.query
    
    # 应用筛选条件
    if client_id > 0:
        query = query.filter(Task.client_id == client_id)
    
    if status:
        query = query.filter(Task.status == status)
    
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Task.created_at >= date_from)
        except:
            pass
    
    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(Task.created_at <= date_to)
        except:
            pass
    
    if search:
        query = query.filter(
            or_(
                Task.name.like(f'%{search}%'),
                Task.description.like(f'%{search}%')
            )
        )
    
    # 应用排序
    if sort_order == 'desc':
        query = query.order_by(desc(getattr(Task, sort_by)))
    else:
        query = query.order_by(getattr(Task, sort_by))
    
    # 分页
    pagination = query.paginate(page=page, per_page=per_page)
    tasks = pagination.items
    
    # 批量操作表单
    batch_form = TaskBatchActionForm()
    
    return render_template(
        'task/list.html',
        tasks=tasks,
        pagination=pagination,
        form=form,
        batch_form=batch_form,
        client_id=client_id,
        status=status,
        date_from=date_from,
        date_to=date_to,
        search=search,
        sort_by=sort_by,
        sort_order=sort_order
    )


@task_bp.route('/create', methods=['GET', 'POST'])
@login_required
@permission_required('task_create')
def task_create():
    """创建任务"""
    form = TaskForm()
    
    # 初始化表单选择项
    form.client_id.choices = [(c.id, c.name) for c in Client.query.filter_by(status=True).all()]
    
    if form.validate_on_submit():
        # 创建新任务
        task = Task(
            name=form.name.data,
            client_id=form.client_id.data,
            description=form.description.data,
            target_count=form.target_count.data,
            status=form.status.data,
            created_by=current_user.id
        )
        
        # 保存到数据库
        db.session.add(task)
        db.session.commit()
        
        # 创建默认批次
        batch = Batch(
            task_id=task.id,
            name=f"{task.name} - 默认批次",
            content_count=0,
            created_by=current_user.id
        )
        db.session.add(batch)
        db.session.commit()
        
        flash('任务创建成功', 'success')
        return redirect(url_for('task.task_view', task_id=task.id))
    
    return render_template('task/create.html', form=form)


@task_bp.route('/<int:task_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('task_edit')
def task_edit(task_id):
    """编辑任务"""
    task = Task.query.get_or_404(task_id)
    form = TaskForm(obj=task)
    
    # 初始化表单选择项
    form.client_id.choices = [(c.id, c.name) for c in Client.query.filter_by(status=True).all()]
    
    if form.validate_on_submit():
        # 更新任务信息
        task.name = form.name.data
        task.client_id = form.client_id.data
        task.description = form.description.data
        task.target_count = form.target_count.data
        task.status = form.status.data
        
        db.session.commit()
        flash('任务更新成功', 'success')
        return redirect(url_for('task.task_view', task_id=task_id))
    
    return render_template('task/edit.html', form=form, task=task)


@task_bp.route('/<int:task_id>')
@login_required
@permission_required('task_view')
def task_view(task_id):
    """查看任务详情"""
    task = Task.query.get_or_404(task_id)
    
    # 获取任务相关的批次
    batches = Batch.query.filter_by(task_id=task_id).order_by(Batch.created_at.desc()).all()
    
    # 获取任务相关的文案数量
    content_count = Content.query.filter_by(task_id=task_id).count()
    
    # 获取任务相关的文案状态统计
    content_stats = db.session.query(
        Content.workflow_status, 
        db.func.count(Content.id)
    ).filter(Content.task_id == task_id).group_by(Content.workflow_status).all()
    
    # 转换为字典格式
    status_counts = {status: count for status, count in content_stats}
    
    # 批次表单
    batch_form = BatchForm()
    batch_form.task_id.choices = [(task.id, task.name)]
    batch_form.task_id.data = task.id
    
    return render_template(
        'task/view.html',
        task=task,
        batches=batches,
        content_count=content_count,
        status_counts=status_counts,
        batch_form=batch_form
    )


@task_bp.route('/<int:task_id>/delete', methods=['POST'])
@login_required
@permission_required('task_delete')
def task_delete(task_id):
    """删除任务"""
    task = Task.query.get_or_404(task_id)
    
    # 检查是否有关联的文案
    content_count = Content.query.filter_by(task_id=task_id).count()
    if content_count > 0:
        flash(f'无法删除任务，该任务下有 {content_count} 个文案', 'danger')
        return redirect(url_for('task.task_view', task_id=task_id))
    
    # 删除关联的批次
    Batch.query.filter_by(task_id=task_id).delete()
    
    # 删除任务
    db.session.delete(task)
    db.session.commit()
    
    flash('任务已删除', 'success')
    return redirect(url_for('task.task_list'))


@task_bp.route('/batch-action', methods=['POST'])
@login_required
@permission_required('task_edit')
def task_batch_action():
    """批量操作任务"""
    form = TaskBatchActionForm()
    
    if form.validate_on_submit():
        action = form.action.data
        task_ids = form.task_ids.data.split(',')
        
        if not task_ids:
            flash('请选择要操作的任务', 'danger')
            return redirect(url_for('task.task_list'))
        
        # 获取所有选中的任务
        tasks = Task.query.filter(Task.id.in_(task_ids)).all()
        
        if action == 'complete':
            # 批量标记完成
            for task in tasks:
                task.status = 'completed'
            
            db.session.commit()
            flash(f'已将 {len(tasks)} 个任务标记为已完成', 'success')
            
        elif action == 'delete' and current_user.has_permission('task_delete'):
            # 批量删除
            deleted_count = 0
            for task in tasks:
                # 检查是否有关联的文案
                content_count = Content.query.filter_by(task_id=task.id).count()
                if content_count == 0:
                    # 删除关联的批次
                    Batch.query.filter_by(task_id=task.id).delete()
                    db.session.delete(task)
                    deleted_count += 1
            
            db.session.commit()
            flash(f'已删除 {deleted_count} 个任务', 'success')
        
        return redirect(url_for('task.task_list'))
    
    flash('表单验证失败', 'danger')
    return redirect(url_for('task.task_list'))


@task_bp.route('/batches')
@login_required
@permission_required('batch_view')
def batch_list():
    """批次列表"""
    form = BatchFilterForm()
    
    # 初始化表单选择项
    form.task_id.choices = [(0, '全部任务')] + [(t.id, t.name) for t in Task.query.all()]
    
    # 获取筛选参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    task_id = request.args.get('task_id', 0, type=int)
    search = request.args.get('search', '')
    sort_by = request.args.get('sort_by', 'created_at')
    sort_order = request.args.get('sort_order', 'desc')
    
    # 构建查询
    query = Batch.query
    
    # 应用筛选条件
    if task_id > 0:
        query = query.filter(Batch.task_id == task_id)
    
    if search:
        query = query.filter(Batch.name.like(f'%{search}%'))
    
    # 应用排序
    if sort_order == 'desc':
        query = query.order_by(desc(getattr(Batch, sort_by)))
    else:
        query = query.order_by(getattr(Batch, sort_by))
    
    # 分页
    pagination = query.paginate(page=page, per_page=per_page)
    batches = pagination.items
    
    return render_template(
        'task/batch_list.html',
        batches=batches,
        pagination=pagination,
        form=form,
        task_id=task_id,
        search=search,
        sort_by=sort_by,
        sort_order=sort_order
    )


@task_bp.route('/batches/create', methods=['POST'])
@login_required
@permission_required('batch_create')
def batch_create():
    """创建批次"""
    form = BatchForm()
    
    # 初始化表单选择项
    form.task_id.choices = [(t.id, t.name) for t in Task.query.all()]
    
    if form.validate_on_submit():
        # 创建新批次
        batch = Batch(
            task_id=form.task_id.data,
            name=form.name.data,
            content_count=form.content_count.data or 0,
            created_by=current_user.id
        )
        
        # 保存到数据库
        db.session.add(batch)
        db.session.commit()
        
        flash('批次创建成功', 'success')
        return redirect(url_for('task.task_view', task_id=form.task_id.data))
    
    # 如果验证失败，返回到任务详情页
    for field, errors in form.errors.items():
        for error in errors:
            flash(f'{getattr(form, field).label.text}: {error}', 'danger')
    
    return redirect(url_for('task.task_view', task_id=form.task_id.data))


@task_bp.route('/batches/<int:batch_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('batch_edit')
def batch_edit(batch_id):
    """编辑批次"""
    batch = Batch.query.get_or_404(batch_id)
    form = BatchForm(obj=batch)
    
    # 初始化表单选择项
    form.task_id.choices = [(t.id, t.name) for t in Task.query.all()]
    
    if form.validate_on_submit():
        # 更新批次信息
        batch.task_id = form.task_id.data
        batch.name = form.name.data
        batch.content_count = form.content_count.data or 0
        
        db.session.commit()
        flash('批次更新成功', 'success')
        return redirect(url_for('task.task_view', task_id=batch.task_id))
    
    return render_template('task/batch_edit.html', form=form, batch=batch)


@task_bp.route('/batches/<int:batch_id>/delete', methods=['POST'])
@login_required
@permission_required('batch_delete')
def batch_delete(batch_id):
    """删除批次"""
    batch = Batch.query.get_or_404(batch_id)
    task_id = batch.task_id
    
    # 检查是否有关联的文案
    content_count = Content.query.filter_by(batch_id=batch_id).count()
    if content_count > 0:
        flash(f'无法删除批次，该批次下有 {content_count} 个文案', 'danger')
        return redirect(url_for('task.task_view', task_id=task_id))
    
    # 检查是否是任务的唯一批次
    batch_count = Batch.query.filter_by(task_id=task_id).count()
    if batch_count <= 1:
        flash('无法删除批次，每个任务至少需要一个批次', 'danger')
        return redirect(url_for('task.task_view', task_id=task_id))
    
    # 删除批次
    db.session.delete(batch)
    db.session.commit()
    
    flash('批次已删除', 'success')
    return redirect(url_for('task.task_view', task_id=task_id))


@task_bp.route('/tasks/get-batches/<int:task_id>')
@login_required
def get_batches(task_id):
    """获取任务的批次列表"""
    batches = Batch.query.filter_by(task_id=task_id).all()
    return jsonify({
        'success': True,
        'batches': [{'id': batch.id, 'name': batch.name} for batch in batches]
    }) 


@task_bp.route('/list-json')
@login_required
def task_list_json():
    """获取任务列表（JSON API）"""
    tasks = Task.query.all()
    result = []
    
    for task in tasks:
        result.append({
            'id': task.id,
            'name': task.name,
            'client_id': task.client_id,
            'status': task.status
        })
    
    return jsonify(result) 