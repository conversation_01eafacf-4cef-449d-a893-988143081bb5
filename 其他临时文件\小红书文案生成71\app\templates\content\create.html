{% extends "base.html" %}

{% block title %}创建文案{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-md-10 col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="mb-0">创建文案</h3>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {{ form.csrf_token }}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.client_id.label(class="form-label") }}
                                    {{ form.client_id(class="form-select" + (" is-invalid" if form.client_id.errors else "")) }}
                                    {% if form.client_id.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.client_id.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.task_id.label(class="form-label") }}
                                    {{ form.task_id(class="form-select" + (" is-invalid" if form.task_id.errors else "")) }}
                                    {% if form.task_id.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.task_id.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.template_id.label(class="form-label") }}
                                    {{ form.template_id(class="form-select" + (" is-invalid" if form.template_id.errors else "")) }}
                                    {% if form.template_id.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.template_id.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.publish_priority.label(class="form-label") }}
                                    {{ form.publish_priority(class="form-select" + (" is-invalid" if form.publish_priority.errors else "")) }}
                                    {% if form.publish_priority.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.publish_priority.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-group">
                                    {{ form.title.label(class="form-label") }}
                                    {{ form.title(class="form-control" + (" is-invalid" if form.title.errors else "")) }}
                                    {% if form.title.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.title.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-group">
                                    {{ form.content.label(class="form-label") }}
                                    {{ form.content(class="form-control" + (" is-invalid" if form.content.errors else "")) }}
                                    {% if form.content.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.content.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.topics.label(class="form-label") }}
                                    {{ form.topics(class="form-control" + (" is-invalid" if form.topics.errors else ""), placeholder="多个话题用逗号分隔") }}
                                    {% if form.topics.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.topics.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <div class="form-text text-muted">多个话题用逗号分隔，最多10个</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.location.label(class="form-label") }}
                                    {{ form.location(class="form-control" + (" is-invalid" if form.location.errors else "")) }}
                                    {% if form.location.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.location.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.display_date.label(class="form-label") }}
                                    {{ form.display_date(class="form-control" + (" is-invalid" if form.display_date.errors else ""), type="date") }}
                                    {% if form.display_date.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.display_date.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.display_time.label(class="form-label") }}
                                    {{ form.display_time(class="form-control" + (" is-invalid" if form.display_time.errors else ""), type="time") }}
                                    {% if form.display_time.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.display_time.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-group">
                                    {{ form.images.label(class="form-label") }}
                                    {{ form.images(class="form-control" + (" is-invalid" if form.images.errors else ""), multiple=True) }}
                                    {% if form.images.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.images.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <div class="form-text text-muted">支持jpg, jpeg, png, gif格式，可多选</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ url_for('content.content_list') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> 返回列表
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存文案
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ simplemde.js }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 客户选择变化时，获取对应的任务列表
        document.getElementById('client_id').addEventListener('change', function() {
            const clientId = this.value;
            if (clientId > 0) {
                fetch(`/contents/get-tasks/${clientId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const taskSelect = document.getElementById('task_id');
                            // 清空现有选项
                            taskSelect.innerHTML = '';
                            // 添加新选项
                            data.tasks.forEach(task => {
                                const option = document.createElement('option');
                                option.value = task.id;
                                option.textContent = task.name;
                                taskSelect.appendChild(option);
                            });
                        }
                    })
                    .catch(error => console.error('Error:', error));
            }
        });
        
        // 模板选择变化时，获取模板内容
        document.getElementById('template_id').addEventListener('change', function() {
            const templateId = this.value;
            if (templateId > 0) {
                fetch(`/contents/get-template/${templateId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 如果标题和内容为空，则填充模板内容
                            const titleInput = document.getElementById('title');
                            
                            if (!titleInput.value) {
                                titleInput.value = data.title;
                            }
                            
                            if (window.simplemde) {
                                if (!window.simplemde.value()) {
                                    window.simplemde.value(data.content);
                                } else {
                                    if (confirm('是否用模板内容替换当前内容？')) {
                                        window.simplemde.value(data.content);
                                    }
                                }
                            }
                        }
                    })
                    .catch(error => console.error('Error:', error));
            }
        });
        
        // 初始化SimpleMDE
        const contentField = document.getElementById('content');
        if (contentField) {
            window.simplemde = new SimpleMDE({
                element: contentField,
                spellChecker: false,
                status: ['lines', 'words', 'cursor'],
                renderingConfig: {
                    codeSyntaxHighlighting: true,
                }
            });
        }
    });
</script>
{% endblock %} 