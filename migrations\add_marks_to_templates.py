"""
添加marks字段到templates表
用于存储模板中提取的标记，提高查询性能
"""

def upgrade_database():
    """升级数据库：添加marks字段"""
    import sys
    import os

    # 添加项目根目录到Python路径
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    from app import create_app, db
    from app.models.template import Template
    import re
    import json

    app = create_app()
    with app.app_context():
        try:
            # 1. 检查marks字段是否已存在
            from sqlalchemy import text
            with db.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT COLUMN_NAME
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = 'templates' AND COLUMN_NAME = 'marks'
                """)).fetchone()

                if not result:
                    # 添加marks字段
                    print("正在添加marks字段到templates表...")
                    conn.execute(text("""
                        ALTER TABLE templates
                        ADD COLUMN marks JSON COMMENT '模板中的标记列表，JSON格式存储'
                    """))
                    conn.commit()
                    print("marks字段添加成功")
                else:
                    print("marks字段已存在，跳过添加")

            # 2. 为现有模板提取并存储标记
            print("正在为现有模板提取标记...")
            templates = Template.query.all()
            updated_count = 0

            for template in templates:
                # 提取标记
                marks = extract_marks_from_template(template)

                # 更新数据库
                marks_json = json.dumps(marks, ensure_ascii=False)
                with db.engine.connect() as conn:
                    conn.execute(
                        text("UPDATE templates SET marks = :marks WHERE id = :template_id"),
                        {"marks": marks_json, "template_id": template.id}
                    )
                    conn.commit()

                if marks:
                    updated_count += 1
                    print(f"模板 {template.id} ({template.title[:30]}...) 提取到标记: {marks}")
                else:
                    print(f"模板 {template.id} ({template.title[:30]}...) 无标记")

            print(f"成功为 {len(templates)} 个模板处理了标记，其中 {updated_count} 个有标记")
            print("数据库升级完成！")

        except Exception as e:
            print(f"数据库升级失败: {e}")
            import traceback
            traceback.print_exc()
            raise

def extract_marks_from_template(template):
    """从模板中提取标记"""
    import re
    
    marks = set()
    
    # 从标题中提取标记
    if template.title:
        title_marks = re.findall(r'\{([^}]+)\}', template.title)
        marks.update(title_marks)
    
    # 从内容中提取标记
    if template.content:
        content_marks = re.findall(r'\{([^}]+)\}', template.content)
        marks.update(content_marks)
    
    return sorted(list(marks))

if __name__ == '__main__':
    upgrade_database()
