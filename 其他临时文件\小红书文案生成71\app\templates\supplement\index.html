{% extends "base.html" %}

{% block title %}文案补充机制{% endblock %}

{% block page_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">文案补充机制</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('supplement.settings') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-cog"></i> 补充设置
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> 当前补充来源设置：</h5>
                        {% if supplement_source == 'unassigned' %}
                        <p>从未分配文案池补充</p>
                        {% else %}
                        <p>生成新文案</p>
                        {% endif %}
                    </div>
                    
                    <h5>任务列表</h5>
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>任务名称</th>
                                    <th>客户</th>
                                    <th>状态</th>
                                    <th>目标数量</th>
                                    <th>实际数量</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for task in tasks %}
                                <tr>
                                    <td>{{ task.id }}</td>
                                    <td>{{ task.name }}</td>
                                    <td>{{ task.client.name if task.client else '无' }}</td>
                                    <td>
                                        {% if task.status == 'in_progress' %}
                                        <span class="badge bg-primary">进行中</span>
                                        {% elif task.status == 'completed' %}
                                        <span class="badge bg-success">已完成</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ task.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ task.target_count }}</td>
                                    <td>{{ task.actual_count }}</td>
                                    <td>{{ task.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        <a href="{{ url_for('supplement.task_detail', task_id=task.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-search"></i> 查看详情
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center">没有找到进行中的任务</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 