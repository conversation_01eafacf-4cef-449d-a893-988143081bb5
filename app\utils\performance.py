#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
性能监控和优化工具
"""

import time
import functools
import logging
from flask import g, request, current_app
from app import db

# 设置日志
logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """性能监控器"""
    
    @staticmethod
    def monitor_db_query(func):
        """数据库查询性能监控装饰器"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            
            execution_time = end_time - start_time
            
            # 记录慢查询（超过1秒）
            if execution_time > 1.0:
                logger.warning(f'慢查询检测: {func.__name__} 执行时间: {execution_time:.2f}秒')
            
            return result
        return wrapper
    
    @staticmethod
    def monitor_api_request(func):
        """API请求性能监控装饰器"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                end_time = time.time()
                execution_time = end_time - start_time
                
                # 记录慢请求（超过3秒）
                if execution_time > 3.0:
                    logger.warning(f'慢请求检测: {request.endpoint} 执行时间: {execution_time:.2f}秒')
                
                return result
                
            except Exception as e:
                end_time = time.time()
                execution_time = end_time - start_time
                logger.error(f'请求异常: {request.endpoint} 执行时间: {execution_time:.2f}秒, 错误: {str(e)}')
                raise
                
        return wrapper
    
    @staticmethod
    def get_system_stats():
        """获取系统统计信息"""
        try:
            from app.models.content import Content
            from app.models.image import ContentImage
            from app.models.client import Client, ClientShareLink
            from app.models.task import Task, Batch
            from app.models.user import User
            
            stats = {
                'database': {
                    'total_contents': Content.query.count(),
                    'total_images': ContentImage.query.count(),
                    'total_clients': Client.query.count(),
                    'total_tasks': Task.query.count(),
                    'total_batches': Batch.query.count(),
                    'total_users': User.query.count(),
                    'active_share_links': ClientShareLink.query.filter_by(is_active=True).count()
                },
                'workflow': {
                    'pending_review': Content.query.filter_by(workflow_status='pending_review').count(),
                    'pending_image_upload': Content.query.filter_by(workflow_status='pending_image_upload').count(),
                    'pending_final_review': Content.query.filter_by(workflow_status='pending_final_review').count(),
                    'pending_client_review': Content.query.filter_by(workflow_status='pending_client_review').count(),
                    'ready_to_publish': Content.query.filter_by(workflow_status='ready_to_publish').count(),
                    'published': Content.query.filter_by(workflow_status='published').count()
                }
            }
            
            return stats
            
        except Exception as e:
            logger.error(f'获取系统统计失败: {str(e)}')
            return None
    
    @staticmethod
    def optimize_query_suggestions():
        """查询优化建议"""
        suggestions = []
        
        try:
            # 检查是否有大量待处理的内容
            from app.models.content import Content
            
            pending_count = Content.query.filter(
                Content.workflow_status.in_(['pending_review', 'pending_image_upload', 'pending_final_review'])
            ).count()
            
            if pending_count > 1000:
                suggestions.append({
                    'type': 'warning',
                    'message': f'系统中有 {pending_count} 篇待处理文案，建议及时处理以提高系统性能'
                })
            
            # 检查是否有过期的分享链接
            from app.models.client import ClientShareLink
            from datetime import datetime
            
            expired_links = ClientShareLink.query.filter(
                ClientShareLink.expires_at < datetime.now(),
                ClientShareLink.is_active == True
            ).count()
            
            if expired_links > 0:
                suggestions.append({
                    'type': 'info',
                    'message': f'发现 {expired_links} 个过期但仍活跃的分享链接，建议清理'
                })
            
            # 检查是否有大量图片文件
            from app.models.image import ContentImage
            
            image_count = ContentImage.query.count()
            if image_count > 10000:
                suggestions.append({
                    'type': 'warning',
                    'message': f'系统中有 {image_count} 张图片，建议定期清理无用图片'
                })
            
        except Exception as e:
            logger.error(f'生成优化建议失败: {str(e)}')
        
        return suggestions

class QueryOptimizer:
    """查询优化器"""
    
    @staticmethod
    def optimize_content_list_query(query, filters=None):
        """优化内容列表查询"""
        # 使用joinedload预加载关联数据
        from sqlalchemy.orm import joinedload
        from app.models.content import Content
        
        # 预加载关联的客户、任务、批次信息
        query = query.options(
            joinedload(Content.client),
            joinedload(Content.task),
            joinedload(Content.batch)
        )
        
        # 应用筛选条件
        if filters:
            if 'workflow_status' in filters and filters['workflow_status']:
                query = query.filter(Content.workflow_status == filters['workflow_status'])
            
            if 'client_id' in filters and filters['client_id']:
                query = query.filter(Content.client_id == filters['client_id'])
            
            if 'task_id' in filters and filters['task_id']:
                query = query.filter(Content.task_id == filters['task_id'])
            
            if 'batch_id' in filters and filters['batch_id']:
                query = query.filter(Content.batch_id == filters['batch_id'])
        
        return query
    
    @staticmethod
    def get_content_with_images(content_id):
        """优化获取带图片的内容"""
        from sqlalchemy.orm import joinedload
        from app.models.content import Content
        
        content = Content.query.options(
            joinedload(Content.client),
            joinedload(Content.task),
            joinedload(Content.batch)
        ).get(content_id)
        
        if content:
            # 单独查询图片，避免N+1问题
            from app.models.image import ContentImage
            images = ContentImage.query.filter_by(content_id=content_id).all()
            return content, images
        
        return None, []

class CacheManager:
    """缓存管理器"""
    
    @staticmethod
    def cache_key(prefix, *args):
        """生成缓存键"""
        return f"{prefix}:{'_'.join(str(arg) for arg in args)}"
    
    @staticmethod
    def get_or_set_cache(key, func, timeout=300):
        """获取或设置缓存"""
        try:
            # 这里可以集成Redis或其他缓存系统
            # 目前使用简单的内存缓存
            if not hasattr(g, '_cache'):
                g._cache = {}
            
            if key in g._cache:
                return g._cache[key]
            
            result = func()
            g._cache[key] = result
            return result
            
        except Exception as e:
            logger.error(f'缓存操作失败: {str(e)}')
            return func()
    
    @staticmethod
    def clear_cache(pattern=None):
        """清除缓存"""
        try:
            if hasattr(g, '_cache'):
                if pattern:
                    # 清除匹配模式的缓存
                    keys_to_remove = [k for k in g._cache.keys() if pattern in k]
                    for key in keys_to_remove:
                        del g._cache[key]
                else:
                    # 清除所有缓存
                    g._cache.clear()
        except Exception as e:
            logger.error(f'清除缓存失败: {str(e)}')

def init_performance_monitoring(app):
    """初始化性能监控"""
    
    @app.before_request
    def before_request():
        g.start_time = time.time()
    
    @app.after_request
    def after_request(response):
        if hasattr(g, 'start_time'):
            execution_time = time.time() - g.start_time
            
            # 记录慢请求
            if execution_time > 2.0:
                logger.warning(f'慢请求: {request.endpoint} - {execution_time:.2f}秒')
            
            # 添加性能头
            response.headers['X-Response-Time'] = f'{execution_time:.3f}s'
        
        return response
    
    @app.teardown_appcontext
    def teardown_db(error):
        """清理数据库连接"""
        if hasattr(g, '_cache'):
            # 清理请求级缓存
            g._cache.clear()

# 性能监控装饰器
monitor_db = PerformanceMonitor.monitor_db_query
monitor_api = PerformanceMonitor.monitor_api_request
